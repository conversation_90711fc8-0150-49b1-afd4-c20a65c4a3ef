// Crypto Engine Library - Main exports
export * from './contracts/TokenFactory';
export * from './contracts/LiquidityPoolManager';
export * from './market/PriceTracker';
export * from './market/MarketAnalyzer';
export * from './wallet/WalletManager';
export * from './defi/DEXIntegration';
export * from './types/CryptoTypes';
export * from './utils/CryptoUtils';

// Core crypto configuration
export interface CryptoConfig {
  ethereumRpcUrl?: string;
  polygonRpcUrl?: string;
  bscRpcUrl?: string;
  infuraApiKey?: string;
  alchemyApiKey?: string;
  coinGeckoApiKey?: string;
  coinMarketCapApiKey?: string;
  defaultNetwork?: string;
  gasLimit?: number;
  gasPrice?: string;
}

// Default configuration
export const defaultCryptoConfig: CryptoConfig = {
  defaultNetwork: 'ethereum',
  gasLimit: 21000,
  gasPrice: '20000000000' // 20 gwei
};

// Initialize Crypto Engine
export class CryptoEngine {
  private config: CryptoConfig;

  constructor(config: Partial<CryptoConfig> = {}) {
    this.config = { ...defaultCryptoConfig, ...config };
  }

  getConfig(): CryptoConfig {
    return this.config;
  }

  updateConfig(newConfig: Partial<CryptoConfig>): void {
    this.config = { ...this.config, ...newConfig };
  }

  // Network utilities
  getSupportedNetworks(): string[] {
    return ['ethereum', 'polygon', 'bsc', 'arbitrum', 'optimism'];
  }

  getNetworkConfig(network: string): any {
    const configs = {
      ethereum: {
        chainId: 1,
        name: 'Ethereum Mainnet',
        rpcUrl: this.config.ethereumRpcUrl || 'https://mainnet.infura.io/v3/',
        blockExplorer: 'https://etherscan.io'
      },
      polygon: {
        chainId: 137,
        name: 'Polygon Mainnet',
        rpcUrl: this.config.polygonRpcUrl || 'https://polygon-rpc.com',
        blockExplorer: 'https://polygonscan.com'
      },
      bsc: {
        chainId: 56,
        name: 'BSC Mainnet',
        rpcUrl: this.config.bscRpcUrl || 'https://bsc-dataseed.binance.org',
        blockExplorer: 'https://bscscan.com'
      }
    };

    return configs[network as keyof typeof configs];
  }
}

// Export singleton instance
export const cryptoEngine = new CryptoEngine();
