import { create } from 'zustand';
import { persist, createJSONStorage } from 'zustand/middleware';

// Types
interface CryptoCurrency {
  id: string;
  symbol: string;
  name: string;
  current_price: number;
  market_cap: number;
  market_cap_rank: number;
  price_change_percentage_24h: number;
  price_change_percentage_7d: number;
  total_volume: number;
  high_24h: number;
  low_24h: number;
  image: string;
  last_updated: string;
}

interface PortfolioItem {
  id: string;
  symbol: string;
  name: string;
  amount: number;
  purchasePrice: number;
  purchaseDate: string;
  currentPrice: number;
  value: number;
  profit: number;
  profitPercentage: number;
}

interface PriceAlert {
  id: string;
  symbol: string;
  targetPrice: number;
  condition: 'above' | 'below';
  isActive: boolean;
  createdAt: string;
}

interface TradingPair {
  symbol: string;
  baseAsset: string;
  quoteAsset: string;
  price: number;
  change24h: number;
  volume24h: number;
}

interface CryptoState {
  // Market data
  currencies: CryptoCurrency[];
  selectedCurrency: CryptoCurrency | null;
  tradingPairs: TradingPair[];
  
  // Portfolio
  portfolio: PortfolioItem[];
  totalPortfolioValue: number;
  totalProfit: number;
  totalProfitPercentage: number;
  
  // Alerts
  priceAlerts: PriceAlert[];
  
  // Settings
  watchlist: string[];
  preferredCurrency: string;
  autoRefresh: boolean;
  refreshInterval: number;
  
  // UI state
  selectedTimeframe: '1h' | '24h' | '7d' | '30d' | '1y';
  chartType: 'line' | 'candlestick' | 'area';
  
  // Loading states
  isLoadingMarketData: boolean;
  isLoadingPortfolio: boolean;
  lastUpdated: number;
  
  // Actions
  setCurrencies: (currencies: CryptoCurrency[]) => void;
  setSelectedCurrency: (currency: CryptoCurrency | null) => void;
  addToWatchlist: (symbol: string) => void;
  removeFromWatchlist: (symbol: string) => void;
  addPortfolioItem: (item: Omit<PortfolioItem, 'id' | 'currentPrice' | 'value' | 'profit' | 'profitPercentage'>) => void;
  updatePortfolioItem: (id: string, updates: Partial<PortfolioItem>) => void;
  removePortfolioItem: (id: string) => void;
  addPriceAlert: (alert: Omit<PriceAlert, 'id' | 'createdAt'>) => void;
  removePriceAlert: (id: string) => void;
  togglePriceAlert: (id: string) => void;
  updateSettings: (settings: Partial<Pick<CryptoState, 'preferredCurrency' | 'autoRefresh' | 'refreshInterval'>>) => void;
  setTimeframe: (timeframe: CryptoState['selectedTimeframe']) => void;
  setChartType: (chartType: CryptoState['chartType']) => void;
  setLoadingMarketData: (loading: boolean) => void;
  setLoadingPortfolio: (loading: boolean) => void;
  updateLastUpdated: () => void;
  calculatePortfolioTotals: () => void;
  reset: () => void;
}

const initialState = {
  currencies: [],
  selectedCurrency: null,
  tradingPairs: [],
  portfolio: [],
  totalPortfolioValue: 0,
  totalProfit: 0,
  totalProfitPercentage: 0,
  priceAlerts: [],
  watchlist: ['bitcoin', 'ethereum', 'cardano', 'polkadot', 'solana'],
  preferredCurrency: 'usd',
  autoRefresh: true,
  refreshInterval: 30000,
  selectedTimeframe: '24h' as const,
  chartType: 'line' as const,
  isLoadingMarketData: false,
  isLoadingPortfolio: false,
  lastUpdated: 0,
};

export const useCryptoStore = create<CryptoState>()(
  persist(
    (set, get) => ({
      ...initialState,
      
      setCurrencies: (currencies) => {
        set({ currencies });
        get().calculatePortfolioTotals();
        get().updateLastUpdated();
      },
      
      setSelectedCurrency: (currency) => set({ selectedCurrency: currency }),
      
      addToWatchlist: (symbol) =>
        set((state) => ({
          watchlist: [...new Set([...state.watchlist, symbol])],
        })),
      
      removeFromWatchlist: (symbol) =>
        set((state) => ({
          watchlist: state.watchlist.filter((s) => s !== symbol),
        })),
      
      addPortfolioItem: (item) => {
        const id = Date.now().toString() + Math.random().toString(36).substr(2, 9);
        const newItem: PortfolioItem = {
          ...item,
          id,
          currentPrice: 0,
          value: 0,
          profit: 0,
          profitPercentage: 0,
        };
        
        set((state) => ({
          portfolio: [...state.portfolio, newItem],
        }));
        
        get().calculatePortfolioTotals();
      },
      
      updatePortfolioItem: (id, updates) =>
        set((state) => ({
          portfolio: state.portfolio.map((item) =>
            item.id === id ? { ...item, ...updates } : item
          ),
        })),
      
      removePortfolioItem: (id) => {
        set((state) => ({
          portfolio: state.portfolio.filter((item) => item.id !== id),
        }));
        get().calculatePortfolioTotals();
      },
      
      addPriceAlert: (alert) => {
        const id = Date.now().toString() + Math.random().toString(36).substr(2, 9);
        const newAlert: PriceAlert = {
          ...alert,
          id,
          createdAt: new Date().toISOString(),
        };
        
        set((state) => ({
          priceAlerts: [...state.priceAlerts, newAlert],
        }));
      },
      
      removePriceAlert: (id) =>
        set((state) => ({
          priceAlerts: state.priceAlerts.filter((alert) => alert.id !== id),
        })),
      
      togglePriceAlert: (id) =>
        set((state) => ({
          priceAlerts: state.priceAlerts.map((alert) =>
            alert.id === id ? { ...alert, isActive: !alert.isActive } : alert
          ),
        })),
      
      updateSettings: (settings) => set(settings),
      
      setTimeframe: (timeframe) => set({ selectedTimeframe: timeframe }),
      
      setChartType: (chartType) => set({ chartType }),
      
      setLoadingMarketData: (loading) => set({ isLoadingMarketData: loading }),
      
      setLoadingPortfolio: (loading) => set({ isLoadingPortfolio: loading }),
      
      updateLastUpdated: () => set({ lastUpdated: Date.now() }),
      
      calculatePortfolioTotals: () => {
        const { portfolio, currencies } = get();
        
        let totalValue = 0;
        let totalProfit = 0;
        let totalInvested = 0;
        
        const updatedPortfolio = portfolio.map((item) => {
          const currency = currencies.find((c) => c.symbol.toLowerCase() === item.symbol.toLowerCase());
          const currentPrice = currency?.current_price || 0;
          const value = item.amount * currentPrice;
          const invested = item.amount * item.purchasePrice;
          const profit = value - invested;
          const profitPercentage = invested > 0 ? (profit / invested) * 100 : 0;
          
          totalValue += value;
          totalProfit += profit;
          totalInvested += invested;
          
          return {
            ...item,
            currentPrice,
            value,
            profit,
            profitPercentage,
          };
        });
        
        const totalProfitPercentage = totalInvested > 0 ? (totalProfit / totalInvested) * 100 : 0;
        
        set({
          portfolio: updatedPortfolio,
          totalPortfolioValue: totalValue,
          totalProfit,
          totalProfitPercentage,
        });
      },
      
      reset: () => set(initialState),
    }),
    {
      name: 'chainsight-crypto-store',
      storage: createJSONStorage(() => localStorage),
      partialize: (state) => ({
        portfolio: state.portfolio,
        priceAlerts: state.priceAlerts,
        watchlist: state.watchlist,
        preferredCurrency: state.preferredCurrency,
        autoRefresh: state.autoRefresh,
        refreshInterval: state.refreshInterval,
        selectedTimeframe: state.selectedTimeframe,
        chartType: state.chartType,
      }),
    }
  )
);

// Selectors
export const useCryptoCurrencies = () => useCryptoStore((state) => state.currencies);
export const useSelectedCurrency = () => useCryptoStore((state) => state.selectedCurrency);
export const usePortfolio = () => useCryptoStore((state) => state.portfolio);
export const usePortfolioTotals = () => useCryptoStore((state) => ({
  totalValue: state.totalPortfolioValue,
  totalProfit: state.totalProfit,
  totalProfitPercentage: state.totalProfitPercentage,
}));
export const usePriceAlerts = () => useCryptoStore((state) => state.priceAlerts);
export const useWatchlist = () => useCryptoStore((state) => state.watchlist);
export const useCryptoSettings = () => useCryptoStore((state) => ({
  preferredCurrency: state.preferredCurrency,
  autoRefresh: state.autoRefresh,
  refreshInterval: state.refreshInterval,
  selectedTimeframe: state.selectedTimeframe,
  chartType: state.chartType,
}));

// Action selectors
export const useCryptoActions = () => useCryptoStore((state) => ({
  setCurrencies: state.setCurrencies,
  setSelectedCurrency: state.setSelectedCurrency,
  addToWatchlist: state.addToWatchlist,
  removeFromWatchlist: state.removeFromWatchlist,
  addPortfolioItem: state.addPortfolioItem,
  updatePortfolioItem: state.updatePortfolioItem,
  removePortfolioItem: state.removePortfolioItem,
  addPriceAlert: state.addPriceAlert,
  removePriceAlert: state.removePriceAlert,
  togglePriceAlert: state.togglePriceAlert,
  updateSettings: state.updateSettings,
  setTimeframe: state.setTimeframe,
  setChartType: state.setChartType,
  calculatePortfolioTotals: state.calculatePortfolioTotals,
}));
