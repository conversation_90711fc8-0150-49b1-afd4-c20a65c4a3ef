"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/store/useAppStore.ts":
/*!**********************************!*\
  !*** ./src/store/useAppStore.ts ***!
  \**********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useAppActions: () => (/* binding */ useAppActions),\n/* harmony export */   useAppStore: () => (/* binding */ useAppStore),\n/* harmony export */   useConnectionStatus: () => (/* binding */ useConnectionStatus),\n/* harmony export */   useError: () => (/* binding */ useError),\n/* harmony export */   useIsAuthenticated: () => (/* binding */ useIsAuthenticated),\n/* harmony export */   useIsLoading: () => (/* binding */ useIsLoading),\n/* harmony export */   useNotifications: () => (/* binding */ useNotifications),\n/* harmony export */   useSettings: () => (/* binding */ useSettings),\n/* harmony export */   useUnreadNotifications: () => (/* binding */ useUnreadNotifications),\n/* harmony export */   useUser: () => (/* binding */ useUser)\n/* harmony export */ });\n/* harmony import */ var zustand__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! zustand */ \"(app-pages-browser)/./node_modules/zustand/esm/react.mjs\");\n/* harmony import */ var zustand_middleware__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! zustand/middleware */ \"(app-pages-browser)/./node_modules/zustand/esm/middleware.mjs\");\n\n\n\n// Safe storage for SSR\nconst safeStorage = ()=>{\n    if (false) {}\n    return localStorage;\n};\nconst initialState = {\n    user: null,\n    isAuthenticated: false,\n    settings: {\n        sidebarCollapsed: false,\n        activeModule: 'crypto',\n        autoRefresh: true,\n        refreshInterval: 30000,\n        enableAnimations: true,\n        enableSounds: true\n    },\n    notifications: [],\n    connectionStatus: {\n        api: 'disconnected',\n        websocket: 'disconnected',\n        blockchain: 'disconnected'\n    },\n    isLoading: false,\n    loadingMessage: '',\n    error: null\n};\nconst useAppStore = (0,zustand__WEBPACK_IMPORTED_MODULE_0__.create)()((0,zustand_middleware__WEBPACK_IMPORTED_MODULE_1__.subscribeWithSelector)((0,zustand_middleware__WEBPACK_IMPORTED_MODULE_1__.persist)((set, get)=>({\n        ...initialState,\n        setUser: (user)=>set({\n                user,\n                isAuthenticated: !!user\n            }),\n        setAuthenticated: (authenticated)=>set({\n                isAuthenticated: authenticated\n            }),\n        updateSettings: (newSettings)=>set((state)=>({\n                    settings: {\n                        ...state.settings,\n                        ...newSettings\n                    }\n                })),\n        addNotification: (notification)=>set((state)=>({\n                    notifications: [\n                        {\n                            ...notification,\n                            id: Date.now().toString() + Math.random().toString(36).substr(2, 9),\n                            timestamp: Date.now(),\n                            read: false\n                        },\n                        ...state.notifications\n                    ].slice(0, 50)\n                })),\n        removeNotification: (id)=>set((state)=>({\n                    notifications: state.notifications.filter((n)=>n.id !== id)\n                })),\n        markNotificationRead: (id)=>set((state)=>({\n                    notifications: state.notifications.map((n)=>n.id === id ? {\n                            ...n,\n                            read: true\n                        } : n)\n                })),\n        clearNotifications: ()=>set({\n                notifications: []\n            }),\n        updateConnectionStatus: (status)=>set((state)=>({\n                    connectionStatus: {\n                        ...state.connectionStatus,\n                        ...status\n                    }\n                })),\n        setLoading: function(loading) {\n            let message = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : '';\n            return set({\n                isLoading: loading,\n                loadingMessage: message\n            });\n        },\n        setError: (error)=>set({\n                error\n            }),\n        reset: ()=>set(initialState)\n    }), {\n    name: 'chainsight-app-store',\n    storage: (0,zustand_middleware__WEBPACK_IMPORTED_MODULE_1__.createJSONStorage)(()=>safeStorage()),\n    partialize: (state)=>({\n            user: state.user,\n            isAuthenticated: state.isAuthenticated,\n            settings: state.settings,\n            notifications: state.notifications.filter((n)=>n.persistent)\n        })\n})));\n// Selectors for better performance\nconst useUser = ()=>useAppStore({\n        \"useUser.useAppStore\": (state)=>state.user\n    }[\"useUser.useAppStore\"]);\nconst useIsAuthenticated = ()=>useAppStore({\n        \"useIsAuthenticated.useAppStore\": (state)=>state.isAuthenticated\n    }[\"useIsAuthenticated.useAppStore\"]);\nconst useSettings = ()=>useAppStore({\n        \"useSettings.useAppStore\": (state)=>state.settings\n    }[\"useSettings.useAppStore\"]);\nconst useNotifications = ()=>useAppStore({\n        \"useNotifications.useAppStore\": (state)=>state.notifications\n    }[\"useNotifications.useAppStore\"]);\nconst useUnreadNotifications = ()=>useAppStore({\n        \"useUnreadNotifications.useAppStore\": (state)=>state.notifications.filter({\n                \"useUnreadNotifications.useAppStore\": (n)=>!n.read\n            }[\"useUnreadNotifications.useAppStore\"])\n    }[\"useUnreadNotifications.useAppStore\"]);\nconst useConnectionStatus = ()=>useAppStore({\n        \"useConnectionStatus.useAppStore\": (state)=>state.connectionStatus\n    }[\"useConnectionStatus.useAppStore\"]);\nconst useIsLoading = ()=>useAppStore({\n        \"useIsLoading.useAppStore\": (state)=>state.isLoading\n    }[\"useIsLoading.useAppStore\"]);\nconst useError = ()=>useAppStore({\n        \"useError.useAppStore\": (state)=>state.error\n    }[\"useError.useAppStore\"]);\n// Action selectors\nconst useAppActions = ()=>useAppStore({\n        \"useAppActions.useAppStore\": (state)=>({\n                setUser: state.setUser,\n                setAuthenticated: state.setAuthenticated,\n                updateSettings: state.updateSettings,\n                addNotification: state.addNotification,\n                removeNotification: state.removeNotification,\n                markNotificationRead: state.markNotificationRead,\n                clearNotifications: state.clearNotifications,\n                updateConnectionStatus: state.updateConnectionStatus,\n                setLoading: state.setLoading,\n                setError: state.setError,\n                reset: state.reset\n            })\n    }[\"useAppActions.useAppStore\"]);\n// Subscribe to changes for side effects\nuseAppStore.subscribe((state)=>state.connectionStatus, (connectionStatus)=>{\n    // Auto-add notifications for connection changes\n    const { addNotification } = useAppStore.getState();\n    Object.entries(connectionStatus).forEach((param)=>{\n        let [service, status] = param;\n        if (status === 'connected') {\n            addNotification({\n                type: 'success',\n                title: 'Connection Established',\n                message: \"\".concat(service.toUpperCase(), \" connection is now active\"),\n                read: false\n            });\n        } else if (status === 'error') {\n            addNotification({\n                type: 'error',\n                title: 'Connection Error',\n                message: \"Failed to connect to \".concat(service.toUpperCase()),\n                read: false\n            });\n        }\n    });\n});\n// Auto-remove old notifications\nuseAppStore.subscribe((state)=>state.notifications, (notifications)=>{\n    const now = Date.now();\n    const oneHour = 60 * 60 * 1000;\n    const expiredNotifications = notifications.filter((n)=>!n.persistent && now - n.timestamp > oneHour);\n    if (expiredNotifications.length > 0) {\n        const { removeNotification } = useAppStore.getState();\n        expiredNotifications.forEach((n)=>removeNotification(n.id));\n    }\n});\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/store/useAppStore.ts\n"));

/***/ })

});