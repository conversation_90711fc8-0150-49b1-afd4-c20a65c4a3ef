# ===================================
# CHAINSIGHT BACKEND DEPENDENCIES
# ===================================

# Core Framework
fastapi==0.104.1
uvicorn[standard]==0.24.0
pydantic==2.5.0
python-multipart==0.0.6
python-dotenv==1.0.0

# Database & ORM
sqlalchemy==2.0.23
alembic==1.12.1
asyncpg==0.29.0
redis==5.0.1

# Authentication & Security
python-jose[cryptography]==3.3.0
passlib[bcrypt]==1.7.4
cryptography==41.0.7

# HTTP Client & WebSocket
httpx==0.25.2
websockets==12.0
python-socketio==5.10.0
socketio==5.10.0

# AI & Machine Learning
openai==1.3.7
anthropic==0.7.7
transformers==4.35.2
torch==2.1.1
scikit-learn==1.3.2
pandas==2.1.3
numpy==1.25.2
tensorflow==2.14.0

# Natural Language Processing
nltk==3.8.1
spacy==3.7.2
textblob==0.17.1

# Blockchain & Web3
web3==6.11.3
eth-account==0.9.0
py-solc-x==1.12.0
eth-utils==2.3.1

# Finance APIs
yfinance==0.2.22
alpha-vantage==2.3.1
ccxt==4.1.30
requests==2.31.0

# Computer Vision & Image Processing
opencv-python-headless==********
Pillow==10.1.0
face-recognition==1.3.0
dlib==19.24.2

# Background Tasks & Queue
celery==5.3.4
kombu==5.3.4
billiard==4.2.0

# Email & Notifications
fastapi-mail==1.4.1
sendgrid==6.10.0
twilio==8.10.0

# Data Processing & Utilities
pydantic-settings==2.1.0
python-dateutil==2.8.2
pytz==2023.3
validators==0.22.0
python-slugify==8.0.1

# File Processing
python-magic==0.4.27
PyPDF2==3.0.1
python-docx==1.1.0
openpyxl==3.1.2

# Monitoring & Logging
prometheus-client==0.19.0
structlog==23.2.0
sentry-sdk[fastapi]==1.38.0

# Testing
pytest==7.4.3
pytest-asyncio==0.21.1
pytest-cov==4.1.0
httpx==0.25.2
factory-boy==3.3.0

# Development Tools
black==23.11.0
isort==5.12.0
flake8==6.1.0
mypy==1.7.1
pre-commit==3.6.0

# Production & Deployment
gunicorn==21.2.0
docker==6.1.3
kubernetes==28.1.0

# Additional Utilities
click==8.1.7
rich==13.7.0
typer==0.9.0
schedule==1.2.0
