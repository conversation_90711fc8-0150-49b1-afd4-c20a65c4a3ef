// Core AI Types and Interfaces

export interface ChatMessage {
  id: string;
  role: 'user' | 'assistant' | 'system';
  content: string;
  timestamp: Date;
  metadata?: Record<string, any>;
}

export interface ChatSession {
  id: string;
  userId: string;
  messages: ChatMessage[];
  createdAt: Date;
  updatedAt: Date;
  context?: string;
}

export interface AIResponse {
  content: string;
  confidence: number;
  processingTime: number;
  model: string;
  tokens?: {
    prompt: number;
    completion: number;
    total: number;
  };
}

export interface TextAnalysisResult {
  sentiment: {
    score: number;
    label: 'positive' | 'negative' | 'neutral';
    confidence: number;
  };
  entities: Array<{
    text: string;
    type: string;
    confidence: number;
  }>;
  keywords: Array<{
    text: string;
    relevance: number;
  }>;
  language: string;
  readability: {
    score: number;
    level: string;
  };
}

export interface PredictionInput {
  data: number[] | Record<string, any>;
  features?: string[];
  timeframe?: string;
  model?: string;
}

export interface PredictionResult {
  prediction: number | string | Record<string, any>;
  confidence: number;
  model: string;
  features: string[];
  timestamp: Date;
  metadata?: Record<string, any>;
}

export interface MLModel {
  id: string;
  name: string;
  type: 'classification' | 'regression' | 'clustering' | 'nlp';
  version: string;
  accuracy?: number;
  trainedAt: Date;
  features: string[];
  metadata?: Record<string, any>;
}

export interface TrainingData {
  inputs: any[];
  outputs: any[];
  features: string[];
  metadata?: Record<string, any>;
}

export interface ModelMetrics {
  accuracy?: number;
  precision?: number;
  recall?: number;
  f1Score?: number;
  mse?: number;
  mae?: number;
  r2Score?: number;
}

export interface AIAgent {
  id: string;
  name: string;
  description: string;
  capabilities: string[];
  model: string;
  systemPrompt: string;
  temperature: number;
  maxTokens: number;
  isActive: boolean;
}

export interface ConversationContext {
  userId: string;
  sessionId: string;
  domain: string;
  previousMessages: ChatMessage[];
  userPreferences?: Record<string, any>;
  metadata?: Record<string, any>;
}

export interface AIError {
  code: string;
  message: string;
  details?: Record<string, any>;
  timestamp: Date;
}

// Enums
export enum AIModelType {
  GPT_3_5_TURBO = 'gpt-3.5-turbo',
  GPT_4 = 'gpt-4',
  GPT_4_TURBO = 'gpt-4-turbo-preview',
  CLAUDE_3_SONNET = 'claude-3-sonnet',
  CLAUDE_3_OPUS = 'claude-3-opus'
}

export enum AnalysisType {
  SENTIMENT = 'sentiment',
  ENTITY_EXTRACTION = 'entity_extraction',
  KEYWORD_EXTRACTION = 'keyword_extraction',
  LANGUAGE_DETECTION = 'language_detection',
  READABILITY = 'readability',
  CLASSIFICATION = 'classification'
}

export enum PredictionType {
  PRICE_PREDICTION = 'price_prediction',
  TREND_ANALYSIS = 'trend_analysis',
  VOLATILITY_FORECAST = 'volatility_forecast',
  SENTIMENT_PREDICTION = 'sentiment_prediction',
  CLASSIFICATION = 'classification',
  REGRESSION = 'regression'
}
