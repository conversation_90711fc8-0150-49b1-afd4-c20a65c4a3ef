{"name": "@chainsight/frontend", "version": "1.0.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "clean": "rm -rf .next"}, "dependencies": {"next": "^14.0.0", "react": "^18.2.0", "react-dom": "^18.2.0", "tailwindcss": "^3.3.0", "framer-motion": "^10.16.0", "ethers": "^6.8.0", "@web3modal/ethereum": "^2.7.0", "@web3modal/react": "^2.7.0", "wagmi": "^1.4.0", "viem": "^1.16.0", "socket.io-client": "^4.7.0", "react-hook-form": "^7.47.0", "react-query": "^3.39.0", "axios": "^1.5.0", "lucide-react": "^0.288.0", "lottie-react": "^2.4.0", "react-webcam": "^7.1.0", "recharts": "^2.8.0", "react-hot-toast": "^2.4.0", "zustand": "^4.4.0"}, "devDependencies": {"@types/node": "^20.8.0", "@types/react": "^18.2.0", "@types/react-dom": "^18.2.0", "typescript": "^5.2.0", "eslint": "^8.51.0", "eslint-config-next": "^14.0.0", "autoprefixer": "^10.4.0", "postcss": "^8.4.0"}}