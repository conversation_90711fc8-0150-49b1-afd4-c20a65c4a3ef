import type { Address } from 'abitype';
import type * as WebAuthnP256 from 'ox/WebAuthnP256';
import type { LocalAccount } from '../../../accounts/types.js';
import type { Client } from '../../../clients/createClient.js';
import type { Hash, Hex } from '../../../types/misc.js';
import type { Assign, OneOf, Prettify } from '../../../types/utils.js';
import { entryPoint06Abi } from '../../constants/abis.js';
import type { SmartAccount, SmartAccountImplementation, WebAuthnAccount } from '../types.js';
export type ToCoinbaseSmartAccountParameters = {
    address?: Address | undefined;
    client: Client;
    ownerIndex?: number | undefined;
    owners: readonly (Address | OneOf<LocalAccount | WebAuthnAccount>)[];
    nonce?: bigint | undefined;
};
export type ToCoinbaseSmartAccountReturnType = Prettify<SmartAccount<CoinbaseSmartAccountImplementation>>;
export type CoinbaseSmartAccountImplementation = Assign<SmartAccountImplementation<typeof entryPoint06Abi, '0.6', {
    abi: typeof abi;
    factory: {
        abi: typeof factoryAbi;
        address: Address;
    };
}>, {
    decodeCalls: NonNullable<SmartAccountImplementation['decodeCalls']>;
    sign: NonNullable<SmartAccountImplementation['sign']>;
}>;
/**
 * @description Create a Coinbase Smart Account.
 *
 * @param parameters - {@link ToCoinbaseSmartAccountParameters}
 * @returns Coinbase Smart Account. {@link ToCoinbaseSmartAccountReturnType}
 *
 * @example
 * import { toCoinbaseSmartAccount } from 'viem/account-abstraction'
 * import { privateKeyToAccount } from 'viem/accounts'
 * import { client } from './client.js'
 *
 * const account = toCoinbaseSmartAccount({
 *   client,
 *   owners: [privateKeyToAccount('0x...')],
 * })
 */
export declare function toCoinbaseSmartAccount(parameters: ToCoinbaseSmartAccountParameters): Promise<ToCoinbaseSmartAccountReturnType>;
/** @internal */
export declare function sign({ hash, owner, }: {
    hash: Hash;
    owner: OneOf<LocalAccount | WebAuthnAccount>;
}): Promise<`0x${string}`>;
/** @internal */
export declare function toReplaySafeHash({ address, chainId, hash, }: {
    address: Address;
    chainId: number;
    hash: Hash;
}): `0x${string}`;
/** @internal */
export declare function toWebAuthnSignature({ webauthn, signature, }: {
    webauthn: WebAuthnP256.SignMetadata;
    signature: Hex;
}): `0x${string}`;
/** @internal */
export declare function wrapSignature(parameters: {
    ownerIndex?: number | undefined;
    signature: Hex;
}): `0x${string}`;
declare const abi: readonly [{
    readonly inputs: readonly [];
    readonly stateMutability: "nonpayable";
    readonly type: "constructor";
}, {
    readonly inputs: readonly [{
        readonly name: "owner";
        readonly type: "bytes";
    }];
    readonly name: "AlreadyOwner";
    readonly type: "error";
}, {
    readonly inputs: readonly [];
    readonly name: "Initialized";
    readonly type: "error";
}, {
    readonly inputs: readonly [{
        readonly name: "owner";
        readonly type: "bytes";
    }];
    readonly name: "InvalidEthereumAddressOwner";
    readonly type: "error";
}, {
    readonly inputs: readonly [{
        readonly name: "key";
        readonly type: "uint256";
    }];
    readonly name: "InvalidNonceKey";
    readonly type: "error";
}, {
    readonly inputs: readonly [{
        readonly name: "owner";
        readonly type: "bytes";
    }];
    readonly name: "InvalidOwnerBytesLength";
    readonly type: "error";
}, {
    readonly inputs: readonly [];
    readonly name: "LastOwner";
    readonly type: "error";
}, {
    readonly inputs: readonly [{
        readonly name: "index";
        readonly type: "uint256";
    }];
    readonly name: "NoOwnerAtIndex";
    readonly type: "error";
}, {
    readonly inputs: readonly [{
        readonly name: "ownersRemaining";
        readonly type: "uint256";
    }];
    readonly name: "NotLastOwner";
    readonly type: "error";
}, {
    readonly inputs: readonly [{
        readonly name: "selector";
        readonly type: "bytes4";
    }];
    readonly name: "SelectorNotAllowed";
    readonly type: "error";
}, {
    readonly inputs: readonly [];
    readonly name: "Unauthorized";
    readonly type: "error";
}, {
    readonly inputs: readonly [];
    readonly name: "UnauthorizedCallContext";
    readonly type: "error";
}, {
    readonly inputs: readonly [];
    readonly name: "UpgradeFailed";
    readonly type: "error";
}, {
    readonly inputs: readonly [{
        readonly name: "index";
        readonly type: "uint256";
    }, {
        readonly name: "expectedOwner";
        readonly type: "bytes";
    }, {
        readonly name: "actualOwner";
        readonly type: "bytes";
    }];
    readonly name: "WrongOwnerAtIndex";
    readonly type: "error";
}, {
    readonly anonymous: false;
    readonly inputs: readonly [{
        readonly indexed: true;
        readonly name: "index";
        readonly type: "uint256";
    }, {
        readonly indexed: false;
        readonly name: "owner";
        readonly type: "bytes";
    }];
    readonly name: "AddOwner";
    readonly type: "event";
}, {
    readonly anonymous: false;
    readonly inputs: readonly [{
        readonly indexed: true;
        readonly name: "index";
        readonly type: "uint256";
    }, {
        readonly indexed: false;
        readonly name: "owner";
        readonly type: "bytes";
    }];
    readonly name: "RemoveOwner";
    readonly type: "event";
}, {
    readonly anonymous: false;
    readonly inputs: readonly [{
        readonly indexed: true;
        readonly name: "implementation";
        readonly type: "address";
    }];
    readonly name: "Upgraded";
    readonly type: "event";
}, {
    readonly stateMutability: "payable";
    readonly type: "fallback";
}, {
    readonly inputs: readonly [];
    readonly name: "REPLAYABLE_NONCE_KEY";
    readonly outputs: readonly [{
        readonly name: "";
        readonly type: "uint256";
    }];
    readonly stateMutability: "view";
    readonly type: "function";
}, {
    readonly inputs: readonly [{
        readonly name: "owner";
        readonly type: "address";
    }];
    readonly name: "addOwnerAddress";
    readonly outputs: readonly [];
    readonly stateMutability: "nonpayable";
    readonly type: "function";
}, {
    readonly inputs: readonly [{
        readonly name: "x";
        readonly type: "bytes32";
    }, {
        readonly name: "y";
        readonly type: "bytes32";
    }];
    readonly name: "addOwnerPublicKey";
    readonly outputs: readonly [];
    readonly stateMutability: "nonpayable";
    readonly type: "function";
}, {
    readonly inputs: readonly [{
        readonly name: "functionSelector";
        readonly type: "bytes4";
    }];
    readonly name: "canSkipChainIdValidation";
    readonly outputs: readonly [{
        readonly name: "";
        readonly type: "bool";
    }];
    readonly stateMutability: "pure";
    readonly type: "function";
}, {
    readonly inputs: readonly [];
    readonly name: "domainSeparator";
    readonly outputs: readonly [{
        readonly name: "";
        readonly type: "bytes32";
    }];
    readonly stateMutability: "view";
    readonly type: "function";
}, {
    readonly inputs: readonly [];
    readonly name: "eip712Domain";
    readonly outputs: readonly [{
        readonly name: "fields";
        readonly type: "bytes1";
    }, {
        readonly name: "name";
        readonly type: "string";
    }, {
        readonly name: "version";
        readonly type: "string";
    }, {
        readonly name: "chainId";
        readonly type: "uint256";
    }, {
        readonly name: "verifyingContract";
        readonly type: "address";
    }, {
        readonly name: "salt";
        readonly type: "bytes32";
    }, {
        readonly name: "extensions";
        readonly type: "uint256[]";
    }];
    readonly stateMutability: "view";
    readonly type: "function";
}, {
    readonly inputs: readonly [];
    readonly name: "entryPoint";
    readonly outputs: readonly [{
        readonly name: "";
        readonly type: "address";
    }];
    readonly stateMutability: "view";
    readonly type: "function";
}, {
    readonly inputs: readonly [{
        readonly name: "target";
        readonly type: "address";
    }, {
        readonly name: "value";
        readonly type: "uint256";
    }, {
        readonly name: "data";
        readonly type: "bytes";
    }];
    readonly name: "execute";
    readonly outputs: readonly [];
    readonly stateMutability: "payable";
    readonly type: "function";
}, {
    readonly inputs: readonly [{
        readonly components: readonly [{
            readonly name: "target";
            readonly type: "address";
        }, {
            readonly name: "value";
            readonly type: "uint256";
        }, {
            readonly name: "data";
            readonly type: "bytes";
        }];
        readonly name: "calls";
        readonly type: "tuple[]";
    }];
    readonly name: "executeBatch";
    readonly outputs: readonly [];
    readonly stateMutability: "payable";
    readonly type: "function";
}, {
    readonly inputs: readonly [{
        readonly name: "calls";
        readonly type: "bytes[]";
    }];
    readonly name: "executeWithoutChainIdValidation";
    readonly outputs: readonly [];
    readonly stateMutability: "payable";
    readonly type: "function";
}, {
    readonly inputs: readonly [{
        readonly components: readonly [{
            readonly name: "sender";
            readonly type: "address";
        }, {
            readonly name: "nonce";
            readonly type: "uint256";
        }, {
            readonly name: "initCode";
            readonly type: "bytes";
        }, {
            readonly name: "callData";
            readonly type: "bytes";
        }, {
            readonly name: "callGasLimit";
            readonly type: "uint256";
        }, {
            readonly name: "verificationGasLimit";
            readonly type: "uint256";
        }, {
            readonly name: "preVerificationGas";
            readonly type: "uint256";
        }, {
            readonly name: "maxFeePerGas";
            readonly type: "uint256";
        }, {
            readonly name: "maxPriorityFeePerGas";
            readonly type: "uint256";
        }, {
            readonly name: "paymasterAndData";
            readonly type: "bytes";
        }, {
            readonly name: "signature";
            readonly type: "bytes";
        }];
        readonly name: "userOp";
        readonly type: "tuple";
    }];
    readonly name: "getUserOpHashWithoutChainId";
    readonly outputs: readonly [{
        readonly name: "";
        readonly type: "bytes32";
    }];
    readonly stateMutability: "view";
    readonly type: "function";
}, {
    readonly inputs: readonly [];
    readonly name: "implementation";
    readonly outputs: readonly [{
        readonly name: "$";
        readonly type: "address";
    }];
    readonly stateMutability: "view";
    readonly type: "function";
}, {
    readonly inputs: readonly [{
        readonly name: "owners";
        readonly type: "bytes[]";
    }];
    readonly name: "initialize";
    readonly outputs: readonly [];
    readonly stateMutability: "payable";
    readonly type: "function";
}, {
    readonly inputs: readonly [{
        readonly name: "account";
        readonly type: "address";
    }];
    readonly name: "isOwnerAddress";
    readonly outputs: readonly [{
        readonly name: "";
        readonly type: "bool";
    }];
    readonly stateMutability: "view";
    readonly type: "function";
}, {
    readonly inputs: readonly [{
        readonly name: "account";
        readonly type: "bytes";
    }];
    readonly name: "isOwnerBytes";
    readonly outputs: readonly [{
        readonly name: "";
        readonly type: "bool";
    }];
    readonly stateMutability: "view";
    readonly type: "function";
}, {
    readonly inputs: readonly [{
        readonly name: "x";
        readonly type: "bytes32";
    }, {
        readonly name: "y";
        readonly type: "bytes32";
    }];
    readonly name: "isOwnerPublicKey";
    readonly outputs: readonly [{
        readonly name: "";
        readonly type: "bool";
    }];
    readonly stateMutability: "view";
    readonly type: "function";
}, {
    readonly inputs: readonly [{
        readonly name: "hash";
        readonly type: "bytes32";
    }, {
        readonly name: "signature";
        readonly type: "bytes";
    }];
    readonly name: "isValidSignature";
    readonly outputs: readonly [{
        readonly name: "result";
        readonly type: "bytes4";
    }];
    readonly stateMutability: "view";
    readonly type: "function";
}, {
    readonly inputs: readonly [];
    readonly name: "nextOwnerIndex";
    readonly outputs: readonly [{
        readonly name: "";
        readonly type: "uint256";
    }];
    readonly stateMutability: "view";
    readonly type: "function";
}, {
    readonly inputs: readonly [{
        readonly name: "index";
        readonly type: "uint256";
    }];
    readonly name: "ownerAtIndex";
    readonly outputs: readonly [{
        readonly name: "";
        readonly type: "bytes";
    }];
    readonly stateMutability: "view";
    readonly type: "function";
}, {
    readonly inputs: readonly [];
    readonly name: "ownerCount";
    readonly outputs: readonly [{
        readonly name: "";
        readonly type: "uint256";
    }];
    readonly stateMutability: "view";
    readonly type: "function";
}, {
    readonly inputs: readonly [];
    readonly name: "proxiableUUID";
    readonly outputs: readonly [{
        readonly name: "";
        readonly type: "bytes32";
    }];
    readonly stateMutability: "view";
    readonly type: "function";
}, {
    readonly inputs: readonly [{
        readonly name: "index";
        readonly type: "uint256";
    }, {
        readonly name: "owner";
        readonly type: "bytes";
    }];
    readonly name: "removeLastOwner";
    readonly outputs: readonly [];
    readonly stateMutability: "nonpayable";
    readonly type: "function";
}, {
    readonly inputs: readonly [{
        readonly name: "index";
        readonly type: "uint256";
    }, {
        readonly name: "owner";
        readonly type: "bytes";
    }];
    readonly name: "removeOwnerAtIndex";
    readonly outputs: readonly [];
    readonly stateMutability: "nonpayable";
    readonly type: "function";
}, {
    readonly inputs: readonly [];
    readonly name: "removedOwnersCount";
    readonly outputs: readonly [{
        readonly name: "";
        readonly type: "uint256";
    }];
    readonly stateMutability: "view";
    readonly type: "function";
}, {
    readonly inputs: readonly [{
        readonly name: "hash";
        readonly type: "bytes32";
    }];
    readonly name: "replaySafeHash";
    readonly outputs: readonly [{
        readonly name: "";
        readonly type: "bytes32";
    }];
    readonly stateMutability: "view";
    readonly type: "function";
}, {
    readonly inputs: readonly [{
        readonly name: "newImplementation";
        readonly type: "address";
    }, {
        readonly name: "data";
        readonly type: "bytes";
    }];
    readonly name: "upgradeToAndCall";
    readonly outputs: readonly [];
    readonly stateMutability: "payable";
    readonly type: "function";
}, {
    readonly inputs: readonly [{
        readonly components: readonly [{
            readonly name: "sender";
            readonly type: "address";
        }, {
            readonly name: "nonce";
            readonly type: "uint256";
        }, {
            readonly name: "initCode";
            readonly type: "bytes";
        }, {
            readonly name: "callData";
            readonly type: "bytes";
        }, {
            readonly name: "callGasLimit";
            readonly type: "uint256";
        }, {
            readonly name: "verificationGasLimit";
            readonly type: "uint256";
        }, {
            readonly name: "preVerificationGas";
            readonly type: "uint256";
        }, {
            readonly name: "maxFeePerGas";
            readonly type: "uint256";
        }, {
            readonly name: "maxPriorityFeePerGas";
            readonly type: "uint256";
        }, {
            readonly name: "paymasterAndData";
            readonly type: "bytes";
        }, {
            readonly name: "signature";
            readonly type: "bytes";
        }];
        readonly name: "userOp";
        readonly type: "tuple";
    }, {
        readonly name: "userOpHash";
        readonly type: "bytes32";
    }, {
        readonly name: "missingAccountFunds";
        readonly type: "uint256";
    }];
    readonly name: "validateUserOp";
    readonly outputs: readonly [{
        readonly name: "validationData";
        readonly type: "uint256";
    }];
    readonly stateMutability: "nonpayable";
    readonly type: "function";
}, {
    readonly stateMutability: "payable";
    readonly type: "receive";
}];
declare const factoryAbi: readonly [{
    readonly inputs: readonly [{
        readonly name: "implementation_";
        readonly type: "address";
    }];
    readonly stateMutability: "payable";
    readonly type: "constructor";
}, {
    readonly inputs: readonly [];
    readonly name: "OwnerRequired";
    readonly type: "error";
}, {
    readonly inputs: readonly [{
        readonly name: "owners";
        readonly type: "bytes[]";
    }, {
        readonly name: "nonce";
        readonly type: "uint256";
    }];
    readonly name: "createAccount";
    readonly outputs: readonly [{
        readonly name: "account";
        readonly type: "address";
    }];
    readonly stateMutability: "payable";
    readonly type: "function";
}, {
    readonly inputs: readonly [{
        readonly name: "owners";
        readonly type: "bytes[]";
    }, {
        readonly name: "nonce";
        readonly type: "uint256";
    }];
    readonly name: "getAddress";
    readonly outputs: readonly [{
        readonly name: "";
        readonly type: "address";
    }];
    readonly stateMutability: "view";
    readonly type: "function";
}, {
    readonly inputs: readonly [];
    readonly name: "implementation";
    readonly outputs: readonly [{
        readonly name: "";
        readonly type: "address";
    }];
    readonly stateMutability: "view";
    readonly type: "function";
}, {
    readonly inputs: readonly [];
    readonly name: "initCodeHash";
    readonly outputs: readonly [{
        readonly name: "";
        readonly type: "bytes32";
    }];
    readonly stateMutability: "view";
    readonly type: "function";
}];
export {};
//# sourceMappingURL=toCoinbaseSmartAccount.d.ts.map