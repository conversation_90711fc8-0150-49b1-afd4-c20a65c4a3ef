'use client';

import { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { Navigation } from '@/components/Navigation';
import { ModularDashboard } from '@/components/ModularDashboard';
import { AIModuleSelector } from '@/components/AIModuleSelector';
import { RealTimeNotifications } from '@/components/RealTimeNotifications';
import { SystemStatus } from '@/components/SystemStatus';
import { useAIModules } from '@/hooks/useAIModules';
import { useRealTimeData } from '@/hooks/useRealTimeData';

export default function HomePage() {
  const [activeModule, setActiveModule] = useState('crypto');
  const { modules, isLoading: modulesLoading } = useAIModules();
  const { connectionStatus, marketData } = useRealTimeData();

  useEffect(() => {
    // Initialize production platform
    console.log('🚀 Chainsight Production Platform Initialized');
  }, []);

  return (
    <div className="min-h-screen bg-dark-900">
      <Navigation />

      <main className="pt-20">
        {/* Hero Section */}
        <section className="relative overflow-hidden">
          <div className="absolute inset-0 bg-gradient-to-br from-primary-500/20 via-transparent to-primary-500/10" />

          <div className="relative max-w-7xl mx-auto px-4 py-20">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              className="text-center space-y-8"
            >
              <div className="space-y-4">
                <h1 className="text-6xl md:text-8xl font-bold">
                  <span className="bg-gradient-to-r from-primary-400 via-primary-500 to-primary-600 bg-clip-text text-transparent">
                    CHAINSIGHT
                  </span>
                </h1>
                <p className="text-xl md:text-2xl text-dark-300 max-w-3xl mx-auto">
                  The Ultimate Modular AI Agent Platform by{' '}
                  <span className="text-primary-500 font-semibold">Connectouch</span>
                </p>
              </div>

              {/* Module Selector */}
              <div className="max-w-4xl mx-auto">
                <AIModuleSelector
                  activeModule={activeModule}
                  onModuleChange={setActiveModule}
                  modules={modules}
                  loading={modulesLoading}
                />
              </div>
            </motion.div>
          </div>
        </section>

        {/* Dashboard Section */}
        <section className="py-20">
          <div className="max-w-7xl mx-auto px-4">
            <ModularDashboard
              activeModule={activeModule}
              marketData={marketData}
              modules={modules}
            />
          </div>
        </section>

        {/* System Status */}
        <div className="fixed bottom-4 right-4">
          <SystemStatus status={connectionStatus} />
        </div>

        {/* Real-time Notifications */}
        <RealTimeNotifications />
      </main>
    </div>
  );
}
