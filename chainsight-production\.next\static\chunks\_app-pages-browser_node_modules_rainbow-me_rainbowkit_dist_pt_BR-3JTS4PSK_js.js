"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_rainbow-me_rainbowkit_dist_pt_BR-3JTS4PSK_js"],{

/***/ "(app-pages-browser)/./node_modules/@rainbow-me/rainbowkit/dist/pt_BR-3JTS4PSK.js":
/*!********************************************************************!*\
  !*** ./node_modules/@rainbow-me/rainbowkit/dist/pt_BR-3JTS4PSK.js ***!
  \********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ pt_BR_default)\n/* harmony export */ });\n/* __next_internal_client_entry_do_not_use__ default auto */ // src/locales/pt_BR.json\nvar pt_BR_default = '{\\n  \"connect_wallet\": {\\n    \"label\": \"Conectar Carteira\",\\n    \"wrong_network\": {\\n      \"label\": \"Rede incorreta\"\\n    }\\n  },\\n  \"intro\": {\\n    \"title\": \"O que \\xE9 uma Carteira?\",\\n    \"description\": \"Uma carteira \\xE9 usada para enviar, receber, armazenar e exibir ativos digitais. Tamb\\xE9m \\xE9 uma nova forma de se conectar, sem precisar criar novas contas e senhas em todo site.\",\\n    \"digital_asset\": {\\n      \"title\": \"Um lar para seus ativos digitais\",\\n      \"description\": \"Carteiras s\\xE3o usadas para enviar, receber, armazenar e exibir ativos digitais como Ethereum e NFTs.\"\\n    },\\n    \"login\": {\\n      \"title\": \"Uma nova maneira de fazer login\",\\n      \"description\": \"Em vez de criar novas contas e senhas em todos os sites, basta conectar sua carteira.\"\\n    },\\n    \"get\": {\\n      \"label\": \"Obter uma Carteira\"\\n    },\\n    \"learn_more\": {\\n      \"label\": \"Saiba mais\"\\n    }\\n  },\\n  \"sign_in\": {\\n    \"label\": \"Verifique sua conta\",\\n    \"description\": \"Para concluir a conex\\xE3o, voc\\xEA deve assinar uma mensagem em sua carteira para confirmar que voc\\xEA \\xE9 o propriet\\xE1rio desta conta.\",\\n    \"message\": {\\n      \"send\": \"Enviar mensagem\",\\n      \"preparing\": \"Preparando mensagem...\",\\n      \"cancel\": \"Cancelar\",\\n      \"preparing_error\": \"Erro ao preparar a mensagem, tente novamente!\"\\n    },\\n    \"signature\": {\\n      \"waiting\": \"Aguardando assinatura...\",\\n      \"verifying\": \"Verificando assinatura...\",\\n      \"signing_error\": \"Erro ao assinar a mensagem, tente novamente!\",\\n      \"verifying_error\": \"Erro ao verificar assinatura, tente novamente!\",\\n      \"oops_error\": \"Ops, algo deu errado!\"\\n    }\\n  },\\n  \"connect\": {\\n    \"label\": \"Conectar\",\\n    \"title\": \"Conectar uma Carteira\",\\n    \"new_to_ethereum\": {\\n      \"description\": \"Novo nas carteiras Ethereum?\",\\n      \"learn_more\": {\\n        \"label\": \"Saiba mais\"\\n      }\\n    },\\n    \"learn_more\": {\\n      \"label\": \"Saiba mais\"\\n    },\\n    \"recent\": \"Recente\",\\n    \"status\": {\\n      \"opening\": \"Abrindo %{wallet}...\",\\n      \"connecting\": \"Conectando\",\\n      \"connect_mobile\": \"Continue em %{wallet}\",\\n      \"not_installed\": \"%{wallet} n\\xE3o est\\xE1 instalado\",\\n      \"not_available\": \"%{wallet} n\\xE3o est\\xE1 dispon\\xEDvel\",\\n      \"confirm\": \"Confirme a conex\\xE3o na extens\\xE3o\",\\n      \"confirm_mobile\": \"Aceite o pedido de conex\\xE3o na carteira\"\\n    },\\n    \"secondary_action\": {\\n      \"get\": {\\n        \"description\": \"N\\xE3o tem %{wallet}?\",\\n        \"label\": \"OBTER\"\\n      },\\n      \"install\": {\\n        \"label\": \"INSTALAR\"\\n      },\\n      \"retry\": {\\n        \"label\": \"TENTAR DE NOVO\"\\n      }\\n    },\\n    \"walletconnect\": {\\n      \"description\": {\\n        \"full\": \"Precisa do modal oficial do WalletConnect?\",\\n        \"compact\": \"Precisa do modal WalletConnect?\"\\n      },\\n      \"open\": {\\n        \"label\": \"ABRIR\"\\n      }\\n    }\\n  },\\n  \"connect_scan\": {\\n    \"title\": \"Digitalize com %{wallet}\",\\n    \"fallback_title\": \"Digitalize com o seu telefone\"\\n  },\\n  \"connector_group\": {\\n    \"installed\": \"Instalado\",\\n    \"recommended\": \"Recomendado\",\\n    \"other\": \"Outro\",\\n    \"popular\": \"Popular\",\\n    \"more\": \"Mais\",\\n    \"others\": \"Outros\"\\n  },\\n  \"get\": {\\n    \"title\": \"Obter uma Carteira\",\\n    \"action\": {\\n      \"label\": \"OBTER\"\\n    },\\n    \"mobile\": {\\n      \"description\": \"Carteira M\\xF3vel\"\\n    },\\n    \"extension\": {\\n      \"description\": \"Extens\\xE3o do Navegador\"\\n    },\\n    \"mobile_and_extension\": {\\n      \"description\": \"Carteira M\\xF3vel e Extens\\xE3o\"\\n    },\\n    \"mobile_and_desktop\": {\\n      \"description\": \"Carteira para Mobile e Desktop\"\\n    },\\n    \"looking_for\": {\\n      \"title\": \"N\\xE3o \\xE9 o que voc\\xEA est\\xE1 procurando?\",\\n      \"mobile\": {\\n        \"description\": \"Selecione uma carteira na tela principal para come\\xE7ar com um provedor de carteira diferente.\"\\n      },\\n      \"desktop\": {\\n        \"compact_description\": \"Selecione uma carteira na tela principal para come\\xE7ar com um provedor de carteira diferente.\",\\n        \"wide_description\": \"Selecione uma carteira \\xE0 esquerda para come\\xE7ar com um provedor de carteira diferente.\"\\n      }\\n    }\\n  },\\n  \"get_options\": {\\n    \"title\": \"Comece com %{wallet}\",\\n    \"short_title\": \"Obtenha %{wallet}\",\\n    \"mobile\": {\\n      \"title\": \"%{wallet} para M\\xF3vel\",\\n      \"description\": \"Use a carteira m\\xF3vel para explorar o mundo do Ethereum.\",\\n      \"download\": {\\n        \"label\": \"Baixe o aplicativo\"\\n      }\\n    },\\n    \"extension\": {\\n      \"title\": \"%{wallet} para %{browser}\",\\n      \"description\": \"Acesse sua carteira diretamente do seu navegador web favorito.\",\\n      \"download\": {\\n        \"label\": \"Adicionar ao %{browser}\"\\n      }\\n    },\\n    \"desktop\": {\\n      \"title\": \"%{wallet} para %{platform}\",\\n      \"description\": \"Acesse sua carteira nativamente do seu desktop poderoso.\",\\n      \"download\": {\\n        \"label\": \"Adicionar ao %{platform}\"\\n      }\\n    }\\n  },\\n  \"get_mobile\": {\\n    \"title\": \"Instale %{wallet}\",\\n    \"description\": \"Escaneie com seu celular para baixar no iOS ou Android\",\\n    \"continue\": {\\n      \"label\": \"Continuar\"\\n    }\\n  },\\n  \"get_instructions\": {\\n    \"mobile\": {\\n      \"connect\": {\\n        \"label\": \"Conectar\"\\n      },\\n      \"learn_more\": {\\n        \"label\": \"Saiba mais\"\\n      }\\n    },\\n    \"extension\": {\\n      \"refresh\": {\\n        \"label\": \"Atualizar\"\\n      },\\n      \"learn_more\": {\\n        \"label\": \"Saiba mais\"\\n      }\\n    },\\n    \"desktop\": {\\n      \"connect\": {\\n        \"label\": \"Conectar\"\\n      },\\n      \"learn_more\": {\\n        \"label\": \"Saiba mais\"\\n      }\\n    }\\n  },\\n  \"chains\": {\\n    \"title\": \"Mudar Redes\",\\n    \"wrong_network\": \"Rede errada detectada, mude ou desconecte para continuar.\",\\n    \"confirm\": \"Confirme na Carteira\",\\n    \"switching_not_supported\": \"Sua carteira n\\xE3o suporta a mudan\\xE7a de redes de %{appName}. Tente mudar de redes dentro da sua carteira.\",\\n    \"switching_not_supported_fallback\": \"Sua carteira n\\xE3o suporta a troca de redes a partir deste aplicativo. Tente trocar de rede dentro de sua carteira.\",\\n    \"disconnect\": \"Desconectar\",\\n    \"connected\": \"Conectado\"\\n  },\\n  \"profile\": {\\n    \"disconnect\": {\\n      \"label\": \"Desconectar\"\\n    },\\n    \"copy_address\": {\\n      \"label\": \"Copiar Endere\\xE7o\",\\n      \"copied\": \"Copiado!\"\\n    },\\n    \"explorer\": {\\n      \"label\": \"Veja mais no explorador\"\\n    },\\n    \"transactions\": {\\n      \"description\": \"%{appName} transa\\xE7\\xF5es aparecer\\xE3o aqui...\",\\n      \"description_fallback\": \"Suas transa\\xE7\\xF5es aparecer\\xE3o aqui...\",\\n      \"recent\": {\\n        \"title\": \"Transa\\xE7\\xF5es Recentes\"\\n      },\\n      \"clear\": {\\n        \"label\": \"Limpar Tudo\"\\n      }\\n    }\\n  },\\n  \"wallet_connectors\": {\\n    \"argent\": {\\n      \"qr_code\": {\\n        \"step1\": {\\n          \"description\": \"Coloque o Argent na tela inicial para um acesso mais r\\xE1pido \\xE0 sua carteira.\",\\n          \"title\": \"Abra o aplicativo Argent\"\\n        },\\n        \"step2\": {\\n          \"description\": \"Crie uma carteira e nome de usu\\xE1rio, ou importe uma carteira existente.\",\\n          \"title\": \"Criar ou Importar uma Carteira\"\\n        },\\n        \"step3\": {\\n          \"description\": \"Depois que voc\\xEA escanear, um prompt de conex\\xE3o aparecer\\xE1 para voc\\xEA conectar sua carteira.\",\\n          \"title\": \"Toque no bot\\xE3o Scan QR\"\\n        }\\n      }\\n    },\\n    \"berasig\": {\\n      \"extension\": {\\n        \"step1\": {\\n          \"title\": \"Instale a extens\\xE3o BeraSig\",\\n          \"description\": \"Recomendamos fixar BeraSig na sua barra de tarefas para facilitar o acesso \\xE0 sua carteira.\"\\n        },\\n        \"step2\": {\\n          \"title\": \"Criar uma Carteira\",\\n          \"description\": \"Certifique-se de fazer backup de sua carteira usando um m\\xE9todo seguro. Nunca compartilhe sua frase secreta com ningu\\xE9m.\"\\n        },\\n        \"step3\": {\\n          \"title\": \"Atualize seu navegador\",\\n          \"description\": \"Depois de configurar sua carteira, clique abaixo para atualizar o navegador e carregar a extens\\xE3o.\"\\n        }\\n      }\\n    },\\n    \"best\": {\\n      \"qr_code\": {\\n        \"step1\": {\\n          \"title\": \"Abra o aplicativo Best Wallet\",\\n          \"description\": \"Adicione a Best Wallet \\xE0 sua tela inicial para um acesso mais r\\xE1pido \\xE0 sua carteira.\"\\n        },\\n        \"step2\": {\\n          \"title\": \"Criar ou Importar uma Carteira\",\\n          \"description\": \"Crie uma nova carteira ou importe uma existente.\"\\n        },\\n        \"step3\": {\\n          \"title\": \"Toque no \\xEDcone do QR e escaneie\",\\n          \"description\": \"Toque no \\xEDcone QR na sua tela inicial, escaneie o c\\xF3digo e confirme o prompt para conectar.\"\\n        }\\n      }\\n    },\\n    \"bifrost\": {\\n      \"qr_code\": {\\n        \"step1\": {\\n          \"description\": \"Recomendamos colocar a Bifrost Wallet na sua tela inicial para um acesso mais r\\xE1pido.\",\\n          \"title\": \"Abra o aplicativo Bifrost Wallet\"\\n        },\\n        \"step2\": {\\n          \"description\": \"Crie ou importe uma carteira usando sua frase de recupera\\xE7\\xE3o.\",\\n          \"title\": \"Criar ou Importar uma Carteira\"\\n        },\\n        \"step3\": {\\n          \"description\": \"Ap\\xF3s voc\\xEA escanear, um prompt de conex\\xE3o aparecer\\xE1 para voc\\xEA conectar sua carteira.\",\\n          \"title\": \"Toque no bot\\xE3o de escanear\"\\n        }\\n      }\\n    },\\n    \"bitget\": {\\n      \"qr_code\": {\\n        \"step1\": {\\n          \"description\": \"Recomendamos colocar a Bitget Wallet na sua tela inicial para um acesso mais r\\xE1pido.\",\\n          \"title\": \"Abra o aplicativo Bitget Wallet\"\\n        },\\n        \"step2\": {\\n          \"description\": \"Certifique-se de fazer backup de sua carteira usando um m\\xE9todo seguro. Nunca compartilhe sua frase secreta com ningu\\xE9m.\",\\n          \"title\": \"Criar ou Importar uma Carteira\"\\n        },\\n        \"step3\": {\\n          \"description\": \"Depois de escanear, um prompt de conex\\xE3o aparecer\\xE1 para voc\\xEA conectar sua carteira.\",\\n          \"title\": \"Toque no bot\\xE3o de escaneamento\"\\n        }\\n      },\\n      \"extension\": {\\n        \"step1\": {\\n          \"description\": \"Recomendamos fixar a Bitget Wallet na sua barra de tarefas para um acesso mais r\\xE1pido \\xE0 sua carteira.\",\\n          \"title\": \"Instale a extens\\xE3o da Carteira Bitget\"\\n        },\\n        \"step2\": {\\n          \"description\": \"Certifique-se de fazer o backup da sua carteira usando um m\\xE9todo seguro. Nunca compartilhe sua frase secreta com ningu\\xE9m.\",\\n          \"title\": \"Criar ou Importar uma Carteira\"\\n        },\\n        \"step3\": {\\n          \"description\": \"Depois de configurar sua carteira, clique abaixo para atualizar o navegador e carregar a extens\\xE3o.\",\\n          \"title\": \"Atualize seu navegador\"\\n        }\\n      }\\n    },\\n    \"bitski\": {\\n      \"extension\": {\\n        \"step1\": {\\n          \"description\": \"Recomendamos fixar o Bitski na sua barra de tarefas para um acesso mais r\\xE1pido \\xE0 sua carteira.\",\\n          \"title\": \"Instale a extens\\xE3o Bitski\"\\n        },\\n        \"step2\": {\\n          \"description\": \"Certifique-se de fazer backup de sua carteira usando um m\\xE9todo seguro. Nunca compartilhe sua frase secreta com ningu\\xE9m.\",\\n          \"title\": \"Criar ou Importar uma Carteira\"\\n        },\\n        \"step3\": {\\n          \"description\": \"Depois de configurar sua carteira, clique abaixo para atualizar o navegador e carregar a extens\\xE3o.\",\\n          \"title\": \"Atualize seu navegador\"\\n        }\\n      }\\n    },\\n    \"bitverse\": {\\n      \"qr_code\": {\\n        \"step1\": {\\n          \"title\": \"Abra o aplicativo Bitverse Wallet\",\\n          \"description\": \"Adicione o Bitverse Wallet \\xE0 sua tela inicial para acessar sua carteira mais rapidamente.\"\\n        },\\n        \"step2\": {\\n          \"title\": \"Criar ou Importar uma Carteira\",\\n          \"description\": \"Crie uma nova carteira ou importe uma existente.\"\\n        },\\n        \"step3\": {\\n          \"title\": \"Toque no \\xEDcone do QR e escaneie\",\\n          \"description\": \"Toque no \\xEDcone QR na sua tela inicial, escaneie o c\\xF3digo e confirme o prompt para conectar.\"\\n        }\\n      }\\n    },\\n    \"bloom\": {\\n      \"desktop\": {\\n        \"step1\": {\\n          \"title\": \"Abra o aplicativo Bloom Wallet\",\\n          \"description\": \"Recomendamos colocar o Bloom Wallet na sua tela inicial para acesso mais r\\xE1pido.\"\\n        },\\n        \"step2\": {\\n          \"description\": \"Crie ou importe uma carteira usando sua frase de recupera\\xE7\\xE3o.\",\\n          \"title\": \"Criar ou Importar uma Carteira\"\\n        },\\n        \"step3\": {\\n          \"description\": \"Depois de ter uma carteira, clique em Conectar para se conectar via Bloom. Um prompt de conex\\xE3o aparecer\\xE1 no aplicativo para voc\\xEA confirmar a conex\\xE3o.\",\\n          \"title\": \"Clique em Conectar\"\\n        }\\n      }\\n    },\\n    \"bybit\": {\\n      \"qr_code\": {\\n        \"step1\": {\\n          \"description\": \"Recomendamos colocar o Bybit na sua tela inicial para acessar sua carteira mais rapidamente.\",\\n          \"title\": \"Abra o aplicativo Bybit\"\\n        },\\n        \"step2\": {\\n          \"description\": \"Voc\\xEA pode facilmente fazer backup de sua carteira usando nosso recurso de backup em seu telefone.\",\\n          \"title\": \"Criar ou Importar uma Carteira\"\\n        },\\n        \"step3\": {\\n          \"description\": \"Depois que voc\\xEA escanear, um prompt de conex\\xE3o aparecer\\xE1 para voc\\xEA conectar sua carteira.\",\\n          \"title\": \"Toque no bot\\xE3o de escanear\"\\n        }\\n      },\\n      \"extension\": {\\n        \"step1\": {\\n          \"description\": \"Clique no canto superior direito do seu navegador e fixe a Carteira Bybit para acesso f\\xE1cil.\",\\n          \"title\": \"Instale a extens\\xE3o da Carteira Bybit\"\\n        },\\n        \"step2\": {\\n          \"description\": \"Crie uma nova carteira ou importe uma existente.\",\\n          \"title\": \"Criar ou Importar uma carteira\"\\n        },\\n        \"step3\": {\\n          \"description\": \"Depois de configurar a Carteira Bybit, clique abaixo para atualizar o navegador e carregar a extens\\xE3o.\",\\n          \"title\": \"Atualize seu navegador\"\\n        }\\n      }\\n    },\\n    \"binance\": {\\n      \"qr_code\": {\\n        \"step1\": {\\n          \"description\": \"Recomendamos colocar o Binance na tela inicial para um acesso mais r\\xE1pido \\xE0 sua carteira.\",\\n          \"title\": \"Abra o aplicativo Binance\"\\n        },\\n        \"step2\": {\\n          \"description\": \"Voc\\xEA pode facilmente fazer backup de sua carteira usando nosso recurso de backup em seu telefone.\",\\n          \"title\": \"Criar ou Importar uma Carteira\"\\n        },\\n        \"step3\": {\\n          \"description\": \"Depois que voc\\xEA escanear, um prompt de conex\\xE3o aparecer\\xE1 para voc\\xEA conectar sua carteira.\",\\n          \"title\": \"Toque no bot\\xE3o WalletConnect\"\\n        }\\n      }\\n    },\\n    \"coin98\": {\\n      \"qr_code\": {\\n        \"step1\": {\\n          \"description\": \"Recomendamos colocar a Carteira Coin98 na tela inicial para um acesso mais r\\xE1pido \\xE0 sua carteira.\",\\n          \"title\": \"Abra o aplicativo Carteira Coin98\"\\n        },\\n        \"step2\": {\\n          \"description\": \"Voc\\xEA pode facilmente fazer backup de sua carteira usando nosso recurso de backup em seu telefone.\",\\n          \"title\": \"Criar ou Importar uma Carteira\"\\n        },\\n        \"step3\": {\\n          \"description\": \"Depois de escanear, uma solicita\\xE7\\xE3o de conex\\xE3o aparecer\\xE1 para voc\\xEA conectar sua carteira.\",\\n          \"title\": \"Toque no bot\\xE3o WalletConnect\"\\n        }\\n      },\\n      \"extension\": {\\n        \"step1\": {\\n          \"description\": \"Clique no canto superior direito do seu navegador e fixe a Carteira Coin98 para f\\xE1cil acesso.\",\\n          \"title\": \"Instale a extens\\xE3o da Carteira Coin98\"\\n        },\\n        \"step2\": {\\n          \"description\": \"Crie uma nova carteira ou importe uma existente.\",\\n          \"title\": \"Criar ou Importar uma carteira\"\\n        },\\n        \"step3\": {\\n          \"description\": \"Depois de configurar a Carteira Coin98, clique abaixo para atualizar o navegador e carregar a extens\\xE3o.\",\\n          \"title\": \"Atualize seu navegador\"\\n        }\\n      }\\n    },\\n    \"coinbase\": {\\n      \"qr_code\": {\\n        \"step1\": {\\n          \"description\": \"Recomendamos colocar a Carteira Coinbase na tela inicial para um acesso mais r\\xE1pido.\",\\n          \"title\": \"Abra o aplicativo Coinbase Wallet\"\\n        },\\n        \"step2\": {\\n          \"description\": \"Voc\\xEA pode fazer backup da sua carteira facilmente usando o recurso de backup na nuvem.\",\\n          \"title\": \"Criar ou Importar uma Carteira\"\\n        },\\n        \"step3\": {\\n          \"description\": \"Depois de escanear, um prompt de conex\\xE3o aparecer\\xE1 para que voc\\xEA conecte sua carteira.\",\\n          \"title\": \"Toque no bot\\xE3o de escanear\"\\n        }\\n      },\\n      \"extension\": {\\n        \"step1\": {\\n          \"description\": \"Recomendamos fixar o Coinbase Wallet na sua barra de tarefas para um acesso mais r\\xE1pido \\xE0 sua carteira.\",\\n          \"title\": \"Instale a extens\\xE3o Coinbase Wallet\"\\n        },\\n        \"step2\": {\\n          \"description\": \"Certifique-se de fazer backup da sua carteira usando um m\\xE9todo seguro. Nunca compartilhe sua frase secreta com ningu\\xE9m.\",\\n          \"title\": \"Criar ou Importar uma Carteira\"\\n        },\\n        \"step3\": {\\n          \"description\": \"Uma vez que voc\\xEA configurou sua carteira, clique abaixo para atualizar o navegador e carregar a extens\\xE3o.\",\\n          \"title\": \"Atualize seu navegador\"\\n        }\\n      }\\n    },\\n    \"compass\": {\\n      \"extension\": {\\n        \"step1\": {\\n          \"description\": \"Recomendamos fixar a Carteira Compass na sua barra de tarefas para um acesso mais r\\xE1pido \\xE0 sua carteira.\",\\n          \"title\": \"Instale a extens\\xE3o Compass Wallet\"\\n        },\\n        \"step2\": {\\n          \"description\": \"Certifique-se de fazer backup de sua carteira usando um m\\xE9todo seguro. Nunca compartilhe sua frase secreta com ningu\\xE9m.\",\\n          \"title\": \"Criar ou Importar uma Carteira\"\\n        },\\n        \"step3\": {\\n          \"description\": \"Depois de configurar sua carteira, clique abaixo para atualizar o navegador e carregar a extens\\xE3o.\",\\n          \"title\": \"Atualize seu navegador\"\\n        }\\n      }\\n    },\\n    \"core\": {\\n      \"qr_code\": {\\n        \"step1\": {\\n          \"description\": \"Recomendamos colocar o Core na tela inicial para um acesso mais r\\xE1pido \\xE0 sua carteira.\",\\n          \"title\": \"Abra o aplicativo Core\"\\n        },\\n        \"step2\": {\\n          \"description\": \"Voc\\xEA pode facilmente salvar sua carteira usando nosso recurso de backup no seu celular.\",\\n          \"title\": \"Criar ou Importar uma Carteira\"\\n        },\\n        \"step3\": {\\n          \"description\": \"Depois de escanear, um prompt de conex\\xE3o aparecer\\xE1 para voc\\xEA conectar sua carteira.\",\\n          \"title\": \"Toque no bot\\xE3o WalletConnect\"\\n        }\\n      },\\n      \"extension\": {\\n        \"step1\": {\\n          \"description\": \"Recomendamos fixar o Core na sua barra de tarefas para um acesso mais r\\xE1pido \\xE0 sua carteira.\",\\n          \"title\": \"Instale a extens\\xE3o Core\"\\n        },\\n        \"step2\": {\\n          \"description\": \"Certifique-se de fazer backup da sua carteira usando um m\\xE9todo seguro. Nunca compartilhe sua frase secreta com ningu\\xE9m.\",\\n          \"title\": \"Criar ou Importar uma Carteira\"\\n        },\\n        \"step3\": {\\n          \"description\": \"Depois de configurar sua carteira, clique abaixo para atualizar o navegador e carregar a extens\\xE3o.\",\\n          \"title\": \"Atualize seu navegador\"\\n        }\\n      }\\n    },\\n    \"fox\": {\\n      \"qr_code\": {\\n        \"step1\": {\\n          \"description\": \"Recomendamos colocar o FoxWallet na tela inicial para um acesso mais r\\xE1pido.\",\\n          \"title\": \"Abra o aplicativo FoxWallet\"\\n        },\\n        \"step2\": {\\n          \"description\": \"Certifique-se de fazer backup de sua carteira usando um m\\xE9todo seguro. Nunca compartilhe sua frase secreta com ningu\\xE9m.\",\\n          \"title\": \"Criar ou Importar uma Carteira\"\\n        },\\n        \"step3\": {\\n          \"description\": \"Depois de escanear, uma solicita\\xE7\\xE3o de conex\\xE3o aparecer\\xE1 para voc\\xEA conectar sua carteira.\",\\n          \"title\": \"Toque no bot\\xE3o de escaneamento\"\\n        }\\n      }\\n    },\\n    \"frontier\": {\\n      \"qr_code\": {\\n        \"step1\": {\\n          \"description\": \"Recomendamos colocar o Frontier Wallet na tela inicial para um acesso mais r\\xE1pido.\",\\n          \"title\": \"Abra o aplicativo Frontier Wallet\"\\n        },\\n        \"step2\": {\\n          \"description\": \"Certifique-se de fazer backup de sua carteira usando um m\\xE9todo seguro. Nunca compartilhe sua frase secreta com ningu\\xE9m.\",\\n          \"title\": \"Criar ou Importar uma Carteira\"\\n        },\\n        \"step3\": {\\n          \"description\": \"Depois de escanear, aparecer\\xE1 um prompt de conex\\xE3o para voc\\xEA conectar sua carteira.\",\\n          \"title\": \"Toque no bot\\xE3o de varredura\"\\n        }\\n      },\\n      \"extension\": {\\n        \"step1\": {\\n          \"description\": \"Recomendamos fixar a Carteira Frontier na sua barra de tarefas para um acesso mais r\\xE1pido \\xE0 sua carteira.\",\\n          \"title\": \"Instale a extens\\xE3o da Carteira Frontier\"\\n        },\\n        \"step2\": {\\n          \"description\": \"Certifique-se de fazer backup da sua carteira usando um m\\xE9todo seguro. Nunca compartilhe sua frase secreta com ningu\\xE9m.\",\\n          \"title\": \"Criar ou Importar uma Carteira\"\\n        },\\n        \"step3\": {\\n          \"description\": \"Depois de configurar sua carteira, clique abaixo para atualizar o navegador e carregar a extens\\xE3o.\",\\n          \"title\": \"Atualize seu navegador\"\\n        }\\n      }\\n    },\\n    \"im_token\": {\\n      \"qr_code\": {\\n        \"step1\": {\\n          \"title\": \"Abra o aplicativo imToken\",\\n          \"description\": \"Coloque o aplicativo imToken na tela inicial para um acesso mais r\\xE1pido \\xE0 sua carteira.\"\\n        },\\n        \"step2\": {\\n          \"title\": \"Criar ou Importar uma Carteira\",\\n          \"description\": \"Crie uma nova carteira ou importe uma existente.\"\\n        },\\n        \"step3\": {\\n          \"title\": \"Toque no \\xEDcone do Scanner no canto superior direito\",\\n          \"description\": \"Escolha Nova Conex\\xE3o, em seguida, escaneie o c\\xF3digo QR e confirme o prompt para conectar.\"\\n        }\\n      }\\n    },\\n    \"iopay\": {\\n      \"qr_code\": {\\n        \"step1\": {\\n          \"description\": \"Recomendamos colocar o ioPay na tela inicial para um acesso mais r\\xE1pido \\xE0 sua carteira.\",\\n          \"title\": \"Abra o aplicativo ioPay\"\\n        },\\n        \"step2\": {\\n          \"description\": \"Voc\\xEA pode facilmente fazer backup de sua carteira usando nosso recurso de backup em seu telefone.\",\\n          \"title\": \"Criar ou Importar uma Carteira\"\\n        },\\n        \"step3\": {\\n          \"description\": \"Depois que voc\\xEA escanear, um prompt de conex\\xE3o aparecer\\xE1 para voc\\xEA conectar sua carteira.\",\\n          \"title\": \"Toque no bot\\xE3o WalletConnect\"\\n        }\\n      }\\n    },\\n    \"kaikas\": {\\n      \"extension\": {\\n        \"step1\": {\\n          \"description\": \"Recomendamos fixar o Kaikas na sua barra de tarefas para acesso mais r\\xE1pido \\xE0 sua carteira.\",\\n          \"title\": \"Instale a extens\\xE3o Kaikas\"\\n        },\\n        \"step2\": {\\n          \"description\": \"Certifique-se de fazer backup de sua carteira usando um m\\xE9todo seguro. Nunca compartilhe sua frase secreta com ningu\\xE9m.\",\\n          \"title\": \"Criar ou Importar uma Carteira\"\\n        },\\n        \"step3\": {\\n          \"description\": \"Depois de configurar sua carteira, clique abaixo para atualizar o navegador e carregar a extens\\xE3o.\",\\n          \"title\": \"Atualize seu navegador\"\\n        }\\n      },\\n      \"qr_code\": {\\n        \"step1\": {\\n          \"title\": \"Abra o aplicativo Kaikas\",\\n          \"description\": \"Coloque o aplicativo Kaikas na sua tela inicial para acessar sua carteira mais rapidamente.\"\\n        },\\n        \"step2\": {\\n          \"title\": \"Criar ou Importar uma Carteira\",\\n          \"description\": \"Crie uma nova carteira ou importe uma existente.\"\\n        },\\n        \"step3\": {\\n          \"title\": \"Toque no \\xEDcone do Scanner no canto superior direito\",\\n          \"description\": \"Escolha Nova Conex\\xE3o, em seguida, escaneie o c\\xF3digo QR e confirme o prompt para conectar.\"\\n        }\\n      }\\n    },\\n    \"kaia\": {\\n      \"extension\": {\\n        \"step1\": {\\n          \"description\": \"Recomendamos fixar o Kaia na sua barra de tarefas para um acesso mais r\\xE1pido \\xE0 sua carteira.\",\\n          \"title\": \"Instale a extens\\xE3o Kaia\"\\n        },\\n        \"step2\": {\\n          \"description\": \"Certifique-se de fazer backup de sua carteira usando um m\\xE9todo seguro. Nunca compartilhe sua frase secreta com ningu\\xE9m.\",\\n          \"title\": \"Criar ou Importar uma Carteira\"\\n        },\\n        \"step3\": {\\n          \"description\": \"Depois de configurar sua carteira, clique abaixo para atualizar o navegador e carregar a extens\\xE3o.\",\\n          \"title\": \"Atualize seu navegador\"\\n        }\\n      },\\n      \"qr_code\": {\\n        \"step1\": {\\n          \"title\": \"Abra o aplicativo Kaia\",\\n          \"description\": \"Coloque o aplicativo Kaia na tela inicial para um acesso mais r\\xE1pido \\xE0 sua carteira.\"\\n        },\\n        \"step2\": {\\n          \"title\": \"Criar ou Importar uma Carteira\",\\n          \"description\": \"Crie uma nova carteira ou importe uma existente.\"\\n        },\\n        \"step3\": {\\n          \"title\": \"Toque no \\xEDcone do Scanner no canto superior direito\",\\n          \"description\": \"Escolha Nova Conex\\xE3o, em seguida, escaneie o c\\xF3digo QR e confirme o prompt para conectar.\"\\n        }\\n      }\\n    },\\n    \"kraken\": {\\n      \"qr_code\": {\\n        \"step1\": {\\n          \"title\": \"Abra o aplicativo Kraken Wallet\",\\n          \"description\": \"Adicione o Kraken Wallet \\xE0 tela inicial para um acesso mais r\\xE1pido \\xE0 sua carteira.\"\\n        },\\n        \"step2\": {\\n          \"title\": \"Criar ou Importar uma Carteira\",\\n          \"description\": \"Crie uma nova carteira ou importe uma existente.\"\\n        },\\n        \"step3\": {\\n          \"title\": \"Toque no \\xEDcone do QR e escaneie\",\\n          \"description\": \"Toque no \\xEDcone QR na sua tela inicial, escaneie o c\\xF3digo e confirme o prompt para conectar.\"\\n        }\\n      }\\n    },\\n    \"kresus\": {\\n      \"qr_code\": {\\n        \"step1\": {\\n          \"title\": \"Abra o aplicativo Kresus Wallet\",\\n          \"description\": \"Adicione a Carteira Kresus \\xE0 sua tela inicial para um acesso mais r\\xE1pido \\xE0 sua carteira.\"\\n        },\\n        \"step2\": {\\n          \"title\": \"Criar ou Importar uma Carteira\",\\n          \"description\": \"Crie uma nova carteira ou importe uma existente.\"\\n        },\\n        \"step3\": {\\n          \"title\": \"Toque no \\xEDcone do QR e escaneie\",\\n          \"description\": \"Toque no \\xEDcone QR na sua tela inicial, escaneie o c\\xF3digo e confirme o prompt para conectar.\"\\n        }\\n      }\\n    },\\n    \"magicEden\": {\\n      \"extension\": {\\n        \"step1\": {\\n          \"title\": \"Instale a extens\\xE3o Magic Eden\",\\n          \"description\": \"Recomendamos fixar o Magic Eden na sua barra de tarefas para facilitar o acesso \\xE0 sua carteira.\"\\n        },\\n        \"step2\": {\\n          \"title\": \"Criar ou Importar uma Carteira\",\\n          \"description\": \"Certifique-se de fazer backup de sua carteira usando um m\\xE9todo seguro. Nunca compartilhe sua frase secreta de recupera\\xE7\\xE3o com ningu\\xE9m.\"\\n        },\\n        \"step3\": {\\n          \"title\": \"Atualize seu navegador\",\\n          \"description\": \"Depois de configurar sua carteira, clique abaixo para atualizar o navegador e carregar a extens\\xE3o.\"\\n        }\\n      }\\n    },\\n    \"metamask\": {\\n      \"qr_code\": {\\n        \"step1\": {\\n          \"title\": \"Abra o aplicativo MetaMask\",\\n          \"description\": \"Recomendamos colocar o MetaMask na tela inicial para um acesso mais r\\xE1pido.\"\\n        },\\n        \"step2\": {\\n          \"title\": \"Criar ou Importar uma Carteira\",\\n          \"description\": \"Certifique-se de fazer backup de sua carteira usando um m\\xE9todo seguro. Nunca compartilhe sua frase secreta com ningu\\xE9m.\"\\n        },\\n        \"step3\": {\\n          \"title\": \"Toque no bot\\xE3o escanear\",\\n          \"description\": \"Depois de escanear, aparecer\\xE1 um prompt de conex\\xE3o para voc\\xEA conectar sua carteira.\"\\n        }\\n      },\\n      \"extension\": {\\n        \"step1\": {\\n          \"title\": \"Instale a extens\\xE3o MetaMask\",\\n          \"description\": \"Recomendamos fixar o MetaMask na barra de tarefas para um acesso mais r\\xE1pido \\xE0 sua carteira.\"\\n        },\\n        \"step2\": {\\n          \"title\": \"Criar ou Importar uma Carteira\",\\n          \"description\": \"Certifique-se de fazer backup da sua carteira usando um m\\xE9todo seguro. Nunca compartilhe sua frase secreta com ningu\\xE9m.\"\\n        },\\n        \"step3\": {\\n          \"title\": \"Atualize o seu navegador\",\\n          \"description\": \"Depois de configurar sua carteira, clique abaixo para atualizar o navegador e carregar a extens\\xE3o.\"\\n        }\\n      }\\n    },\\n    \"nestwallet\": {\\n      \"extension\": {\\n        \"step1\": {\\n          \"title\": \"Instale a extens\\xE3o NestWallet\",\\n          \"description\": \"Recomendamos fixar o NestWallet na sua barra de tarefas para um acesso mais r\\xE1pido \\xE0 sua carteira.\"\\n        },\\n        \"step2\": {\\n          \"title\": \"Criar ou Importar uma Carteira\",\\n          \"description\": \"Certifique-se de fazer backup de sua carteira usando um m\\xE9todo seguro. Nunca compartilhe sua frase secreta com ningu\\xE9m.\"\\n        },\\n        \"step3\": {\\n          \"title\": \"Atualize seu navegador\",\\n          \"description\": \"Depois de configurar sua carteira, clique abaixo para atualizar o navegador e carregar a extens\\xE3o.\"\\n        }\\n      }\\n    },\\n    \"okx\": {\\n      \"qr_code\": {\\n        \"step1\": {\\n          \"title\": \"Abra o aplicativo da Carteira OKX\",\\n          \"description\": \"Recomendamos colocar a Carteira OKX na tela inicial para um acesso mais r\\xE1pido.\"\\n        },\\n        \"step2\": {\\n          \"title\": \"Criar ou Importar uma Carteira\",\\n          \"description\": \"Certifique-se de fazer o backup da sua carteira utilizando um m\\xE9todo seguro. Nunca compartilhe sua frase secreta com ningu\\xE9m.\"\\n        },\\n        \"step3\": {\\n          \"title\": \"Toque no bot\\xE3o de digitaliza\\xE7\\xE3o\",\\n          \"description\": \"Depois de escanear, aparecer\\xE1 um prompt de conex\\xE3o para voc\\xEA conectar sua carteira.\"\\n        }\\n      },\\n      \"extension\": {\\n        \"step1\": {\\n          \"title\": \"Instale a extens\\xE3o OKX Wallet\",\\n          \"description\": \"Recomendamos fixar a OKX Wallet na sua barra de tarefas para um acesso mais r\\xE1pido \\xE0 sua carteira.\"\\n        },\\n        \"step2\": {\\n          \"title\": \"Criar ou Importar uma Carteira\",\\n          \"description\": \"Certifique-se de fazer o backup da sua carteira utilizando um m\\xE9todo seguro. Nunca compartilhe sua frase secreta com ningu\\xE9m.\"\\n        },\\n        \"step3\": {\\n          \"title\": \"Atualize o seu navegador\",\\n          \"description\": \"Uma vez que voc\\xEA configurou sua carteira, clique abaixo para atualizar o navegador e carregar a extens\\xE3o.\"\\n        }\\n      }\\n    },\\n    \"omni\": {\\n      \"qr_code\": {\\n        \"step1\": {\\n          \"title\": \"Abra o aplicativo Omni\",\\n          \"description\": \"Adicione o Omni \\xE0 sua tela inicial para um acesso mais r\\xE1pido \\xE0 sua carteira.\"\\n        },\\n        \"step2\": {\\n          \"title\": \"Criar ou Importar uma Carteira\",\\n          \"description\": \"Crie uma nova carteira ou importe uma existente.\"\\n        },\\n        \"step3\": {\\n          \"title\": \"Toque no \\xEDcone do QR e escaneie\",\\n          \"description\": \"Toque no \\xEDcone QR na tela inicial, escaneie o c\\xF3digo e confirme o prompt para conectar.\"\\n        }\\n      }\\n    },\\n    \"1inch\": {\\n      \"qr_code\": {\\n        \"step1\": {\\n          \"description\": \"Coloque o 1inch Wallet na sua tela inicial para acessar sua carteira mais rapidamente.\",\\n          \"title\": \"Abra o aplicativo 1inch Wallet\"\\n        },\\n        \"step2\": {\\n          \"description\": \"Crie uma carteira e nome de usu\\xE1rio, ou importe uma carteira existente.\",\\n          \"title\": \"Criar ou Importar uma Carteira\"\\n        },\\n        \"step3\": {\\n          \"description\": \"Depois que voc\\xEA escanear, um prompt de conex\\xE3o aparecer\\xE1 para voc\\xEA conectar sua carteira.\",\\n          \"title\": \"Toque no bot\\xE3o Scan QR\"\\n        }\\n      }\\n    },\\n    \"token_pocket\": {\\n      \"qr_code\": {\\n        \"step1\": {\\n          \"title\": \"Abra o aplicativo TokenPocket\",\\n          \"description\": \"Recomendamos colocar o TokenPocket na tela inicial para um acesso mais r\\xE1pido.\"\\n        },\\n        \"step2\": {\\n          \"title\": \"Criar ou Importar uma Carteira\",\\n          \"description\": \"Certifique-se de fazer backup de sua carteira usando um m\\xE9todo seguro. Nunca compartilhe sua frase secreta com ningu\\xE9m.\"\\n        },\\n        \"step3\": {\\n          \"title\": \"Toque no bot\\xE3o de digitaliza\\xE7\\xE3o\",\\n          \"description\": \"Depois de escanear, aparecer\\xE1 um prompt de conex\\xE3o para voc\\xEA conectar sua carteira.\"\\n        }\\n      },\\n      \"extension\": {\\n        \"step1\": {\\n          \"title\": \"Instale a extens\\xE3o TokenPocket\",\\n          \"description\": \"Recomendamos fixar o TokenPocket em sua barra de tarefas para um acesso mais r\\xE1pido \\xE0 sua carteira.\"\\n        },\\n        \"step2\": {\\n          \"title\": \"Criar ou Importar uma Carteira\",\\n          \"description\": \"Certifique-se de fazer backup de sua carteira usando um m\\xE9todo seguro. Nunca compartilhe sua frase secreta com ningu\\xE9m.\"\\n        },\\n        \"step3\": {\\n          \"title\": \"Atualize seu navegador\",\\n          \"description\": \"Uma vez que voc\\xEA configurou sua carteira, clique abaixo para atualizar o navegador e carregar a extens\\xE3o.\"\\n        }\\n      }\\n    },\\n    \"trust\": {\\n      \"qr_code\": {\\n        \"step1\": {\\n          \"title\": \"Abra o aplicativo Trust Wallet\",\\n          \"description\": \"Coloque o Trust Wallet na tela inicial para um acesso mais r\\xE1pido \\xE0 sua carteira.\"\\n        },\\n        \"step2\": {\\n          \"title\": \"Criar ou Importar uma Carteira\",\\n          \"description\": \"Crie uma nova carteira ou importe uma existente.\"\\n        },\\n        \"step3\": {\\n          \"title\": \"Toque em WalletConnect nas Configura\\xE7\\xF5es\",\\n          \"description\": \"Escolha Nova Conex\\xE3o, depois escaneie o QR code e confirme o prompt para se conectar.\"\\n        }\\n      },\\n      \"extension\": {\\n        \"step1\": {\\n          \"title\": \"Instale a extens\\xE3o Trust Wallet\",\\n          \"description\": \"Clique no canto superior direito do seu navegador e marque Trust Wallet para f\\xE1cil acesso.\"\\n        },\\n        \"step2\": {\\n          \"title\": \"Crie ou Importe uma carteira\",\\n          \"description\": \"Crie uma nova carteira ou importe uma existente.\"\\n        },\\n        \"step3\": {\\n          \"title\": \"Atualize seu navegador\",\\n          \"description\": \"Depois que configurar a Trust Wallet, clique abaixo para atualizar o navegador e carregar a extens\\xE3o.\"\\n        }\\n      }\\n    },\\n    \"uniswap\": {\\n      \"qr_code\": {\\n        \"step1\": {\\n          \"title\": \"Abra o aplicativo Uniswap\",\\n          \"description\": \"Adicione a Carteira Uniswap \\xE0 sua tela inicial para um acesso mais r\\xE1pido \\xE0 sua carteira.\"\\n        },\\n        \"step2\": {\\n          \"title\": \"Criar ou Importar uma Carteira\",\\n          \"description\": \"Crie uma nova carteira ou importe uma existente.\"\\n        },\\n        \"step3\": {\\n          \"title\": \"Toque no \\xEDcone QR e escaneie\",\\n          \"description\": \"Toque no \\xEDcone QR na sua tela inicial, escaneie o c\\xF3digo e confirme o prompt para conectar.\"\\n        }\\n      }\\n    },\\n    \"zerion\": {\\n      \"qr_code\": {\\n        \"step1\": {\\n          \"title\": \"Abra o aplicativo Zerion\",\\n          \"description\": \"Recomendamos colocar o Zerion na sua tela inicial para um acesso mais r\\xE1pido.\"\\n        },\\n        \"step2\": {\\n          \"title\": \"Criar ou Importar uma Carteira\",\\n          \"description\": \"Certifique-se de fazer backup da sua carteira usando um m\\xE9todo seguro. Nunca compartilhe sua frase secreta com ningu\\xE9m.\"\\n        },\\n        \"step3\": {\\n          \"title\": \"Toque no bot\\xE3o de digitaliza\\xE7\\xE3o\",\\n          \"description\": \"Depois de digitalizar, um prompt de conex\\xE3o aparecer\\xE1 para que voc\\xEA possa conectar sua carteira.\"\\n        }\\n      },\\n      \"extension\": {\\n        \"step1\": {\\n          \"title\": \"Instale a extens\\xE3o Zerion\",\\n          \"description\": \"Recomendamos fixar o Zerion na sua barra de tarefas para um acesso mais r\\xE1pido \\xE0 sua carteira.\"\\n        },\\n        \"step2\": {\\n          \"title\": \"Criar ou Importar uma Carteira\",\\n          \"description\": \"Certifique-se de fazer backup da sua carteira usando um m\\xE9todo seguro. Nunca compartilhe sua frase secreta com ningu\\xE9m.\"\\n        },\\n        \"step3\": {\\n          \"title\": \"Atualize seu navegador\",\\n          \"description\": \"Depois de configurar sua carteira, clique abaixo para atualizar o navegador e carregar a extens\\xE3o.\"\\n        }\\n      }\\n    },\\n    \"rainbow\": {\\n      \"qr_code\": {\\n        \"step1\": {\\n          \"title\": \"Abra o aplicativo Rainbow\",\\n          \"description\": \"Recomendamos colocar o Rainbow na tela inicial para um acesso mais r\\xE1pido \\xE0 sua carteira.\"\\n        },\\n        \"step2\": {\\n          \"title\": \"Criar ou Importar uma Carteira\",\\n          \"description\": \"Voc\\xEA pode facilmente fazer backup da sua carteira usando nosso recurso de backup no seu telefone.\"\\n        },\\n        \"step3\": {\\n          \"title\": \"Toque no bot\\xE3o de digitalizar\",\\n          \"description\": \"Depois de escanear, uma solicita\\xE7\\xE3o de conex\\xE3o aparecer\\xE1 para voc\\xEA conectar sua carteira.\"\\n        }\\n      }\\n    },\\n    \"enkrypt\": {\\n      \"extension\": {\\n        \"step1\": {\\n          \"description\": \"Recomendamos fixar a Carteira Enkrypt na sua barra de tarefas para um acesso mais r\\xE1pido \\xE0 sua carteira.\",\\n          \"title\": \"Instale a extens\\xE3o da Carteira Enkrypt\"\\n        },\\n        \"step2\": {\\n          \"description\": \"Certifique-se de fazer backup da sua carteira usando um m\\xE9todo seguro. Nunca compartilhe sua frase secreta com ningu\\xE9m.\",\\n          \"title\": \"Criar ou Importar uma Carteira\"\\n        },\\n        \"step3\": {\\n          \"description\": \"Depois de configurar sua carteira, clique abaixo para atualizar o navegador e carregar a extens\\xE3o.\",\\n          \"title\": \"Atualize o seu navegador\"\\n        }\\n      }\\n    },\\n    \"frame\": {\\n      \"extension\": {\\n        \"step1\": {\\n          \"description\": \"Recomendamos fixar o Frame na sua barra de tarefas para um acesso mais r\\xE1pido \\xE0 sua carteira.\",\\n          \"title\": \"Instale o Frame e a extens\\xE3o complementar\"\\n        },\\n        \"step2\": {\\n          \"description\": \"Certifique-se de fazer backup da sua carteira usando um m\\xE9todo seguro. Nunca compartilhe sua frase secreta com ningu\\xE9m.\",\\n          \"title\": \"Criar ou Importar uma Carteira\"\\n        },\\n        \"step3\": {\\n          \"description\": \"Depois de configurar sua carteira, clique abaixo para atualizar o navegador e carregar a extens\\xE3o.\",\\n          \"title\": \"Atualize seu navegador\"\\n        }\\n      }\\n    },\\n    \"one_key\": {\\n      \"extension\": {\\n        \"step1\": {\\n          \"title\": \"Instale a extens\\xE3o OneKey Wallet\",\\n          \"description\": \"Recomendamos fixar a OneKey Wallet na sua barra de tarefas para um acesso mais r\\xE1pido \\xE0 sua carteira.\"\\n        },\\n        \"step2\": {\\n          \"title\": \"Criar ou Importar uma Carteira\",\\n          \"description\": \"Certifique-se de fazer backup de sua carteira usando um m\\xE9todo seguro. Nunca compartilhe sua frase secreta com ningu\\xE9m.\"\\n        },\\n        \"step3\": {\\n          \"title\": \"Atualize seu navegador\",\\n          \"description\": \"Uma vez que voc\\xEA configurou sua carteira, clique abaixo para atualizar o navegador e carregar a extens\\xE3o.\"\\n        }\\n      }\\n    },\\n    \"paraswap\": {\\n      \"qr_code\": {\\n        \"step1\": {\\n          \"title\": \"Abra o aplicativo ParaSwap\",\\n          \"description\": \"Adicione a Carteira ParaSwap \\xE0 sua tela inicial para um acesso mais r\\xE1pido \\xE0 sua carteira.\"\\n        },\\n        \"step2\": {\\n          \"title\": \"Criar ou Importar uma Carteira\",\\n          \"description\": \"Crie uma nova carteira ou importe uma existente.\"\\n        },\\n        \"step3\": {\\n          \"title\": \"Toque no \\xEDcone do QR e escaneie\",\\n          \"description\": \"Toque no \\xEDcone QR na sua tela inicial, escaneie o c\\xF3digo e confirme o prompt para conectar.\"\\n        }\\n      }\\n    },\\n    \"phantom\": {\\n      \"extension\": {\\n        \"step1\": {\\n          \"title\": \"Instale a extens\\xE3o Phantom\",\\n          \"description\": \"Recomendamos fixar o Phantom na sua barra de tarefas para facilitar o acesso \\xE0 sua carteira.\"\\n        },\\n        \"step2\": {\\n          \"title\": \"Criar ou Importar uma Carteira\",\\n          \"description\": \"Certifique-se de fazer backup de sua carteira usando um m\\xE9todo seguro. Nunca compartilhe sua frase secreta de recupera\\xE7\\xE3o com ningu\\xE9m.\"\\n        },\\n        \"step3\": {\\n          \"title\": \"Atualize seu navegador\",\\n          \"description\": \"Depois de configurar sua carteira, clique abaixo para atualizar o navegador e carregar a extens\\xE3o.\"\\n        }\\n      }\\n    },\\n    \"rabby\": {\\n      \"extension\": {\\n        \"step1\": {\\n          \"title\": \"Instale a extens\\xE3o Rabby\",\\n          \"description\": \"Recomendamos fixar Rabby na sua barra de tarefas para um acesso mais r\\xE1pido \\xE0 sua carteira.\"\\n        },\\n        \"step2\": {\\n          \"title\": \"Criar ou Importar uma Carteira\",\\n          \"description\": \"Certifique-se de fazer backup da sua carteira usando um m\\xE9todo seguro. Nunca compartilhe sua frase secreta com ningu\\xE9m.\"\\n        },\\n        \"step3\": {\\n          \"title\": \"Atualize seu navegador\",\\n          \"description\": \"Depois de configurar sua carteira, clique abaixo para atualizar o navegador e carregar a extens\\xE3o.\"\\n        }\\n      }\\n    },\\n    \"ronin\": {\\n      \"qr_code\": {\\n        \"step1\": {\\n          \"description\": \"Recomendamos colocar a Carteira Ronin na tela inicial para um acesso mais r\\xE1pido.\",\\n          \"title\": \"Abra o aplicativo Carteira Ronin\"\\n        },\\n        \"step2\": {\\n          \"description\": \"Certifique-se de fazer backup de sua carteira usando um m\\xE9todo seguro. Nunca compartilhe sua frase secreta com ningu\\xE9m.\",\\n          \"title\": \"Criar ou Importar uma Carteira\"\\n        },\\n        \"step3\": {\\n          \"description\": \"Depois que voc\\xEA escanear, um prompt de conex\\xE3o aparecer\\xE1 para voc\\xEA conectar sua carteira.\",\\n          \"title\": \"Toque no bot\\xE3o de escanear\"\\n        }\\n      },\\n      \"extension\": {\\n        \"step1\": {\\n          \"description\": \"Recomendamos fixar a Carteira Ronin na sua barra de tarefas para um acesso mais r\\xE1pido \\xE0 sua carteira.\",\\n          \"title\": \"Instale a extens\\xE3o da Carteira Ronin\"\\n        },\\n        \"step2\": {\\n          \"description\": \"Certifique-se de fazer backup de sua carteira usando um m\\xE9todo seguro. Nunca compartilhe sua frase secreta com ningu\\xE9m.\",\\n          \"title\": \"Criar ou Importar uma Carteira\"\\n        },\\n        \"step3\": {\\n          \"description\": \"Depois de configurar sua carteira, clique abaixo para atualizar o navegador e carregar a extens\\xE3o.\",\\n          \"title\": \"Atualize seu navegador\"\\n        }\\n      }\\n    },\\n    \"ramper\": {\\n      \"extension\": {\\n        \"step1\": {\\n          \"title\": \"Instale a extens\\xE3o Ramper\",\\n          \"description\": \"Recomendamos fixar o Ramper na sua barra de tarefas para um acesso mais f\\xE1cil \\xE0 sua carteira.\"\\n        },\\n        \"step2\": {\\n          \"title\": \"Criar uma Carteira\",\\n          \"description\": \"Certifique-se de fazer backup de sua carteira usando um m\\xE9todo seguro. Nunca compartilhe sua frase secreta com ningu\\xE9m.\"\\n        },\\n        \"step3\": {\\n          \"title\": \"Atualize seu navegador\",\\n          \"description\": \"Depois de configurar sua carteira, clique abaixo para atualizar o navegador e carregar a extens\\xE3o.\"\\n        }\\n      }\\n    },\\n    \"safeheron\": {\\n      \"extension\": {\\n        \"step1\": {\\n          \"title\": \"Instale a extens\\xE3o Core\",\\n          \"description\": \"Recomendamos fixar Safeheron na sua barra de tarefas para um acesso mais r\\xE1pido \\xE0 sua carteira.\"\\n        },\\n        \"step2\": {\\n          \"title\": \"Criar ou Importar uma Carteira\",\\n          \"description\": \"Certifique-se de fazer o backup da sua carteira usando um m\\xE9todo seguro. Nunca compartilhe sua frase secreta com ningu\\xE9m.\"\\n        },\\n        \"step3\": {\\n          \"title\": \"Atualize seu navegador\",\\n          \"description\": \"Depois de configurar sua carteira, clique abaixo para atualizar o navegador e carregar a extens\\xE3o.\"\\n        }\\n      }\\n    },\\n    \"taho\": {\\n      \"extension\": {\\n        \"step1\": {\\n          \"title\": \"Instale a extens\\xE3o Taho\",\\n          \"description\": \"Recomendamos fixar o Taho na sua barra de tarefas para um acesso mais r\\xE1pido \\xE0 sua carteira.\"\\n        },\\n        \"step2\": {\\n          \"title\": \"Criar ou Importar uma Carteira\",\\n          \"description\": \"Certifique-se de fazer o backup da sua carteira usando um m\\xE9todo seguro. Nunca compartilhe sua frase secreta com ningu\\xE9m.\"\\n        },\\n        \"step3\": {\\n          \"title\": \"Atualize seu navegador\",\\n          \"description\": \"Depois de configurar sua carteira, clique abaixo para atualizar o navegador e carregar a extens\\xE3o.\"\\n        }\\n      }\\n    },\\n    \"wigwam\": {\\n      \"extension\": {\\n        \"step1\": {\\n          \"title\": \"Instale a extens\\xE3o Wigwam\",\\n          \"description\": \"Recomendamos fixar o Wigwam na sua barra de tarefas para um acesso mais r\\xE1pido \\xE0 sua carteira.\"\\n        },\\n        \"step2\": {\\n          \"title\": \"Criar ou Importar uma Carteira\",\\n          \"description\": \"Certifique-se de fazer backup de sua carteira usando um m\\xE9todo seguro. Nunca compartilhe sua frase secreta com ningu\\xE9m.\"\\n        },\\n        \"step3\": {\\n          \"title\": \"Atualize seu navegador\",\\n          \"description\": \"Depois de configurar sua carteira, clique abaixo para atualizar o navegador e carregar a extens\\xE3o.\"\\n        }\\n      }\\n    },\\n    \"talisman\": {\\n      \"extension\": {\\n        \"step1\": {\\n          \"title\": \"Instale a extens\\xE3o Talisman\",\\n          \"description\": \"Recomendamos fixar o Talisman na sua barra de tarefas para um acesso mais r\\xE1pido \\xE0 sua carteira.\"\\n        },\\n        \"step2\": {\\n          \"title\": \"Crie ou Importe uma Carteira Ethereum\",\\n          \"description\": \"Certifique-se de fazer backup de sua carteira usando um m\\xE9todo seguro. Nunca compartilhe sua frase de recupera\\xE7\\xE3o com ningu\\xE9m.\"\\n        },\\n        \"step3\": {\\n          \"title\": \"Atualize o seu navegador\",\\n          \"description\": \"Depois de configurar sua carteira, clique abaixo para atualizar o navegador e carregar a extens\\xE3o.\"\\n        }\\n      }\\n    },\\n    \"xdefi\": {\\n      \"extension\": {\\n        \"step1\": {\\n          \"title\": \"Instale a extens\\xE3o XDEFI Wallet\",\\n          \"description\": \"Recomendamos fixar a Carteira XDEFI na sua barra de tarefas para um acesso mais r\\xE1pido \\xE0 sua carteira.\"\\n        },\\n        \"step2\": {\\n          \"title\": \"Criar ou Importar uma Carteira\",\\n          \"description\": \"Certifique-se de fazer backup de sua carteira usando um m\\xE9todo seguro. Nunca compartilhe sua frase secreta com ningu\\xE9m.\"\\n        },\\n        \"step3\": {\\n          \"title\": \"Atualize seu navegador\",\\n          \"description\": \"Depois de configurar sua carteira, clique abaixo para atualizar o navegador e carregar a extens\\xE3o.\"\\n        }\\n      }\\n    },\\n    \"zeal\": {\\n      \"qr_code\": {\\n        \"step1\": {\\n          \"title\": \"Abra o aplicativo Zeal\",\\n          \"description\": \"Adicione a Carteira Zeal \\xE0 sua tela inicial para um acesso mais r\\xE1pido \\xE0 sua carteira.\"\\n        },\\n        \"step2\": {\\n          \"title\": \"Criar ou Importar uma Carteira\",\\n          \"description\": \"Crie uma nova carteira ou importe uma existente.\"\\n        },\\n        \"step3\": {\\n          \"title\": \"Toque no \\xEDcone do QR e escaneie\",\\n          \"description\": \"Toque no \\xEDcone QR na sua tela inicial, escaneie o c\\xF3digo e confirme o prompt para conectar.\"\\n        }\\n      },\\n      \"extension\": {\\n        \"step1\": {\\n          \"title\": \"Instale a extens\\xE3o Zeal\",\\n          \"description\": \"Recomendamos fixar o Zeal na sua barra de tarefas para um acesso mais r\\xE1pido \\xE0 sua carteira.\"\\n        },\\n        \"step2\": {\\n          \"title\": \"Criar ou Importar uma Carteira\",\\n          \"description\": \"Certifique-se de fazer backup de sua carteira usando um m\\xE9todo seguro. Nunca compartilhe sua frase secreta com ningu\\xE9m.\"\\n        },\\n        \"step3\": {\\n          \"title\": \"Atualize seu navegador\",\\n          \"description\": \"Depois de configurar sua carteira, clique abaixo para atualizar o navegador e carregar a extens\\xE3o.\"\\n        }\\n      }\\n    },\\n    \"safepal\": {\\n      \"extension\": {\\n        \"step1\": {\\n          \"title\": \"Instale a extens\\xE3o da Carteira SafePal\",\\n          \"description\": \"Clique no canto superior direito do seu navegador e fixe a Carteira SafePal para f\\xE1cil acesso.\"\\n        },\\n        \"step2\": {\\n          \"title\": \"Criar ou Importar uma carteira\",\\n          \"description\": \"Crie uma nova carteira ou importe uma existente.\"\\n        },\\n        \"step3\": {\\n          \"title\": \"Atualize seu navegador\",\\n          \"description\": \"Depois de configurar a Carteira SafePal, clique abaixo para atualizar o navegador e carregar a extens\\xE3o.\"\\n        }\\n      },\\n      \"qr_code\": {\\n        \"step1\": {\\n          \"title\": \"Abra o aplicativo Carteira SafePal\",\\n          \"description\": \"Coloque a Carteira SafePal na tela inicial para um acesso mais r\\xE1pido \\xE0 sua carteira.\"\\n        },\\n        \"step2\": {\\n          \"title\": \"Criar ou Importar uma Carteira\",\\n          \"description\": \"Crie uma nova carteira ou importe uma existente.\"\\n        },\\n        \"step3\": {\\n          \"title\": \"Toque em WalletConnect nas Configura\\xE7\\xF5es\",\\n          \"description\": \"Escolha Nova Conex\\xE3o, em seguida, escaneie o c\\xF3digo QR e confirme o prompt para conectar.\"\\n        }\\n      }\\n    },\\n    \"desig\": {\\n      \"extension\": {\\n        \"step1\": {\\n          \"title\": \"Instale a extens\\xE3o Desig\",\\n          \"description\": \"Recomendamos fixar Desig na sua barra de tarefas para facilitar o acesso \\xE0 sua carteira.\"\\n        },\\n        \"step2\": {\\n          \"title\": \"Criar uma Carteira\",\\n          \"description\": \"Certifique-se de fazer backup de sua carteira usando um m\\xE9todo seguro. Nunca compartilhe sua frase secreta com ningu\\xE9m.\"\\n        },\\n        \"step3\": {\\n          \"title\": \"Atualize seu navegador\",\\n          \"description\": \"Depois de configurar sua carteira, clique abaixo para atualizar o navegador e carregar a extens\\xE3o.\"\\n        }\\n      }\\n    },\\n    \"subwallet\": {\\n      \"extension\": {\\n        \"step1\": {\\n          \"title\": \"Instale a extens\\xE3o SubWallet\",\\n          \"description\": \"Recomendamos fixar SubWallet na sua barra de tarefas para acesso mais r\\xE1pido \\xE0 sua carteira.\"\\n        },\\n        \"step2\": {\\n          \"title\": \"Criar ou Importar uma Carteira\",\\n          \"description\": \"Certifique-se de fazer backup de sua carteira usando um m\\xE9todo seguro. Nunca compartilhe sua frase de recupera\\xE7\\xE3o com ningu\\xE9m.\"\\n        },\\n        \"step3\": {\\n          \"title\": \"Atualize seu navegador\",\\n          \"description\": \"Depois de configurar sua carteira, clique abaixo para atualizar o navegador e carregar a extens\\xE3o.\"\\n        }\\n      },\\n      \"qr_code\": {\\n        \"step1\": {\\n          \"title\": \"Abra o aplicativo SubWallet\",\\n          \"description\": \"Recomendamos colocar SubWallet na tela inicial para acesso mais r\\xE1pido.\"\\n        },\\n        \"step2\": {\\n          \"title\": \"Criar ou Importar uma Carteira\",\\n          \"description\": \"Certifique-se de fazer backup de sua carteira usando um m\\xE9todo seguro. Nunca compartilhe sua frase secreta com ningu\\xE9m.\"\\n        },\\n        \"step3\": {\\n          \"title\": \"Toque no bot\\xE3o de escanear\",\\n          \"description\": \"Depois que voc\\xEA escanear, um prompt de conex\\xE3o aparecer\\xE1 para voc\\xEA conectar sua carteira.\"\\n        }\\n      }\\n    },\\n    \"clv\": {\\n      \"extension\": {\\n        \"step1\": {\\n          \"title\": \"Instale a extens\\xE3o CLV Wallet\",\\n          \"description\": \"Recomendamos fixar CLV Wallet na sua barra de tarefas para acesso mais r\\xE1pido \\xE0 sua carteira.\"\\n        },\\n        \"step2\": {\\n          \"title\": \"Criar ou Importar uma Carteira\",\\n          \"description\": \"Certifique-se de fazer backup de sua carteira usando um m\\xE9todo seguro. Nunca compartilhe sua frase secreta com ningu\\xE9m.\"\\n        },\\n        \"step3\": {\\n          \"title\": \"Atualize seu navegador\",\\n          \"description\": \"Depois de configurar sua carteira, clique abaixo para atualizar o navegador e carregar a extens\\xE3o.\"\\n        }\\n      },\\n      \"qr_code\": {\\n        \"step1\": {\\n          \"title\": \"Abra o aplicativo da carteira CLV\",\\n          \"description\": \"Recomendamos colocar a Carteira CLV na tela inicial para acesso mais r\\xE1pido.\"\\n        },\\n        \"step2\": {\\n          \"title\": \"Criar ou Importar uma Carteira\",\\n          \"description\": \"Certifique-se de fazer backup de sua carteira usando um m\\xE9todo seguro. Nunca compartilhe sua frase secreta com ningu\\xE9m.\"\\n        },\\n        \"step3\": {\\n          \"title\": \"Toque no bot\\xE3o de escanear\",\\n          \"description\": \"Depois que voc\\xEA escanear, um prompt de conex\\xE3o aparecer\\xE1 para voc\\xEA conectar sua carteira.\"\\n        }\\n      }\\n    },\\n    \"okto\": {\\n      \"qr_code\": {\\n        \"step1\": {\\n          \"title\": \"Abra o aplicativo Okto\",\\n          \"description\": \"Adicione Okto \\xE0 sua tela inicial para acesso r\\xE1pido\"\\n        },\\n        \"step2\": {\\n          \"title\": \"Crie uma carteira MPC\",\\n          \"description\": \"Crie uma conta e gere uma carteira\"\\n        },\\n        \"step3\": {\\n          \"title\": \"Toque em WalletConnect nas Configura\\xE7\\xF5es\",\\n          \"description\": \"Toque no \\xEDcone Scan QR no canto superior direito e confirme o prompt para conectar.\"\\n        }\\n      }\\n    },\\n    \"ledger\": {\\n      \"desktop\": {\\n        \"step1\": {\\n          \"title\": \"Abra o aplicativo Ledger Live\",\\n          \"description\": \"Recomendamos colocar o Ledger Live na tela inicial para um acesso mais r\\xE1pido.\"\\n        },\\n        \"step2\": {\\n          \"title\": \"Configure seu Ledger\",\\n          \"description\": \"Configure um novo Ledger ou conecte-se a um j\\xE1 existente.\"\\n        },\\n        \"step3\": {\\n          \"title\": \"Conectar\",\\n          \"description\": \"Depois de escanear, aparecer\\xE1 um prompt de conex\\xE3o para voc\\xEA conectar sua carteira.\"\\n        }\\n      },\\n      \"qr_code\": {\\n        \"step1\": {\\n          \"title\": \"Abra o aplicativo Ledger Live\",\\n          \"description\": \"Recomendamos colocar o Ledger Live na tela inicial para um acesso mais r\\xE1pido.\"\\n        },\\n        \"step2\": {\\n          \"title\": \"Configure seu Ledger\",\\n          \"description\": \"Voc\\xEA pode sincronizar com o aplicativo de desktop ou conectar seu Ledger.\"\\n        },\\n        \"step3\": {\\n          \"title\": \"Escanear o c\\xF3digo\",\\n          \"description\": \"Toque em WalletConnect e em seguida mude para Scanner. Depois de escanear, aparecer\\xE1 um prompt de conex\\xE3o para voc\\xEA conectar sua carteira.\"\\n        }\\n      }\\n    },\\n    \"valora\": {\\n      \"qr_code\": {\\n        \"step1\": {\\n          \"title\": \"Abra o aplicativo Valora\",\\n          \"description\": \"Recomendamos colocar o Valora na tela inicial para um acesso mais r\\xE1pido.\"\\n        },\\n        \"step2\": {\\n          \"title\": \"Criar ou Importar uma carteira\",\\n          \"description\": \"Crie uma nova carteira ou importe uma existente.\"\\n        },\\n        \"step3\": {\\n          \"title\": \"Toque no bot\\xE3o de escanear\",\\n          \"description\": \"Depois que voc\\xEA escanear, um prompt de conex\\xE3o aparecer\\xE1 para voc\\xEA conectar sua carteira.\"\\n        }\\n      }\\n    },\\n    \"gate\": {\\n      \"qr_code\": {\\n        \"step1\": {\\n          \"title\": \"Abra o aplicativo Gate\",\\n          \"description\": \"Recomendamos colocar o Gate na sua tela inicial para um acesso mais r\\xE1pido.\"\\n        },\\n        \"step2\": {\\n          \"title\": \"Criar ou Importar uma Carteira\",\\n          \"description\": \"Crie uma nova carteira ou importe uma existente.\"\\n        },\\n        \"step3\": {\\n          \"title\": \"Toque no bot\\xE3o de escanear\",\\n          \"description\": \"Depois que voc\\xEA escanear, um prompt de conex\\xE3o aparecer\\xE1 para voc\\xEA conectar sua carteira.\"\\n        }\\n      },\\n      \"extension\": {\\n        \"step1\": {\\n          \"title\": \"Instale a extens\\xE3o Gate\",\\n          \"description\": \"Recomendamos fixar o Gate na sua barra de tarefas para facilitar o acesso \\xE0 sua carteira.\"\\n        },\\n        \"step2\": {\\n          \"title\": \"Criar ou Importar uma Carteira\",\\n          \"description\": \"Certifique-se de fazer backup de sua carteira usando um m\\xE9todo seguro. Nunca compartilhe sua frase secreta de recupera\\xE7\\xE3o com ningu\\xE9m.\"\\n        },\\n        \"step3\": {\\n          \"title\": \"Atualize seu navegador\",\\n          \"description\": \"Depois de configurar sua carteira, clique abaixo para atualizar o navegador e carregar a extens\\xE3o.\"\\n        }\\n      }\\n    },\\n    \"xportal\": {\\n      \"qr_code\": {\\n        \"step1\": {\\n          \"description\": \"Coloque o xPortal na tela inicial para um acesso mais r\\xE1pido \\xE0 sua carteira.\",\\n          \"title\": \"Abra o aplicativo xPortal\"\\n        },\\n        \"step2\": {\\n          \"description\": \"Crie uma nova carteira ou importe uma existente.\",\\n          \"title\": \"Criar ou Importar uma Carteira\"\\n        },\\n        \"step3\": {\\n          \"description\": \"Depois que voc\\xEA escanear, um prompt de conex\\xE3o aparecer\\xE1 para voc\\xEA conectar sua carteira.\",\\n          \"title\": \"Toque no bot\\xE3o Scan QR\"\\n        }\\n      }\\n    },\\n    \"mew\": {\\n      \"qr_code\": {\\n        \"step1\": {\\n          \"description\": \"Recomendamos colocar a Carteira MEW na tela inicial para acesso mais r\\xE1pido.\",\\n          \"title\": \"Abra o aplicativo da Carteira MEW\"\\n        },\\n        \"step2\": {\\n          \"description\": \"Voc\\xEA pode fazer backup da sua carteira facilmente usando o recurso de backup na nuvem.\",\\n          \"title\": \"Criar ou Importar uma Carteira\"\\n        },\\n        \"step3\": {\\n          \"description\": \"Depois que voc\\xEA escanear, um prompt de conex\\xE3o aparecer\\xE1 para voc\\xEA conectar sua carteira.\",\\n          \"title\": \"Toque no bot\\xE3o de escanear\"\\n        }\\n      }\\n    }\\n  },\\n  \"zilpay\": {\\n    \"qr_code\": {\\n      \"step1\": {\\n        \"title\": \"Abra o aplicativo ZilPay\",\\n        \"description\": \"Adicione o ZilPay \\xE0 sua tela inicial para um acesso mais r\\xE1pido \\xE0 sua carteira.\"\\n      },\\n      \"step2\": {\\n        \"title\": \"Criar ou Importar uma Carteira\",\\n        \"description\": \"Crie uma nova carteira ou importe uma existente.\"\\n      },\\n      \"step3\": {\\n        \"title\": \"Toque no bot\\xE3o de escanear\",\\n        \"description\": \"Depois que voc\\xEA escanear, um prompt de conex\\xE3o aparecer\\xE1 para voc\\xEA conectar sua carteira.\"\\n      }\\n    }\\n  }\\n}\\n';\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@rainbow-me/rainbowkit/dist/pt_BR-3JTS4PSK.js\n"));

/***/ })

}]);