"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/json-rpc-engine";
exports.ids = ["vendor-chunks/json-rpc-engine"];
exports.modules = {

/***/ "(ssr)/./node_modules/json-rpc-engine/dist/JsonRpcEngine.js":
/*!************************************************************!*\
  !*** ./node_modules/json-rpc-engine/dist/JsonRpcEngine.js ***!
  \************************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

eval("\nvar __importDefault = (this && this.__importDefault) || function (mod) {\n    return (mod && mod.__esModule) ? mod : { \"default\": mod };\n};\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.JsonRpcEngine = void 0;\nconst safe_event_emitter_1 = __importDefault(__webpack_require__(/*! @metamask/safe-event-emitter */ \"(ssr)/./node_modules/json-rpc-engine/node_modules/@metamask/safe-event-emitter/index.js\"));\nconst eth_rpc_errors_1 = __webpack_require__(/*! eth-rpc-errors */ \"(ssr)/./node_modules/eth-rpc-errors/dist/index.js\");\n/**\n * A JSON-RPC request and response processor.\n * Give it a stack of middleware, pass it requests, and get back responses.\n */\nclass JsonRpcEngine extends safe_event_emitter_1.default {\n    constructor() {\n        super();\n        this._middleware = [];\n    }\n    /**\n     * Add a middleware function to the engine's middleware stack.\n     *\n     * @param middleware - The middleware function to add.\n     */\n    push(middleware) {\n        this._middleware.push(middleware);\n    }\n    handle(req, cb) {\n        if (cb && typeof cb !== 'function') {\n            throw new Error('\"callback\" must be a function if provided.');\n        }\n        if (Array.isArray(req)) {\n            if (cb) {\n                return this._handleBatch(req, cb);\n            }\n            return this._handleBatch(req);\n        }\n        if (cb) {\n            return this._handle(req, cb);\n        }\n        return this._promiseHandle(req);\n    }\n    /**\n     * Returns this engine as a middleware function that can be pushed to other\n     * engines.\n     *\n     * @returns This engine as a middleware function.\n     */\n    asMiddleware() {\n        return async (req, res, next, end) => {\n            try {\n                const [middlewareError, isComplete, returnHandlers,] = await JsonRpcEngine._runAllMiddleware(req, res, this._middleware);\n                if (isComplete) {\n                    await JsonRpcEngine._runReturnHandlers(returnHandlers);\n                    return end(middlewareError);\n                }\n                return next(async (handlerCallback) => {\n                    try {\n                        await JsonRpcEngine._runReturnHandlers(returnHandlers);\n                    }\n                    catch (error) {\n                        return handlerCallback(error);\n                    }\n                    return handlerCallback();\n                });\n            }\n            catch (error) {\n                return end(error);\n            }\n        };\n    }\n    async _handleBatch(reqs, cb) {\n        // The order here is important\n        try {\n            // 2. Wait for all requests to finish, or throw on some kind of fatal\n            // error\n            const responses = await Promise.all(\n            // 1. Begin executing each request in the order received\n            reqs.map(this._promiseHandle.bind(this)));\n            // 3. Return batch response\n            if (cb) {\n                return cb(null, responses);\n            }\n            return responses;\n        }\n        catch (error) {\n            if (cb) {\n                return cb(error);\n            }\n            throw error;\n        }\n    }\n    /**\n     * A promise-wrapped _handle.\n     */\n    _promiseHandle(req) {\n        return new Promise((resolve) => {\n            this._handle(req, (_err, res) => {\n                // There will always be a response, and it will always have any error\n                // that is caught and propagated.\n                resolve(res);\n            });\n        });\n    }\n    /**\n     * Ensures that the request object is valid, processes it, and passes any\n     * error and the response object to the given callback.\n     *\n     * Does not reject.\n     */\n    async _handle(callerReq, cb) {\n        if (!callerReq ||\n            Array.isArray(callerReq) ||\n            typeof callerReq !== 'object') {\n            const error = new eth_rpc_errors_1.EthereumRpcError(eth_rpc_errors_1.errorCodes.rpc.invalidRequest, `Requests must be plain objects. Received: ${typeof callerReq}`, { request: callerReq });\n            return cb(error, { id: undefined, jsonrpc: '2.0', error });\n        }\n        if (typeof callerReq.method !== 'string') {\n            const error = new eth_rpc_errors_1.EthereumRpcError(eth_rpc_errors_1.errorCodes.rpc.invalidRequest, `Must specify a string method. Received: ${typeof callerReq.method}`, { request: callerReq });\n            return cb(error, { id: callerReq.id, jsonrpc: '2.0', error });\n        }\n        const req = Object.assign({}, callerReq);\n        const res = {\n            id: req.id,\n            jsonrpc: req.jsonrpc,\n        };\n        let error = null;\n        try {\n            await this._processRequest(req, res);\n        }\n        catch (_error) {\n            // A request handler error, a re-thrown middleware error, or something\n            // unexpected.\n            error = _error;\n        }\n        if (error) {\n            // Ensure no result is present on an errored response\n            delete res.result;\n            if (!res.error) {\n                res.error = eth_rpc_errors_1.serializeError(error);\n            }\n        }\n        return cb(error, res);\n    }\n    /**\n     * For the given request and response, runs all middleware and their return\n     * handlers, if any, and ensures that internal request processing semantics\n     * are satisfied.\n     */\n    async _processRequest(req, res) {\n        const [error, isComplete, returnHandlers,] = await JsonRpcEngine._runAllMiddleware(req, res, this._middleware);\n        // Throw if \"end\" was not called, or if the response has neither a result\n        // nor an error.\n        JsonRpcEngine._checkForCompletion(req, res, isComplete);\n        // The return handlers should run even if an error was encountered during\n        // middleware processing.\n        await JsonRpcEngine._runReturnHandlers(returnHandlers);\n        // Now we re-throw the middleware processing error, if any, to catch it\n        // further up the call chain.\n        if (error) {\n            throw error;\n        }\n    }\n    /**\n     * Serially executes the given stack of middleware.\n     *\n     * @returns An array of any error encountered during middleware execution,\n     * a boolean indicating whether the request was completed, and an array of\n     * middleware-defined return handlers.\n     */\n    static async _runAllMiddleware(req, res, middlewareStack) {\n        const returnHandlers = [];\n        let error = null;\n        let isComplete = false;\n        // Go down stack of middleware, call and collect optional returnHandlers\n        for (const middleware of middlewareStack) {\n            [error, isComplete] = await JsonRpcEngine._runMiddleware(req, res, middleware, returnHandlers);\n            if (isComplete) {\n                break;\n            }\n        }\n        return [error, isComplete, returnHandlers.reverse()];\n    }\n    /**\n     * Runs an individual middleware.\n     *\n     * @returns An array of any error encountered during middleware exection,\n     * and a boolean indicating whether the request should end.\n     */\n    static _runMiddleware(req, res, middleware, returnHandlers) {\n        return new Promise((resolve) => {\n            const end = (err) => {\n                const error = err || res.error;\n                if (error) {\n                    res.error = eth_rpc_errors_1.serializeError(error);\n                }\n                // True indicates that the request should end\n                resolve([error, true]);\n            };\n            const next = (returnHandler) => {\n                if (res.error) {\n                    end(res.error);\n                }\n                else {\n                    if (returnHandler) {\n                        if (typeof returnHandler !== 'function') {\n                            end(new eth_rpc_errors_1.EthereumRpcError(eth_rpc_errors_1.errorCodes.rpc.internal, `JsonRpcEngine: \"next\" return handlers must be functions. ` +\n                                `Received \"${typeof returnHandler}\" for request:\\n${jsonify(req)}`, { request: req }));\n                        }\n                        returnHandlers.push(returnHandler);\n                    }\n                    // False indicates that the request should not end\n                    resolve([null, false]);\n                }\n            };\n            try {\n                middleware(req, res, next, end);\n            }\n            catch (error) {\n                end(error);\n            }\n        });\n    }\n    /**\n     * Serially executes array of return handlers. The request and response are\n     * assumed to be in their scope.\n     */\n    static async _runReturnHandlers(handlers) {\n        for (const handler of handlers) {\n            await new Promise((resolve, reject) => {\n                handler((err) => (err ? reject(err) : resolve()));\n            });\n        }\n    }\n    /**\n     * Throws an error if the response has neither a result nor an error, or if\n     * the \"isComplete\" flag is falsy.\n     */\n    static _checkForCompletion(req, res, isComplete) {\n        if (!('result' in res) && !('error' in res)) {\n            throw new eth_rpc_errors_1.EthereumRpcError(eth_rpc_errors_1.errorCodes.rpc.internal, `JsonRpcEngine: Response has no error or result for request:\\n${jsonify(req)}`, { request: req });\n        }\n        if (!isComplete) {\n            throw new eth_rpc_errors_1.EthereumRpcError(eth_rpc_errors_1.errorCodes.rpc.internal, `JsonRpcEngine: Nothing ended request:\\n${jsonify(req)}`, { request: req });\n        }\n    }\n}\nexports.JsonRpcEngine = JsonRpcEngine;\nfunction jsonify(request) {\n    return JSON.stringify(request, null, 2);\n}\n//# sourceMappingURL=data:application/json;base64,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# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/json-rpc-engine/dist/JsonRpcEngine.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/json-rpc-engine/dist/createAsyncMiddleware.js":
/*!********************************************************************!*\
  !*** ./node_modules/json-rpc-engine/dist/createAsyncMiddleware.js ***!
  \********************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.createAsyncMiddleware = void 0;\n/**\n * JsonRpcEngine only accepts callback-based middleware directly.\n * createAsyncMiddleware exists to enable consumers to pass in async middleware\n * functions.\n *\n * Async middleware have no \"end\" function. Instead, they \"end\" if they return\n * without calling \"next\". Rather than passing in explicit return handlers,\n * async middleware can simply await \"next\", and perform operations on the\n * response object when execution resumes.\n *\n * To accomplish this, createAsyncMiddleware passes the async middleware a\n * wrapped \"next\" function. That function calls the internal JsonRpcEngine\n * \"next\" function with a return handler that resolves a promise when called.\n *\n * The return handler will always be called. Its resolution of the promise\n * enables the control flow described above.\n */\nfunction createAsyncMiddleware(asyncMiddleware) {\n    return async (req, res, next, end) => {\n        // nextPromise is the key to the implementation\n        // it is resolved by the return handler passed to the\n        // \"next\" function\n        let resolveNextPromise;\n        const nextPromise = new Promise((resolve) => {\n            resolveNextPromise = resolve;\n        });\n        let returnHandlerCallback = null;\n        let nextWasCalled = false;\n        // This will be called by the consumer's async middleware.\n        const asyncNext = async () => {\n            nextWasCalled = true;\n            // We pass a return handler to next(). When it is called by the engine,\n            // the consumer's async middleware will resume executing.\n            // eslint-disable-next-line node/callback-return\n            next((runReturnHandlersCallback) => {\n                // This callback comes from JsonRpcEngine._runReturnHandlers\n                returnHandlerCallback = runReturnHandlersCallback;\n                resolveNextPromise();\n            });\n            await nextPromise;\n        };\n        try {\n            await asyncMiddleware(req, res, asyncNext);\n            if (nextWasCalled) {\n                await nextPromise; // we must wait until the return handler is called\n                returnHandlerCallback(null);\n            }\n            else {\n                end(null);\n            }\n        }\n        catch (error) {\n            if (returnHandlerCallback) {\n                returnHandlerCallback(error);\n            }\n            else {\n                end(error);\n            }\n        }\n    };\n}\nexports.createAsyncMiddleware = createAsyncMiddleware;\n//# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiY3JlYXRlQXN5bmNNaWRkbGV3YXJlLmpzIiwic291cmNlUm9vdCI6IiIsInNvdXJjZXMiOlsiLi4vc3JjL2NyZWF0ZUFzeW5jTWlkZGxld2FyZS50cyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiOzs7QUFnQkE7Ozs7Ozs7Ozs7Ozs7Ozs7R0FnQkc7QUFDSCxTQUFnQixxQkFBcUIsQ0FDbkMsZUFBNkM7SUFFN0MsT0FBTyxLQUFLLEVBQUUsR0FBRyxFQUFFLEdBQUcsRUFBRSxJQUFJLEVBQUUsR0FBRyxFQUFFLEVBQUU7UUFDbkMsK0NBQStDO1FBQy9DLHFEQUFxRDtRQUNyRCxrQkFBa0I7UUFDbEIsSUFBSSxrQkFBOEIsQ0FBQztRQUNuQyxNQUFNLFdBQVcsR0FBRyxJQUFJLE9BQU8sQ0FBQyxDQUFDLE9BQU8sRUFBRSxFQUFFO1lBQzFDLGtCQUFrQixHQUFHLE9BQU8sQ0FBQztRQUMvQixDQUFDLENBQUMsQ0FBQztRQUVILElBQUkscUJBQXFCLEdBQVksSUFBSSxDQUFDO1FBQzFDLElBQUksYUFBYSxHQUFHLEtBQUssQ0FBQztRQUUxQiwwREFBMEQ7UUFDMUQsTUFBTSxTQUFTLEdBQUcsS0FBSyxJQUFJLEVBQUU7WUFDM0IsYUFBYSxHQUFHLElBQUksQ0FBQztZQUVyQix1RUFBdUU7WUFDdkUseURBQXlEO1lBQ3pELGdEQUFnRDtZQUNoRCxJQUFJLENBQUMsQ0FBQyx5QkFBeUIsRUFBRSxFQUFFO2dCQUNqQyw0REFBNEQ7Z0JBQzVELHFCQUFxQixHQUFHLHlCQUF5QixDQUFDO2dCQUNsRCxrQkFBa0IsRUFBRSxDQUFDO1lBQ3ZCLENBQUMsQ0FBQyxDQUFDO1lBQ0gsTUFBTSxXQUFXLENBQUM7UUFDcEIsQ0FBQyxDQUFDO1FBRUYsSUFBSTtZQUNGLE1BQU0sZUFBZSxDQUFDLEdBQUcsRUFBRSxHQUFHLEVBQUUsU0FBUyxDQUFDLENBQUM7WUFFM0MsSUFBSSxhQUFhLEVBQUU7Z0JBQ2pCLE1BQU0sV0FBVyxDQUFDLENBQUMsa0RBQWtEO2dCQUNwRSxxQkFBK0MsQ0FBQyxJQUFJLENBQUMsQ0FBQzthQUN4RDtpQkFBTTtnQkFDTCxHQUFHLENBQUMsSUFBSSxDQUFDLENBQUM7YUFDWDtTQUNGO1FBQUMsT0FBTyxLQUFLLEVBQUU7WUFDZCxJQUFJLHFCQUFxQixFQUFFO2dCQUN4QixxQkFBK0MsQ0FBQyxLQUFLLENBQUMsQ0FBQzthQUN6RDtpQkFBTTtnQkFDTCxHQUFHLENBQUMsS0FBSyxDQUFDLENBQUM7YUFDWjtTQUNGO0lBQ0gsQ0FBQyxDQUFDO0FBQ0osQ0FBQztBQS9DRCxzREErQ0MifQ==//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/json-rpc-engine/dist/createAsyncMiddleware.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/json-rpc-engine/dist/createScaffoldMiddleware.js":
/*!***********************************************************************!*\
  !*** ./node_modules/json-rpc-engine/dist/createScaffoldMiddleware.js ***!
  \***********************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.createScaffoldMiddleware = void 0;\nfunction createScaffoldMiddleware(handlers) {\n    return (req, res, next, end) => {\n        const handler = handlers[req.method];\n        // if no handler, return\n        if (handler === undefined) {\n            return next();\n        }\n        // if handler is fn, call as middleware\n        if (typeof handler === 'function') {\n            return handler(req, res, next, end);\n        }\n        // if handler is some other value, use as result\n        res.result = handler;\n        return end();\n    };\n}\nexports.createScaffoldMiddleware = createScaffoldMiddleware;\n//# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiY3JlYXRlU2NhZmZvbGRNaWRkbGV3YXJlLmpzIiwic291cmNlUm9vdCI6IiIsInNvdXJjZXMiOlsiLi4vc3JjL2NyZWF0ZVNjYWZmb2xkTWlkZGxld2FyZS50cyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiOzs7QUFJQSxTQUFnQix3QkFBd0IsQ0FBQyxRQUV4QztJQUNDLE9BQU8sQ0FBQyxHQUFHLEVBQUUsR0FBRyxFQUFFLElBQUksRUFBRSxHQUFHLEVBQUUsRUFBRTtRQUM3QixNQUFNLE9BQU8sR0FBRyxRQUFRLENBQUMsR0FBRyxDQUFDLE1BQU0sQ0FBQyxDQUFDO1FBQ3JDLHdCQUF3QjtRQUN4QixJQUFJLE9BQU8sS0FBSyxTQUFTLEVBQUU7WUFDekIsT0FBTyxJQUFJLEVBQUUsQ0FBQztTQUNmO1FBQ0QsdUNBQXVDO1FBQ3ZDLElBQUksT0FBTyxPQUFPLEtBQUssVUFBVSxFQUFFO1lBQ2pDLE9BQU8sT0FBTyxDQUFDLEdBQUcsRUFBRSxHQUFHLEVBQUUsSUFBSSxFQUFFLEdBQUcsQ0FBQyxDQUFDO1NBQ3JDO1FBQ0QsZ0RBQWdEO1FBQy9DLEdBQStCLENBQUMsTUFBTSxHQUFHLE9BQU8sQ0FBQztRQUNsRCxPQUFPLEdBQUcsRUFBRSxDQUFDO0lBQ2YsQ0FBQyxDQUFDO0FBQ0osQ0FBQztBQWpCRCw0REFpQkMifQ==//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/json-rpc-engine/dist/createScaffoldMiddleware.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/json-rpc-engine/dist/getUniqueId.js":
/*!**********************************************************!*\
  !*** ./node_modules/json-rpc-engine/dist/getUniqueId.js ***!
  \**********************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.getUniqueId = void 0;\n// uint32 (two's complement) max\n// more conservative than Number.MAX_SAFE_INTEGER\nconst MAX = 4294967295;\nlet idCounter = Math.floor(Math.random() * MAX);\nfunction getUniqueId() {\n    idCounter = (idCounter + 1) % MAX;\n    return idCounter;\n}\nexports.getUniqueId = getUniqueId;\n//# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiZ2V0VW5pcXVlSWQuanMiLCJzb3VyY2VSb290IjoiIiwic291cmNlcyI6WyIuLi9zcmMvZ2V0VW5pcXVlSWQudHMiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6Ijs7O0FBQUEsZ0NBQWdDO0FBQ2hDLGlEQUFpRDtBQUNqRCxNQUFNLEdBQUcsR0FBRyxVQUFVLENBQUM7QUFDdkIsSUFBSSxTQUFTLEdBQUcsSUFBSSxDQUFDLEtBQUssQ0FBQyxJQUFJLENBQUMsTUFBTSxFQUFFLEdBQUcsR0FBRyxDQUFDLENBQUM7QUFFaEQsU0FBZ0IsV0FBVztJQUN6QixTQUFTLEdBQUcsQ0FBQyxTQUFTLEdBQUcsQ0FBQyxDQUFDLEdBQUcsR0FBRyxDQUFDO0lBQ2xDLE9BQU8sU0FBUyxDQUFDO0FBQ25CLENBQUM7QUFIRCxrQ0FHQyJ9//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvanNvbi1ycGMtZW5naW5lL2Rpc3QvZ2V0VW5pcXVlSWQuanMiLCJtYXBwaW5ncyI6IkFBQWE7QUFDYiw4Q0FBNkMsRUFBRSxhQUFhLEVBQUM7QUFDN0QsbUJBQW1CO0FBQ25CO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxtQkFBbUI7QUFDbkIsMkNBQTJDIiwic291cmNlcyI6WyJGOlxcQUkgY2hhaW5cXGNoYWluc2lnaHQtcHJvZHVjdGlvblxcbm9kZV9tb2R1bGVzXFxqc29uLXJwYy1lbmdpbmVcXGRpc3RcXGdldFVuaXF1ZUlkLmpzIl0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIHN0cmljdFwiO1xuT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsIFwiX19lc01vZHVsZVwiLCB7IHZhbHVlOiB0cnVlIH0pO1xuZXhwb3J0cy5nZXRVbmlxdWVJZCA9IHZvaWQgMDtcbi8vIHVpbnQzMiAodHdvJ3MgY29tcGxlbWVudCkgbWF4XG4vLyBtb3JlIGNvbnNlcnZhdGl2ZSB0aGFuIE51bWJlci5NQVhfU0FGRV9JTlRFR0VSXG5jb25zdCBNQVggPSA0Mjk0OTY3Mjk1O1xubGV0IGlkQ291bnRlciA9IE1hdGguZmxvb3IoTWF0aC5yYW5kb20oKSAqIE1BWCk7XG5mdW5jdGlvbiBnZXRVbmlxdWVJZCgpIHtcbiAgICBpZENvdW50ZXIgPSAoaWRDb3VudGVyICsgMSkgJSBNQVg7XG4gICAgcmV0dXJuIGlkQ291bnRlcjtcbn1cbmV4cG9ydHMuZ2V0VW5pcXVlSWQgPSBnZXRVbmlxdWVJZDtcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPWRhdGE6YXBwbGljYXRpb24vanNvbjtiYXNlNjQsZXlKMlpYSnphVzl1SWpvekxDSm1hV3hsSWpvaVoyVjBWVzVwY1hWbFNXUXVhbk1pTENKemIzVnlZMlZTYjI5MElqb2lJaXdpYzI5MWNtTmxjeUk2V3lJdUxpOXpjbU12WjJWMFZXNXBjWFZsU1dRdWRITWlYU3dpYm1GdFpYTWlPbHRkTENKdFlYQndhVzVuY3lJNklqczdPMEZCUVVFc1owTkJRV2RETzBGQlEyaERMR2xFUVVGcFJEdEJRVU5xUkN4TlFVRk5MRWRCUVVjc1IwRkJSeXhWUVVGVkxFTkJRVU03UVVGRGRrSXNTVUZCU1N4VFFVRlRMRWRCUVVjc1NVRkJTU3hEUVVGRExFdEJRVXNzUTBGQlF5eEpRVUZKTEVOQlFVTXNUVUZCVFN4RlFVRkZMRWRCUVVjc1IwRkJSeXhEUVVGRExFTkJRVU03UVVGRmFFUXNVMEZCWjBJc1YwRkJWenRKUVVONlFpeFRRVUZUTEVkQlFVY3NRMEZCUXl4VFFVRlRMRWRCUVVjc1EwRkJReXhEUVVGRExFZEJRVWNzUjBGQlJ5eERRVUZETzBsQlEyeERMRTlCUVU4c1UwRkJVeXhEUVVGRE8wRkJRMjVDTEVOQlFVTTdRVUZJUkN4clEwRkhReUo5Il0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/json-rpc-engine/dist/getUniqueId.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/json-rpc-engine/dist/idRemapMiddleware.js":
/*!****************************************************************!*\
  !*** ./node_modules/json-rpc-engine/dist/idRemapMiddleware.js ***!
  \****************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.createIdRemapMiddleware = void 0;\nconst getUniqueId_1 = __webpack_require__(/*! ./getUniqueId */ \"(ssr)/./node_modules/json-rpc-engine/dist/getUniqueId.js\");\nfunction createIdRemapMiddleware() {\n    return (req, res, next, _end) => {\n        const originalId = req.id;\n        const newId = getUniqueId_1.getUniqueId();\n        req.id = newId;\n        res.id = newId;\n        next((done) => {\n            req.id = originalId;\n            res.id = originalId;\n            done();\n        });\n    };\n}\nexports.createIdRemapMiddleware = createIdRemapMiddleware;\n//# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiaWRSZW1hcE1pZGRsZXdhcmUuanMiLCJzb3VyY2VSb290IjoiIiwic291cmNlcyI6WyIuLi9zcmMvaWRSZW1hcE1pZGRsZXdhcmUudHMiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6Ijs7O0FBQUEsK0NBQTRDO0FBRzVDLFNBQWdCLHVCQUF1QjtJQUNyQyxPQUFPLENBQUMsR0FBRyxFQUFFLEdBQUcsRUFBRSxJQUFJLEVBQUUsSUFBSSxFQUFFLEVBQUU7UUFDOUIsTUFBTSxVQUFVLEdBQUcsR0FBRyxDQUFDLEVBQUUsQ0FBQztRQUMxQixNQUFNLEtBQUssR0FBRyx5QkFBVyxFQUFFLENBQUM7UUFDNUIsR0FBRyxDQUFDLEVBQUUsR0FBRyxLQUFLLENBQUM7UUFDZixHQUFHLENBQUMsRUFBRSxHQUFHLEtBQUssQ0FBQztRQUNmLElBQUksQ0FBQyxDQUFDLElBQUksRUFBRSxFQUFFO1lBQ1osR0FBRyxDQUFDLEVBQUUsR0FBRyxVQUFVLENBQUM7WUFDcEIsR0FBRyxDQUFDLEVBQUUsR0FBRyxVQUFVLENBQUM7WUFDcEIsSUFBSSxFQUFFLENBQUM7UUFDVCxDQUFDLENBQUMsQ0FBQztJQUNMLENBQUMsQ0FBQztBQUNKLENBQUM7QUFaRCwwREFZQyJ9//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/json-rpc-engine/dist/idRemapMiddleware.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/json-rpc-engine/dist/index.js":
/*!****************************************************!*\
  !*** ./node_modules/json-rpc-engine/dist/index.js ***!
  \****************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

eval("\nvar __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    Object.defineProperty(o, k2, { enumerable: true, get: function() { return m[k]; } });\n}) : (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    o[k2] = m[k];\n}));\nvar __exportStar = (this && this.__exportStar) || function(m, exports) {\n    for (var p in m) if (p !== \"default\" && !Object.prototype.hasOwnProperty.call(exports, p)) __createBinding(exports, m, p);\n};\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\n__exportStar(__webpack_require__(/*! ./idRemapMiddleware */ \"(ssr)/./node_modules/json-rpc-engine/dist/idRemapMiddleware.js\"), exports);\n__exportStar(__webpack_require__(/*! ./createAsyncMiddleware */ \"(ssr)/./node_modules/json-rpc-engine/dist/createAsyncMiddleware.js\"), exports);\n__exportStar(__webpack_require__(/*! ./createScaffoldMiddleware */ \"(ssr)/./node_modules/json-rpc-engine/dist/createScaffoldMiddleware.js\"), exports);\n__exportStar(__webpack_require__(/*! ./getUniqueId */ \"(ssr)/./node_modules/json-rpc-engine/dist/getUniqueId.js\"), exports);\n__exportStar(__webpack_require__(/*! ./JsonRpcEngine */ \"(ssr)/./node_modules/json-rpc-engine/dist/JsonRpcEngine.js\"), exports);\n__exportStar(__webpack_require__(/*! ./mergeMiddleware */ \"(ssr)/./node_modules/json-rpc-engine/dist/mergeMiddleware.js\"), exports);\n//# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiaW5kZXguanMiLCJzb3VyY2VSb290IjoiIiwic291cmNlcyI6WyIuLi9zcmMvaW5kZXgudHMiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7O0FBQUEsc0RBQW9DO0FBQ3BDLDBEQUF3QztBQUN4Qyw2REFBMkM7QUFDM0MsZ0RBQThCO0FBQzlCLGtEQUFnQztBQUNoQyxvREFBa0MifQ==//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/json-rpc-engine/dist/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/json-rpc-engine/dist/mergeMiddleware.js":
/*!**************************************************************!*\
  !*** ./node_modules/json-rpc-engine/dist/mergeMiddleware.js ***!
  \**************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.mergeMiddleware = void 0;\nconst JsonRpcEngine_1 = __webpack_require__(/*! ./JsonRpcEngine */ \"(ssr)/./node_modules/json-rpc-engine/dist/JsonRpcEngine.js\");\nfunction mergeMiddleware(middlewareStack) {\n    const engine = new JsonRpcEngine_1.JsonRpcEngine();\n    middlewareStack.forEach((middleware) => engine.push(middleware));\n    return engine.asMiddleware();\n}\nexports.mergeMiddleware = mergeMiddleware;\n//# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoibWVyZ2VNaWRkbGV3YXJlLmpzIiwic291cmNlUm9vdCI6IiIsInNvdXJjZXMiOlsiLi4vc3JjL21lcmdlTWlkZGxld2FyZS50cyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiOzs7QUFBQSxtREFBbUU7QUFFbkUsU0FBZ0IsZUFBZSxDQUFDLGVBQXNEO0lBQ3BGLE1BQU0sTUFBTSxHQUFHLElBQUksNkJBQWEsRUFBRSxDQUFDO0lBQ25DLGVBQWUsQ0FBQyxPQUFPLENBQUMsQ0FBQyxVQUFVLEVBQUUsRUFBRSxDQUFDLE1BQU0sQ0FBQyxJQUFJLENBQUMsVUFBVSxDQUFDLENBQUMsQ0FBQztJQUNqRSxPQUFPLE1BQU0sQ0FBQyxZQUFZLEVBQUUsQ0FBQztBQUMvQixDQUFDO0FBSkQsMENBSUMifQ==//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvanNvbi1ycGMtZW5naW5lL2Rpc3QvbWVyZ2VNaWRkbGV3YXJlLmpzIiwibWFwcGluZ3MiOiJBQUFhO0FBQ2IsOENBQTZDLEVBQUUsYUFBYSxFQUFDO0FBQzdELHVCQUF1QjtBQUN2Qix3QkFBd0IsbUJBQU8sQ0FBQyxtRkFBaUI7QUFDakQ7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHVCQUF1QjtBQUN2QiwyQ0FBMkMiLCJzb3VyY2VzIjpbIkY6XFxBSSBjaGFpblxcY2hhaW5zaWdodC1wcm9kdWN0aW9uXFxub2RlX21vZHVsZXNcXGpzb24tcnBjLWVuZ2luZVxcZGlzdFxcbWVyZ2VNaWRkbGV3YXJlLmpzIl0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIHN0cmljdFwiO1xuT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsIFwiX19lc01vZHVsZVwiLCB7IHZhbHVlOiB0cnVlIH0pO1xuZXhwb3J0cy5tZXJnZU1pZGRsZXdhcmUgPSB2b2lkIDA7XG5jb25zdCBKc29uUnBjRW5naW5lXzEgPSByZXF1aXJlKFwiLi9Kc29uUnBjRW5naW5lXCIpO1xuZnVuY3Rpb24gbWVyZ2VNaWRkbGV3YXJlKG1pZGRsZXdhcmVTdGFjaykge1xuICAgIGNvbnN0IGVuZ2luZSA9IG5ldyBKc29uUnBjRW5naW5lXzEuSnNvblJwY0VuZ2luZSgpO1xuICAgIG1pZGRsZXdhcmVTdGFjay5mb3JFYWNoKChtaWRkbGV3YXJlKSA9PiBlbmdpbmUucHVzaChtaWRkbGV3YXJlKSk7XG4gICAgcmV0dXJuIGVuZ2luZS5hc01pZGRsZXdhcmUoKTtcbn1cbmV4cG9ydHMubWVyZ2VNaWRkbGV3YXJlID0gbWVyZ2VNaWRkbGV3YXJlO1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9ZGF0YTphcHBsaWNhdGlvbi9qc29uO2Jhc2U2NCxleUoyWlhKemFXOXVJam96TENKbWFXeGxJam9pYldWeVoyVk5hV1JrYkdWM1lYSmxMbXB6SWl3aWMyOTFjbU5sVW05dmRDSTZJaUlzSW5OdmRYSmpaWE1pT2xzaUxpNHZjM0pqTDIxbGNtZGxUV2xrWkd4bGQyRnlaUzUwY3lKZExDSnVZVzFsY3lJNlcxMHNJbTFoY0hCcGJtZHpJam9pT3pzN1FVRkJRU3h0UkVGQmJVVTdRVUZGYmtVc1UwRkJaMElzWlVGQlpTeERRVUZETEdWQlFYTkVPMGxCUTNCR0xFMUJRVTBzVFVGQlRTeEhRVUZITEVsQlFVa3NOa0pCUVdFc1JVRkJSU3hEUVVGRE8wbEJRMjVETEdWQlFXVXNRMEZCUXl4UFFVRlBMRU5CUVVNc1EwRkJReXhWUVVGVkxFVkJRVVVzUlVGQlJTeERRVUZETEUxQlFVMHNRMEZCUXl4SlFVRkpMRU5CUVVNc1ZVRkJWU3hEUVVGRExFTkJRVU1zUTBGQlF6dEpRVU5xUlN4UFFVRlBMRTFCUVUwc1EwRkJReXhaUVVGWkxFVkJRVVVzUTBGQlF6dEJRVU12UWl4RFFVRkRPMEZCU2tRc01FTkJTVU1pZlE9PSJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/json-rpc-engine/dist/mergeMiddleware.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/json-rpc-engine/node_modules/@metamask/safe-event-emitter/index.js":
/*!*****************************************************************************************!*\
  !*** ./node_modules/json-rpc-engine/node_modules/@metamask/safe-event-emitter/index.js ***!
  \*****************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nconst events_1 = __webpack_require__(/*! events */ \"events\");\nfunction safeApply(handler, context, args) {\n    try {\n        Reflect.apply(handler, context, args);\n    }\n    catch (err) {\n        // Throw error after timeout so as not to interrupt the stack\n        setTimeout(() => {\n            throw err;\n        });\n    }\n}\nfunction arrayClone(arr) {\n    const n = arr.length;\n    const copy = new Array(n);\n    for (let i = 0; i < n; i += 1) {\n        copy[i] = arr[i];\n    }\n    return copy;\n}\nclass SafeEventEmitter extends events_1.EventEmitter {\n    emit(type, ...args) {\n        let doError = type === 'error';\n        const events = this._events;\n        if (events !== undefined) {\n            doError = doError && events.error === undefined;\n        }\n        else if (!doError) {\n            return false;\n        }\n        // If there is no 'error' event listener then throw.\n        if (doError) {\n            let er;\n            if (args.length > 0) {\n                [er] = args;\n            }\n            if (er instanceof Error) {\n                // Note: The comments on the `throw` lines are intentional, they show\n                // up in Node's output if this results in an unhandled exception.\n                throw er; // Unhandled 'error' event\n            }\n            // At least give some kind of context to the user\n            const err = new Error(`Unhandled error.${er ? ` (${er.message})` : ''}`);\n            err.context = er;\n            throw err; // Unhandled 'error' event\n        }\n        const handler = events[type];\n        if (handler === undefined) {\n            return false;\n        }\n        if (typeof handler === 'function') {\n            safeApply(handler, this, args);\n        }\n        else {\n            const len = handler.length;\n            const listeners = arrayClone(handler);\n            for (let i = 0; i < len; i += 1) {\n                safeApply(listeners[i], this, args);\n            }\n        }\n        return true;\n    }\n}\nexports[\"default\"] = SafeEventEmitter;\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/json-rpc-engine/node_modules/@metamask/safe-event-emitter/index.js\n");

/***/ })

};
;