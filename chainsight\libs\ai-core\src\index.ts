// AI Core Library - Main exports
export * from './chat/ChatEngine';
export * from './nlp/TextAnalyzer';
export * from './ml/PredictionEngine';
export * from './sentiment/SentimentAnalyzer';
export * from './types/AITypes';
export * from './utils/AIUtils';

// Core AI configuration
export interface AIConfig {
  openaiApiKey?: string;
  modelName?: string;
  temperature?: number;
  maxTokens?: number;
  enableLogging?: boolean;
}

// Default configuration
export const defaultAIConfig: AIConfig = {
  modelName: 'gpt-3.5-turbo',
  temperature: 0.7,
  maxTokens: 500,
  enableLogging: false
};

// Initialize AI Core
export class AICore {
  private config: AIConfig;

  constructor(config: Partial<AIConfig> = {}) {
    this.config = { ...defaultAIConfig, ...config };
  }

  getConfig(): AIConfig {
    return this.config;
  }

  updateConfig(newConfig: Partial<AIConfig>): void {
    this.config = { ...this.config, ...newConfig };
  }
}

// Export singleton instance
export const aiCore = new AICore();
