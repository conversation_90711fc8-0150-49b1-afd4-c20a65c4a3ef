import { chainConfig } from '../../op-stack/chainConfig.js'
import { define<PERSON>hain } from '../../utils/chain/defineChain.js'

const sourceId = 1 // mainnet

export const shape = /*#__PURE__*/ defineChain({
  ...chainConfig,
  id: 360,
  name: '<PERSON>hape',
  nativeCurrency: { name: 'Ether', symbol: 'ETH', decimals: 18 },
  rpcUrls: {
    default: {
      http: ['https://mainnet.shape.network'],
    },
  },
  blockExplorers: {
    default: {
      name: 'shapescan',
      url: 'https://shapescan.xyz',
      apiUrl: 'https://shapescan.xyz/api',
    },
  },
  contracts: {
    ...chainConfig.contracts,
    l2OutputOracle: {
      [sourceId]: {
        address: '******************************************',
        blockCreated: 20369940,
      },
    },
    multicall3: {
      address: '******************************************',
      blockCreated: 1,
    },
    portal: {
      [sourceId]: {
        address: '******************************************',
        blockCreated: 20369933,
      },
    },
    l1StandardBridge: {
      [sourceId]: {
        address: '******************************************',
        blockCreated: 20369935,
      },
    },
  },
  sourceId,
})
