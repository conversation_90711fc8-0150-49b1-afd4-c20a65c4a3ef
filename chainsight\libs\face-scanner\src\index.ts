// Face Scanner Library - Main exports
export * from './detection/FaceDetector';
export * from './recognition/FaceRecognizer';
export * from './analysis/EmotionAnalyzer';
export * from './analysis/AgeEstimator';
export * from './comparison/FaceComparator';
export * from './utils/ImageProcessor';
export * from './types/FaceTypes';
export * from './models/ModelLoader';

// Core face scanner configuration
export interface FaceScannerConfig {
  modelPath?: string;
  confidenceThreshold?: number;
  maxFaces?: number;
  enableEmotion?: boolean;
  enableAge?: boolean;
  enableGender?: boolean;
  enableLandmarks?: boolean;
  imageQuality?: 'low' | 'medium' | 'high';
  enableGPU?: boolean;
}

// Default configuration
export const defaultFaceScannerConfig: FaceScannerConfig = {
  confidenceThreshold: 0.5,
  maxFaces: 10,
  enableEmotion: true,
  enableAge: true,
  enableGender: true,
  enableLandmarks: true,
  imageQuality: 'medium',
  enableGPU: false
};

// Initialize Face Scanner
export class FaceScanner {
  private config: FaceScannerConfig;
  private modelsLoaded: boolean = false;

  constructor(config: Partial<FaceScannerConfig> = {}) {
    this.config = { ...defaultFaceScannerConfig, ...config };
  }

  getConfig(): FaceScannerConfig {
    return this.config;
  }

  updateConfig(newConfig: Partial<FaceScannerConfig>): void {
    this.config = { ...this.config, ...newConfig };
  }

  async initialize(): Promise<void> {
    if (this.modelsLoaded) return;

    try {
      // Initialize TensorFlow.js
      await import('@tensorflow/tfjs-node');
      
      // Load face detection models
      // In production, you would load actual model files
      console.log('Loading face detection models...');
      
      // Simulate model loading
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      this.modelsLoaded = true;
      console.log('Face scanner models loaded successfully');
    } catch (error) {
      throw new Error(`Failed to initialize face scanner: ${error}`);
    }
  }

  isInitialized(): boolean {
    return this.modelsLoaded;
  }

  // Utility methods
  getSupportedFormats(): string[] {
    return ['jpg', 'jpeg', 'png', 'bmp', 'webp'];
  }

  getMaxImageSize(): number {
    return 10 * 1024 * 1024; // 10MB
  }

  getRecommendedImageSize(): { width: number; height: number } {
    return { width: 640, height: 480 };
  }

  validateImageFormat(filename: string): boolean {
    const extension = filename.toLowerCase().split('.').pop();
    return this.getSupportedFormats().includes(extension || '');
  }

  validateImageSize(size: number): boolean {
    return size <= this.getMaxImageSize();
  }
}

// Export singleton instance
export const faceScanner = new FaceScanner();
