/* ==================== VALIDATION UTILITIES ====================
   Form validation and data validation utilities
   ========================================================== */

class ValidationUtils {
    /**
     * Email validation
     * @param {string} email - Email to validate
     * @returns {boolean}
     */
    static isValidEmail(email) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return emailRegex.test(email);
    }

    /**
     * Phone number validation (international format)
     * @param {string} phone - Phone number to validate
     * @returns {boolean}
     */
    static isValidPhone(phone) {
        const phoneRegex = /^\+?[\d\s\-\(\)]{10,}$/;
        return phoneRegex.test(phone);
    }

    /**
     * URL validation
     * @param {string} url - URL to validate
     * @returns {boolean}
     */
    static isValidURL(url) {
        try {
            new URL(url);
            return true;
        } catch {
            return false;
        }
    }

    /**
     * Password strength validation
     * @param {string} password - Password to validate
     * @param {Object} options - Validation options
     * @returns {Object} - Validation result with score and feedback
     */
    static validatePassword(password, options = {}) {
        const {
            minLength = 8,
            requireUppercase = true,
            requireLowercase = true,
            requireNumbers = true,
            requireSpecialChars = true,
            maxLength = 128
        } = options;

        const result = {
            isValid: true,
            score: 0,
            feedback: [],
            strength: 'weak'
        };

        // Length check
        if (password.length < minLength) {
            result.isValid = false;
            result.feedback.push(`Password must be at least ${minLength} characters long`);
        } else {
            result.score += 1;
        }

        if (password.length > maxLength) {
            result.isValid = false;
            result.feedback.push(`Password must be no more than ${maxLength} characters long`);
        }

        // Character type checks
        if (requireUppercase && !/[A-Z]/.test(password)) {
            result.isValid = false;
            result.feedback.push('Password must contain at least one uppercase letter');
        } else if (requireUppercase) {
            result.score += 1;
        }

        if (requireLowercase && !/[a-z]/.test(password)) {
            result.isValid = false;
            result.feedback.push('Password must contain at least one lowercase letter');
        } else if (requireLowercase) {
            result.score += 1;
        }

        if (requireNumbers && !/\d/.test(password)) {
            result.isValid = false;
            result.feedback.push('Password must contain at least one number');
        } else if (requireNumbers) {
            result.score += 1;
        }

        if (requireSpecialChars && !/[!@#$%^&*(),.?":{}|<>]/.test(password)) {
            result.isValid = false;
            result.feedback.push('Password must contain at least one special character');
        } else if (requireSpecialChars) {
            result.score += 1;
        }

        // Additional strength checks
        if (password.length >= 12) result.score += 1;
        if (/[!@#$%^&*(),.?":{}|<>]/.test(password)) result.score += 1;
        if (!/(.)\1{2,}/.test(password)) result.score += 1; // No repeated characters

        // Determine strength
        if (result.score >= 6) result.strength = 'strong';
        else if (result.score >= 4) result.strength = 'medium';
        else result.strength = 'weak';

        return result;
    }

    /**
     * Credit card number validation (Luhn algorithm)
     * @param {string} cardNumber - Credit card number
     * @returns {boolean}
     */
    static isValidCreditCard(cardNumber) {
        const cleaned = cardNumber.replace(/\D/g, '');
        
        if (cleaned.length < 13 || cleaned.length > 19) {
            return false;
        }

        let sum = 0;
        let isEven = false;

        for (let i = cleaned.length - 1; i >= 0; i--) {
            let digit = parseInt(cleaned[i]);

            if (isEven) {
                digit *= 2;
                if (digit > 9) {
                    digit -= 9;
                }
            }

            sum += digit;
            isEven = !isEven;
        }

        return sum % 10 === 0;
    }

    /**
     * Date validation
     * @param {string} date - Date string
     * @param {string} format - Expected format (optional)
     * @returns {boolean}
     */
    static isValidDate(date, format = null) {
        if (format) {
            // Simple format validation (YYYY-MM-DD, MM/DD/YYYY, etc.)
            const formatRegex = {
                'YYYY-MM-DD': /^\d{4}-\d{2}-\d{2}$/,
                'MM/DD/YYYY': /^\d{2}\/\d{2}\/\d{4}$/,
                'DD/MM/YYYY': /^\d{2}\/\d{2}\/\d{4}$/
            };

            if (formatRegex[format] && !formatRegex[format].test(date)) {
                return false;
            }
        }

        const parsedDate = new Date(date);
        return !isNaN(parsedDate.getTime());
    }

    /**
     * Required field validation
     * @param {*} value - Value to validate
     * @returns {boolean}
     */
    static isRequired(value) {
        if (value === null || value === undefined) return false;
        if (typeof value === 'string') return value.trim().length > 0;
        if (Array.isArray(value)) return value.length > 0;
        return true;
    }

    /**
     * Minimum length validation
     * @param {string} value - Value to validate
     * @param {number} minLength - Minimum length
     * @returns {boolean}
     */
    static minLength(value, minLength) {
        return typeof value === 'string' && value.length >= minLength;
    }

    /**
     * Maximum length validation
     * @param {string} value - Value to validate
     * @param {number} maxLength - Maximum length
     * @returns {boolean}
     */
    static maxLength(value, maxLength) {
        return typeof value === 'string' && value.length <= maxLength;
    }

    /**
     * Numeric validation
     * @param {*} value - Value to validate
     * @returns {boolean}
     */
    static isNumeric(value) {
        return !isNaN(parseFloat(value)) && isFinite(value);
    }

    /**
     * Integer validation
     * @param {*} value - Value to validate
     * @returns {boolean}
     */
    static isInteger(value) {
        return Number.isInteger(Number(value));
    }

    /**
     * Range validation
     * @param {number} value - Value to validate
     * @param {number} min - Minimum value
     * @param {number} max - Maximum value
     * @returns {boolean}
     */
    static inRange(value, min, max) {
        const num = Number(value);
        return !isNaN(num) && num >= min && num <= max;
    }

    /**
     * Pattern validation
     * @param {string} value - Value to validate
     * @param {RegExp} pattern - Regular expression pattern
     * @returns {boolean}
     */
    static matchesPattern(value, pattern) {
        return pattern.test(value);
    }

    /**
     * File validation
     * @param {File} file - File to validate
     * @param {Object} options - Validation options
     * @returns {Object} - Validation result
     */
    static validateFile(file, options = {}) {
        const {
            maxSize = 10 * 1024 * 1024, // 10MB
            allowedTypes = [],
            allowedExtensions = []
        } = options;

        const result = {
            isValid: true,
            errors: []
        };

        if (!file) {
            result.isValid = false;
            result.errors.push('No file provided');
            return result;
        }

        // Size validation
        if (file.size > maxSize) {
            result.isValid = false;
            result.errors.push(`File size exceeds ${maxSize / 1024 / 1024}MB limit`);
        }

        // Type validation
        if (allowedTypes.length > 0 && !allowedTypes.includes(file.type)) {
            result.isValid = false;
            result.errors.push(`File type ${file.type} is not allowed`);
        }

        // Extension validation
        if (allowedExtensions.length > 0) {
            const extension = file.name.split('.').pop().toLowerCase();
            if (!allowedExtensions.includes(extension)) {
                result.isValid = false;
                result.errors.push(`File extension .${extension} is not allowed`);
            }
        }

        return result;
    }

    /**
     * Form validation
     * @param {Object} data - Form data
     * @param {Object} rules - Validation rules
     * @returns {Object} - Validation result
     */
    static validateForm(data, rules) {
        const result = {
            isValid: true,
            errors: {},
            hasErrors: false
        };

        Object.entries(rules).forEach(([field, fieldRules]) => {
            const value = data[field];
            const fieldErrors = [];

            fieldRules.forEach(rule => {
                if (typeof rule === 'function') {
                    const ruleResult = rule(value);
                    if (ruleResult !== true) {
                        fieldErrors.push(ruleResult);
                    }
                } else if (typeof rule === 'object') {
                    const { validator, message, ...params } = rule;
                    
                    let isValid = false;
                    if (typeof validator === 'string') {
                        isValid = this[validator](value, ...Object.values(params));
                    } else if (typeof validator === 'function') {
                        isValid = validator(value, ...Object.values(params));
                    }

                    if (!isValid) {
                        fieldErrors.push(message || `Validation failed for ${field}`);
                    }
                }
            });

            if (fieldErrors.length > 0) {
                result.errors[field] = fieldErrors;
                result.hasErrors = true;
                result.isValid = false;
            }
        });

        return result;
    }

    /**
     * Sanitize HTML string
     * @param {string} html - HTML string to sanitize
     * @returns {string} - Sanitized HTML
     */
    static sanitizeHTML(html) {
        const div = document.createElement('div');
        div.textContent = html;
        return div.innerHTML;
    }

    /**
     * Escape special characters for regex
     * @param {string} string - String to escape
     * @returns {string} - Escaped string
     */
    static escapeRegex(string) {
        return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
    }

    /**
     * Check if value is empty
     * @param {*} value - Value to check
     * @returns {boolean}
     */
    static isEmpty(value) {
        if (value === null || value === undefined) return true;
        if (typeof value === 'string') return value.trim() === '';
        if (Array.isArray(value)) return value.length === 0;
        if (typeof value === 'object') return Object.keys(value).length === 0;
        return false;
    }
}

// Export for module systems
if (typeof module !== 'undefined' && module.exports) {
    module.exports = ValidationUtils;
}

// Global assignment for browser
if (typeof window !== 'undefined') {
    window.ValidationUtils = ValidationUtils;
}
