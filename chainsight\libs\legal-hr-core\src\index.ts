// Legal HR Core Library - Main exports
export * from './resume/ResumeAnalyzer';
export * from './resume/SkillExtractor';
export * from './contracts/ContractAnalyzer';
export * from './contracts/RiskAssessment';
export * from './compliance/ComplianceChecker';
export * from './documents/DocumentParser';
export * from './nlp/LegalNLP';
export * from './types/LegalHRTypes';
export * from './utils/LegalHRUtils';

// Core legal HR configuration
export interface LegalHRConfig {
  enableSkillExtraction?: boolean;
  enableSentimentAnalysis?: boolean;
  enableComplianceCheck?: boolean;
  skillDatabase?: string[];
  legalTermsDatabase?: string[];
  confidenceThreshold?: number;
  maxDocumentSize?: number;
  supportedFormats?: string[];
}

// Default configuration
export const defaultLegalHRConfig: LegalHRConfig = {
  enableSkillExtraction: true,
  enableSentimentAnalysis: true,
  enableComplianceCheck: true,
  confidenceThreshold: 0.7,
  maxDocumentSize: 10 * 1024 * 1024, // 10MB
  supportedFormats: ['pdf', 'docx', 'txt', 'rtf']
};

// Initialize Legal HR Core
export class LegalHRCore {
  private config: LegalHRConfig;
  private skillDatabase: Set<string> = new Set();
  private legalTerms: Set<string> = new Set();

  constructor(config: Partial<LegalHRConfig> = {}) {
    this.config = { ...defaultLegalHRConfig, ...config };
    this.initializeDatabase();
  }

  getConfig(): LegalHRConfig {
    return this.config;
  }

  updateConfig(newConfig: Partial<LegalHRConfig>): void {
    this.config = { ...this.config, ...newConfig };
  }

  private initializeDatabase(): void {
    // Initialize skill database
    const defaultSkills = [
      // Technical Skills
      'JavaScript', 'TypeScript', 'Python', 'Java', 'C++', 'React', 'Node.js',
      'Angular', 'Vue.js', 'SQL', 'MongoDB', 'PostgreSQL', 'AWS', 'Azure',
      'Docker', 'Kubernetes', 'Git', 'Jenkins', 'Terraform', 'GraphQL',
      
      // Soft Skills
      'Leadership', 'Communication', 'Problem Solving', 'Team Management',
      'Project Management', 'Strategic Planning', 'Analytical Thinking',
      'Creativity', 'Adaptability', 'Time Management', 'Negotiation',
      
      // Business Skills
      'Marketing', 'Sales', 'Finance', 'Accounting', 'Operations',
      'Business Analysis', 'Data Analysis', 'Product Management',
      'Customer Service', 'Quality Assurance', 'Risk Management'
    ];

    defaultSkills.forEach(skill => this.skillDatabase.add(skill.toLowerCase()));

    // Initialize legal terms database
    const defaultLegalTerms = [
      'contract', 'agreement', 'liability', 'indemnification', 'confidentiality',
      'non-disclosure', 'intellectual property', 'copyright', 'trademark',
      'patent', 'breach', 'termination', 'force majeure', 'arbitration',
      'jurisdiction', 'governing law', 'damages', 'warranty', 'representation',
      'covenant', 'consideration', 'assignment', 'sublicense', 'amendment'
    ];

    defaultLegalTerms.forEach(term => this.legalTerms.add(term.toLowerCase()));
  }

  // Skill database management
  addSkill(skill: string): void {
    this.skillDatabase.add(skill.toLowerCase());
  }

  removeSkill(skill: string): void {
    this.skillDatabase.delete(skill.toLowerCase());
  }

  getSkills(): string[] {
    return Array.from(this.skillDatabase);
  }

  // Legal terms management
  addLegalTerm(term: string): void {
    this.legalTerms.add(term.toLowerCase());
  }

  removeLegalTerm(term: string): void {
    this.legalTerms.delete(term.toLowerCase());
  }

  getLegalTerms(): string[] {
    return Array.from(this.legalTerms);
  }

  // Utility methods
  getSupportedFormats(): string[] {
    return this.config.supportedFormats || ['pdf', 'docx', 'txt', 'rtf'];
  }

  getMaxDocumentSize(): number {
    return this.config.maxDocumentSize || 10 * 1024 * 1024;
  }

  validateDocument(filename: string, size: number): { valid: boolean; error?: string } {
    const extension = filename.toLowerCase().split('.').pop();
    
    if (!extension || !this.getSupportedFormats().includes(extension)) {
      return {
        valid: false,
        error: `Unsupported file format. Supported formats: ${this.getSupportedFormats().join(', ')}`
      };
    }

    if (size > this.getMaxDocumentSize()) {
      return {
        valid: false,
        error: `File size exceeds maximum limit of ${this.getMaxDocumentSize() / (1024 * 1024)}MB`
      };
    }

    return { valid: true };
  }

  // Document type detection
  detectDocumentType(content: string): 'resume' | 'contract' | 'policy' | 'agreement' | 'other' {
    const contentLower = content.toLowerCase();
    
    // Resume indicators
    const resumeKeywords = ['experience', 'education', 'skills', 'objective', 'summary', 'employment'];
    const resumeScore = resumeKeywords.filter(keyword => contentLower.includes(keyword)).length;
    
    // Contract indicators
    const contractKeywords = ['agreement', 'contract', 'party', 'whereas', 'consideration', 'terms'];
    const contractScore = contractKeywords.filter(keyword => contentLower.includes(keyword)).length;
    
    // Policy indicators
    const policyKeywords = ['policy', 'procedure', 'guidelines', 'rules', 'regulations', 'compliance'];
    const policyScore = policyKeywords.filter(keyword => contentLower.includes(keyword)).length;
    
    if (resumeScore >= 3) return 'resume';
    if (contractScore >= 3) return 'contract';
    if (policyScore >= 3) return 'policy';
    
    return 'other';
  }

  // Text preprocessing
  preprocessText(text: string): string {
    return text
      .replace(/\s+/g, ' ') // Normalize whitespace
      .replace(/[^\w\s.,!?;:()\-]/g, '') // Remove special characters
      .trim();
  }

  // Confidence scoring
  calculateConfidence(matches: number, total: number): number {
    if (total === 0) return 0;
    return Math.min(matches / total, 1);
  }
}

// Export singleton instance
export const legalHRCore = new LegalHRCore();
