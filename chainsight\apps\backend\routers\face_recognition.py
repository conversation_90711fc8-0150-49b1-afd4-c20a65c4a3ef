from fastapi import APIRouter, Depends, HTTPException, UploadFile, File
from sqlalchemy.orm import Session
from pydantic import BaseModel
from typing import Dict, Any, List
import cv2
import numpy as np
import face_recognition
import io
from PIL import Image
import hashlib
from datetime import datetime

from db import get_db, FaceAnalysis, User
from routers.auth import get_current_user

router = APIRouter()

# Pydantic models
class FaceAnalysisResponse(BaseModel):
    detected_faces: int
    age_estimates: List[int]
    emotion_predictions: List[str]
    confidence_scores: Dict[str, float]
    face_locations: List[List[int]]
    analysis_id: int

class EmotionAnalysis(BaseModel):
    emotion: str
    confidence: float

class AgeGenderAnalysis(BaseModel):
    age: int
    gender: str
    confidence: float

@router.post("/analyze", response_model=FaceAnalysisResponse)
async def analyze_face(
    file: UploadFile = File(...),
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Analyze faces in an uploaded image"""
    try:
        # Read and process image
        contents = await file.read()
        image = Image.open(io.BytesIO(contents))
        
        # Convert to RGB if necessary
        if image.mode != 'RGB':
            image = image.convert('RGB')
        
        # Convert to numpy array for face_recognition
        image_array = np.array(image)
        
        # Generate image hash for tracking
        image_hash = hashlib.md5(contents).hexdigest()
        
        # Detect faces
        face_locations = face_recognition.face_locations(image_array)
        face_encodings = face_recognition.face_encodings(image_array, face_locations)
        
        if not face_locations:
            raise HTTPException(status_code=400, detail="No faces detected in the image")
        
        # Analyze each face
        age_estimates = []
        emotion_predictions = []
        confidence_scores = {}
        
        for i, (face_location, face_encoding) in enumerate(zip(face_locations, face_encodings)):
            # Extract face region
            top, right, bottom, left = face_location
            face_image = image_array[top:bottom, left:right]
            
            # Analyze age and emotion (using mock analysis for demo)
            age, age_confidence = estimate_age(face_image)
            emotion, emotion_confidence = predict_emotion(face_image)
            
            age_estimates.append(age)
            emotion_predictions.append(emotion)
            confidence_scores[f"face_{i}_age"] = age_confidence
            confidence_scores[f"face_{i}_emotion"] = emotion_confidence
        
        # Save analysis to database
        analysis = FaceAnalysis(
            user_id=current_user.id,
            image_hash=image_hash,
            detected_age=age_estimates[0] if age_estimates else None,
            detected_emotion=emotion_predictions[0] if emotion_predictions else None,
            confidence_scores=confidence_scores
        )
        db.add(analysis)
        db.commit()
        db.refresh(analysis)
        
        return FaceAnalysisResponse(
            detected_faces=len(face_locations),
            age_estimates=age_estimates,
            emotion_predictions=emotion_predictions,
            confidence_scores=confidence_scores,
            face_locations=[[int(coord) for coord in loc] for loc in face_locations],
            analysis_id=analysis.id
        )
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Face analysis failed: {str(e)}")

def estimate_age(face_image: np.ndarray) -> tuple:
    """Estimate age from face image (mock implementation)"""
    # In a real implementation, you would use a trained age estimation model
    # For demo purposes, we'll use image characteristics to estimate age
    
    # Convert to grayscale for analysis
    gray = cv2.cvtColor(face_image, cv2.COLOR_RGB2GRAY)
    
    # Calculate image statistics as age indicators
    mean_intensity = np.mean(gray)
    std_intensity = np.std(gray)
    
    # Mock age estimation based on image characteristics
    # This is a simplified approach for demonstration
    if mean_intensity > 150:  # Brighter faces might indicate younger age
        base_age = 25
    elif mean_intensity > 100:
        base_age = 35
    else:
        base_age = 45
    
    # Add some variation based on standard deviation
    age_variation = int(std_intensity / 10)
    estimated_age = max(18, min(80, base_age + age_variation))
    
    # Mock confidence score
    confidence = 0.75 + (std_intensity / 1000)  # Higher variation = higher confidence
    confidence = min(0.95, max(0.6, confidence))
    
    return estimated_age, confidence

def predict_emotion(face_image: np.ndarray) -> tuple:
    """Predict emotion from face image (mock implementation)"""
    # In a real implementation, you would use a trained emotion recognition model
    # For demo purposes, we'll use image characteristics to predict emotion
    
    # Convert to grayscale
    gray = cv2.cvtColor(face_image, cv2.COLOR_RGB2GRAY)
    
    # Calculate image features
    mean_intensity = np.mean(gray)
    contrast = np.std(gray)
    
    # Mock emotion prediction based on image characteristics
    emotions = ['happy', 'sad', 'angry', 'surprised', 'neutral', 'fear', 'disgust']
    
    # Simple heuristic based on brightness and contrast
    if mean_intensity > 140 and contrast > 30:
        emotion = 'happy'
        confidence = 0.85
    elif mean_intensity < 80:
        emotion = 'sad'
        confidence = 0.75
    elif contrast > 50:
        emotion = 'surprised'
        confidence = 0.80
    elif mean_intensity > 120:
        emotion = 'neutral'
        confidence = 0.70
    else:
        emotion = 'neutral'
        confidence = 0.65
    
    return emotion, confidence

@router.post("/compare-faces")
async def compare_faces(
    file1: UploadFile = File(...),
    file2: UploadFile = File(...),
    current_user: User = Depends(get_current_user)
):
    """Compare two faces for similarity"""
    try:
        # Read both images
        contents1 = await file1.read()
        contents2 = await file2.read()
        
        image1 = Image.open(io.BytesIO(contents1))
        image2 = Image.open(io.BytesIO(contents2))
        
        # Convert to RGB
        if image1.mode != 'RGB':
            image1 = image1.convert('RGB')
        if image2.mode != 'RGB':
            image2 = image2.convert('RGB')
        
        # Convert to numpy arrays
        image1_array = np.array(image1)
        image2_array = np.array(image2)
        
        # Get face encodings
        encodings1 = face_recognition.face_encodings(image1_array)
        encodings2 = face_recognition.face_encodings(image2_array)
        
        if not encodings1 or not encodings2:
            raise HTTPException(status_code=400, detail="Could not detect faces in one or both images")
        
        # Compare faces
        distances = face_recognition.face_distance(encodings1, encodings2[0])
        similarity = 1 - distances[0]  # Convert distance to similarity
        
        # Determine if it's a match (threshold of 0.6)
        is_match = similarity > 0.4
        
        return {
            "similarity_score": float(similarity),
            "is_match": is_match,
            "confidence": float(similarity),
            "threshold": 0.4,
            "faces_detected": {
                "image1": len(encodings1),
                "image2": len(encodings2)
            }
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Face comparison failed: {str(e)}")

@router.get("/analysis-history")
async def get_analysis_history(
    limit: int = 50,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Get face analysis history for the current user"""
    analyses = db.query(FaceAnalysis).filter(
        FaceAnalysis.user_id == current_user.id
    ).order_by(FaceAnalysis.analysis_timestamp.desc()).limit(limit).all()
    
    return {
        "analyses": [
            {
                "id": analysis.id,
                "detected_age": analysis.detected_age,
                "detected_emotion": analysis.detected_emotion,
                "confidence_scores": analysis.confidence_scores,
                "timestamp": analysis.analysis_timestamp
            }
            for analysis in analyses
        ]
    }

@router.get("/statistics")
async def get_face_statistics(
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Get face analysis statistics for the current user"""
    analyses = db.query(FaceAnalysis).filter(
        FaceAnalysis.user_id == current_user.id
    ).all()
    
    if not analyses:
        return {"message": "No analysis data available"}
    
    # Calculate statistics
    ages = [a.detected_age for a in analyses if a.detected_age]
    emotions = [a.detected_emotion for a in analyses if a.detected_emotion]
    
    # Age statistics
    age_stats = {}
    if ages:
        age_stats = {
            "average_age": sum(ages) / len(ages),
            "min_age": min(ages),
            "max_age": max(ages),
            "total_analyses": len(ages)
        }
    
    # Emotion distribution
    emotion_counts = {}
    for emotion in emotions:
        emotion_counts[emotion] = emotion_counts.get(emotion, 0) + 1
    
    return {
        "total_analyses": len(analyses),
        "age_statistics": age_stats,
        "emotion_distribution": emotion_counts,
        "most_common_emotion": max(emotion_counts, key=emotion_counts.get) if emotion_counts else None
    }

@router.delete("/analysis/{analysis_id}")
async def delete_analysis(
    analysis_id: int,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Delete a face analysis record"""
    analysis = db.query(FaceAnalysis).filter(
        FaceAnalysis.id == analysis_id,
        FaceAnalysis.user_id == current_user.id
    ).first()
    
    if not analysis:
        raise HTTPException(status_code=404, detail="Analysis not found")
    
    db.delete(analysis)
    db.commit()
    
    return {"message": "Analysis deleted successfully"}

@router.post("/batch-analyze")
async def batch_analyze_faces(
    files: List[UploadFile] = File(...),
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Analyze multiple images in batch"""
    if len(files) > 10:
        raise HTTPException(status_code=400, detail="Maximum 10 files allowed per batch")
    
    results = []
    
    for i, file in enumerate(files):
        try:
            # Process each file
            contents = await file.read()
            image = Image.open(io.BytesIO(contents))
            
            if image.mode != 'RGB':
                image = image.convert('RGB')
            
            image_array = np.array(image)
            image_hash = hashlib.md5(contents).hexdigest()
            
            # Detect faces
            face_locations = face_recognition.face_locations(image_array)
            
            if face_locations:
                # Analyze first face
                top, right, bottom, left = face_locations[0]
                face_image = image_array[top:bottom, left:right]
                
                age, age_conf = estimate_age(face_image)
                emotion, emotion_conf = predict_emotion(face_image)
                
                # Save to database
                analysis = FaceAnalysis(
                    user_id=current_user.id,
                    image_hash=image_hash,
                    detected_age=age,
                    detected_emotion=emotion,
                    confidence_scores={"age": age_conf, "emotion": emotion_conf}
                )
                db.add(analysis)
                
                results.append({
                    "file_index": i,
                    "filename": file.filename,
                    "faces_detected": len(face_locations),
                    "age": age,
                    "emotion": emotion,
                    "success": True
                })
            else:
                results.append({
                    "file_index": i,
                    "filename": file.filename,
                    "faces_detected": 0,
                    "success": False,
                    "error": "No faces detected"
                })
                
        except Exception as e:
            results.append({
                "file_index": i,
                "filename": file.filename,
                "success": False,
                "error": str(e)
            })
    
    db.commit()
    
    return {
        "total_files": len(files),
        "successful_analyses": len([r for r in results if r["success"]]),
        "results": results
    }
