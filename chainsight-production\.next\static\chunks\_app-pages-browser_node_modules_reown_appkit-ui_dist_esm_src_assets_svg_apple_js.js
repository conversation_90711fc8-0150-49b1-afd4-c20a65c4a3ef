"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_reown_appkit-ui_dist_esm_src_assets_svg_apple_js"],{

/***/ "(app-pages-browser)/./node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/apple.js":
/*!************************************************************************!*\
  !*** ./node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/apple.js ***!
  \************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   appleSvg: () => (/* binding */ appleSvg)\n/* harmony export */ });\n/* harmony import */ var lit__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! lit */ \"(app-pages-browser)/./node_modules/lit/index.js\");\n\nconst appleSvg = (0,lit__WEBPACK_IMPORTED_MODULE_0__.svg) `<svg fill=\"none\" viewBox=\"0 0 40 40\">\n  <g clip-path=\"url(#a)\">\n    <g clip-path=\"url(#b)\">\n      <circle cx=\"20\" cy=\"19.89\" r=\"20\" fill=\"#000\" />\n      <g clip-path=\"url(#c)\">\n        <path\n          fill=\"#fff\"\n          d=\"M28.77 23.3c-.69 1.99-2.75 5.52-4.87 5.56-1.4.03-1.86-.84-3.46-.84-1.61 0-2.12.81-3.45.86-2.25.1-5.72-5.1-5.72-9.62 0-4.15 2.9-6.2 5.42-6.25 1.36-.02 2.64.92 ********** 0 2.38-1.13 4.02-.97.68.03 2.6.28 3.84 2.08-3.27 2.14-2.76 6.61.75 8.25ZM24.2 7.88c-2.47.1-4.49 2.69-4.2 4.84 2.28.17 4.47-2.39 4.2-4.84Z\"\n        />\n      </g>\n    </g>\n  </g>\n  <defs>\n    <clipPath id=\"a\"><rect width=\"40\" height=\"40\" fill=\"#fff\" rx=\"20\" /></clipPath>\n    <clipPath id=\"b\"><path fill=\"#fff\" d=\"M0 0h40v40H0z\" /></clipPath>\n    <clipPath id=\"c\"><path fill=\"#fff\" d=\"M8 7.89h24v24H8z\" /></clipPath>\n  </defs>\n</svg>`;\n//# sourceMappingURL=apple.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9AcmVvd24vYXBwa2l0LXVpL2Rpc3QvZXNtL3NyYy9hc3NldHMvc3ZnL2FwcGxlLmpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQTBCO0FBQ25CLGlCQUFpQix3Q0FBRztBQUMzQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIkY6XFxBSSBjaGFpblxcY2hhaW5zaWdodC1wcm9kdWN0aW9uXFxub2RlX21vZHVsZXNcXEByZW93blxcYXBwa2l0LXVpXFxkaXN0XFxlc21cXHNyY1xcYXNzZXRzXFxzdmdcXGFwcGxlLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IHN2ZyB9IGZyb20gJ2xpdCc7XG5leHBvcnQgY29uc3QgYXBwbGVTdmcgPSBzdmcgYDxzdmcgZmlsbD1cIm5vbmVcIiB2aWV3Qm94PVwiMCAwIDQwIDQwXCI+XG4gIDxnIGNsaXAtcGF0aD1cInVybCgjYSlcIj5cbiAgICA8ZyBjbGlwLXBhdGg9XCJ1cmwoI2IpXCI+XG4gICAgICA8Y2lyY2xlIGN4PVwiMjBcIiBjeT1cIjE5Ljg5XCIgcj1cIjIwXCIgZmlsbD1cIiMwMDBcIiAvPlxuICAgICAgPGcgY2xpcC1wYXRoPVwidXJsKCNjKVwiPlxuICAgICAgICA8cGF0aFxuICAgICAgICAgIGZpbGw9XCIjZmZmXCJcbiAgICAgICAgICBkPVwiTTI4Ljc3IDIzLjNjLS42OSAxLjk5LTIuNzUgNS41Mi00Ljg3IDUuNTYtMS40LjAzLTEuODYtLjg0LTMuNDYtLjg0LTEuNjEgMC0yLjEyLjgxLTMuNDUuODYtMi4yNS4xLTUuNzItNS4xLTUuNzItOS42MiAwLTQuMTUgMi45LTYuMiA1LjQyLTYuMjUgMS4zNi0uMDIgMi42NC45MiAzLjQ3LjkyLjgzIDAgMi4zOC0xLjEzIDQuMDItLjk3LjY4LjAzIDIuNi4yOCAzLjg0IDIuMDgtMy4yNyAyLjE0LTIuNzYgNi42MS43NSA4LjI1Wk0yNC4yIDcuODhjLTIuNDcuMS00LjQ5IDIuNjktNC4yIDQuODQgMi4yOC4xNyA0LjQ3LTIuMzkgNC4yLTQuODRaXCJcbiAgICAgICAgLz5cbiAgICAgIDwvZz5cbiAgICA8L2c+XG4gIDwvZz5cbiAgPGRlZnM+XG4gICAgPGNsaXBQYXRoIGlkPVwiYVwiPjxyZWN0IHdpZHRoPVwiNDBcIiBoZWlnaHQ9XCI0MFwiIGZpbGw9XCIjZmZmXCIgcng9XCIyMFwiIC8+PC9jbGlwUGF0aD5cbiAgICA8Y2xpcFBhdGggaWQ9XCJiXCI+PHBhdGggZmlsbD1cIiNmZmZcIiBkPVwiTTAgMGg0MHY0MEgwelwiIC8+PC9jbGlwUGF0aD5cbiAgICA8Y2xpcFBhdGggaWQ9XCJjXCI+PHBhdGggZmlsbD1cIiNmZmZcIiBkPVwiTTggNy44OWgyNHYyNEg4elwiIC8+PC9jbGlwUGF0aD5cbiAgPC9kZWZzPlxuPC9zdmc+YDtcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPWFwcGxlLmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/apple.js\n"));

/***/ })

}]);