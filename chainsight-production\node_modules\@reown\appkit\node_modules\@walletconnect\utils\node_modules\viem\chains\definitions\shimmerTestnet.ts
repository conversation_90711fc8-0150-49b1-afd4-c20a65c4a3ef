import { define<PERSON>hain } from '../../utils/chain/defineChain.js'

export const shimmerTestnet = /*#__PURE__*/ defineChain({
  id: 1073,
  name: 'Shimmer Testnet',
  network: 'shimmer-testnet',
  nativeCurrency: {
    decimals: 18,
    name: '<PERSON>mmer',
    symbol: 'SMR',
  },
  rpcUrls: {
    default: {
      http: ['https://json-rpc.evm.testnet.shimmer.network'],
    },
  },
  blockExplorers: {
    default: {
      name: 'Shimmer Network Explorer',
      url: 'https://explorer.evm.testnet.shimmer.network',
      apiUrl: 'https://explorer.evm.testnet.shimmer.network/api',
    },
  },
  testnet: true,
})
