import type { Address } from 'abitype'

import type {
  TestClient,
  TestClientMode,
} from '../../clients/createTestClient.js'
import type { Transport } from '../../clients/transports/createTransport.js'
import type { ErrorType } from '../../errors/utils.js'
import type { Account } from '../../types/account.js'
import type { Chain } from '../../types/chain.js'
import type { Hex } from '../../types/misc.js'
import type { RequestErrorType } from '../../utils/buildRequest.js'

export type SetCodeParameters = {
  /** The account address. */
  address: Address
  /** The bytecode to set */
  bytecode: Hex
}

export type SetCodeErrorType = RequestErrorType | ErrorType

/**
 * Modifies the bytecode stored at an account's address.
 *
 * - Docs: https://viem.sh/docs/actions/test/setCode
 *
 * @param client - Client to use
 * @param parameters – {@link SetCodeParameters}
 *
 * @example
 * import { createTestClient, http } from 'viem'
 * import { foundry } from 'viem/chains'
 * import { setCode } from 'viem/test'
 *
 * const client = createTestClient({
 *   mode: 'anvil',
 *   chain: 'foundry',
 *   transport: http(),
 * })
 * await setCode(client, {
 *   address: '******************************************',
 *   bytecode: '0x60806040526000600355600019600955600c80546001600160a01b031916737a250d5630b4cf539739df…',
 * })
 */
export async function setCode<
  chain extends Chain | undefined,
  account extends Account | undefined,
>(
  client: TestClient<TestClientMode, Transport, chain, account, false>,
  { address, bytecode }: SetCodeParameters,
) {
  if (client.mode === 'ganache')
    await client.request({
      method: 'evm_setAccountCode',
      params: [address, bytecode],
    })
  else
    await client.request({
      method: `${client.mode}_setCode`,
      params: [address, bytecode],
    })
}
