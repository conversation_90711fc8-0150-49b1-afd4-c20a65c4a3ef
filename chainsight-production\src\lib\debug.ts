// Debug and Error Handling Utilities for Chainsight Platform

interface DebugConfig {
  enabled: boolean;
  level: 'error' | 'warn' | 'info' | 'debug';
  prefix: string;
}

class ChainSightDebugger {
  private config: DebugConfig;
  private errors: Array<{ timestamp: Date; error: any; context?: string }> = [];

  constructor() {
    this.config = {
      enabled: process.env.NODE_ENV === 'development',
      level: 'debug',
      prefix: '🔧 Chainsight Debug',
    };

    // Initialize error tracking
    this.initializeErrorTracking();
  }

  private initializeErrorTracking() {
    // Global error handler
    if (typeof window !== 'undefined') {
      window.addEventListener('error', (event) => {
        this.logError('Global Error', event.error, {
          filename: event.filename,
          lineno: event.lineno,
          colno: event.colno,
        });
      });

      window.addEventListener('unhandledrejection', (event) => {
        this.logError('Unhandled Promise Rejection', event.reason);
      });

      // Console override for better tracking
      const originalConsoleError = console.error;
      console.error = (...args) => {
        this.logError('Console Error', args);
        originalConsoleError.apply(console, args);
      };
    }
  }

  logError(context: string, error: any, metadata?: any) {
    const errorEntry = {
      timestamp: new Date(),
      error,
      context,
      metadata,
    };

    this.errors.push(errorEntry);

    if (this.config.enabled) {
      console.group(`🚨 ${this.config.prefix} - ${context}`);
      console.error('Error:', error);
      if (metadata) {
        console.error('Metadata:', metadata);
      }
      console.error('Timestamp:', errorEntry.timestamp.toISOString());
      console.groupEnd();
    }

    // Keep only last 50 errors
    if (this.errors.length > 50) {
      this.errors = this.errors.slice(-50);
    }
  }

  logInfo(message: string, data?: any) {
    if (this.config.enabled && ['info', 'debug'].includes(this.config.level)) {
      console.log(`ℹ️ ${this.config.prefix} - ${message}`, data || '');
    }
  }

  logWarning(message: string, data?: any) {
    if (this.config.enabled && ['warn', 'info', 'debug'].includes(this.config.level)) {
      console.warn(`⚠️ ${this.config.prefix} - ${message}`, data || '');
    }
  }

  logDebug(message: string, data?: any) {
    if (this.config.enabled && this.config.level === 'debug') {
      console.debug(`🐛 ${this.config.prefix} - ${message}`, data || '');
    }
  }

  getErrorReport() {
    return {
      totalErrors: this.errors.length,
      recentErrors: this.errors.slice(-10),
      errorsByType: this.groupErrorsByType(),
      systemInfo: this.getSystemInfo(),
    };
  }

  private groupErrorsByType() {
    const grouped: Record<string, number> = {};
    this.errors.forEach(({ context }) => {
      grouped[context] = (grouped[context] || 0) + 1;
    });
    return grouped;
  }

  private getSystemInfo() {
    if (typeof window === 'undefined') return {};

    return {
      userAgent: navigator.userAgent,
      platform: navigator.platform,
      language: navigator.language,
      cookieEnabled: navigator.cookieEnabled,
      onLine: navigator.onLine,
      screen: {
        width: screen.width,
        height: screen.height,
        colorDepth: screen.colorDepth,
      },
      viewport: {
        width: window.innerWidth,
        height: window.innerHeight,
      },
      url: window.location.href,
      timestamp: new Date().toISOString(),
    };
  }

  clearErrors() {
    this.errors = [];
    this.logInfo('Error log cleared');
  }

  exportErrorLog() {
    const report = this.getErrorReport();
    const blob = new Blob([JSON.stringify(report, null, 2)], {
      type: 'application/json',
    });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `chainsight-error-log-${Date.now()}.json`;
    a.click();
    URL.revokeObjectURL(url);
  }
}

// Create global debugger instance
export const debugger = new ChainSightDebugger();

// Utility functions for common debugging scenarios
export const debugUtils = {
  // Component lifecycle debugging
  componentMount: (componentName: string) => {
    debugger.logDebug(`Component Mounted: ${componentName}`);
  },

  componentUnmount: (componentName: string) => {
    debugger.logDebug(`Component Unmounted: ${componentName}`);
  },

  // API call debugging
  apiCall: (endpoint: string, method: string, data?: any) => {
    debugger.logDebug(`API Call: ${method} ${endpoint}`, data);
  },

  apiResponse: (endpoint: string, status: number, data?: any) => {
    debugger.logDebug(`API Response: ${endpoint} (${status})`, data);
  },

  apiError: (endpoint: string, error: any) => {
    debugger.logError(`API Error: ${endpoint}`, error);
  },

  // State management debugging
  stateChange: (storeName: string, action: string, newState?: any) => {
    debugger.logDebug(`State Change: ${storeName}.${action}`, newState);
  },

  // WebSocket debugging
  wsConnect: (url: string) => {
    debugger.logInfo(`WebSocket Connected: ${url}`);
  },

  wsDisconnect: (url: string, reason?: string) => {
    debugger.logWarning(`WebSocket Disconnected: ${url}`, reason);
  },

  wsError: (url: string, error: any) => {
    debugger.logError(`WebSocket Error: ${url}`, error);
  },

  // Performance debugging
  performanceMark: (name: string) => {
    if (typeof window !== 'undefined' && window.performance) {
      performance.mark(name);
      debugger.logDebug(`Performance Mark: ${name}`);
    }
  },

  performanceMeasure: (name: string, startMark: string, endMark: string) => {
    if (typeof window !== 'undefined' && window.performance) {
      try {
        performance.measure(name, startMark, endMark);
        const measure = performance.getEntriesByName(name)[0];
        debugger.logDebug(`Performance Measure: ${name}`, `${measure.duration.toFixed(2)}ms`);
      } catch (error) {
        debugger.logError(`Performance Measure Error: ${name}`, error);
      }
    }
  },
};

// React Hook for debugging
export function useDebug(componentName: string) {
  const logMount = () => debugUtils.componentMount(componentName);
  const logUnmount = () => debugUtils.componentUnmount(componentName);
  const logError = (error: any, context?: string) => 
    debugger.logError(`${componentName}${context ? ` - ${context}` : ''}`, error);
  const logInfo = (message: string, data?: any) => 
    debugger.logInfo(`${componentName} - ${message}`, data);

  return {
    logMount,
    logUnmount,
    logError,
    logInfo,
    debugger,
  };
}

// Export for global access
if (typeof window !== 'undefined') {
  (window as any).chainSightDebugger = debugger;
  (window as any).chainSightDebugUtils = debugUtils;
}
