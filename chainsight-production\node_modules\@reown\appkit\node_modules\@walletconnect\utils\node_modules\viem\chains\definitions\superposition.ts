import { define<PERSON><PERSON><PERSON> } from '../../utils/chain/defineChain.js'

export const superposition = /*#__PURE__*/ defineChain({
  id: 55244,
  name: 'Superposition',
  nativeCurrency: { name: '<PERSON><PERSON>', symbol: 'ETH', decimals: 18 },
  rpcUrls: {
    default: { http: ['https://rpc.superposition.so'] },
  },
  blockExplorers: {
    default: {
      name: 'Superposition Explorer',
      url: 'https://explorer.superposition.so',
    },
  },
  contracts: {
    multicall3: {
      address: '******************************************',
      blockCreated: 39,
    },
  },
  testnet: false,
})
