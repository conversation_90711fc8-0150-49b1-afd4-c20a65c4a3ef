{"name": "@chainsight/web3-dapp-core", "version": "1.0.0", "description": "Web3 DApp core functionality plugin for blockchain interactions", "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"build": "tsc", "dev": "tsc --watch", "test": "jest", "lint": "eslint src/**/*.ts", "clean": "rm -rf dist"}, "dependencies": {"@chainsight/crypto-engine": "workspace:*", "ethers": "^6.7.1", "web3": "^4.2.0", "wagmi": "^1.4.3", "@wagmi/core": "^1.4.3", "@web3modal/wagmi": "^3.0.0", "viem": "^1.10.9", "@rainbow-me/rainbowkit": "^1.0.12"}, "devDependencies": {"@types/node": "^20.5.0", "typescript": "^5.1.6", "jest": "^29.6.2", "@types/jest": "^29.5.4", "eslint": "^8.47.0", "@typescript-eslint/eslint-plugin": "^6.4.0", "@typescript-eslint/parser": "^6.4.0"}, "keywords": ["web3", "dapp", "blockchain", "ethereum", "defi", "wallet", "chainsight"], "author": "Connectouch", "license": "MIT"}