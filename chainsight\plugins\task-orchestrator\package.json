{"name": "@chainsight/task-orchestrator", "version": "1.0.0", "description": "AI task orchestration and workflow management plugin", "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"build": "tsc", "dev": "tsc --watch", "test": "jest", "lint": "eslint src/**/*.ts", "clean": "rm -rf dist"}, "dependencies": {"@chainsight/ai-core": "workspace:*", "@chainsight/crypto-engine": "workspace:*", "@chainsight/finance-analyzer": "workspace:*", "bull": "^4.11.3", "redis": "^4.6.8", "cron": "^2.4.4", "uuid": "^9.0.0"}, "devDependencies": {"@types/node": "^20.5.0", "@types/uuid": "^9.0.2", "@types/bull": "^4.10.0", "@types/cron": "^2.0.1", "typescript": "^5.1.6", "jest": "^29.6.2", "@types/jest": "^29.5.4", "eslint": "^8.47.0", "@typescript-eslint/eslint-plugin": "^6.4.0", "@typescript-eslint/parser": "^6.4.0"}, "keywords": ["task-orchestration", "workflow", "ai-automation", "job-queue", "scheduling", "chainsight"], "author": "Connectouch", "license": "MIT"}