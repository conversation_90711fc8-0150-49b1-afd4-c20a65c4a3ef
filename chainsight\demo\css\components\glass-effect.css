/* ==================== GLASS EFFECT COMPONENT ====================
   Glass morphism effects and related components
   ============================================================= */

.glass-effect {
    background: var(--glass-bg);
    backdrop-filter: var(--glass-backdrop);
    -webkit-backdrop-filter: var(--glass-backdrop);
    border: 1px solid var(--glass-border);
    box-shadow: var(--glass-shadow);
    transition: all var(--transition-normal);
}

.glass-effect:hover {
    background: rgba(255, 255, 255, 0.15);
    border-color: rgba(255, 255, 255, 0.3);
    box-shadow: 
        var(--glass-shadow),
        0 0 20px rgba(0, 212, 255, 0.3);
    transform: translateY(-2px);
}

/* Glass Card Variants */
.glass-card {
    @extend .glass-effect;
    border-radius: var(--radius-xl);
    padding: var(--space-lg);
    position: relative;
    overflow: hidden;
}

.glass-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(
        90deg,
        transparent,
        rgba(255, 255, 255, 0.4),
        transparent
    );
}

.glass-card-sm {
    @extend .glass-card;
    padding: var(--space-md);
    border-radius: var(--radius-lg);
}

.glass-card-lg {
    @extend .glass-card;
    padding: var(--space-2xl);
    border-radius: var(--radius-2xl);
}

/* Glass Button */
.glass-btn {
    @extend .glass-effect;
    border-radius: var(--radius-lg);
    padding: var(--space-sm) var(--space-lg);
    font-weight: var(--font-medium);
    text-align: center;
    cursor: pointer;
    user-select: none;
    transition: all var(--transition-fast);
    position: relative;
    overflow: hidden;
}

.glass-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(
        90deg,
        transparent,
        rgba(255, 255, 255, 0.2),
        transparent
    );
    transition: left var(--transition-normal);
}

.glass-btn:hover::before {
    left: 100%;
}

.glass-btn:active {
    transform: scale(0.98);
}

.glass-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    pointer-events: none;
}

/* Glass Input */
.glass-input {
    @extend .glass-effect;
    border-radius: var(--radius-lg);
    padding: var(--space-sm) var(--space-md);
    background: rgba(255, 255, 255, 0.05);
    color: var(--text-primary);
    font-size: var(--text-base);
    width: 100%;
    transition: all var(--transition-fast);
}

.glass-input::placeholder {
    color: var(--text-tertiary);
}

.glass-input:focus {
    background: rgba(255, 255, 255, 0.1);
    border-color: var(--neon-blue);
    box-shadow: 
        var(--glass-shadow),
        0 0 0 2px rgba(0, 212, 255, 0.3);
    outline: none;
}

/* Glass Modal */
.glass-modal {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.8);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: var(--z-modal);
    opacity: 0;
    visibility: hidden;
    transition: all var(--transition-normal);
}

.glass-modal.active {
    opacity: 1;
    visibility: visible;
}

.glass-modal-content {
    @extend .glass-effect;
    border-radius: var(--radius-2xl);
    padding: var(--space-2xl);
    max-width: 90vw;
    max-height: 90vh;
    overflow-y: auto;
    transform: scale(0.9);
    transition: transform var(--transition-normal);
}

.glass-modal.active .glass-modal-content {
    transform: scale(1);
}

/* Glass Tooltip */
.glass-tooltip {
    @extend .glass-effect;
    border-radius: var(--radius-md);
    padding: var(--space-xs) var(--space-sm);
    font-size: var(--text-sm);
    position: absolute;
    z-index: var(--z-tooltip);
    opacity: 0;
    visibility: hidden;
    transform: translateY(5px);
    transition: all var(--transition-fast);
    pointer-events: none;
    white-space: nowrap;
}

.glass-tooltip.show {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

/* Glass Navigation */
.glass-nav {
    @extend .glass-effect;
    border-radius: var(--radius-2xl);
    padding: var(--space-sm);
    display: flex;
    align-items: center;
    gap: var(--space-sm);
}

.glass-nav-item {
    padding: var(--space-sm) var(--space-md);
    border-radius: var(--radius-lg);
    transition: all var(--transition-fast);
    cursor: pointer;
    position: relative;
}

.glass-nav-item:hover {
    background: rgba(255, 255, 255, 0.1);
}

.glass-nav-item.active {
    background: var(--neon-blue);
    color: var(--luxury-black);
    box-shadow: 0 0 20px rgba(0, 212, 255, 0.5);
}

/* Glass Progress Bar */
.glass-progress {
    @extend .glass-effect;
    border-radius: var(--radius-full);
    height: 8px;
    overflow: hidden;
    position: relative;
}

.glass-progress-bar {
    height: 100%;
    background: linear-gradient(90deg, var(--neon-blue), var(--neon-cyan));
    border-radius: var(--radius-full);
    transition: width var(--transition-normal);
    position: relative;
}

.glass-progress-bar::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(
        90deg,
        transparent,
        rgba(255, 255, 255, 0.3),
        transparent
    );
    animation: shimmer 2s infinite;
}

@keyframes shimmer {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
}

/* Glass Badge */
.glass-badge {
    @extend .glass-effect;
    border-radius: var(--radius-full);
    padding: var(--space-xs) var(--space-sm);
    font-size: var(--text-xs);
    font-weight: var(--font-medium);
    display: inline-flex;
    align-items: center;
    gap: var(--space-xs);
}

.glass-badge-success {
    @extend .glass-badge;
    background: rgba(16, 185, 129, 0.2);
    border-color: var(--color-success);
    color: var(--color-success);
}

.glass-badge-warning {
    @extend .glass-badge;
    background: rgba(245, 158, 11, 0.2);
    border-color: var(--color-warning);
    color: var(--color-warning);
}

.glass-badge-error {
    @extend .glass-badge;
    background: rgba(239, 68, 68, 0.2);
    border-color: var(--color-error);
    color: var(--color-error);
}

/* Glass Loading Spinner */
.glass-spinner {
    @extend .glass-effect;
    border-radius: 50%;
    width: 40px;
    height: 40px;
    border: 3px solid transparent;
    border-top-color: var(--neon-blue);
    animation: spin 1s linear infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* Responsive Glass Effects */
@media (max-width: 768px) {
    .glass-card {
        padding: var(--space-md);
        border-radius: var(--radius-lg);
    }
    
    .glass-modal-content {
        padding: var(--space-lg);
        margin: var(--space-md);
    }
}
