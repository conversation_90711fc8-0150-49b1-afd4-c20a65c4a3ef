'use client';

import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { Activity, Wifi, WifiOff, Settings, BarChart3, MessageSquare } from 'lucide-react';
import MarketDataWidget from './MarketDataWidget';
import LiveChatInterface from './LiveChatInterface';
import NotificationCenter from './NotificationCenter';
import { useWebSocket } from '@/hooks/useWebSocket';

interface RealTimeDashboardProps {
  layout?: 'grid' | 'sidebar' | 'fullscreen';
  showChat?: boolean;
  showMarketData?: boolean;
  showNotifications?: boolean;
  autoRefresh?: boolean;
  refreshInterval?: number;
}

const RealTimeDashboard: React.FC<RealTimeDashboardProps> = ({
  layout = 'grid',
  showChat = true,
  showMarketData = true,
  showNotifications = true,
  autoRefresh = true,
  refreshInterval = 30000
}) => {
  const { connected, connecting, error } = useWebSocket();
  const [activeTab, setActiveTab] = useState<'market' | 'chat' | 'analytics'>('market');
  const [isSettingsOpen, setIsSettingsOpen] = useState(false);
  const [dashboardStats, setDashboardStats] = useState({
    totalConnections: 0,
    messagesProcessed: 0,
    marketUpdates: 0,
    systemHealth: 'healthy'
  });

  // Simulate dashboard statistics updates
  useEffect(() => {
    if (!connected) return;

    const interval = setInterval(() => {
      setDashboardStats(prev => ({
        ...prev,
        totalConnections: Math.floor(Math.random() * 1000) + 500,
        messagesProcessed: prev.messagesProcessed + Math.floor(Math.random() * 10),
        marketUpdates: prev.marketUpdates + Math.floor(Math.random() * 5),
        systemHealth: Math.random() > 0.1 ? 'healthy' : 'warning'
      }));
    }, refreshInterval);

    return () => clearInterval(interval);
  }, [connected, refreshInterval]);

  const getConnectionStatus = () => {
    if (connecting) return { icon: Activity, color: 'text-yellow-400', text: 'Connecting...' };
    if (connected) return { icon: Wifi, color: 'text-green-400', text: 'Connected' };
    return { icon: WifiOff, color: 'text-red-400', text: 'Disconnected' };
  };

  const connectionStatus = getConnectionStatus();

  const renderGridLayout = () => (
    <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 h-full">
      {/* Market Data Column */}
      {showMarketData && (
        <div className="lg:col-span-2 space-y-6">
          <MarketDataWidget 
            symbols={['BTC', 'ETH', 'USDT', 'BNB', 'ADA', 'SOL', 'MATIC', 'DOT']}
            showVolume={true}
            showMarketCap={true}
          />
          
          {/* Real-time Analytics */}
          <div className="bg-gradient-to-br from-gray-900 to-black border border-yellow-400/20 rounded-xl p-6">
            <h3 className="text-lg font-semibold text-white mb-4 flex items-center space-x-2">
              <BarChart3 className="w-5 h-5 text-yellow-400" />
              <span>Real-time Analytics</span>
            </h3>
            
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <div className="bg-gray-800/50 rounded-lg p-4">
                <div className="text-sm text-gray-400 mb-1">Active Connections</div>
                <div className="text-2xl font-bold text-white">
                  {dashboardStats.totalConnections.toLocaleString()}
                </div>
              </div>
              
              <div className="bg-gray-800/50 rounded-lg p-4">
                <div className="text-sm text-gray-400 mb-1">Messages Processed</div>
                <div className="text-2xl font-bold text-white">
                  {dashboardStats.messagesProcessed.toLocaleString()}
                </div>
              </div>
              
              <div className="bg-gray-800/50 rounded-lg p-4">
                <div className="text-sm text-gray-400 mb-1">Market Updates</div>
                <div className="text-2xl font-bold text-white">
                  {dashboardStats.marketUpdates.toLocaleString()}
                </div>
              </div>
              
              <div className="bg-gray-800/50 rounded-lg p-4">
                <div className="text-sm text-gray-400 mb-1">System Health</div>
                <div className={`text-lg font-semibold ${
                  dashboardStats.systemHealth === 'healthy' ? 'text-green-400' : 'text-yellow-400'
                }`}>
                  {dashboardStats.systemHealth.toUpperCase()}
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Chat Column */}
      {showChat && (
        <div className="lg:col-span-1">
          <LiveChatInterface 
            sessionId="dashboard-chat"
            placeholder="Ask about market trends, portfolio analysis, or any crypto questions..."
            maxHeight={600}
            showConfidence={true}
            minimizable={false}
            autoFocus={false}
          />
        </div>
      )}
    </div>
  );

  const renderSidebarLayout = () => (
    <div className="flex h-full space-x-6">
      {/* Main Content */}
      <div className="flex-1 space-y-6">
        {/* Tab Navigation */}
        <div className="flex space-x-4 border-b border-gray-700">
          {showMarketData && (
            <button
              onClick={() => setActiveTab('market')}
              className={`pb-2 px-1 text-sm font-medium transition-colors ${
                activeTab === 'market'
                  ? 'text-yellow-400 border-b-2 border-yellow-400'
                  : 'text-gray-400 hover:text-white'
              }`}
            >
              Market Data
            </button>
          )}
          <button
            onClick={() => setActiveTab('analytics')}
            className={`pb-2 px-1 text-sm font-medium transition-colors ${
              activeTab === 'analytics'
                ? 'text-yellow-400 border-b-2 border-yellow-400'
                : 'text-gray-400 hover:text-white'
            }`}
          >
            Analytics
          </button>
        </div>

        {/* Tab Content */}
        <div className="flex-1">
          {activeTab === 'market' && showMarketData && (
            <MarketDataWidget 
              symbols={['BTC', 'ETH', 'USDT', 'BNB', 'ADA', 'SOL']}
              showVolume={true}
              showMarketCap={true}
            />
          )}
          
          {activeTab === 'analytics' && (
            <div className="bg-gradient-to-br from-gray-900 to-black border border-yellow-400/20 rounded-xl p-6">
              <h3 className="text-lg font-semibold text-white mb-6">System Analytics</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-4">
                  <h4 className="text-md font-medium text-gray-300">Performance Metrics</h4>
                  <div className="space-y-3">
                    <div className="flex justify-between">
                      <span className="text-gray-400">Response Time</span>
                      <span className="text-green-400">45ms</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-400">Uptime</span>
                      <span className="text-green-400">99.9%</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-400">Error Rate</span>
                      <span className="text-green-400">0.1%</span>
                    </div>
                  </div>
                </div>
                
                <div className="space-y-4">
                  <h4 className="text-md font-medium text-gray-300">Usage Statistics</h4>
                  <div className="space-y-3">
                    <div className="flex justify-between">
                      <span className="text-gray-400">API Calls/min</span>
                      <span className="text-white">1,247</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-400">Active Users</span>
                      <span className="text-white">342</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-400">Data Processed</span>
                      <span className="text-white">2.4 GB</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Sidebar */}
      {showChat && (
        <div className="w-96">
          <LiveChatInterface 
            sessionId="sidebar-chat"
            maxHeight={700}
            minimizable={false}
          />
        </div>
      )}
    </div>
  );

  return (
    <div className="min-h-screen bg-black text-white p-6">
      {/* Header */}
      <div className="flex items-center justify-between mb-8">
        <div>
          <h1 className="text-3xl font-bold bg-gradient-to-r from-yellow-400 to-yellow-600 bg-clip-text text-transparent">
            Chainsight Real-Time Dashboard
          </h1>
          <p className="text-gray-400 mt-1">Live market data, AI assistance, and system monitoring</p>
        </div>

        <div className="flex items-center space-x-4">
          {/* Connection Status */}
          <div className="flex items-center space-x-2">
            <connectionStatus.icon className={`w-5 h-5 ${connectionStatus.color}`} />
            <span className={`text-sm ${connectionStatus.color}`}>
              {connectionStatus.text}
            </span>
          </div>

          {/* Settings */}
          <button
            onClick={() => setIsSettingsOpen(!isSettingsOpen)}
            className="p-2 rounded-lg bg-gray-800 hover:bg-gray-700 transition-colors"
          >
            <Settings className="w-5 h-5 text-gray-400" />
          </button>
        </div>
      </div>

      {/* Error Message */}
      {error && (
        <motion.div
          initial={{ opacity: 0, y: -10 }}
          animate={{ opacity: 1, y: 0 }}
          className="bg-red-500/20 border border-red-500/50 rounded-lg p-4 mb-6"
        >
          <div className="flex items-center space-x-2">
            <WifiOff className="w-5 h-5 text-red-400" />
            <span className="text-red-400">Connection Error: {error}</span>
          </div>
        </motion.div>
      )}

      {/* Dashboard Content */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="flex-1"
      >
        {layout === 'grid' ? renderGridLayout() : renderSidebarLayout()}
      </motion.div>

      {/* Notifications */}
      {showNotifications && (
        <NotificationCenter 
          position="top-right"
          maxNotifications={100}
          showTimestamp={true}
        />
      )}
    </div>
  );
};

export default RealTimeDashboard;
