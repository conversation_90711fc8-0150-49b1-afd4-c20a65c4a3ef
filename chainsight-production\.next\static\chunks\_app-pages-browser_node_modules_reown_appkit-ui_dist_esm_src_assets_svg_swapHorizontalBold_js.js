"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_reown_appkit-ui_dist_esm_src_assets_svg_swapHorizontalBold_js"],{

/***/ "(app-pages-browser)/./node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/swapHorizontalBold.js":
/*!*************************************************************************************!*\
  !*** ./node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/swapHorizontalBold.js ***!
  \*************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   swapHorizontalBoldSvg: () => (/* binding */ swapHorizontalBoldSvg)\n/* harmony export */ });\n/* harmony import */ var lit__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! lit */ \"(app-pages-browser)/./node_modules/lit/index.js\");\n\nconst swapHorizontalBoldSvg = (0,lit__WEBPACK_IMPORTED_MODULE_0__.svg) `<svg width=\"10\" height=\"10\" viewBox=\"0 0 10 10\">\n  <path\n    fill=\"currentColor\"\n    fill-rule=\"evenodd\"\n    d=\"M3.77986 0.566631C4.0589 0.845577 4.0589 1.29784 3.77986 1.57678L3.08261 2.2738H6.34184C6.73647 2.2738 7.05637 2.5936 7.05637 2.98808C7.05637 3.38257 6.73647 3.70237 6.34184 3.70237H3.08261L3.77986 4.39938C4.0589 4.67833 4.0589 5.13059 3.77986 5.40954C3.50082 5.68848 3.04841 5.68848 2.76937 5.40954L0.852346 3.49316C0.573306 3.21421 0.573306 2.76195 0.852346 2.48301L2.76937 0.566631C3.04841 0.287685 3.50082 0.287685 3.77986 0.566631ZM6.22 4.59102C6.49904 4.31208 6.95145 4.31208 7.23049 4.59102L9.14751 6.5074C9.42655 6.78634 9.42655 7.23861 9.14751 7.51755L7.23049 9.43393C6.95145 9.71287 6.49904 9.71287 6.22 9.43393C5.94096 9.15498 5.94096 8.70272 6.22 8.42377L6.91725 7.72676L3.65802 7.72676C3.26339 7.72676 2.94349 7.40696 2.94349 7.01247C2.94349 6.61798 3.26339 6.29819 3.65802 6.29819L6.91725 6.29819L6.22 5.60117C5.94096 5.32223 5.94096 4.86997 6.22 4.59102Z\"\n    clip-rule=\"evenodd\"\n  />\n</svg>`;\n//# sourceMappingURL=swapHorizontalBold.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/swapHorizontalBold.js\n"));

/***/ })

}]);