"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@noble";
exports.ids = ["vendor-chunks/@noble"];
exports.modules = {

/***/ "(ssr)/./node_modules/@noble/hashes/_md.js":
/*!*******************************************!*\
  !*** ./node_modules/@noble/hashes/_md.js ***!
  \*******************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.SHA512_IV = exports.SHA384_IV = exports.SHA224_IV = exports.SHA256_IV = exports.HashMD = void 0;\nexports.setBigUint64 = setBigUint64;\nexports.Chi = Chi;\nexports.Maj = Maj;\n/**\n * Internal Merkle-Damgard hash utils.\n * @module\n */\nconst utils_ts_1 = __webpack_require__(/*! ./utils.js */ \"(ssr)/./node_modules/@noble/hashes/utils.js\");\n/** Polyfill for Safari 14. https://caniuse.com/mdn-javascript_builtins_dataview_setbiguint64 */\nfunction setBigUint64(view, byteOffset, value, isLE) {\n    if (typeof view.setBigUint64 === 'function')\n        return view.setBigUint64(byteOffset, value, isLE);\n    const _32n = BigInt(32);\n    const _u32_max = BigInt(0xffffffff);\n    const wh = Number((value >> _32n) & _u32_max);\n    const wl = Number(value & _u32_max);\n    const h = isLE ? 4 : 0;\n    const l = isLE ? 0 : 4;\n    view.setUint32(byteOffset + h, wh, isLE);\n    view.setUint32(byteOffset + l, wl, isLE);\n}\n/** Choice: a ? b : c */\nfunction Chi(a, b, c) {\n    return (a & b) ^ (~a & c);\n}\n/** Majority function, true if any two inputs is true. */\nfunction Maj(a, b, c) {\n    return (a & b) ^ (a & c) ^ (b & c);\n}\n/**\n * Merkle-Damgard hash construction base class.\n * Could be used to create MD5, RIPEMD, SHA1, SHA2.\n */\nclass HashMD extends utils_ts_1.Hash {\n    constructor(blockLen, outputLen, padOffset, isLE) {\n        super();\n        this.finished = false;\n        this.length = 0;\n        this.pos = 0;\n        this.destroyed = false;\n        this.blockLen = blockLen;\n        this.outputLen = outputLen;\n        this.padOffset = padOffset;\n        this.isLE = isLE;\n        this.buffer = new Uint8Array(blockLen);\n        this.view = (0, utils_ts_1.createView)(this.buffer);\n    }\n    update(data) {\n        (0, utils_ts_1.aexists)(this);\n        data = (0, utils_ts_1.toBytes)(data);\n        (0, utils_ts_1.abytes)(data);\n        const { view, buffer, blockLen } = this;\n        const len = data.length;\n        for (let pos = 0; pos < len;) {\n            const take = Math.min(blockLen - this.pos, len - pos);\n            // Fast path: we have at least one block in input, cast it to view and process\n            if (take === blockLen) {\n                const dataView = (0, utils_ts_1.createView)(data);\n                for (; blockLen <= len - pos; pos += blockLen)\n                    this.process(dataView, pos);\n                continue;\n            }\n            buffer.set(data.subarray(pos, pos + take), this.pos);\n            this.pos += take;\n            pos += take;\n            if (this.pos === blockLen) {\n                this.process(view, 0);\n                this.pos = 0;\n            }\n        }\n        this.length += data.length;\n        this.roundClean();\n        return this;\n    }\n    digestInto(out) {\n        (0, utils_ts_1.aexists)(this);\n        (0, utils_ts_1.aoutput)(out, this);\n        this.finished = true;\n        // Padding\n        // We can avoid allocation of buffer for padding completely if it\n        // was previously not allocated here. But it won't change performance.\n        const { buffer, view, blockLen, isLE } = this;\n        let { pos } = this;\n        // append the bit '1' to the message\n        buffer[pos++] = 0b10000000;\n        (0, utils_ts_1.clean)(this.buffer.subarray(pos));\n        // we have less than padOffset left in buffer, so we cannot put length in\n        // current block, need process it and pad again\n        if (this.padOffset > blockLen - pos) {\n            this.process(view, 0);\n            pos = 0;\n        }\n        // Pad until full block byte with zeros\n        for (let i = pos; i < blockLen; i++)\n            buffer[i] = 0;\n        // Note: sha512 requires length to be 128bit integer, but length in JS will overflow before that\n        // You need to write around 2 exabytes (u64_max / 8 / (1024**6)) for this to happen.\n        // So we just write lowest 64 bits of that value.\n        setBigUint64(view, blockLen - 8, BigInt(this.length * 8), isLE);\n        this.process(view, 0);\n        const oview = (0, utils_ts_1.createView)(out);\n        const len = this.outputLen;\n        // NOTE: we do division by 4 later, which should be fused in single op with modulo by JIT\n        if (len % 4)\n            throw new Error('_sha2: outputLen should be aligned to 32bit');\n        const outLen = len / 4;\n        const state = this.get();\n        if (outLen > state.length)\n            throw new Error('_sha2: outputLen bigger than state');\n        for (let i = 0; i < outLen; i++)\n            oview.setUint32(4 * i, state[i], isLE);\n    }\n    digest() {\n        const { buffer, outputLen } = this;\n        this.digestInto(buffer);\n        const res = buffer.slice(0, outputLen);\n        this.destroy();\n        return res;\n    }\n    _cloneInto(to) {\n        to || (to = new this.constructor());\n        to.set(...this.get());\n        const { blockLen, buffer, length, finished, destroyed, pos } = this;\n        to.destroyed = destroyed;\n        to.finished = finished;\n        to.length = length;\n        to.pos = pos;\n        if (length % blockLen)\n            to.buffer.set(buffer);\n        return to;\n    }\n    clone() {\n        return this._cloneInto();\n    }\n}\nexports.HashMD = HashMD;\n/**\n * Initial SHA-2 state: fractional parts of square roots of first 16 primes 2..53.\n * Check out `test/misc/sha2-gen-iv.js` for recomputation guide.\n */\n/** Initial SHA256 state. Bits 0..32 of frac part of sqrt of primes 2..19 */\nexports.SHA256_IV = Uint32Array.from([\n    0x6a09e667, 0xbb67ae85, 0x3c6ef372, 0xa54ff53a, 0x510e527f, 0x9b05688c, 0x1f83d9ab, 0x5be0cd19,\n]);\n/** Initial SHA224 state. Bits 32..64 of frac part of sqrt of primes 23..53 */\nexports.SHA224_IV = Uint32Array.from([\n    0xc1059ed8, 0x367cd507, 0x3070dd17, 0xf70e5939, 0xffc00b31, 0x68581511, 0x64f98fa7, 0xbefa4fa4,\n]);\n/** Initial SHA384 state. Bits 0..64 of frac part of sqrt of primes 23..53 */\nexports.SHA384_IV = Uint32Array.from([\n    0xcbbb9d5d, 0xc1059ed8, 0x629a292a, 0x367cd507, 0x9159015a, 0x3070dd17, 0x152fecd8, 0xf70e5939,\n    0x67332667, 0xffc00b31, 0x8eb44a87, 0x68581511, 0xdb0c2e0d, 0x64f98fa7, 0x47b5481d, 0xbefa4fa4,\n]);\n/** Initial SHA512 state. Bits 0..64 of frac part of sqrt of primes 2..19 */\nexports.SHA512_IV = Uint32Array.from([\n    0x6a09e667, 0xf3bcc908, 0xbb67ae85, 0x84caa73b, 0x3c6ef372, 0xfe94f82b, 0xa54ff53a, 0x5f1d36f1,\n    0x510e527f, 0xade682d1, 0x9b05688c, 0x2b3e6c1f, 0x1f83d9ab, 0xfb41bd6b, 0x5be0cd19, 0x137e2179,\n]);\n//# sourceMappingURL=_md.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@noble/hashes/_md.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@noble/hashes/_u64.js":
/*!********************************************!*\
  !*** ./node_modules/@noble/hashes/_u64.js ***!
  \********************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.toBig = exports.shrSL = exports.shrSH = exports.rotrSL = exports.rotrSH = exports.rotrBL = exports.rotrBH = exports.rotr32L = exports.rotr32H = exports.rotlSL = exports.rotlSH = exports.rotlBL = exports.rotlBH = exports.add5L = exports.add5H = exports.add4L = exports.add4H = exports.add3L = exports.add3H = void 0;\nexports.add = add;\nexports.fromBig = fromBig;\nexports.split = split;\n/**\n * Internal helpers for u64. BigUint64Array is too slow as per 2025, so we implement it using Uint32Array.\n * @todo re-check https://issues.chromium.org/issues/42212588\n * @module\n */\nconst U32_MASK64 = /* @__PURE__ */ BigInt(2 ** 32 - 1);\nconst _32n = /* @__PURE__ */ BigInt(32);\nfunction fromBig(n, le = false) {\n    if (le)\n        return { h: Number(n & U32_MASK64), l: Number((n >> _32n) & U32_MASK64) };\n    return { h: Number((n >> _32n) & U32_MASK64) | 0, l: Number(n & U32_MASK64) | 0 };\n}\nfunction split(lst, le = false) {\n    const len = lst.length;\n    let Ah = new Uint32Array(len);\n    let Al = new Uint32Array(len);\n    for (let i = 0; i < len; i++) {\n        const { h, l } = fromBig(lst[i], le);\n        [Ah[i], Al[i]] = [h, l];\n    }\n    return [Ah, Al];\n}\nconst toBig = (h, l) => (BigInt(h >>> 0) << _32n) | BigInt(l >>> 0);\nexports.toBig = toBig;\n// for Shift in [0, 32)\nconst shrSH = (h, _l, s) => h >>> s;\nexports.shrSH = shrSH;\nconst shrSL = (h, l, s) => (h << (32 - s)) | (l >>> s);\nexports.shrSL = shrSL;\n// Right rotate for Shift in [1, 32)\nconst rotrSH = (h, l, s) => (h >>> s) | (l << (32 - s));\nexports.rotrSH = rotrSH;\nconst rotrSL = (h, l, s) => (h << (32 - s)) | (l >>> s);\nexports.rotrSL = rotrSL;\n// Right rotate for Shift in (32, 64), NOTE: 32 is special case.\nconst rotrBH = (h, l, s) => (h << (64 - s)) | (l >>> (s - 32));\nexports.rotrBH = rotrBH;\nconst rotrBL = (h, l, s) => (h >>> (s - 32)) | (l << (64 - s));\nexports.rotrBL = rotrBL;\n// Right rotate for shift===32 (just swaps l&h)\nconst rotr32H = (_h, l) => l;\nexports.rotr32H = rotr32H;\nconst rotr32L = (h, _l) => h;\nexports.rotr32L = rotr32L;\n// Left rotate for Shift in [1, 32)\nconst rotlSH = (h, l, s) => (h << s) | (l >>> (32 - s));\nexports.rotlSH = rotlSH;\nconst rotlSL = (h, l, s) => (l << s) | (h >>> (32 - s));\nexports.rotlSL = rotlSL;\n// Left rotate for Shift in (32, 64), NOTE: 32 is special case.\nconst rotlBH = (h, l, s) => (l << (s - 32)) | (h >>> (64 - s));\nexports.rotlBH = rotlBH;\nconst rotlBL = (h, l, s) => (h << (s - 32)) | (l >>> (64 - s));\nexports.rotlBL = rotlBL;\n// JS uses 32-bit signed integers for bitwise operations which means we cannot\n// simple take carry out of low bit sum by shift, we need to use division.\nfunction add(Ah, Al, Bh, Bl) {\n    const l = (Al >>> 0) + (Bl >>> 0);\n    return { h: (Ah + Bh + ((l / 2 ** 32) | 0)) | 0, l: l | 0 };\n}\n// Addition with more than 2 elements\nconst add3L = (Al, Bl, Cl) => (Al >>> 0) + (Bl >>> 0) + (Cl >>> 0);\nexports.add3L = add3L;\nconst add3H = (low, Ah, Bh, Ch) => (Ah + Bh + Ch + ((low / 2 ** 32) | 0)) | 0;\nexports.add3H = add3H;\nconst add4L = (Al, Bl, Cl, Dl) => (Al >>> 0) + (Bl >>> 0) + (Cl >>> 0) + (Dl >>> 0);\nexports.add4L = add4L;\nconst add4H = (low, Ah, Bh, Ch, Dh) => (Ah + Bh + Ch + Dh + ((low / 2 ** 32) | 0)) | 0;\nexports.add4H = add4H;\nconst add5L = (Al, Bl, Cl, Dl, El) => (Al >>> 0) + (Bl >>> 0) + (Cl >>> 0) + (Dl >>> 0) + (El >>> 0);\nexports.add5L = add5L;\nconst add5H = (low, Ah, Bh, Ch, Dh, Eh) => (Ah + Bh + Ch + Dh + Eh + ((low / 2 ** 32) | 0)) | 0;\nexports.add5H = add5H;\n// prettier-ignore\nconst u64 = {\n    fromBig, split, toBig,\n    shrSH, shrSL,\n    rotrSH, rotrSL, rotrBH, rotrBL,\n    rotr32H, rotr32L,\n    rotlSH, rotlSL, rotlBH, rotlBL,\n    add, add3L, add3H, add4L, add4H, add5H, add5L,\n};\nexports[\"default\"] = u64;\n//# sourceMappingURL=_u64.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@noble/hashes/_u64.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@noble/hashes/cryptoNode.js":
/*!**************************************************!*\
  !*** ./node_modules/@noble/hashes/cryptoNode.js ***!
  \**************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.crypto = void 0;\n/**\n * Internal webcrypto alias.\n * We prefer WebCrypto aka globalThis.crypto, which exists in node.js 16+.\n * Falls back to Node.js built-in crypto for Node.js <=v14.\n * See utils.ts for details.\n * @module\n */\n// @ts-ignore\nconst nc = __webpack_require__(/*! node:crypto */ \"node:crypto\");\nexports.crypto = nc && typeof nc === 'object' && 'webcrypto' in nc\n    ? nc.webcrypto\n    : nc && typeof nc === 'object' && 'randomBytes' in nc\n        ? nc\n        : undefined;\n//# sourceMappingURL=cryptoNode.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQG5vYmxlL2hhc2hlcy9jcnlwdG9Ob2RlLmpzIiwibWFwcGluZ3MiOiJBQUFhO0FBQ2IsOENBQTZDLEVBQUUsYUFBYSxFQUFDO0FBQzdELGNBQWM7QUFDZDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsV0FBVyxtQkFBTyxDQUFDLGdDQUFhO0FBQ2hDLGNBQWM7QUFDZDtBQUNBO0FBQ0E7QUFDQTtBQUNBIiwic291cmNlcyI6WyJGOlxcQUkgY2hhaW5cXGNoYWluc2lnaHQtcHJvZHVjdGlvblxcbm9kZV9tb2R1bGVzXFxAbm9ibGVcXGhhc2hlc1xcY3J5cHRvTm9kZS5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBzdHJpY3RcIjtcbk9iamVjdC5kZWZpbmVQcm9wZXJ0eShleHBvcnRzLCBcIl9fZXNNb2R1bGVcIiwgeyB2YWx1ZTogdHJ1ZSB9KTtcbmV4cG9ydHMuY3J5cHRvID0gdm9pZCAwO1xuLyoqXG4gKiBJbnRlcm5hbCB3ZWJjcnlwdG8gYWxpYXMuXG4gKiBXZSBwcmVmZXIgV2ViQ3J5cHRvIGFrYSBnbG9iYWxUaGlzLmNyeXB0bywgd2hpY2ggZXhpc3RzIGluIG5vZGUuanMgMTYrLlxuICogRmFsbHMgYmFjayB0byBOb2RlLmpzIGJ1aWx0LWluIGNyeXB0byBmb3IgTm9kZS5qcyA8PXYxNC5cbiAqIFNlZSB1dGlscy50cyBmb3IgZGV0YWlscy5cbiAqIEBtb2R1bGVcbiAqL1xuLy8gQHRzLWlnbm9yZVxuY29uc3QgbmMgPSByZXF1aXJlKFwibm9kZTpjcnlwdG9cIik7XG5leHBvcnRzLmNyeXB0byA9IG5jICYmIHR5cGVvZiBuYyA9PT0gJ29iamVjdCcgJiYgJ3dlYmNyeXB0bycgaW4gbmNcbiAgICA/IG5jLndlYmNyeXB0b1xuICAgIDogbmMgJiYgdHlwZW9mIG5jID09PSAnb2JqZWN0JyAmJiAncmFuZG9tQnl0ZXMnIGluIG5jXG4gICAgICAgID8gbmNcbiAgICAgICAgOiB1bmRlZmluZWQ7XG4vLyMgc291cmNlTWFwcGluZ1VSTD1jcnlwdG9Ob2RlLmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@noble/hashes/cryptoNode.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@noble/hashes/esm/_md.js":
/*!***********************************************!*\
  !*** ./node_modules/@noble/hashes/esm/_md.js ***!
  \***********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Chi: () => (/* binding */ Chi),\n/* harmony export */   HashMD: () => (/* binding */ HashMD),\n/* harmony export */   Maj: () => (/* binding */ Maj),\n/* harmony export */   SHA224_IV: () => (/* binding */ SHA224_IV),\n/* harmony export */   SHA256_IV: () => (/* binding */ SHA256_IV),\n/* harmony export */   SHA384_IV: () => (/* binding */ SHA384_IV),\n/* harmony export */   SHA512_IV: () => (/* binding */ SHA512_IV),\n/* harmony export */   setBigUint64: () => (/* binding */ setBigUint64)\n/* harmony export */ });\n/* harmony import */ var _utils_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./utils.js */ \"(ssr)/./node_modules/@noble/hashes/esm/utils.js\");\n/**\n * Internal Merkle-Damgard hash utils.\n * @module\n */\n\n/** Polyfill for Safari 14. https://caniuse.com/mdn-javascript_builtins_dataview_setbiguint64 */\nfunction setBigUint64(view, byteOffset, value, isLE) {\n    if (typeof view.setBigUint64 === 'function')\n        return view.setBigUint64(byteOffset, value, isLE);\n    const _32n = BigInt(32);\n    const _u32_max = BigInt(0xffffffff);\n    const wh = Number((value >> _32n) & _u32_max);\n    const wl = Number(value & _u32_max);\n    const h = isLE ? 4 : 0;\n    const l = isLE ? 0 : 4;\n    view.setUint32(byteOffset + h, wh, isLE);\n    view.setUint32(byteOffset + l, wl, isLE);\n}\n/** Choice: a ? b : c */\nfunction Chi(a, b, c) {\n    return (a & b) ^ (~a & c);\n}\n/** Majority function, true if any two inputs is true. */\nfunction Maj(a, b, c) {\n    return (a & b) ^ (a & c) ^ (b & c);\n}\n/**\n * Merkle-Damgard hash construction base class.\n * Could be used to create MD5, RIPEMD, SHA1, SHA2.\n */\nclass HashMD extends _utils_js__WEBPACK_IMPORTED_MODULE_0__.Hash {\n    constructor(blockLen, outputLen, padOffset, isLE) {\n        super();\n        this.finished = false;\n        this.length = 0;\n        this.pos = 0;\n        this.destroyed = false;\n        this.blockLen = blockLen;\n        this.outputLen = outputLen;\n        this.padOffset = padOffset;\n        this.isLE = isLE;\n        this.buffer = new Uint8Array(blockLen);\n        this.view = (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.createView)(this.buffer);\n    }\n    update(data) {\n        (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.aexists)(this);\n        data = (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.toBytes)(data);\n        (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.abytes)(data);\n        const { view, buffer, blockLen } = this;\n        const len = data.length;\n        for (let pos = 0; pos < len;) {\n            const take = Math.min(blockLen - this.pos, len - pos);\n            // Fast path: we have at least one block in input, cast it to view and process\n            if (take === blockLen) {\n                const dataView = (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.createView)(data);\n                for (; blockLen <= len - pos; pos += blockLen)\n                    this.process(dataView, pos);\n                continue;\n            }\n            buffer.set(data.subarray(pos, pos + take), this.pos);\n            this.pos += take;\n            pos += take;\n            if (this.pos === blockLen) {\n                this.process(view, 0);\n                this.pos = 0;\n            }\n        }\n        this.length += data.length;\n        this.roundClean();\n        return this;\n    }\n    digestInto(out) {\n        (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.aexists)(this);\n        (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.aoutput)(out, this);\n        this.finished = true;\n        // Padding\n        // We can avoid allocation of buffer for padding completely if it\n        // was previously not allocated here. But it won't change performance.\n        const { buffer, view, blockLen, isLE } = this;\n        let { pos } = this;\n        // append the bit '1' to the message\n        buffer[pos++] = 0b10000000;\n        (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.clean)(this.buffer.subarray(pos));\n        // we have less than padOffset left in buffer, so we cannot put length in\n        // current block, need process it and pad again\n        if (this.padOffset > blockLen - pos) {\n            this.process(view, 0);\n            pos = 0;\n        }\n        // Pad until full block byte with zeros\n        for (let i = pos; i < blockLen; i++)\n            buffer[i] = 0;\n        // Note: sha512 requires length to be 128bit integer, but length in JS will overflow before that\n        // You need to write around 2 exabytes (u64_max / 8 / (1024**6)) for this to happen.\n        // So we just write lowest 64 bits of that value.\n        setBigUint64(view, blockLen - 8, BigInt(this.length * 8), isLE);\n        this.process(view, 0);\n        const oview = (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.createView)(out);\n        const len = this.outputLen;\n        // NOTE: we do division by 4 later, which should be fused in single op with modulo by JIT\n        if (len % 4)\n            throw new Error('_sha2: outputLen should be aligned to 32bit');\n        const outLen = len / 4;\n        const state = this.get();\n        if (outLen > state.length)\n            throw new Error('_sha2: outputLen bigger than state');\n        for (let i = 0; i < outLen; i++)\n            oview.setUint32(4 * i, state[i], isLE);\n    }\n    digest() {\n        const { buffer, outputLen } = this;\n        this.digestInto(buffer);\n        const res = buffer.slice(0, outputLen);\n        this.destroy();\n        return res;\n    }\n    _cloneInto(to) {\n        to || (to = new this.constructor());\n        to.set(...this.get());\n        const { blockLen, buffer, length, finished, destroyed, pos } = this;\n        to.destroyed = destroyed;\n        to.finished = finished;\n        to.length = length;\n        to.pos = pos;\n        if (length % blockLen)\n            to.buffer.set(buffer);\n        return to;\n    }\n    clone() {\n        return this._cloneInto();\n    }\n}\n/**\n * Initial SHA-2 state: fractional parts of square roots of first 16 primes 2..53.\n * Check out `test/misc/sha2-gen-iv.js` for recomputation guide.\n */\n/** Initial SHA256 state. Bits 0..32 of frac part of sqrt of primes 2..19 */\nconst SHA256_IV = /* @__PURE__ */ Uint32Array.from([\n    0x6a09e667, 0xbb67ae85, 0x3c6ef372, 0xa54ff53a, 0x510e527f, 0x9b05688c, 0x1f83d9ab, 0x5be0cd19,\n]);\n/** Initial SHA224 state. Bits 32..64 of frac part of sqrt of primes 23..53 */\nconst SHA224_IV = /* @__PURE__ */ Uint32Array.from([\n    0xc1059ed8, 0x367cd507, 0x3070dd17, 0xf70e5939, 0xffc00b31, 0x68581511, 0x64f98fa7, 0xbefa4fa4,\n]);\n/** Initial SHA384 state. Bits 0..64 of frac part of sqrt of primes 23..53 */\nconst SHA384_IV = /* @__PURE__ */ Uint32Array.from([\n    0xcbbb9d5d, 0xc1059ed8, 0x629a292a, 0x367cd507, 0x9159015a, 0x3070dd17, 0x152fecd8, 0xf70e5939,\n    0x67332667, 0xffc00b31, 0x8eb44a87, 0x68581511, 0xdb0c2e0d, 0x64f98fa7, 0x47b5481d, 0xbefa4fa4,\n]);\n/** Initial SHA512 state. Bits 0..64 of frac part of sqrt of primes 2..19 */\nconst SHA512_IV = /* @__PURE__ */ Uint32Array.from([\n    0x6a09e667, 0xf3bcc908, 0xbb67ae85, 0x84caa73b, 0x3c6ef372, 0xfe94f82b, 0xa54ff53a, 0x5f1d36f1,\n    0x510e527f, 0xade682d1, 0x9b05688c, 0x2b3e6c1f, 0x1f83d9ab, 0xfb41bd6b, 0x5be0cd19, 0x137e2179,\n]);\n//# sourceMappingURL=_md.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@noble/hashes/esm/_md.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@noble/hashes/esm/_u64.js":
/*!************************************************!*\
  !*** ./node_modules/@noble/hashes/esm/_u64.js ***!
  \************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   add: () => (/* binding */ add),\n/* harmony export */   add3H: () => (/* binding */ add3H),\n/* harmony export */   add3L: () => (/* binding */ add3L),\n/* harmony export */   add4H: () => (/* binding */ add4H),\n/* harmony export */   add4L: () => (/* binding */ add4L),\n/* harmony export */   add5H: () => (/* binding */ add5H),\n/* harmony export */   add5L: () => (/* binding */ add5L),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   fromBig: () => (/* binding */ fromBig),\n/* harmony export */   rotlBH: () => (/* binding */ rotlBH),\n/* harmony export */   rotlBL: () => (/* binding */ rotlBL),\n/* harmony export */   rotlSH: () => (/* binding */ rotlSH),\n/* harmony export */   rotlSL: () => (/* binding */ rotlSL),\n/* harmony export */   rotr32H: () => (/* binding */ rotr32H),\n/* harmony export */   rotr32L: () => (/* binding */ rotr32L),\n/* harmony export */   rotrBH: () => (/* binding */ rotrBH),\n/* harmony export */   rotrBL: () => (/* binding */ rotrBL),\n/* harmony export */   rotrSH: () => (/* binding */ rotrSH),\n/* harmony export */   rotrSL: () => (/* binding */ rotrSL),\n/* harmony export */   shrSH: () => (/* binding */ shrSH),\n/* harmony export */   shrSL: () => (/* binding */ shrSL),\n/* harmony export */   split: () => (/* binding */ split),\n/* harmony export */   toBig: () => (/* binding */ toBig)\n/* harmony export */ });\n/**\n * Internal helpers for u64. BigUint64Array is too slow as per 2025, so we implement it using Uint32Array.\n * @todo re-check https://issues.chromium.org/issues/42212588\n * @module\n */\nconst U32_MASK64 = /* @__PURE__ */ BigInt(2 ** 32 - 1);\nconst _32n = /* @__PURE__ */ BigInt(32);\nfunction fromBig(n, le = false) {\n    if (le)\n        return { h: Number(n & U32_MASK64), l: Number((n >> _32n) & U32_MASK64) };\n    return { h: Number((n >> _32n) & U32_MASK64) | 0, l: Number(n & U32_MASK64) | 0 };\n}\nfunction split(lst, le = false) {\n    const len = lst.length;\n    let Ah = new Uint32Array(len);\n    let Al = new Uint32Array(len);\n    for (let i = 0; i < len; i++) {\n        const { h, l } = fromBig(lst[i], le);\n        [Ah[i], Al[i]] = [h, l];\n    }\n    return [Ah, Al];\n}\nconst toBig = (h, l) => (BigInt(h >>> 0) << _32n) | BigInt(l >>> 0);\n// for Shift in [0, 32)\nconst shrSH = (h, _l, s) => h >>> s;\nconst shrSL = (h, l, s) => (h << (32 - s)) | (l >>> s);\n// Right rotate for Shift in [1, 32)\nconst rotrSH = (h, l, s) => (h >>> s) | (l << (32 - s));\nconst rotrSL = (h, l, s) => (h << (32 - s)) | (l >>> s);\n// Right rotate for Shift in (32, 64), NOTE: 32 is special case.\nconst rotrBH = (h, l, s) => (h << (64 - s)) | (l >>> (s - 32));\nconst rotrBL = (h, l, s) => (h >>> (s - 32)) | (l << (64 - s));\n// Right rotate for shift===32 (just swaps l&h)\nconst rotr32H = (_h, l) => l;\nconst rotr32L = (h, _l) => h;\n// Left rotate for Shift in [1, 32)\nconst rotlSH = (h, l, s) => (h << s) | (l >>> (32 - s));\nconst rotlSL = (h, l, s) => (l << s) | (h >>> (32 - s));\n// Left rotate for Shift in (32, 64), NOTE: 32 is special case.\nconst rotlBH = (h, l, s) => (l << (s - 32)) | (h >>> (64 - s));\nconst rotlBL = (h, l, s) => (h << (s - 32)) | (l >>> (64 - s));\n// JS uses 32-bit signed integers for bitwise operations which means we cannot\n// simple take carry out of low bit sum by shift, we need to use division.\nfunction add(Ah, Al, Bh, Bl) {\n    const l = (Al >>> 0) + (Bl >>> 0);\n    return { h: (Ah + Bh + ((l / 2 ** 32) | 0)) | 0, l: l | 0 };\n}\n// Addition with more than 2 elements\nconst add3L = (Al, Bl, Cl) => (Al >>> 0) + (Bl >>> 0) + (Cl >>> 0);\nconst add3H = (low, Ah, Bh, Ch) => (Ah + Bh + Ch + ((low / 2 ** 32) | 0)) | 0;\nconst add4L = (Al, Bl, Cl, Dl) => (Al >>> 0) + (Bl >>> 0) + (Cl >>> 0) + (Dl >>> 0);\nconst add4H = (low, Ah, Bh, Ch, Dh) => (Ah + Bh + Ch + Dh + ((low / 2 ** 32) | 0)) | 0;\nconst add5L = (Al, Bl, Cl, Dl, El) => (Al >>> 0) + (Bl >>> 0) + (Cl >>> 0) + (Dl >>> 0) + (El >>> 0);\nconst add5H = (low, Ah, Bh, Ch, Dh, Eh) => (Ah + Bh + Ch + Dh + Eh + ((low / 2 ** 32) | 0)) | 0;\n// prettier-ignore\n\n// prettier-ignore\nconst u64 = {\n    fromBig, split, toBig,\n    shrSH, shrSL,\n    rotrSH, rotrSL, rotrBH, rotrBL,\n    rotr32H, rotr32L,\n    rotlSH, rotlSL, rotlBH, rotlBL,\n    add, add3L, add3H, add4L, add4H, add5H, add5L,\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (u64);\n//# sourceMappingURL=_u64.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@noble/hashes/esm/_u64.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@noble/hashes/esm/cryptoNode.js":
/*!******************************************************!*\
  !*** ./node_modules/@noble/hashes/esm/cryptoNode.js ***!
  \******************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("var node_crypto__WEBPACK_IMPORTED_MODULE_0___namespace_cache;\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   crypto: () => (/* binding */ crypto)\n/* harmony export */ });\n/* harmony import */ var node_crypto__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! node:crypto */ \"node:crypto\");\n/**\n * Internal webcrypto alias.\n * We prefer WebCrypto aka globalThis.crypto, which exists in node.js 16+.\n * Falls back to Node.js built-in crypto for Node.js <=v14.\n * See utils.ts for details.\n * @module\n */\n// @ts-ignore\n\nconst crypto = /*#__PURE__*/ (node_crypto__WEBPACK_IMPORTED_MODULE_0___namespace_cache || (node_crypto__WEBPACK_IMPORTED_MODULE_0___namespace_cache = __webpack_require__.t(node_crypto__WEBPACK_IMPORTED_MODULE_0__, 2))) && typeof /*#__PURE__*/ (node_crypto__WEBPACK_IMPORTED_MODULE_0___namespace_cache || (node_crypto__WEBPACK_IMPORTED_MODULE_0___namespace_cache = __webpack_require__.t(node_crypto__WEBPACK_IMPORTED_MODULE_0__, 2))) === 'object' && \"webcrypto\" in /*#__PURE__*/ (node_crypto__WEBPACK_IMPORTED_MODULE_0___namespace_cache || (node_crypto__WEBPACK_IMPORTED_MODULE_0___namespace_cache = __webpack_require__.t(node_crypto__WEBPACK_IMPORTED_MODULE_0__, 2)))\n    ? node_crypto__WEBPACK_IMPORTED_MODULE_0__.webcrypto\n    : /*#__PURE__*/ (node_crypto__WEBPACK_IMPORTED_MODULE_0___namespace_cache || (node_crypto__WEBPACK_IMPORTED_MODULE_0___namespace_cache = __webpack_require__.t(node_crypto__WEBPACK_IMPORTED_MODULE_0__, 2))) && typeof /*#__PURE__*/ (node_crypto__WEBPACK_IMPORTED_MODULE_0___namespace_cache || (node_crypto__WEBPACK_IMPORTED_MODULE_0___namespace_cache = __webpack_require__.t(node_crypto__WEBPACK_IMPORTED_MODULE_0__, 2))) === 'object' && \"randomBytes\" in /*#__PURE__*/ (node_crypto__WEBPACK_IMPORTED_MODULE_0___namespace_cache || (node_crypto__WEBPACK_IMPORTED_MODULE_0___namespace_cache = __webpack_require__.t(node_crypto__WEBPACK_IMPORTED_MODULE_0__, 2)))\n        ? /*#__PURE__*/ (node_crypto__WEBPACK_IMPORTED_MODULE_0___namespace_cache || (node_crypto__WEBPACK_IMPORTED_MODULE_0___namespace_cache = __webpack_require__.t(node_crypto__WEBPACK_IMPORTED_MODULE_0__, 2)))\n        : undefined;\n//# sourceMappingURL=cryptoNode.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQG5vYmxlL2hhc2hlcy9lc20vY3J5cHRvTm9kZS5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ2tDO0FBQzNCLGVBQWUsMk1BQUUsV0FBVywyTUFBRSxpQkFBaUIsME5BQWlCO0FBQ3ZFLE1BQU0sa0RBQVk7QUFDbEIsTUFBTSwyTUFBRSxXQUFXLDJNQUFFLGlCQUFpQiw0TkFBbUI7QUFDekQsVUFBVSwyTUFBRTtBQUNaO0FBQ0EiLCJzb3VyY2VzIjpbIkY6XFxBSSBjaGFpblxcY2hhaW5zaWdodC1wcm9kdWN0aW9uXFxub2RlX21vZHVsZXNcXEBub2JsZVxcaGFzaGVzXFxlc21cXGNyeXB0b05vZGUuanMiXSwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBJbnRlcm5hbCB3ZWJjcnlwdG8gYWxpYXMuXG4gKiBXZSBwcmVmZXIgV2ViQ3J5cHRvIGFrYSBnbG9iYWxUaGlzLmNyeXB0bywgd2hpY2ggZXhpc3RzIGluIG5vZGUuanMgMTYrLlxuICogRmFsbHMgYmFjayB0byBOb2RlLmpzIGJ1aWx0LWluIGNyeXB0byBmb3IgTm9kZS5qcyA8PXYxNC5cbiAqIFNlZSB1dGlscy50cyBmb3IgZGV0YWlscy5cbiAqIEBtb2R1bGVcbiAqL1xuLy8gQHRzLWlnbm9yZVxuaW1wb3J0ICogYXMgbmMgZnJvbSAnbm9kZTpjcnlwdG8nO1xuZXhwb3J0IGNvbnN0IGNyeXB0byA9IG5jICYmIHR5cGVvZiBuYyA9PT0gJ29iamVjdCcgJiYgJ3dlYmNyeXB0bycgaW4gbmNcbiAgICA/IG5jLndlYmNyeXB0b1xuICAgIDogbmMgJiYgdHlwZW9mIG5jID09PSAnb2JqZWN0JyAmJiAncmFuZG9tQnl0ZXMnIGluIG5jXG4gICAgICAgID8gbmNcbiAgICAgICAgOiB1bmRlZmluZWQ7XG4vLyMgc291cmNlTWFwcGluZ1VSTD1jcnlwdG9Ob2RlLmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@noble/hashes/esm/cryptoNode.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@noble/hashes/esm/hmac.js":
/*!************************************************!*\
  !*** ./node_modules/@noble/hashes/esm/hmac.js ***!
  \************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   HMAC: () => (/* binding */ HMAC),\n/* harmony export */   hmac: () => (/* binding */ hmac)\n/* harmony export */ });\n/* harmony import */ var _utils_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./utils.js */ \"(ssr)/./node_modules/@noble/hashes/esm/utils.js\");\n/**\n * HMAC: RFC2104 message authentication code.\n * @module\n */\n\nclass HMAC extends _utils_js__WEBPACK_IMPORTED_MODULE_0__.Hash {\n    constructor(hash, _key) {\n        super();\n        this.finished = false;\n        this.destroyed = false;\n        (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.ahash)(hash);\n        const key = (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.toBytes)(_key);\n        this.iHash = hash.create();\n        if (typeof this.iHash.update !== 'function')\n            throw new Error('Expected instance of class which extends utils.Hash');\n        this.blockLen = this.iHash.blockLen;\n        this.outputLen = this.iHash.outputLen;\n        const blockLen = this.blockLen;\n        const pad = new Uint8Array(blockLen);\n        // blockLen can be bigger than outputLen\n        pad.set(key.length > blockLen ? hash.create().update(key).digest() : key);\n        for (let i = 0; i < pad.length; i++)\n            pad[i] ^= 0x36;\n        this.iHash.update(pad);\n        // By doing update (processing of first block) of outer hash here we can re-use it between multiple calls via clone\n        this.oHash = hash.create();\n        // Undo internal XOR && apply outer XOR\n        for (let i = 0; i < pad.length; i++)\n            pad[i] ^= 0x36 ^ 0x5c;\n        this.oHash.update(pad);\n        (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.clean)(pad);\n    }\n    update(buf) {\n        (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.aexists)(this);\n        this.iHash.update(buf);\n        return this;\n    }\n    digestInto(out) {\n        (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.aexists)(this);\n        (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.abytes)(out, this.outputLen);\n        this.finished = true;\n        this.iHash.digestInto(out);\n        this.oHash.update(out);\n        this.oHash.digestInto(out);\n        this.destroy();\n    }\n    digest() {\n        const out = new Uint8Array(this.oHash.outputLen);\n        this.digestInto(out);\n        return out;\n    }\n    _cloneInto(to) {\n        // Create new instance without calling constructor since key already in state and we don't know it.\n        to || (to = Object.create(Object.getPrototypeOf(this), {}));\n        const { oHash, iHash, finished, destroyed, blockLen, outputLen } = this;\n        to = to;\n        to.finished = finished;\n        to.destroyed = destroyed;\n        to.blockLen = blockLen;\n        to.outputLen = outputLen;\n        to.oHash = oHash._cloneInto(to.oHash);\n        to.iHash = iHash._cloneInto(to.iHash);\n        return to;\n    }\n    clone() {\n        return this._cloneInto();\n    }\n    destroy() {\n        this.destroyed = true;\n        this.oHash.destroy();\n        this.iHash.destroy();\n    }\n}\n/**\n * HMAC: RFC2104 message authentication code.\n * @param hash - function that would be used e.g. sha256\n * @param key - message key\n * @param message - message data\n * @example\n * import { hmac } from '@noble/hashes/hmac';\n * import { sha256 } from '@noble/hashes/sha2';\n * const mac1 = hmac(sha256, 'key', 'message');\n */\nconst hmac = (hash, key, message) => new HMAC(hash, key).update(message).digest();\nhmac.create = (hash, key) => new HMAC(hash, key);\n//# sourceMappingURL=hmac.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@noble/hashes/esm/hmac.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@noble/hashes/esm/legacy.js":
/*!**************************************************!*\
  !*** ./node_modules/@noble/hashes/esm/legacy.js ***!
  \**************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   MD5: () => (/* binding */ MD5),\n/* harmony export */   RIPEMD160: () => (/* binding */ RIPEMD160),\n/* harmony export */   SHA1: () => (/* binding */ SHA1),\n/* harmony export */   md5: () => (/* binding */ md5),\n/* harmony export */   ripemd160: () => (/* binding */ ripemd160),\n/* harmony export */   sha1: () => (/* binding */ sha1)\n/* harmony export */ });\n/* harmony import */ var _md_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./_md.js */ \"(ssr)/./node_modules/@noble/hashes/esm/_md.js\");\n/* harmony import */ var _utils_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./utils.js */ \"(ssr)/./node_modules/@noble/hashes/esm/utils.js\");\n/**\n\nSHA1 (RFC 3174), MD5 (RFC 1321) and RIPEMD160 (RFC 2286) legacy, weak hash functions.\nDon't use them in a new protocol. What \"weak\" means:\n\n- Collisions can be made with 2^18 effort in MD5, 2^60 in SHA1, 2^80 in RIPEMD160.\n- No practical pre-image attacks (only theoretical, 2^123.4)\n- HMAC seems kinda ok: https://datatracker.ietf.org/doc/html/rfc6151\n * @module\n */\n\n\n/** Initial SHA1 state */\nconst SHA1_IV = /* @__PURE__ */ Uint32Array.from([\n    0x67452301, 0xefcdab89, 0x98badcfe, 0x10325476, 0xc3d2e1f0,\n]);\n// Reusable temporary buffer\nconst SHA1_W = /* @__PURE__ */ new Uint32Array(80);\n/** SHA1 legacy hash class. */\nclass SHA1 extends _md_js__WEBPACK_IMPORTED_MODULE_0__.HashMD {\n    constructor() {\n        super(64, 20, 8, false);\n        this.A = SHA1_IV[0] | 0;\n        this.B = SHA1_IV[1] | 0;\n        this.C = SHA1_IV[2] | 0;\n        this.D = SHA1_IV[3] | 0;\n        this.E = SHA1_IV[4] | 0;\n    }\n    get() {\n        const { A, B, C, D, E } = this;\n        return [A, B, C, D, E];\n    }\n    set(A, B, C, D, E) {\n        this.A = A | 0;\n        this.B = B | 0;\n        this.C = C | 0;\n        this.D = D | 0;\n        this.E = E | 0;\n    }\n    process(view, offset) {\n        for (let i = 0; i < 16; i++, offset += 4)\n            SHA1_W[i] = view.getUint32(offset, false);\n        for (let i = 16; i < 80; i++)\n            SHA1_W[i] = (0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.rotl)(SHA1_W[i - 3] ^ SHA1_W[i - 8] ^ SHA1_W[i - 14] ^ SHA1_W[i - 16], 1);\n        // Compression function main loop, 80 rounds\n        let { A, B, C, D, E } = this;\n        for (let i = 0; i < 80; i++) {\n            let F, K;\n            if (i < 20) {\n                F = (0,_md_js__WEBPACK_IMPORTED_MODULE_0__.Chi)(B, C, D);\n                K = 0x5a827999;\n            }\n            else if (i < 40) {\n                F = B ^ C ^ D;\n                K = 0x6ed9eba1;\n            }\n            else if (i < 60) {\n                F = (0,_md_js__WEBPACK_IMPORTED_MODULE_0__.Maj)(B, C, D);\n                K = 0x8f1bbcdc;\n            }\n            else {\n                F = B ^ C ^ D;\n                K = 0xca62c1d6;\n            }\n            const T = ((0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.rotl)(A, 5) + F + E + K + SHA1_W[i]) | 0;\n            E = D;\n            D = C;\n            C = (0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.rotl)(B, 30);\n            B = A;\n            A = T;\n        }\n        // Add the compressed chunk to the current hash value\n        A = (A + this.A) | 0;\n        B = (B + this.B) | 0;\n        C = (C + this.C) | 0;\n        D = (D + this.D) | 0;\n        E = (E + this.E) | 0;\n        this.set(A, B, C, D, E);\n    }\n    roundClean() {\n        (0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.clean)(SHA1_W);\n    }\n    destroy() {\n        this.set(0, 0, 0, 0, 0);\n        (0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.clean)(this.buffer);\n    }\n}\n/** SHA1 (RFC 3174) legacy hash function. It was cryptographically broken. */\nconst sha1 = /* @__PURE__ */ (0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.createHasher)(() => new SHA1());\n/** Per-round constants */\nconst p32 = /* @__PURE__ */ Math.pow(2, 32);\nconst K = /* @__PURE__ */ Array.from({ length: 64 }, (_, i) => Math.floor(p32 * Math.abs(Math.sin(i + 1))));\n/** md5 initial state: same as sha1, but 4 u32 instead of 5. */\nconst MD5_IV = /* @__PURE__ */ SHA1_IV.slice(0, 4);\n// Reusable temporary buffer\nconst MD5_W = /* @__PURE__ */ new Uint32Array(16);\n/** MD5 legacy hash class. */\nclass MD5 extends _md_js__WEBPACK_IMPORTED_MODULE_0__.HashMD {\n    constructor() {\n        super(64, 16, 8, true);\n        this.A = MD5_IV[0] | 0;\n        this.B = MD5_IV[1] | 0;\n        this.C = MD5_IV[2] | 0;\n        this.D = MD5_IV[3] | 0;\n    }\n    get() {\n        const { A, B, C, D } = this;\n        return [A, B, C, D];\n    }\n    set(A, B, C, D) {\n        this.A = A | 0;\n        this.B = B | 0;\n        this.C = C | 0;\n        this.D = D | 0;\n    }\n    process(view, offset) {\n        for (let i = 0; i < 16; i++, offset += 4)\n            MD5_W[i] = view.getUint32(offset, true);\n        // Compression function main loop, 64 rounds\n        let { A, B, C, D } = this;\n        for (let i = 0; i < 64; i++) {\n            let F, g, s;\n            if (i < 16) {\n                F = (0,_md_js__WEBPACK_IMPORTED_MODULE_0__.Chi)(B, C, D);\n                g = i;\n                s = [7, 12, 17, 22];\n            }\n            else if (i < 32) {\n                F = (0,_md_js__WEBPACK_IMPORTED_MODULE_0__.Chi)(D, B, C);\n                g = (5 * i + 1) % 16;\n                s = [5, 9, 14, 20];\n            }\n            else if (i < 48) {\n                F = B ^ C ^ D;\n                g = (3 * i + 5) % 16;\n                s = [4, 11, 16, 23];\n            }\n            else {\n                F = C ^ (B | ~D);\n                g = (7 * i) % 16;\n                s = [6, 10, 15, 21];\n            }\n            F = F + A + K[i] + MD5_W[g];\n            A = D;\n            D = C;\n            C = B;\n            B = B + (0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.rotl)(F, s[i % 4]);\n        }\n        // Add the compressed chunk to the current hash value\n        A = (A + this.A) | 0;\n        B = (B + this.B) | 0;\n        C = (C + this.C) | 0;\n        D = (D + this.D) | 0;\n        this.set(A, B, C, D);\n    }\n    roundClean() {\n        (0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.clean)(MD5_W);\n    }\n    destroy() {\n        this.set(0, 0, 0, 0);\n        (0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.clean)(this.buffer);\n    }\n}\n/**\n * MD5 (RFC 1321) legacy hash function. It was cryptographically broken.\n * MD5 architecture is similar to SHA1, with some differences:\n * - Reduced output length: 16 bytes (128 bit) instead of 20\n * - 64 rounds, instead of 80\n * - Little-endian: could be faster, but will require more code\n * - Non-linear index selection: huge speed-up for unroll\n * - Per round constants: more memory accesses, additional speed-up for unroll\n */\nconst md5 = /* @__PURE__ */ (0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.createHasher)(() => new MD5());\n// RIPEMD-160\nconst Rho160 = /* @__PURE__ */ Uint8Array.from([\n    7, 4, 13, 1, 10, 6, 15, 3, 12, 0, 9, 5, 2, 14, 11, 8,\n]);\nconst Id160 = /* @__PURE__ */ (() => Uint8Array.from(new Array(16).fill(0).map((_, i) => i)))();\nconst Pi160 = /* @__PURE__ */ (() => Id160.map((i) => (9 * i + 5) % 16))();\nconst idxLR = /* @__PURE__ */ (() => {\n    const L = [Id160];\n    const R = [Pi160];\n    const res = [L, R];\n    for (let i = 0; i < 4; i++)\n        for (let j of res)\n            j.push(j[i].map((k) => Rho160[k]));\n    return res;\n})();\nconst idxL = /* @__PURE__ */ (() => idxLR[0])();\nconst idxR = /* @__PURE__ */ (() => idxLR[1])();\n// const [idxL, idxR] = idxLR;\nconst shifts160 = /* @__PURE__ */ [\n    [11, 14, 15, 12, 5, 8, 7, 9, 11, 13, 14, 15, 6, 7, 9, 8],\n    [12, 13, 11, 15, 6, 9, 9, 7, 12, 15, 11, 13, 7, 8, 7, 7],\n    [13, 15, 14, 11, 7, 7, 6, 8, 13, 14, 13, 12, 5, 5, 6, 9],\n    [14, 11, 12, 14, 8, 6, 5, 5, 15, 12, 15, 14, 9, 9, 8, 6],\n    [15, 12, 13, 13, 9, 5, 8, 6, 14, 11, 12, 11, 8, 6, 5, 5],\n].map((i) => Uint8Array.from(i));\nconst shiftsL160 = /* @__PURE__ */ idxL.map((idx, i) => idx.map((j) => shifts160[i][j]));\nconst shiftsR160 = /* @__PURE__ */ idxR.map((idx, i) => idx.map((j) => shifts160[i][j]));\nconst Kl160 = /* @__PURE__ */ Uint32Array.from([\n    0x00000000, 0x5a827999, 0x6ed9eba1, 0x8f1bbcdc, 0xa953fd4e,\n]);\nconst Kr160 = /* @__PURE__ */ Uint32Array.from([\n    0x50a28be6, 0x5c4dd124, 0x6d703ef3, 0x7a6d76e9, 0x00000000,\n]);\n// It's called f() in spec.\nfunction ripemd_f(group, x, y, z) {\n    if (group === 0)\n        return x ^ y ^ z;\n    if (group === 1)\n        return (x & y) | (~x & z);\n    if (group === 2)\n        return (x | ~y) ^ z;\n    if (group === 3)\n        return (x & z) | (y & ~z);\n    return x ^ (y | ~z);\n}\n// Reusable temporary buffer\nconst BUF_160 = /* @__PURE__ */ new Uint32Array(16);\nclass RIPEMD160 extends _md_js__WEBPACK_IMPORTED_MODULE_0__.HashMD {\n    constructor() {\n        super(64, 20, 8, true);\n        this.h0 = 0x67452301 | 0;\n        this.h1 = 0xefcdab89 | 0;\n        this.h2 = 0x98badcfe | 0;\n        this.h3 = 0x10325476 | 0;\n        this.h4 = 0xc3d2e1f0 | 0;\n    }\n    get() {\n        const { h0, h1, h2, h3, h4 } = this;\n        return [h0, h1, h2, h3, h4];\n    }\n    set(h0, h1, h2, h3, h4) {\n        this.h0 = h0 | 0;\n        this.h1 = h1 | 0;\n        this.h2 = h2 | 0;\n        this.h3 = h3 | 0;\n        this.h4 = h4 | 0;\n    }\n    process(view, offset) {\n        for (let i = 0; i < 16; i++, offset += 4)\n            BUF_160[i] = view.getUint32(offset, true);\n        // prettier-ignore\n        let al = this.h0 | 0, ar = al, bl = this.h1 | 0, br = bl, cl = this.h2 | 0, cr = cl, dl = this.h3 | 0, dr = dl, el = this.h4 | 0, er = el;\n        // Instead of iterating 0 to 80, we split it into 5 groups\n        // And use the groups in constants, functions, etc. Much simpler\n        for (let group = 0; group < 5; group++) {\n            const rGroup = 4 - group;\n            const hbl = Kl160[group], hbr = Kr160[group]; // prettier-ignore\n            const rl = idxL[group], rr = idxR[group]; // prettier-ignore\n            const sl = shiftsL160[group], sr = shiftsR160[group]; // prettier-ignore\n            for (let i = 0; i < 16; i++) {\n                const tl = ((0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.rotl)(al + ripemd_f(group, bl, cl, dl) + BUF_160[rl[i]] + hbl, sl[i]) + el) | 0;\n                al = el, el = dl, dl = (0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.rotl)(cl, 10) | 0, cl = bl, bl = tl; // prettier-ignore\n            }\n            // 2 loops are 10% faster\n            for (let i = 0; i < 16; i++) {\n                const tr = ((0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.rotl)(ar + ripemd_f(rGroup, br, cr, dr) + BUF_160[rr[i]] + hbr, sr[i]) + er) | 0;\n                ar = er, er = dr, dr = (0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.rotl)(cr, 10) | 0, cr = br, br = tr; // prettier-ignore\n            }\n        }\n        // Add the compressed chunk to the current hash value\n        this.set((this.h1 + cl + dr) | 0, (this.h2 + dl + er) | 0, (this.h3 + el + ar) | 0, (this.h4 + al + br) | 0, (this.h0 + bl + cr) | 0);\n    }\n    roundClean() {\n        (0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.clean)(BUF_160);\n    }\n    destroy() {\n        this.destroyed = true;\n        (0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.clean)(this.buffer);\n        this.set(0, 0, 0, 0, 0);\n    }\n}\n/**\n * RIPEMD-160 - a legacy hash function from 1990s.\n * * https://homes.esat.kuleuven.be/~bosselae/ripemd160.html\n * * https://homes.esat.kuleuven.be/~bosselae/ripemd160/pdf/AB-9601/AB-9601.pdf\n */\nconst ripemd160 = /* @__PURE__ */ (0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.createHasher)(() => new RIPEMD160());\n//# sourceMappingURL=legacy.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@noble/hashes/esm/legacy.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@noble/hashes/esm/ripemd160.js":
/*!*****************************************************!*\
  !*** ./node_modules/@noble/hashes/esm/ripemd160.js ***!
  \*****************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   RIPEMD160: () => (/* binding */ RIPEMD160),\n/* harmony export */   ripemd160: () => (/* binding */ ripemd160)\n/* harmony export */ });\n/* harmony import */ var _legacy_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./legacy.js */ \"(ssr)/./node_modules/@noble/hashes/esm/legacy.js\");\n/**\n * RIPEMD-160 legacy hash function.\n * https://homes.esat.kuleuven.be/~bosselae/ripemd160.html\n * https://homes.esat.kuleuven.be/~bosselae/ripemd160/pdf/AB-9601/AB-9601.pdf\n * @module\n * @deprecated\n */\n\n/** @deprecated Use import from `noble/hashes/legacy` module */\nconst RIPEMD160 = _legacy_js__WEBPACK_IMPORTED_MODULE_0__.RIPEMD160;\n/** @deprecated Use import from `noble/hashes/legacy` module */\nconst ripemd160 = _legacy_js__WEBPACK_IMPORTED_MODULE_0__.ripemd160;\n//# sourceMappingURL=ripemd160.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQG5vYmxlL2hhc2hlcy9lc20vcmlwZW1kMTYwLmpzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQytFO0FBQy9FO0FBQ08sa0JBQWtCLGlEQUFVO0FBQ25DO0FBQ08sa0JBQWtCLGlEQUFVO0FBQ25DIiwic291cmNlcyI6WyJGOlxcQUkgY2hhaW5cXGNoYWluc2lnaHQtcHJvZHVjdGlvblxcbm9kZV9tb2R1bGVzXFxAbm9ibGVcXGhhc2hlc1xcZXNtXFxyaXBlbWQxNjAuanMiXSwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBSSVBFTUQtMTYwIGxlZ2FjeSBoYXNoIGZ1bmN0aW9uLlxuICogaHR0cHM6Ly9ob21lcy5lc2F0Lmt1bGV1dmVuLmJlL35ib3NzZWxhZS9yaXBlbWQxNjAuaHRtbFxuICogaHR0cHM6Ly9ob21lcy5lc2F0Lmt1bGV1dmVuLmJlL35ib3NzZWxhZS9yaXBlbWQxNjAvcGRmL0FCLTk2MDEvQUItOTYwMS5wZGZcbiAqIEBtb2R1bGVcbiAqIEBkZXByZWNhdGVkXG4gKi9cbmltcG9ydCB7IFJJUEVNRDE2MCBhcyBSSVBFTUQxNjBuLCByaXBlbWQxNjAgYXMgcmlwZW1kMTYwbiB9IGZyb20gXCIuL2xlZ2FjeS5qc1wiO1xuLyoqIEBkZXByZWNhdGVkIFVzZSBpbXBvcnQgZnJvbSBgbm9ibGUvaGFzaGVzL2xlZ2FjeWAgbW9kdWxlICovXG5leHBvcnQgY29uc3QgUklQRU1EMTYwID0gUklQRU1EMTYwbjtcbi8qKiBAZGVwcmVjYXRlZCBVc2UgaW1wb3J0IGZyb20gYG5vYmxlL2hhc2hlcy9sZWdhY3lgIG1vZHVsZSAqL1xuZXhwb3J0IGNvbnN0IHJpcGVtZDE2MCA9IHJpcGVtZDE2MG47XG4vLyMgc291cmNlTWFwcGluZ1VSTD1yaXBlbWQxNjAuanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@noble/hashes/esm/ripemd160.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@noble/hashes/esm/sha2.js":
/*!************************************************!*\
  !*** ./node_modules/@noble/hashes/esm/sha2.js ***!
  \************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SHA224: () => (/* binding */ SHA224),\n/* harmony export */   SHA256: () => (/* binding */ SHA256),\n/* harmony export */   SHA384: () => (/* binding */ SHA384),\n/* harmony export */   SHA512: () => (/* binding */ SHA512),\n/* harmony export */   SHA512_224: () => (/* binding */ SHA512_224),\n/* harmony export */   SHA512_256: () => (/* binding */ SHA512_256),\n/* harmony export */   sha224: () => (/* binding */ sha224),\n/* harmony export */   sha256: () => (/* binding */ sha256),\n/* harmony export */   sha384: () => (/* binding */ sha384),\n/* harmony export */   sha512: () => (/* binding */ sha512),\n/* harmony export */   sha512_224: () => (/* binding */ sha512_224),\n/* harmony export */   sha512_256: () => (/* binding */ sha512_256)\n/* harmony export */ });\n/* harmony import */ var _md_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./_md.js */ \"(ssr)/./node_modules/@noble/hashes/esm/_md.js\");\n/* harmony import */ var _u64_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./_u64.js */ \"(ssr)/./node_modules/@noble/hashes/esm/_u64.js\");\n/* harmony import */ var _utils_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./utils.js */ \"(ssr)/./node_modules/@noble/hashes/esm/utils.js\");\n/**\n * SHA2 hash function. A.k.a. sha256, sha384, sha512, sha512_224, sha512_256.\n * SHA256 is the fastest hash implementable in JS, even faster than Blake3.\n * Check out [RFC 4634](https://datatracker.ietf.org/doc/html/rfc4634) and\n * [FIPS 180-4](https://nvlpubs.nist.gov/nistpubs/FIPS/NIST.FIPS.180-4.pdf).\n * @module\n */\n\n\n\n/**\n * Round constants:\n * First 32 bits of fractional parts of the cube roots of the first 64 primes 2..311)\n */\n// prettier-ignore\nconst SHA256_K = /* @__PURE__ */ Uint32Array.from([\n    0x428a2f98, 0x71374491, 0xb5c0fbcf, 0xe9b5dba5, 0x3956c25b, 0x59f111f1, 0x923f82a4, 0xab1c5ed5,\n    0xd807aa98, 0x12835b01, 0x243185be, 0x550c7dc3, 0x72be5d74, 0x80deb1fe, 0x9bdc06a7, 0xc19bf174,\n    0xe49b69c1, 0xefbe4786, 0x0fc19dc6, 0x240ca1cc, 0x2de92c6f, 0x4a7484aa, 0x5cb0a9dc, 0x76f988da,\n    0x983e5152, 0xa831c66d, 0xb00327c8, 0xbf597fc7, 0xc6e00bf3, 0xd5a79147, 0x06ca6351, 0x14292967,\n    0x27b70a85, 0x2e1b2138, 0x4d2c6dfc, 0x53380d13, 0x650a7354, 0x766a0abb, 0x81c2c92e, 0x92722c85,\n    0xa2bfe8a1, 0xa81a664b, 0xc24b8b70, 0xc76c51a3, 0xd192e819, 0xd6990624, 0xf40e3585, 0x106aa070,\n    0x19a4c116, 0x1e376c08, 0x2748774c, 0x34b0bcb5, 0x391c0cb3, 0x4ed8aa4a, 0x5b9cca4f, 0x682e6ff3,\n    0x748f82ee, 0x78a5636f, 0x84c87814, 0x8cc70208, 0x90befffa, 0xa4506ceb, 0xbef9a3f7, 0xc67178f2\n]);\n/** Reusable temporary buffer. \"W\" comes straight from spec. */\nconst SHA256_W = /* @__PURE__ */ new Uint32Array(64);\nclass SHA256 extends _md_js__WEBPACK_IMPORTED_MODULE_0__.HashMD {\n    constructor(outputLen = 32) {\n        super(64, outputLen, 8, false);\n        // We cannot use array here since array allows indexing by variable\n        // which means optimizer/compiler cannot use registers.\n        this.A = _md_js__WEBPACK_IMPORTED_MODULE_0__.SHA256_IV[0] | 0;\n        this.B = _md_js__WEBPACK_IMPORTED_MODULE_0__.SHA256_IV[1] | 0;\n        this.C = _md_js__WEBPACK_IMPORTED_MODULE_0__.SHA256_IV[2] | 0;\n        this.D = _md_js__WEBPACK_IMPORTED_MODULE_0__.SHA256_IV[3] | 0;\n        this.E = _md_js__WEBPACK_IMPORTED_MODULE_0__.SHA256_IV[4] | 0;\n        this.F = _md_js__WEBPACK_IMPORTED_MODULE_0__.SHA256_IV[5] | 0;\n        this.G = _md_js__WEBPACK_IMPORTED_MODULE_0__.SHA256_IV[6] | 0;\n        this.H = _md_js__WEBPACK_IMPORTED_MODULE_0__.SHA256_IV[7] | 0;\n    }\n    get() {\n        const { A, B, C, D, E, F, G, H } = this;\n        return [A, B, C, D, E, F, G, H];\n    }\n    // prettier-ignore\n    set(A, B, C, D, E, F, G, H) {\n        this.A = A | 0;\n        this.B = B | 0;\n        this.C = C | 0;\n        this.D = D | 0;\n        this.E = E | 0;\n        this.F = F | 0;\n        this.G = G | 0;\n        this.H = H | 0;\n    }\n    process(view, offset) {\n        // Extend the first 16 words into the remaining 48 words w[16..63] of the message schedule array\n        for (let i = 0; i < 16; i++, offset += 4)\n            SHA256_W[i] = view.getUint32(offset, false);\n        for (let i = 16; i < 64; i++) {\n            const W15 = SHA256_W[i - 15];\n            const W2 = SHA256_W[i - 2];\n            const s0 = (0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.rotr)(W15, 7) ^ (0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.rotr)(W15, 18) ^ (W15 >>> 3);\n            const s1 = (0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.rotr)(W2, 17) ^ (0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.rotr)(W2, 19) ^ (W2 >>> 10);\n            SHA256_W[i] = (s1 + SHA256_W[i - 7] + s0 + SHA256_W[i - 16]) | 0;\n        }\n        // Compression function main loop, 64 rounds\n        let { A, B, C, D, E, F, G, H } = this;\n        for (let i = 0; i < 64; i++) {\n            const sigma1 = (0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.rotr)(E, 6) ^ (0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.rotr)(E, 11) ^ (0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.rotr)(E, 25);\n            const T1 = (H + sigma1 + (0,_md_js__WEBPACK_IMPORTED_MODULE_0__.Chi)(E, F, G) + SHA256_K[i] + SHA256_W[i]) | 0;\n            const sigma0 = (0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.rotr)(A, 2) ^ (0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.rotr)(A, 13) ^ (0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.rotr)(A, 22);\n            const T2 = (sigma0 + (0,_md_js__WEBPACK_IMPORTED_MODULE_0__.Maj)(A, B, C)) | 0;\n            H = G;\n            G = F;\n            F = E;\n            E = (D + T1) | 0;\n            D = C;\n            C = B;\n            B = A;\n            A = (T1 + T2) | 0;\n        }\n        // Add the compressed chunk to the current hash value\n        A = (A + this.A) | 0;\n        B = (B + this.B) | 0;\n        C = (C + this.C) | 0;\n        D = (D + this.D) | 0;\n        E = (E + this.E) | 0;\n        F = (F + this.F) | 0;\n        G = (G + this.G) | 0;\n        H = (H + this.H) | 0;\n        this.set(A, B, C, D, E, F, G, H);\n    }\n    roundClean() {\n        (0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.clean)(SHA256_W);\n    }\n    destroy() {\n        this.set(0, 0, 0, 0, 0, 0, 0, 0);\n        (0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.clean)(this.buffer);\n    }\n}\nclass SHA224 extends SHA256 {\n    constructor() {\n        super(28);\n        this.A = _md_js__WEBPACK_IMPORTED_MODULE_0__.SHA224_IV[0] | 0;\n        this.B = _md_js__WEBPACK_IMPORTED_MODULE_0__.SHA224_IV[1] | 0;\n        this.C = _md_js__WEBPACK_IMPORTED_MODULE_0__.SHA224_IV[2] | 0;\n        this.D = _md_js__WEBPACK_IMPORTED_MODULE_0__.SHA224_IV[3] | 0;\n        this.E = _md_js__WEBPACK_IMPORTED_MODULE_0__.SHA224_IV[4] | 0;\n        this.F = _md_js__WEBPACK_IMPORTED_MODULE_0__.SHA224_IV[5] | 0;\n        this.G = _md_js__WEBPACK_IMPORTED_MODULE_0__.SHA224_IV[6] | 0;\n        this.H = _md_js__WEBPACK_IMPORTED_MODULE_0__.SHA224_IV[7] | 0;\n    }\n}\n// SHA2-512 is slower than sha256 in js because u64 operations are slow.\n// Round contants\n// First 32 bits of the fractional parts of the cube roots of the first 80 primes 2..409\n// prettier-ignore\nconst K512 = /* @__PURE__ */ (() => _u64_js__WEBPACK_IMPORTED_MODULE_2__.split([\n    '0x428a2f98d728ae22', '0x7137449123ef65cd', '0xb5c0fbcfec4d3b2f', '0xe9b5dba58189dbbc',\n    '0x3956c25bf348b538', '0x59f111f1b605d019', '0x923f82a4af194f9b', '0xab1c5ed5da6d8118',\n    '0xd807aa98a3030242', '0x12835b0145706fbe', '0x243185be4ee4b28c', '0x550c7dc3d5ffb4e2',\n    '0x72be5d74f27b896f', '0x80deb1fe3b1696b1', '0x9bdc06a725c71235', '0xc19bf174cf692694',\n    '0xe49b69c19ef14ad2', '0xefbe4786384f25e3', '0x0fc19dc68b8cd5b5', '0x240ca1cc77ac9c65',\n    '0x2de92c6f592b0275', '0x4a7484aa6ea6e483', '0x5cb0a9dcbd41fbd4', '0x76f988da831153b5',\n    '0x983e5152ee66dfab', '0xa831c66d2db43210', '0xb00327c898fb213f', '0xbf597fc7beef0ee4',\n    '0xc6e00bf33da88fc2', '0xd5a79147930aa725', '0x06ca6351e003826f', '0x142929670a0e6e70',\n    '0x27b70a8546d22ffc', '0x2e1b21385c26c926', '0x4d2c6dfc5ac42aed', '0x53380d139d95b3df',\n    '0x650a73548baf63de', '0x766a0abb3c77b2a8', '0x81c2c92e47edaee6', '0x92722c851482353b',\n    '0xa2bfe8a14cf10364', '0xa81a664bbc423001', '0xc24b8b70d0f89791', '0xc76c51a30654be30',\n    '0xd192e819d6ef5218', '0xd69906245565a910', '0xf40e35855771202a', '0x106aa07032bbd1b8',\n    '0x19a4c116b8d2d0c8', '0x1e376c085141ab53', '0x2748774cdf8eeb99', '0x34b0bcb5e19b48a8',\n    '0x391c0cb3c5c95a63', '0x4ed8aa4ae3418acb', '0x5b9cca4f7763e373', '0x682e6ff3d6b2b8a3',\n    '0x748f82ee5defb2fc', '0x78a5636f43172f60', '0x84c87814a1f0ab72', '0x8cc702081a6439ec',\n    '0x90befffa23631e28', '0xa4506cebde82bde9', '0xbef9a3f7b2c67915', '0xc67178f2e372532b',\n    '0xca273eceea26619c', '0xd186b8c721c0c207', '0xeada7dd6cde0eb1e', '0xf57d4f7fee6ed178',\n    '0x06f067aa72176fba', '0x0a637dc5a2c898a6', '0x113f9804bef90dae', '0x1b710b35131c471b',\n    '0x28db77f523047d84', '0x32caab7b40c72493', '0x3c9ebe0a15c9bebc', '0x431d67c49c100d4c',\n    '0x4cc5d4becb3e42b6', '0x597f299cfc657e2a', '0x5fcb6fab3ad6faec', '0x6c44198c4a475817'\n].map(n => BigInt(n))))();\nconst SHA512_Kh = /* @__PURE__ */ (() => K512[0])();\nconst SHA512_Kl = /* @__PURE__ */ (() => K512[1])();\n// Reusable temporary buffers\nconst SHA512_W_H = /* @__PURE__ */ new Uint32Array(80);\nconst SHA512_W_L = /* @__PURE__ */ new Uint32Array(80);\nclass SHA512 extends _md_js__WEBPACK_IMPORTED_MODULE_0__.HashMD {\n    constructor(outputLen = 64) {\n        super(128, outputLen, 16, false);\n        // We cannot use array here since array allows indexing by variable\n        // which means optimizer/compiler cannot use registers.\n        // h -- high 32 bits, l -- low 32 bits\n        this.Ah = _md_js__WEBPACK_IMPORTED_MODULE_0__.SHA512_IV[0] | 0;\n        this.Al = _md_js__WEBPACK_IMPORTED_MODULE_0__.SHA512_IV[1] | 0;\n        this.Bh = _md_js__WEBPACK_IMPORTED_MODULE_0__.SHA512_IV[2] | 0;\n        this.Bl = _md_js__WEBPACK_IMPORTED_MODULE_0__.SHA512_IV[3] | 0;\n        this.Ch = _md_js__WEBPACK_IMPORTED_MODULE_0__.SHA512_IV[4] | 0;\n        this.Cl = _md_js__WEBPACK_IMPORTED_MODULE_0__.SHA512_IV[5] | 0;\n        this.Dh = _md_js__WEBPACK_IMPORTED_MODULE_0__.SHA512_IV[6] | 0;\n        this.Dl = _md_js__WEBPACK_IMPORTED_MODULE_0__.SHA512_IV[7] | 0;\n        this.Eh = _md_js__WEBPACK_IMPORTED_MODULE_0__.SHA512_IV[8] | 0;\n        this.El = _md_js__WEBPACK_IMPORTED_MODULE_0__.SHA512_IV[9] | 0;\n        this.Fh = _md_js__WEBPACK_IMPORTED_MODULE_0__.SHA512_IV[10] | 0;\n        this.Fl = _md_js__WEBPACK_IMPORTED_MODULE_0__.SHA512_IV[11] | 0;\n        this.Gh = _md_js__WEBPACK_IMPORTED_MODULE_0__.SHA512_IV[12] | 0;\n        this.Gl = _md_js__WEBPACK_IMPORTED_MODULE_0__.SHA512_IV[13] | 0;\n        this.Hh = _md_js__WEBPACK_IMPORTED_MODULE_0__.SHA512_IV[14] | 0;\n        this.Hl = _md_js__WEBPACK_IMPORTED_MODULE_0__.SHA512_IV[15] | 0;\n    }\n    // prettier-ignore\n    get() {\n        const { Ah, Al, Bh, Bl, Ch, Cl, Dh, Dl, Eh, El, Fh, Fl, Gh, Gl, Hh, Hl } = this;\n        return [Ah, Al, Bh, Bl, Ch, Cl, Dh, Dl, Eh, El, Fh, Fl, Gh, Gl, Hh, Hl];\n    }\n    // prettier-ignore\n    set(Ah, Al, Bh, Bl, Ch, Cl, Dh, Dl, Eh, El, Fh, Fl, Gh, Gl, Hh, Hl) {\n        this.Ah = Ah | 0;\n        this.Al = Al | 0;\n        this.Bh = Bh | 0;\n        this.Bl = Bl | 0;\n        this.Ch = Ch | 0;\n        this.Cl = Cl | 0;\n        this.Dh = Dh | 0;\n        this.Dl = Dl | 0;\n        this.Eh = Eh | 0;\n        this.El = El | 0;\n        this.Fh = Fh | 0;\n        this.Fl = Fl | 0;\n        this.Gh = Gh | 0;\n        this.Gl = Gl | 0;\n        this.Hh = Hh | 0;\n        this.Hl = Hl | 0;\n    }\n    process(view, offset) {\n        // Extend the first 16 words into the remaining 64 words w[16..79] of the message schedule array\n        for (let i = 0; i < 16; i++, offset += 4) {\n            SHA512_W_H[i] = view.getUint32(offset);\n            SHA512_W_L[i] = view.getUint32((offset += 4));\n        }\n        for (let i = 16; i < 80; i++) {\n            // s0 := (w[i-15] rightrotate 1) xor (w[i-15] rightrotate 8) xor (w[i-15] rightshift 7)\n            const W15h = SHA512_W_H[i - 15] | 0;\n            const W15l = SHA512_W_L[i - 15] | 0;\n            const s0h = _u64_js__WEBPACK_IMPORTED_MODULE_2__.rotrSH(W15h, W15l, 1) ^ _u64_js__WEBPACK_IMPORTED_MODULE_2__.rotrSH(W15h, W15l, 8) ^ _u64_js__WEBPACK_IMPORTED_MODULE_2__.shrSH(W15h, W15l, 7);\n            const s0l = _u64_js__WEBPACK_IMPORTED_MODULE_2__.rotrSL(W15h, W15l, 1) ^ _u64_js__WEBPACK_IMPORTED_MODULE_2__.rotrSL(W15h, W15l, 8) ^ _u64_js__WEBPACK_IMPORTED_MODULE_2__.shrSL(W15h, W15l, 7);\n            // s1 := (w[i-2] rightrotate 19) xor (w[i-2] rightrotate 61) xor (w[i-2] rightshift 6)\n            const W2h = SHA512_W_H[i - 2] | 0;\n            const W2l = SHA512_W_L[i - 2] | 0;\n            const s1h = _u64_js__WEBPACK_IMPORTED_MODULE_2__.rotrSH(W2h, W2l, 19) ^ _u64_js__WEBPACK_IMPORTED_MODULE_2__.rotrBH(W2h, W2l, 61) ^ _u64_js__WEBPACK_IMPORTED_MODULE_2__.shrSH(W2h, W2l, 6);\n            const s1l = _u64_js__WEBPACK_IMPORTED_MODULE_2__.rotrSL(W2h, W2l, 19) ^ _u64_js__WEBPACK_IMPORTED_MODULE_2__.rotrBL(W2h, W2l, 61) ^ _u64_js__WEBPACK_IMPORTED_MODULE_2__.shrSL(W2h, W2l, 6);\n            // SHA256_W[i] = s0 + s1 + SHA256_W[i - 7] + SHA256_W[i - 16];\n            const SUMl = _u64_js__WEBPACK_IMPORTED_MODULE_2__.add4L(s0l, s1l, SHA512_W_L[i - 7], SHA512_W_L[i - 16]);\n            const SUMh = _u64_js__WEBPACK_IMPORTED_MODULE_2__.add4H(SUMl, s0h, s1h, SHA512_W_H[i - 7], SHA512_W_H[i - 16]);\n            SHA512_W_H[i] = SUMh | 0;\n            SHA512_W_L[i] = SUMl | 0;\n        }\n        let { Ah, Al, Bh, Bl, Ch, Cl, Dh, Dl, Eh, El, Fh, Fl, Gh, Gl, Hh, Hl } = this;\n        // Compression function main loop, 80 rounds\n        for (let i = 0; i < 80; i++) {\n            // S1 := (e rightrotate 14) xor (e rightrotate 18) xor (e rightrotate 41)\n            const sigma1h = _u64_js__WEBPACK_IMPORTED_MODULE_2__.rotrSH(Eh, El, 14) ^ _u64_js__WEBPACK_IMPORTED_MODULE_2__.rotrSH(Eh, El, 18) ^ _u64_js__WEBPACK_IMPORTED_MODULE_2__.rotrBH(Eh, El, 41);\n            const sigma1l = _u64_js__WEBPACK_IMPORTED_MODULE_2__.rotrSL(Eh, El, 14) ^ _u64_js__WEBPACK_IMPORTED_MODULE_2__.rotrSL(Eh, El, 18) ^ _u64_js__WEBPACK_IMPORTED_MODULE_2__.rotrBL(Eh, El, 41);\n            //const T1 = (H + sigma1 + Chi(E, F, G) + SHA256_K[i] + SHA256_W[i]) | 0;\n            const CHIh = (Eh & Fh) ^ (~Eh & Gh);\n            const CHIl = (El & Fl) ^ (~El & Gl);\n            // T1 = H + sigma1 + Chi(E, F, G) + SHA512_K[i] + SHA512_W[i]\n            // prettier-ignore\n            const T1ll = _u64_js__WEBPACK_IMPORTED_MODULE_2__.add5L(Hl, sigma1l, CHIl, SHA512_Kl[i], SHA512_W_L[i]);\n            const T1h = _u64_js__WEBPACK_IMPORTED_MODULE_2__.add5H(T1ll, Hh, sigma1h, CHIh, SHA512_Kh[i], SHA512_W_H[i]);\n            const T1l = T1ll | 0;\n            // S0 := (a rightrotate 28) xor (a rightrotate 34) xor (a rightrotate 39)\n            const sigma0h = _u64_js__WEBPACK_IMPORTED_MODULE_2__.rotrSH(Ah, Al, 28) ^ _u64_js__WEBPACK_IMPORTED_MODULE_2__.rotrBH(Ah, Al, 34) ^ _u64_js__WEBPACK_IMPORTED_MODULE_2__.rotrBH(Ah, Al, 39);\n            const sigma0l = _u64_js__WEBPACK_IMPORTED_MODULE_2__.rotrSL(Ah, Al, 28) ^ _u64_js__WEBPACK_IMPORTED_MODULE_2__.rotrBL(Ah, Al, 34) ^ _u64_js__WEBPACK_IMPORTED_MODULE_2__.rotrBL(Ah, Al, 39);\n            const MAJh = (Ah & Bh) ^ (Ah & Ch) ^ (Bh & Ch);\n            const MAJl = (Al & Bl) ^ (Al & Cl) ^ (Bl & Cl);\n            Hh = Gh | 0;\n            Hl = Gl | 0;\n            Gh = Fh | 0;\n            Gl = Fl | 0;\n            Fh = Eh | 0;\n            Fl = El | 0;\n            ({ h: Eh, l: El } = _u64_js__WEBPACK_IMPORTED_MODULE_2__.add(Dh | 0, Dl | 0, T1h | 0, T1l | 0));\n            Dh = Ch | 0;\n            Dl = Cl | 0;\n            Ch = Bh | 0;\n            Cl = Bl | 0;\n            Bh = Ah | 0;\n            Bl = Al | 0;\n            const All = _u64_js__WEBPACK_IMPORTED_MODULE_2__.add3L(T1l, sigma0l, MAJl);\n            Ah = _u64_js__WEBPACK_IMPORTED_MODULE_2__.add3H(All, T1h, sigma0h, MAJh);\n            Al = All | 0;\n        }\n        // Add the compressed chunk to the current hash value\n        ({ h: Ah, l: Al } = _u64_js__WEBPACK_IMPORTED_MODULE_2__.add(this.Ah | 0, this.Al | 0, Ah | 0, Al | 0));\n        ({ h: Bh, l: Bl } = _u64_js__WEBPACK_IMPORTED_MODULE_2__.add(this.Bh | 0, this.Bl | 0, Bh | 0, Bl | 0));\n        ({ h: Ch, l: Cl } = _u64_js__WEBPACK_IMPORTED_MODULE_2__.add(this.Ch | 0, this.Cl | 0, Ch | 0, Cl | 0));\n        ({ h: Dh, l: Dl } = _u64_js__WEBPACK_IMPORTED_MODULE_2__.add(this.Dh | 0, this.Dl | 0, Dh | 0, Dl | 0));\n        ({ h: Eh, l: El } = _u64_js__WEBPACK_IMPORTED_MODULE_2__.add(this.Eh | 0, this.El | 0, Eh | 0, El | 0));\n        ({ h: Fh, l: Fl } = _u64_js__WEBPACK_IMPORTED_MODULE_2__.add(this.Fh | 0, this.Fl | 0, Fh | 0, Fl | 0));\n        ({ h: Gh, l: Gl } = _u64_js__WEBPACK_IMPORTED_MODULE_2__.add(this.Gh | 0, this.Gl | 0, Gh | 0, Gl | 0));\n        ({ h: Hh, l: Hl } = _u64_js__WEBPACK_IMPORTED_MODULE_2__.add(this.Hh | 0, this.Hl | 0, Hh | 0, Hl | 0));\n        this.set(Ah, Al, Bh, Bl, Ch, Cl, Dh, Dl, Eh, El, Fh, Fl, Gh, Gl, Hh, Hl);\n    }\n    roundClean() {\n        (0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.clean)(SHA512_W_H, SHA512_W_L);\n    }\n    destroy() {\n        (0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.clean)(this.buffer);\n        this.set(0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0);\n    }\n}\nclass SHA384 extends SHA512 {\n    constructor() {\n        super(48);\n        this.Ah = _md_js__WEBPACK_IMPORTED_MODULE_0__.SHA384_IV[0] | 0;\n        this.Al = _md_js__WEBPACK_IMPORTED_MODULE_0__.SHA384_IV[1] | 0;\n        this.Bh = _md_js__WEBPACK_IMPORTED_MODULE_0__.SHA384_IV[2] | 0;\n        this.Bl = _md_js__WEBPACK_IMPORTED_MODULE_0__.SHA384_IV[3] | 0;\n        this.Ch = _md_js__WEBPACK_IMPORTED_MODULE_0__.SHA384_IV[4] | 0;\n        this.Cl = _md_js__WEBPACK_IMPORTED_MODULE_0__.SHA384_IV[5] | 0;\n        this.Dh = _md_js__WEBPACK_IMPORTED_MODULE_0__.SHA384_IV[6] | 0;\n        this.Dl = _md_js__WEBPACK_IMPORTED_MODULE_0__.SHA384_IV[7] | 0;\n        this.Eh = _md_js__WEBPACK_IMPORTED_MODULE_0__.SHA384_IV[8] | 0;\n        this.El = _md_js__WEBPACK_IMPORTED_MODULE_0__.SHA384_IV[9] | 0;\n        this.Fh = _md_js__WEBPACK_IMPORTED_MODULE_0__.SHA384_IV[10] | 0;\n        this.Fl = _md_js__WEBPACK_IMPORTED_MODULE_0__.SHA384_IV[11] | 0;\n        this.Gh = _md_js__WEBPACK_IMPORTED_MODULE_0__.SHA384_IV[12] | 0;\n        this.Gl = _md_js__WEBPACK_IMPORTED_MODULE_0__.SHA384_IV[13] | 0;\n        this.Hh = _md_js__WEBPACK_IMPORTED_MODULE_0__.SHA384_IV[14] | 0;\n        this.Hl = _md_js__WEBPACK_IMPORTED_MODULE_0__.SHA384_IV[15] | 0;\n    }\n}\n/**\n * Truncated SHA512/256 and SHA512/224.\n * SHA512_IV is XORed with 0xa5a5a5a5a5a5a5a5, then used as \"intermediary\" IV of SHA512/t.\n * Then t hashes string to produce result IV.\n * See `test/misc/sha2-gen-iv.js`.\n */\n/** SHA512/224 IV */\nconst T224_IV = /* @__PURE__ */ Uint32Array.from([\n    0x8c3d37c8, 0x19544da2, 0x73e19966, 0x89dcd4d6, 0x1dfab7ae, 0x32ff9c82, 0x679dd514, 0x582f9fcf,\n    0x0f6d2b69, 0x7bd44da8, 0x77e36f73, 0x04c48942, 0x3f9d85a8, 0x6a1d36c8, 0x1112e6ad, 0x91d692a1,\n]);\n/** SHA512/256 IV */\nconst T256_IV = /* @__PURE__ */ Uint32Array.from([\n    0x22312194, 0xfc2bf72c, 0x9f555fa3, 0xc84c64c2, 0x2393b86b, 0x6f53b151, 0x96387719, 0x5940eabd,\n    0x96283ee2, 0xa88effe3, 0xbe5e1e25, 0x53863992, 0x2b0199fc, 0x2c85b8aa, 0x0eb72ddc, 0x81c52ca2,\n]);\nclass SHA512_224 extends SHA512 {\n    constructor() {\n        super(28);\n        this.Ah = T224_IV[0] | 0;\n        this.Al = T224_IV[1] | 0;\n        this.Bh = T224_IV[2] | 0;\n        this.Bl = T224_IV[3] | 0;\n        this.Ch = T224_IV[4] | 0;\n        this.Cl = T224_IV[5] | 0;\n        this.Dh = T224_IV[6] | 0;\n        this.Dl = T224_IV[7] | 0;\n        this.Eh = T224_IV[8] | 0;\n        this.El = T224_IV[9] | 0;\n        this.Fh = T224_IV[10] | 0;\n        this.Fl = T224_IV[11] | 0;\n        this.Gh = T224_IV[12] | 0;\n        this.Gl = T224_IV[13] | 0;\n        this.Hh = T224_IV[14] | 0;\n        this.Hl = T224_IV[15] | 0;\n    }\n}\nclass SHA512_256 extends SHA512 {\n    constructor() {\n        super(32);\n        this.Ah = T256_IV[0] | 0;\n        this.Al = T256_IV[1] | 0;\n        this.Bh = T256_IV[2] | 0;\n        this.Bl = T256_IV[3] | 0;\n        this.Ch = T256_IV[4] | 0;\n        this.Cl = T256_IV[5] | 0;\n        this.Dh = T256_IV[6] | 0;\n        this.Dl = T256_IV[7] | 0;\n        this.Eh = T256_IV[8] | 0;\n        this.El = T256_IV[9] | 0;\n        this.Fh = T256_IV[10] | 0;\n        this.Fl = T256_IV[11] | 0;\n        this.Gh = T256_IV[12] | 0;\n        this.Gl = T256_IV[13] | 0;\n        this.Hh = T256_IV[14] | 0;\n        this.Hl = T256_IV[15] | 0;\n    }\n}\n/**\n * SHA2-256 hash function from RFC 4634.\n *\n * It is the fastest JS hash, even faster than Blake3.\n * To break sha256 using birthday attack, attackers need to try 2^128 hashes.\n * BTC network is doing 2^70 hashes/sec (2^95 hashes/year) as per 2025.\n */\nconst sha256 = /* @__PURE__ */ (0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.createHasher)(() => new SHA256());\n/** SHA2-224 hash function from RFC 4634 */\nconst sha224 = /* @__PURE__ */ (0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.createHasher)(() => new SHA224());\n/** SHA2-512 hash function from RFC 4634. */\nconst sha512 = /* @__PURE__ */ (0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.createHasher)(() => new SHA512());\n/** SHA2-384 hash function from RFC 4634. */\nconst sha384 = /* @__PURE__ */ (0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.createHasher)(() => new SHA384());\n/**\n * SHA2-512/256 \"truncated\" hash function, with improved resistance to length extension attacks.\n * See the paper on [truncated SHA512](https://eprint.iacr.org/2010/548.pdf).\n */\nconst sha512_256 = /* @__PURE__ */ (0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.createHasher)(() => new SHA512_256());\n/**\n * SHA2-512/224 \"truncated\" hash function, with improved resistance to length extension attacks.\n * See the paper on [truncated SHA512](https://eprint.iacr.org/2010/548.pdf).\n */\nconst sha512_224 = /* @__PURE__ */ (0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.createHasher)(() => new SHA512_224());\n//# sourceMappingURL=sha2.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@noble/hashes/esm/sha2.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@noble/hashes/esm/sha256.js":
/*!**************************************************!*\
  !*** ./node_modules/@noble/hashes/esm/sha256.js ***!
  \**************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SHA224: () => (/* binding */ SHA224),\n/* harmony export */   SHA256: () => (/* binding */ SHA256),\n/* harmony export */   sha224: () => (/* binding */ sha224),\n/* harmony export */   sha256: () => (/* binding */ sha256)\n/* harmony export */ });\n/* harmony import */ var _sha2_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./sha2.js */ \"(ssr)/./node_modules/@noble/hashes/esm/sha2.js\");\n/**\n * SHA2-256 a.k.a. sha256. In JS, it is the fastest hash, even faster than Blake3.\n *\n * To break sha256 using birthday attack, attackers need to try 2^128 hashes.\n * BTC network is doing 2^70 hashes/sec (2^95 hashes/year) as per 2025.\n *\n * Check out [FIPS 180-4](https://nvlpubs.nist.gov/nistpubs/FIPS/NIST.FIPS.180-4.pdf).\n * @module\n * @deprecated\n */\n\n/** @deprecated Use import from `noble/hashes/sha2` module */\nconst SHA256 = _sha2_js__WEBPACK_IMPORTED_MODULE_0__.SHA256;\n/** @deprecated Use import from `noble/hashes/sha2` module */\nconst sha256 = _sha2_js__WEBPACK_IMPORTED_MODULE_0__.sha256;\n/** @deprecated Use import from `noble/hashes/sha2` module */\nconst SHA224 = _sha2_js__WEBPACK_IMPORTED_MODULE_0__.SHA224;\n/** @deprecated Use import from `noble/hashes/sha2` module */\nconst sha224 = _sha2_js__WEBPACK_IMPORTED_MODULE_0__.sha224;\n//# sourceMappingURL=sha256.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQG5vYmxlL2hhc2hlcy9lc20vc2hhMjU2LmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7O0FBQUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDd0c7QUFDeEc7QUFDTyxlQUFlLDRDQUFPO0FBQzdCO0FBQ08sZUFBZSw0Q0FBTztBQUM3QjtBQUNPLGVBQWUsNENBQU87QUFDN0I7QUFDTyxlQUFlLDRDQUFPO0FBQzdCIiwic291cmNlcyI6WyJGOlxcQUkgY2hhaW5cXGNoYWluc2lnaHQtcHJvZHVjdGlvblxcbm9kZV9tb2R1bGVzXFxAbm9ibGVcXGhhc2hlc1xcZXNtXFxzaGEyNTYuanMiXSwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBTSEEyLTI1NiBhLmsuYS4gc2hhMjU2LiBJbiBKUywgaXQgaXMgdGhlIGZhc3Rlc3QgaGFzaCwgZXZlbiBmYXN0ZXIgdGhhbiBCbGFrZTMuXG4gKlxuICogVG8gYnJlYWsgc2hhMjU2IHVzaW5nIGJpcnRoZGF5IGF0dGFjaywgYXR0YWNrZXJzIG5lZWQgdG8gdHJ5IDJeMTI4IGhhc2hlcy5cbiAqIEJUQyBuZXR3b3JrIGlzIGRvaW5nIDJeNzAgaGFzaGVzL3NlYyAoMl45NSBoYXNoZXMveWVhcikgYXMgcGVyIDIwMjUuXG4gKlxuICogQ2hlY2sgb3V0IFtGSVBTIDE4MC00XShodHRwczovL252bHB1YnMubmlzdC5nb3YvbmlzdHB1YnMvRklQUy9OSVNULkZJUFMuMTgwLTQucGRmKS5cbiAqIEBtb2R1bGVcbiAqIEBkZXByZWNhdGVkXG4gKi9cbmltcG9ydCB7IFNIQTIyNCBhcyBTSEEyMjRuLCBzaGEyMjQgYXMgc2hhMjI0biwgU0hBMjU2IGFzIFNIQTI1Nm4sIHNoYTI1NiBhcyBzaGEyNTZuLCB9IGZyb20gXCIuL3NoYTIuanNcIjtcbi8qKiBAZGVwcmVjYXRlZCBVc2UgaW1wb3J0IGZyb20gYG5vYmxlL2hhc2hlcy9zaGEyYCBtb2R1bGUgKi9cbmV4cG9ydCBjb25zdCBTSEEyNTYgPSBTSEEyNTZuO1xuLyoqIEBkZXByZWNhdGVkIFVzZSBpbXBvcnQgZnJvbSBgbm9ibGUvaGFzaGVzL3NoYTJgIG1vZHVsZSAqL1xuZXhwb3J0IGNvbnN0IHNoYTI1NiA9IHNoYTI1Nm47XG4vKiogQGRlcHJlY2F0ZWQgVXNlIGltcG9ydCBmcm9tIGBub2JsZS9oYXNoZXMvc2hhMmAgbW9kdWxlICovXG5leHBvcnQgY29uc3QgU0hBMjI0ID0gU0hBMjI0bjtcbi8qKiBAZGVwcmVjYXRlZCBVc2UgaW1wb3J0IGZyb20gYG5vYmxlL2hhc2hlcy9zaGEyYCBtb2R1bGUgKi9cbmV4cG9ydCBjb25zdCBzaGEyMjQgPSBzaGEyMjRuO1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9c2hhMjU2LmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@noble/hashes/esm/sha256.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@noble/hashes/esm/sha3.js":
/*!************************************************!*\
  !*** ./node_modules/@noble/hashes/esm/sha3.js ***!
  \************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Keccak: () => (/* binding */ Keccak),\n/* harmony export */   keccakP: () => (/* binding */ keccakP),\n/* harmony export */   keccak_224: () => (/* binding */ keccak_224),\n/* harmony export */   keccak_256: () => (/* binding */ keccak_256),\n/* harmony export */   keccak_384: () => (/* binding */ keccak_384),\n/* harmony export */   keccak_512: () => (/* binding */ keccak_512),\n/* harmony export */   sha3_224: () => (/* binding */ sha3_224),\n/* harmony export */   sha3_256: () => (/* binding */ sha3_256),\n/* harmony export */   sha3_384: () => (/* binding */ sha3_384),\n/* harmony export */   sha3_512: () => (/* binding */ sha3_512),\n/* harmony export */   shake128: () => (/* binding */ shake128),\n/* harmony export */   shake256: () => (/* binding */ shake256)\n/* harmony export */ });\n/* harmony import */ var _u64_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./_u64.js */ \"(ssr)/./node_modules/@noble/hashes/esm/_u64.js\");\n/* harmony import */ var _utils_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./utils.js */ \"(ssr)/./node_modules/@noble/hashes/esm/utils.js\");\n/**\n * SHA3 (keccak) hash function, based on a new \"Sponge function\" design.\n * Different from older hashes, the internal state is bigger than output size.\n *\n * Check out [FIPS-202](https://nvlpubs.nist.gov/nistpubs/FIPS/NIST.FIPS.202.pdf),\n * [Website](https://keccak.team/keccak.html),\n * [the differences between SHA-3 and Keccak](https://crypto.stackexchange.com/questions/15727/what-are-the-key-differences-between-the-draft-sha-3-standard-and-the-keccak-sub).\n *\n * Check out `sha3-addons` module for cSHAKE, k12, and others.\n * @module\n */\n\n// prettier-ignore\n\n// No __PURE__ annotations in sha3 header:\n// EVERYTHING is in fact used on every export.\n// Various per round constants calculations\nconst _0n = BigInt(0);\nconst _1n = BigInt(1);\nconst _2n = BigInt(2);\nconst _7n = BigInt(7);\nconst _256n = BigInt(256);\nconst _0x71n = BigInt(0x71);\nconst SHA3_PI = [];\nconst SHA3_ROTL = [];\nconst _SHA3_IOTA = [];\nfor (let round = 0, R = _1n, x = 1, y = 0; round < 24; round++) {\n    // Pi\n    [x, y] = [y, (2 * x + 3 * y) % 5];\n    SHA3_PI.push(2 * (5 * y + x));\n    // Rotational\n    SHA3_ROTL.push((((round + 1) * (round + 2)) / 2) % 64);\n    // Iota\n    let t = _0n;\n    for (let j = 0; j < 7; j++) {\n        R = ((R << _1n) ^ ((R >> _7n) * _0x71n)) % _256n;\n        if (R & _2n)\n            t ^= _1n << ((_1n << /* @__PURE__ */ BigInt(j)) - _1n);\n    }\n    _SHA3_IOTA.push(t);\n}\nconst IOTAS = (0,_u64_js__WEBPACK_IMPORTED_MODULE_0__.split)(_SHA3_IOTA, true);\nconst SHA3_IOTA_H = IOTAS[0];\nconst SHA3_IOTA_L = IOTAS[1];\n// Left rotation (without 0, 32, 64)\nconst rotlH = (h, l, s) => (s > 32 ? (0,_u64_js__WEBPACK_IMPORTED_MODULE_0__.rotlBH)(h, l, s) : (0,_u64_js__WEBPACK_IMPORTED_MODULE_0__.rotlSH)(h, l, s));\nconst rotlL = (h, l, s) => (s > 32 ? (0,_u64_js__WEBPACK_IMPORTED_MODULE_0__.rotlBL)(h, l, s) : (0,_u64_js__WEBPACK_IMPORTED_MODULE_0__.rotlSL)(h, l, s));\n/** `keccakf1600` internal function, additionally allows to adjust round count. */\nfunction keccakP(s, rounds = 24) {\n    const B = new Uint32Array(5 * 2);\n    // NOTE: all indices are x2 since we store state as u32 instead of u64 (bigints to slow in js)\n    for (let round = 24 - rounds; round < 24; round++) {\n        // Theta θ\n        for (let x = 0; x < 10; x++)\n            B[x] = s[x] ^ s[x + 10] ^ s[x + 20] ^ s[x + 30] ^ s[x + 40];\n        for (let x = 0; x < 10; x += 2) {\n            const idx1 = (x + 8) % 10;\n            const idx0 = (x + 2) % 10;\n            const B0 = B[idx0];\n            const B1 = B[idx0 + 1];\n            const Th = rotlH(B0, B1, 1) ^ B[idx1];\n            const Tl = rotlL(B0, B1, 1) ^ B[idx1 + 1];\n            for (let y = 0; y < 50; y += 10) {\n                s[x + y] ^= Th;\n                s[x + y + 1] ^= Tl;\n            }\n        }\n        // Rho (ρ) and Pi (π)\n        let curH = s[2];\n        let curL = s[3];\n        for (let t = 0; t < 24; t++) {\n            const shift = SHA3_ROTL[t];\n            const Th = rotlH(curH, curL, shift);\n            const Tl = rotlL(curH, curL, shift);\n            const PI = SHA3_PI[t];\n            curH = s[PI];\n            curL = s[PI + 1];\n            s[PI] = Th;\n            s[PI + 1] = Tl;\n        }\n        // Chi (χ)\n        for (let y = 0; y < 50; y += 10) {\n            for (let x = 0; x < 10; x++)\n                B[x] = s[y + x];\n            for (let x = 0; x < 10; x++)\n                s[y + x] ^= ~B[(x + 2) % 10] & B[(x + 4) % 10];\n        }\n        // Iota (ι)\n        s[0] ^= SHA3_IOTA_H[round];\n        s[1] ^= SHA3_IOTA_L[round];\n    }\n    (0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.clean)(B);\n}\n/** Keccak sponge function. */\nclass Keccak extends _utils_js__WEBPACK_IMPORTED_MODULE_1__.Hash {\n    // NOTE: we accept arguments in bytes instead of bits here.\n    constructor(blockLen, suffix, outputLen, enableXOF = false, rounds = 24) {\n        super();\n        this.pos = 0;\n        this.posOut = 0;\n        this.finished = false;\n        this.destroyed = false;\n        this.enableXOF = false;\n        this.blockLen = blockLen;\n        this.suffix = suffix;\n        this.outputLen = outputLen;\n        this.enableXOF = enableXOF;\n        this.rounds = rounds;\n        // Can be passed from user as dkLen\n        (0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.anumber)(outputLen);\n        // 1600 = 5x5 matrix of 64bit.  1600 bits === 200 bytes\n        // 0 < blockLen < 200\n        if (!(0 < blockLen && blockLen < 200))\n            throw new Error('only keccak-f1600 function is supported');\n        this.state = new Uint8Array(200);\n        this.state32 = (0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.u32)(this.state);\n    }\n    clone() {\n        return this._cloneInto();\n    }\n    keccak() {\n        (0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.swap32IfBE)(this.state32);\n        keccakP(this.state32, this.rounds);\n        (0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.swap32IfBE)(this.state32);\n        this.posOut = 0;\n        this.pos = 0;\n    }\n    update(data) {\n        (0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.aexists)(this);\n        data = (0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.toBytes)(data);\n        (0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.abytes)(data);\n        const { blockLen, state } = this;\n        const len = data.length;\n        for (let pos = 0; pos < len;) {\n            const take = Math.min(blockLen - this.pos, len - pos);\n            for (let i = 0; i < take; i++)\n                state[this.pos++] ^= data[pos++];\n            if (this.pos === blockLen)\n                this.keccak();\n        }\n        return this;\n    }\n    finish() {\n        if (this.finished)\n            return;\n        this.finished = true;\n        const { state, suffix, pos, blockLen } = this;\n        // Do the padding\n        state[pos] ^= suffix;\n        if ((suffix & 0x80) !== 0 && pos === blockLen - 1)\n            this.keccak();\n        state[blockLen - 1] ^= 0x80;\n        this.keccak();\n    }\n    writeInto(out) {\n        (0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.aexists)(this, false);\n        (0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.abytes)(out);\n        this.finish();\n        const bufferOut = this.state;\n        const { blockLen } = this;\n        for (let pos = 0, len = out.length; pos < len;) {\n            if (this.posOut >= blockLen)\n                this.keccak();\n            const take = Math.min(blockLen - this.posOut, len - pos);\n            out.set(bufferOut.subarray(this.posOut, this.posOut + take), pos);\n            this.posOut += take;\n            pos += take;\n        }\n        return out;\n    }\n    xofInto(out) {\n        // Sha3/Keccak usage with XOF is probably mistake, only SHAKE instances can do XOF\n        if (!this.enableXOF)\n            throw new Error('XOF is not possible for this instance');\n        return this.writeInto(out);\n    }\n    xof(bytes) {\n        (0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.anumber)(bytes);\n        return this.xofInto(new Uint8Array(bytes));\n    }\n    digestInto(out) {\n        (0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.aoutput)(out, this);\n        if (this.finished)\n            throw new Error('digest() was already called');\n        this.writeInto(out);\n        this.destroy();\n        return out;\n    }\n    digest() {\n        return this.digestInto(new Uint8Array(this.outputLen));\n    }\n    destroy() {\n        this.destroyed = true;\n        (0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.clean)(this.state);\n    }\n    _cloneInto(to) {\n        const { blockLen, suffix, outputLen, rounds, enableXOF } = this;\n        to || (to = new Keccak(blockLen, suffix, outputLen, enableXOF, rounds));\n        to.state32.set(this.state32);\n        to.pos = this.pos;\n        to.posOut = this.posOut;\n        to.finished = this.finished;\n        to.rounds = rounds;\n        // Suffix can change in cSHAKE\n        to.suffix = suffix;\n        to.outputLen = outputLen;\n        to.enableXOF = enableXOF;\n        to.destroyed = this.destroyed;\n        return to;\n    }\n}\nconst gen = (suffix, blockLen, outputLen) => (0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.createHasher)(() => new Keccak(blockLen, suffix, outputLen));\n/** SHA3-224 hash function. */\nconst sha3_224 = /* @__PURE__ */ (() => gen(0x06, 144, 224 / 8))();\n/** SHA3-256 hash function. Different from keccak-256. */\nconst sha3_256 = /* @__PURE__ */ (() => gen(0x06, 136, 256 / 8))();\n/** SHA3-384 hash function. */\nconst sha3_384 = /* @__PURE__ */ (() => gen(0x06, 104, 384 / 8))();\n/** SHA3-512 hash function. */\nconst sha3_512 = /* @__PURE__ */ (() => gen(0x06, 72, 512 / 8))();\n/** keccak-224 hash function. */\nconst keccak_224 = /* @__PURE__ */ (() => gen(0x01, 144, 224 / 8))();\n/** keccak-256 hash function. Different from SHA3-256. */\nconst keccak_256 = /* @__PURE__ */ (() => gen(0x01, 136, 256 / 8))();\n/** keccak-384 hash function. */\nconst keccak_384 = /* @__PURE__ */ (() => gen(0x01, 104, 384 / 8))();\n/** keccak-512 hash function. */\nconst keccak_512 = /* @__PURE__ */ (() => gen(0x01, 72, 512 / 8))();\nconst genShake = (suffix, blockLen, outputLen) => (0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.createXOFer)((opts = {}) => new Keccak(blockLen, suffix, opts.dkLen === undefined ? outputLen : opts.dkLen, true));\n/** SHAKE128 XOF with 128-bit security. */\nconst shake128 = /* @__PURE__ */ (() => genShake(0x1f, 168, 128 / 8))();\n/** SHAKE256 XOF with 256-bit security. */\nconst shake256 = /* @__PURE__ */ (() => genShake(0x1f, 136, 256 / 8))();\n//# sourceMappingURL=sha3.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@noble/hashes/esm/sha3.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@noble/hashes/esm/utils.js":
/*!*************************************************!*\
  !*** ./node_modules/@noble/hashes/esm/utils.js ***!
  \*************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Hash: () => (/* binding */ Hash),\n/* harmony export */   abytes: () => (/* binding */ abytes),\n/* harmony export */   aexists: () => (/* binding */ aexists),\n/* harmony export */   ahash: () => (/* binding */ ahash),\n/* harmony export */   anumber: () => (/* binding */ anumber),\n/* harmony export */   aoutput: () => (/* binding */ aoutput),\n/* harmony export */   asyncLoop: () => (/* binding */ asyncLoop),\n/* harmony export */   byteSwap: () => (/* binding */ byteSwap),\n/* harmony export */   byteSwap32: () => (/* binding */ byteSwap32),\n/* harmony export */   byteSwapIfBE: () => (/* binding */ byteSwapIfBE),\n/* harmony export */   bytesToHex: () => (/* binding */ bytesToHex),\n/* harmony export */   bytesToUtf8: () => (/* binding */ bytesToUtf8),\n/* harmony export */   checkOpts: () => (/* binding */ checkOpts),\n/* harmony export */   clean: () => (/* binding */ clean),\n/* harmony export */   concatBytes: () => (/* binding */ concatBytes),\n/* harmony export */   createHasher: () => (/* binding */ createHasher),\n/* harmony export */   createOptHasher: () => (/* binding */ createOptHasher),\n/* harmony export */   createView: () => (/* binding */ createView),\n/* harmony export */   createXOFer: () => (/* binding */ createXOFer),\n/* harmony export */   hexToBytes: () => (/* binding */ hexToBytes),\n/* harmony export */   isBytes: () => (/* binding */ isBytes),\n/* harmony export */   isLE: () => (/* binding */ isLE),\n/* harmony export */   kdfInputToBytes: () => (/* binding */ kdfInputToBytes),\n/* harmony export */   nextTick: () => (/* binding */ nextTick),\n/* harmony export */   randomBytes: () => (/* binding */ randomBytes),\n/* harmony export */   rotl: () => (/* binding */ rotl),\n/* harmony export */   rotr: () => (/* binding */ rotr),\n/* harmony export */   swap32IfBE: () => (/* binding */ swap32IfBE),\n/* harmony export */   swap8IfBE: () => (/* binding */ swap8IfBE),\n/* harmony export */   toBytes: () => (/* binding */ toBytes),\n/* harmony export */   u32: () => (/* binding */ u32),\n/* harmony export */   u8: () => (/* binding */ u8),\n/* harmony export */   utf8ToBytes: () => (/* binding */ utf8ToBytes),\n/* harmony export */   wrapConstructor: () => (/* binding */ wrapConstructor),\n/* harmony export */   wrapConstructorWithOpts: () => (/* binding */ wrapConstructorWithOpts),\n/* harmony export */   wrapXOFConstructorWithOpts: () => (/* binding */ wrapXOFConstructorWithOpts)\n/* harmony export */ });\n/* harmony import */ var _noble_hashes_crypto__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @noble/hashes/crypto */ \"(ssr)/./node_modules/@noble/hashes/esm/cryptoNode.js\");\n/**\n * Utilities for hex, bytes, CSPRNG.\n * @module\n */\n/*! noble-hashes - MIT License (c) 2022 Paul Miller (paulmillr.com) */\n// We use WebCrypto aka globalThis.crypto, which exists in browsers and node.js 16+.\n// node.js versions earlier than v19 don't declare it in global scope.\n// For node.js, package.json#exports field mapping rewrites import\n// from `crypto` to `cryptoNode`, which imports native module.\n// Makes the utils un-importable in browsers without a bundler.\n// Once node.js 18 is deprecated (2025-04-30), we can just drop the import.\n\n/** Checks if something is Uint8Array. Be careful: nodejs Buffer will return true. */\nfunction isBytes(a) {\n    return a instanceof Uint8Array || (ArrayBuffer.isView(a) && a.constructor.name === 'Uint8Array');\n}\n/** Asserts something is positive integer. */\nfunction anumber(n) {\n    if (!Number.isSafeInteger(n) || n < 0)\n        throw new Error('positive integer expected, got ' + n);\n}\n/** Asserts something is Uint8Array. */\nfunction abytes(b, ...lengths) {\n    if (!isBytes(b))\n        throw new Error('Uint8Array expected');\n    if (lengths.length > 0 && !lengths.includes(b.length))\n        throw new Error('Uint8Array expected of length ' + lengths + ', got length=' + b.length);\n}\n/** Asserts something is hash */\nfunction ahash(h) {\n    if (typeof h !== 'function' || typeof h.create !== 'function')\n        throw new Error('Hash should be wrapped by utils.createHasher');\n    anumber(h.outputLen);\n    anumber(h.blockLen);\n}\n/** Asserts a hash instance has not been destroyed / finished */\nfunction aexists(instance, checkFinished = true) {\n    if (instance.destroyed)\n        throw new Error('Hash instance has been destroyed');\n    if (checkFinished && instance.finished)\n        throw new Error('Hash#digest() has already been called');\n}\n/** Asserts output is properly-sized byte array */\nfunction aoutput(out, instance) {\n    abytes(out);\n    const min = instance.outputLen;\n    if (out.length < min) {\n        throw new Error('digestInto() expects output buffer of length at least ' + min);\n    }\n}\n/** Cast u8 / u16 / u32 to u8. */\nfunction u8(arr) {\n    return new Uint8Array(arr.buffer, arr.byteOffset, arr.byteLength);\n}\n/** Cast u8 / u16 / u32 to u32. */\nfunction u32(arr) {\n    return new Uint32Array(arr.buffer, arr.byteOffset, Math.floor(arr.byteLength / 4));\n}\n/** Zeroize a byte array. Warning: JS provides no guarantees. */\nfunction clean(...arrays) {\n    for (let i = 0; i < arrays.length; i++) {\n        arrays[i].fill(0);\n    }\n}\n/** Create DataView of an array for easy byte-level manipulation. */\nfunction createView(arr) {\n    return new DataView(arr.buffer, arr.byteOffset, arr.byteLength);\n}\n/** The rotate right (circular right shift) operation for uint32 */\nfunction rotr(word, shift) {\n    return (word << (32 - shift)) | (word >>> shift);\n}\n/** The rotate left (circular left shift) operation for uint32 */\nfunction rotl(word, shift) {\n    return (word << shift) | ((word >>> (32 - shift)) >>> 0);\n}\n/** Is current platform little-endian? Most are. Big-Endian platform: IBM */\nconst isLE = /* @__PURE__ */ (() => new Uint8Array(new Uint32Array([0x11223344]).buffer)[0] === 0x44)();\n/** The byte swap operation for uint32 */\nfunction byteSwap(word) {\n    return (((word << 24) & 0xff000000) |\n        ((word << 8) & 0xff0000) |\n        ((word >>> 8) & 0xff00) |\n        ((word >>> 24) & 0xff));\n}\n/** Conditionally byte swap if on a big-endian platform */\nconst swap8IfBE = isLE\n    ? (n) => n\n    : (n) => byteSwap(n);\n/** @deprecated */\nconst byteSwapIfBE = swap8IfBE;\n/** In place byte swap for Uint32Array */\nfunction byteSwap32(arr) {\n    for (let i = 0; i < arr.length; i++) {\n        arr[i] = byteSwap(arr[i]);\n    }\n    return arr;\n}\nconst swap32IfBE = isLE\n    ? (u) => u\n    : byteSwap32;\n// Built-in hex conversion https://caniuse.com/mdn-javascript_builtins_uint8array_fromhex\nconst hasHexBuiltin = /* @__PURE__ */ (() => \n// @ts-ignore\ntypeof Uint8Array.from([]).toHex === 'function' && typeof Uint8Array.fromHex === 'function')();\n// Array where index 0xf0 (240) is mapped to string 'f0'\nconst hexes = /* @__PURE__ */ Array.from({ length: 256 }, (_, i) => i.toString(16).padStart(2, '0'));\n/**\n * Convert byte array to hex string. Uses built-in function, when available.\n * @example bytesToHex(Uint8Array.from([0xca, 0xfe, 0x01, 0x23])) // 'cafe0123'\n */\nfunction bytesToHex(bytes) {\n    abytes(bytes);\n    // @ts-ignore\n    if (hasHexBuiltin)\n        return bytes.toHex();\n    // pre-caching improves the speed 6x\n    let hex = '';\n    for (let i = 0; i < bytes.length; i++) {\n        hex += hexes[bytes[i]];\n    }\n    return hex;\n}\n// We use optimized technique to convert hex string to byte array\nconst asciis = { _0: 48, _9: 57, A: 65, F: 70, a: 97, f: 102 };\nfunction asciiToBase16(ch) {\n    if (ch >= asciis._0 && ch <= asciis._9)\n        return ch - asciis._0; // '2' => 50-48\n    if (ch >= asciis.A && ch <= asciis.F)\n        return ch - (asciis.A - 10); // 'B' => 66-(65-10)\n    if (ch >= asciis.a && ch <= asciis.f)\n        return ch - (asciis.a - 10); // 'b' => 98-(97-10)\n    return;\n}\n/**\n * Convert hex string to byte array. Uses built-in function, when available.\n * @example hexToBytes('cafe0123') // Uint8Array.from([0xca, 0xfe, 0x01, 0x23])\n */\nfunction hexToBytes(hex) {\n    if (typeof hex !== 'string')\n        throw new Error('hex string expected, got ' + typeof hex);\n    // @ts-ignore\n    if (hasHexBuiltin)\n        return Uint8Array.fromHex(hex);\n    const hl = hex.length;\n    const al = hl / 2;\n    if (hl % 2)\n        throw new Error('hex string expected, got unpadded hex of length ' + hl);\n    const array = new Uint8Array(al);\n    for (let ai = 0, hi = 0; ai < al; ai++, hi += 2) {\n        const n1 = asciiToBase16(hex.charCodeAt(hi));\n        const n2 = asciiToBase16(hex.charCodeAt(hi + 1));\n        if (n1 === undefined || n2 === undefined) {\n            const char = hex[hi] + hex[hi + 1];\n            throw new Error('hex string expected, got non-hex character \"' + char + '\" at index ' + hi);\n        }\n        array[ai] = n1 * 16 + n2; // multiply first octet, e.g. 'a3' => 10*16+3 => 160 + 3 => 163\n    }\n    return array;\n}\n/**\n * There is no setImmediate in browser and setTimeout is slow.\n * Call of async fn will return Promise, which will be fullfiled only on\n * next scheduler queue processing step and this is exactly what we need.\n */\nconst nextTick = async () => { };\n/** Returns control to thread each 'tick' ms to avoid blocking. */\nasync function asyncLoop(iters, tick, cb) {\n    let ts = Date.now();\n    for (let i = 0; i < iters; i++) {\n        cb(i);\n        // Date.now() is not monotonic, so in case if clock goes backwards we return return control too\n        const diff = Date.now() - ts;\n        if (diff >= 0 && diff < tick)\n            continue;\n        await nextTick();\n        ts += diff;\n    }\n}\n/**\n * Converts string to bytes using UTF8 encoding.\n * @example utf8ToBytes('abc') // Uint8Array.from([97, 98, 99])\n */\nfunction utf8ToBytes(str) {\n    if (typeof str !== 'string')\n        throw new Error('string expected');\n    return new Uint8Array(new TextEncoder().encode(str)); // https://bugzil.la/1681809\n}\n/**\n * Converts bytes to string using UTF8 encoding.\n * @example bytesToUtf8(Uint8Array.from([97, 98, 99])) // 'abc'\n */\nfunction bytesToUtf8(bytes) {\n    return new TextDecoder().decode(bytes);\n}\n/**\n * Normalizes (non-hex) string or Uint8Array to Uint8Array.\n * Warning: when Uint8Array is passed, it would NOT get copied.\n * Keep in mind for future mutable operations.\n */\nfunction toBytes(data) {\n    if (typeof data === 'string')\n        data = utf8ToBytes(data);\n    abytes(data);\n    return data;\n}\n/**\n * Helper for KDFs: consumes uint8array or string.\n * When string is passed, does utf8 decoding, using TextDecoder.\n */\nfunction kdfInputToBytes(data) {\n    if (typeof data === 'string')\n        data = utf8ToBytes(data);\n    abytes(data);\n    return data;\n}\n/** Copies several Uint8Arrays into one. */\nfunction concatBytes(...arrays) {\n    let sum = 0;\n    for (let i = 0; i < arrays.length; i++) {\n        const a = arrays[i];\n        abytes(a);\n        sum += a.length;\n    }\n    const res = new Uint8Array(sum);\n    for (let i = 0, pad = 0; i < arrays.length; i++) {\n        const a = arrays[i];\n        res.set(a, pad);\n        pad += a.length;\n    }\n    return res;\n}\nfunction checkOpts(defaults, opts) {\n    if (opts !== undefined && {}.toString.call(opts) !== '[object Object]')\n        throw new Error('options should be object or undefined');\n    const merged = Object.assign(defaults, opts);\n    return merged;\n}\n/** For runtime check if class implements interface */\nclass Hash {\n}\n/** Wraps hash function, creating an interface on top of it */\nfunction createHasher(hashCons) {\n    const hashC = (msg) => hashCons().update(toBytes(msg)).digest();\n    const tmp = hashCons();\n    hashC.outputLen = tmp.outputLen;\n    hashC.blockLen = tmp.blockLen;\n    hashC.create = () => hashCons();\n    return hashC;\n}\nfunction createOptHasher(hashCons) {\n    const hashC = (msg, opts) => hashCons(opts).update(toBytes(msg)).digest();\n    const tmp = hashCons({});\n    hashC.outputLen = tmp.outputLen;\n    hashC.blockLen = tmp.blockLen;\n    hashC.create = (opts) => hashCons(opts);\n    return hashC;\n}\nfunction createXOFer(hashCons) {\n    const hashC = (msg, opts) => hashCons(opts).update(toBytes(msg)).digest();\n    const tmp = hashCons({});\n    hashC.outputLen = tmp.outputLen;\n    hashC.blockLen = tmp.blockLen;\n    hashC.create = (opts) => hashCons(opts);\n    return hashC;\n}\nconst wrapConstructor = createHasher;\nconst wrapConstructorWithOpts = createOptHasher;\nconst wrapXOFConstructorWithOpts = createXOFer;\n/** Cryptographically secure PRNG. Uses internal OS-level `crypto.getRandomValues`. */\nfunction randomBytes(bytesLength = 32) {\n    if (_noble_hashes_crypto__WEBPACK_IMPORTED_MODULE_0__.crypto && typeof _noble_hashes_crypto__WEBPACK_IMPORTED_MODULE_0__.crypto.getRandomValues === 'function') {\n        return _noble_hashes_crypto__WEBPACK_IMPORTED_MODULE_0__.crypto.getRandomValues(new Uint8Array(bytesLength));\n    }\n    // Legacy Node.js compatibility\n    if (_noble_hashes_crypto__WEBPACK_IMPORTED_MODULE_0__.crypto && typeof _noble_hashes_crypto__WEBPACK_IMPORTED_MODULE_0__.crypto.randomBytes === 'function') {\n        return Uint8Array.from(_noble_hashes_crypto__WEBPACK_IMPORTED_MODULE_0__.crypto.randomBytes(bytesLength));\n    }\n    throw new Error('crypto.getRandomValues must be defined');\n}\n//# sourceMappingURL=utils.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@noble/hashes/esm/utils.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@noble/hashes/hmac.js":
/*!********************************************!*\
  !*** ./node_modules/@noble/hashes/hmac.js ***!
  \********************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.hmac = exports.HMAC = void 0;\n/**\n * HMAC: RFC2104 message authentication code.\n * @module\n */\nconst utils_ts_1 = __webpack_require__(/*! ./utils.js */ \"(ssr)/./node_modules/@noble/hashes/utils.js\");\nclass HMAC extends utils_ts_1.Hash {\n    constructor(hash, _key) {\n        super();\n        this.finished = false;\n        this.destroyed = false;\n        (0, utils_ts_1.ahash)(hash);\n        const key = (0, utils_ts_1.toBytes)(_key);\n        this.iHash = hash.create();\n        if (typeof this.iHash.update !== 'function')\n            throw new Error('Expected instance of class which extends utils.Hash');\n        this.blockLen = this.iHash.blockLen;\n        this.outputLen = this.iHash.outputLen;\n        const blockLen = this.blockLen;\n        const pad = new Uint8Array(blockLen);\n        // blockLen can be bigger than outputLen\n        pad.set(key.length > blockLen ? hash.create().update(key).digest() : key);\n        for (let i = 0; i < pad.length; i++)\n            pad[i] ^= 0x36;\n        this.iHash.update(pad);\n        // By doing update (processing of first block) of outer hash here we can re-use it between multiple calls via clone\n        this.oHash = hash.create();\n        // Undo internal XOR && apply outer XOR\n        for (let i = 0; i < pad.length; i++)\n            pad[i] ^= 0x36 ^ 0x5c;\n        this.oHash.update(pad);\n        (0, utils_ts_1.clean)(pad);\n    }\n    update(buf) {\n        (0, utils_ts_1.aexists)(this);\n        this.iHash.update(buf);\n        return this;\n    }\n    digestInto(out) {\n        (0, utils_ts_1.aexists)(this);\n        (0, utils_ts_1.abytes)(out, this.outputLen);\n        this.finished = true;\n        this.iHash.digestInto(out);\n        this.oHash.update(out);\n        this.oHash.digestInto(out);\n        this.destroy();\n    }\n    digest() {\n        const out = new Uint8Array(this.oHash.outputLen);\n        this.digestInto(out);\n        return out;\n    }\n    _cloneInto(to) {\n        // Create new instance without calling constructor since key already in state and we don't know it.\n        to || (to = Object.create(Object.getPrototypeOf(this), {}));\n        const { oHash, iHash, finished, destroyed, blockLen, outputLen } = this;\n        to = to;\n        to.finished = finished;\n        to.destroyed = destroyed;\n        to.blockLen = blockLen;\n        to.outputLen = outputLen;\n        to.oHash = oHash._cloneInto(to.oHash);\n        to.iHash = iHash._cloneInto(to.iHash);\n        return to;\n    }\n    clone() {\n        return this._cloneInto();\n    }\n    destroy() {\n        this.destroyed = true;\n        this.oHash.destroy();\n        this.iHash.destroy();\n    }\n}\nexports.HMAC = HMAC;\n/**\n * HMAC: RFC2104 message authentication code.\n * @param hash - function that would be used e.g. sha256\n * @param key - message key\n * @param message - message data\n * @example\n * import { hmac } from '@noble/hashes/hmac';\n * import { sha256 } from '@noble/hashes/sha2';\n * const mac1 = hmac(sha256, 'key', 'message');\n */\nconst hmac = (hash, key, message) => new HMAC(hash, key).update(message).digest();\nexports.hmac = hmac;\nexports.hmac.create = (hash, key) => new HMAC(hash, key);\n//# sourceMappingURL=hmac.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@noble/hashes/hmac.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@noble/hashes/legacy.js":
/*!**********************************************!*\
  !*** ./node_modules/@noble/hashes/legacy.js ***!
  \**********************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.ripemd160 = exports.RIPEMD160 = exports.md5 = exports.MD5 = exports.sha1 = exports.SHA1 = void 0;\n/**\n\nSHA1 (RFC 3174), MD5 (RFC 1321) and RIPEMD160 (RFC 2286) legacy, weak hash functions.\nDon't use them in a new protocol. What \"weak\" means:\n\n- Collisions can be made with 2^18 effort in MD5, 2^60 in SHA1, 2^80 in RIPEMD160.\n- No practical pre-image attacks (only theoretical, 2^123.4)\n- HMAC seems kinda ok: https://datatracker.ietf.org/doc/html/rfc6151\n * @module\n */\nconst _md_ts_1 = __webpack_require__(/*! ./_md.js */ \"(ssr)/./node_modules/@noble/hashes/_md.js\");\nconst utils_ts_1 = __webpack_require__(/*! ./utils.js */ \"(ssr)/./node_modules/@noble/hashes/utils.js\");\n/** Initial SHA1 state */\nconst SHA1_IV = /* @__PURE__ */ Uint32Array.from([\n    0x67452301, 0xefcdab89, 0x98badcfe, 0x10325476, 0xc3d2e1f0,\n]);\n// Reusable temporary buffer\nconst SHA1_W = /* @__PURE__ */ new Uint32Array(80);\n/** SHA1 legacy hash class. */\nclass SHA1 extends _md_ts_1.HashMD {\n    constructor() {\n        super(64, 20, 8, false);\n        this.A = SHA1_IV[0] | 0;\n        this.B = SHA1_IV[1] | 0;\n        this.C = SHA1_IV[2] | 0;\n        this.D = SHA1_IV[3] | 0;\n        this.E = SHA1_IV[4] | 0;\n    }\n    get() {\n        const { A, B, C, D, E } = this;\n        return [A, B, C, D, E];\n    }\n    set(A, B, C, D, E) {\n        this.A = A | 0;\n        this.B = B | 0;\n        this.C = C | 0;\n        this.D = D | 0;\n        this.E = E | 0;\n    }\n    process(view, offset) {\n        for (let i = 0; i < 16; i++, offset += 4)\n            SHA1_W[i] = view.getUint32(offset, false);\n        for (let i = 16; i < 80; i++)\n            SHA1_W[i] = (0, utils_ts_1.rotl)(SHA1_W[i - 3] ^ SHA1_W[i - 8] ^ SHA1_W[i - 14] ^ SHA1_W[i - 16], 1);\n        // Compression function main loop, 80 rounds\n        let { A, B, C, D, E } = this;\n        for (let i = 0; i < 80; i++) {\n            let F, K;\n            if (i < 20) {\n                F = (0, _md_ts_1.Chi)(B, C, D);\n                K = 0x5a827999;\n            }\n            else if (i < 40) {\n                F = B ^ C ^ D;\n                K = 0x6ed9eba1;\n            }\n            else if (i < 60) {\n                F = (0, _md_ts_1.Maj)(B, C, D);\n                K = 0x8f1bbcdc;\n            }\n            else {\n                F = B ^ C ^ D;\n                K = 0xca62c1d6;\n            }\n            const T = ((0, utils_ts_1.rotl)(A, 5) + F + E + K + SHA1_W[i]) | 0;\n            E = D;\n            D = C;\n            C = (0, utils_ts_1.rotl)(B, 30);\n            B = A;\n            A = T;\n        }\n        // Add the compressed chunk to the current hash value\n        A = (A + this.A) | 0;\n        B = (B + this.B) | 0;\n        C = (C + this.C) | 0;\n        D = (D + this.D) | 0;\n        E = (E + this.E) | 0;\n        this.set(A, B, C, D, E);\n    }\n    roundClean() {\n        (0, utils_ts_1.clean)(SHA1_W);\n    }\n    destroy() {\n        this.set(0, 0, 0, 0, 0);\n        (0, utils_ts_1.clean)(this.buffer);\n    }\n}\nexports.SHA1 = SHA1;\n/** SHA1 (RFC 3174) legacy hash function. It was cryptographically broken. */\nexports.sha1 = (0, utils_ts_1.createHasher)(() => new SHA1());\n/** Per-round constants */\nconst p32 = /* @__PURE__ */ Math.pow(2, 32);\nconst K = /* @__PURE__ */ Array.from({ length: 64 }, (_, i) => Math.floor(p32 * Math.abs(Math.sin(i + 1))));\n/** md5 initial state: same as sha1, but 4 u32 instead of 5. */\nconst MD5_IV = /* @__PURE__ */ SHA1_IV.slice(0, 4);\n// Reusable temporary buffer\nconst MD5_W = /* @__PURE__ */ new Uint32Array(16);\n/** MD5 legacy hash class. */\nclass MD5 extends _md_ts_1.HashMD {\n    constructor() {\n        super(64, 16, 8, true);\n        this.A = MD5_IV[0] | 0;\n        this.B = MD5_IV[1] | 0;\n        this.C = MD5_IV[2] | 0;\n        this.D = MD5_IV[3] | 0;\n    }\n    get() {\n        const { A, B, C, D } = this;\n        return [A, B, C, D];\n    }\n    set(A, B, C, D) {\n        this.A = A | 0;\n        this.B = B | 0;\n        this.C = C | 0;\n        this.D = D | 0;\n    }\n    process(view, offset) {\n        for (let i = 0; i < 16; i++, offset += 4)\n            MD5_W[i] = view.getUint32(offset, true);\n        // Compression function main loop, 64 rounds\n        let { A, B, C, D } = this;\n        for (let i = 0; i < 64; i++) {\n            let F, g, s;\n            if (i < 16) {\n                F = (0, _md_ts_1.Chi)(B, C, D);\n                g = i;\n                s = [7, 12, 17, 22];\n            }\n            else if (i < 32) {\n                F = (0, _md_ts_1.Chi)(D, B, C);\n                g = (5 * i + 1) % 16;\n                s = [5, 9, 14, 20];\n            }\n            else if (i < 48) {\n                F = B ^ C ^ D;\n                g = (3 * i + 5) % 16;\n                s = [4, 11, 16, 23];\n            }\n            else {\n                F = C ^ (B | ~D);\n                g = (7 * i) % 16;\n                s = [6, 10, 15, 21];\n            }\n            F = F + A + K[i] + MD5_W[g];\n            A = D;\n            D = C;\n            C = B;\n            B = B + (0, utils_ts_1.rotl)(F, s[i % 4]);\n        }\n        // Add the compressed chunk to the current hash value\n        A = (A + this.A) | 0;\n        B = (B + this.B) | 0;\n        C = (C + this.C) | 0;\n        D = (D + this.D) | 0;\n        this.set(A, B, C, D);\n    }\n    roundClean() {\n        (0, utils_ts_1.clean)(MD5_W);\n    }\n    destroy() {\n        this.set(0, 0, 0, 0);\n        (0, utils_ts_1.clean)(this.buffer);\n    }\n}\nexports.MD5 = MD5;\n/**\n * MD5 (RFC 1321) legacy hash function. It was cryptographically broken.\n * MD5 architecture is similar to SHA1, with some differences:\n * - Reduced output length: 16 bytes (128 bit) instead of 20\n * - 64 rounds, instead of 80\n * - Little-endian: could be faster, but will require more code\n * - Non-linear index selection: huge speed-up for unroll\n * - Per round constants: more memory accesses, additional speed-up for unroll\n */\nexports.md5 = (0, utils_ts_1.createHasher)(() => new MD5());\n// RIPEMD-160\nconst Rho160 = /* @__PURE__ */ Uint8Array.from([\n    7, 4, 13, 1, 10, 6, 15, 3, 12, 0, 9, 5, 2, 14, 11, 8,\n]);\nconst Id160 = /* @__PURE__ */ (() => Uint8Array.from(new Array(16).fill(0).map((_, i) => i)))();\nconst Pi160 = /* @__PURE__ */ (() => Id160.map((i) => (9 * i + 5) % 16))();\nconst idxLR = /* @__PURE__ */ (() => {\n    const L = [Id160];\n    const R = [Pi160];\n    const res = [L, R];\n    for (let i = 0; i < 4; i++)\n        for (let j of res)\n            j.push(j[i].map((k) => Rho160[k]));\n    return res;\n})();\nconst idxL = /* @__PURE__ */ (() => idxLR[0])();\nconst idxR = /* @__PURE__ */ (() => idxLR[1])();\n// const [idxL, idxR] = idxLR;\nconst shifts160 = /* @__PURE__ */ [\n    [11, 14, 15, 12, 5, 8, 7, 9, 11, 13, 14, 15, 6, 7, 9, 8],\n    [12, 13, 11, 15, 6, 9, 9, 7, 12, 15, 11, 13, 7, 8, 7, 7],\n    [13, 15, 14, 11, 7, 7, 6, 8, 13, 14, 13, 12, 5, 5, 6, 9],\n    [14, 11, 12, 14, 8, 6, 5, 5, 15, 12, 15, 14, 9, 9, 8, 6],\n    [15, 12, 13, 13, 9, 5, 8, 6, 14, 11, 12, 11, 8, 6, 5, 5],\n].map((i) => Uint8Array.from(i));\nconst shiftsL160 = /* @__PURE__ */ idxL.map((idx, i) => idx.map((j) => shifts160[i][j]));\nconst shiftsR160 = /* @__PURE__ */ idxR.map((idx, i) => idx.map((j) => shifts160[i][j]));\nconst Kl160 = /* @__PURE__ */ Uint32Array.from([\n    0x00000000, 0x5a827999, 0x6ed9eba1, 0x8f1bbcdc, 0xa953fd4e,\n]);\nconst Kr160 = /* @__PURE__ */ Uint32Array.from([\n    0x50a28be6, 0x5c4dd124, 0x6d703ef3, 0x7a6d76e9, 0x00000000,\n]);\n// It's called f() in spec.\nfunction ripemd_f(group, x, y, z) {\n    if (group === 0)\n        return x ^ y ^ z;\n    if (group === 1)\n        return (x & y) | (~x & z);\n    if (group === 2)\n        return (x | ~y) ^ z;\n    if (group === 3)\n        return (x & z) | (y & ~z);\n    return x ^ (y | ~z);\n}\n// Reusable temporary buffer\nconst BUF_160 = /* @__PURE__ */ new Uint32Array(16);\nclass RIPEMD160 extends _md_ts_1.HashMD {\n    constructor() {\n        super(64, 20, 8, true);\n        this.h0 = 0x67452301 | 0;\n        this.h1 = 0xefcdab89 | 0;\n        this.h2 = 0x98badcfe | 0;\n        this.h3 = 0x10325476 | 0;\n        this.h4 = 0xc3d2e1f0 | 0;\n    }\n    get() {\n        const { h0, h1, h2, h3, h4 } = this;\n        return [h0, h1, h2, h3, h4];\n    }\n    set(h0, h1, h2, h3, h4) {\n        this.h0 = h0 | 0;\n        this.h1 = h1 | 0;\n        this.h2 = h2 | 0;\n        this.h3 = h3 | 0;\n        this.h4 = h4 | 0;\n    }\n    process(view, offset) {\n        for (let i = 0; i < 16; i++, offset += 4)\n            BUF_160[i] = view.getUint32(offset, true);\n        // prettier-ignore\n        let al = this.h0 | 0, ar = al, bl = this.h1 | 0, br = bl, cl = this.h2 | 0, cr = cl, dl = this.h3 | 0, dr = dl, el = this.h4 | 0, er = el;\n        // Instead of iterating 0 to 80, we split it into 5 groups\n        // And use the groups in constants, functions, etc. Much simpler\n        for (let group = 0; group < 5; group++) {\n            const rGroup = 4 - group;\n            const hbl = Kl160[group], hbr = Kr160[group]; // prettier-ignore\n            const rl = idxL[group], rr = idxR[group]; // prettier-ignore\n            const sl = shiftsL160[group], sr = shiftsR160[group]; // prettier-ignore\n            for (let i = 0; i < 16; i++) {\n                const tl = ((0, utils_ts_1.rotl)(al + ripemd_f(group, bl, cl, dl) + BUF_160[rl[i]] + hbl, sl[i]) + el) | 0;\n                al = el, el = dl, dl = (0, utils_ts_1.rotl)(cl, 10) | 0, cl = bl, bl = tl; // prettier-ignore\n            }\n            // 2 loops are 10% faster\n            for (let i = 0; i < 16; i++) {\n                const tr = ((0, utils_ts_1.rotl)(ar + ripemd_f(rGroup, br, cr, dr) + BUF_160[rr[i]] + hbr, sr[i]) + er) | 0;\n                ar = er, er = dr, dr = (0, utils_ts_1.rotl)(cr, 10) | 0, cr = br, br = tr; // prettier-ignore\n            }\n        }\n        // Add the compressed chunk to the current hash value\n        this.set((this.h1 + cl + dr) | 0, (this.h2 + dl + er) | 0, (this.h3 + el + ar) | 0, (this.h4 + al + br) | 0, (this.h0 + bl + cr) | 0);\n    }\n    roundClean() {\n        (0, utils_ts_1.clean)(BUF_160);\n    }\n    destroy() {\n        this.destroyed = true;\n        (0, utils_ts_1.clean)(this.buffer);\n        this.set(0, 0, 0, 0, 0);\n    }\n}\nexports.RIPEMD160 = RIPEMD160;\n/**\n * RIPEMD-160 - a legacy hash function from 1990s.\n * * https://homes.esat.kuleuven.be/~bosselae/ripemd160.html\n * * https://homes.esat.kuleuven.be/~bosselae/ripemd160/pdf/AB-9601/AB-9601.pdf\n */\nexports.ripemd160 = (0, utils_ts_1.createHasher)(() => new RIPEMD160());\n//# sourceMappingURL=legacy.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@noble/hashes/legacy.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@noble/hashes/ripemd160.js":
/*!*************************************************!*\
  !*** ./node_modules/@noble/hashes/ripemd160.js ***!
  \*************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.ripemd160 = exports.RIPEMD160 = void 0;\n/**\n * RIPEMD-160 legacy hash function.\n * https://homes.esat.kuleuven.be/~bosselae/ripemd160.html\n * https://homes.esat.kuleuven.be/~bosselae/ripemd160/pdf/AB-9601/AB-9601.pdf\n * @module\n * @deprecated\n */\nconst legacy_ts_1 = __webpack_require__(/*! ./legacy.js */ \"(ssr)/./node_modules/@noble/hashes/legacy.js\");\n/** @deprecated Use import from `noble/hashes/legacy` module */\nexports.RIPEMD160 = legacy_ts_1.RIPEMD160;\n/** @deprecated Use import from `noble/hashes/legacy` module */\nexports.ripemd160 = legacy_ts_1.ripemd160;\n//# sourceMappingURL=ripemd160.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQG5vYmxlL2hhc2hlcy9yaXBlbWQxNjAuanMiLCJtYXBwaW5ncyI6IkFBQWE7QUFDYiw4Q0FBNkMsRUFBRSxhQUFhLEVBQUM7QUFDN0QsaUJBQWlCLEdBQUcsaUJBQWlCO0FBQ3JDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esb0JBQW9CLG1CQUFPLENBQUMsaUVBQWE7QUFDekM7QUFDQSxpQkFBaUI7QUFDakI7QUFDQSxpQkFBaUI7QUFDakIiLCJzb3VyY2VzIjpbIkY6XFxBSSBjaGFpblxcY2hhaW5zaWdodC1wcm9kdWN0aW9uXFxub2RlX21vZHVsZXNcXEBub2JsZVxcaGFzaGVzXFxyaXBlbWQxNjAuanMiXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2Ugc3RyaWN0XCI7XG5PYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgXCJfX2VzTW9kdWxlXCIsIHsgdmFsdWU6IHRydWUgfSk7XG5leHBvcnRzLnJpcGVtZDE2MCA9IGV4cG9ydHMuUklQRU1EMTYwID0gdm9pZCAwO1xuLyoqXG4gKiBSSVBFTUQtMTYwIGxlZ2FjeSBoYXNoIGZ1bmN0aW9uLlxuICogaHR0cHM6Ly9ob21lcy5lc2F0Lmt1bGV1dmVuLmJlL35ib3NzZWxhZS9yaXBlbWQxNjAuaHRtbFxuICogaHR0cHM6Ly9ob21lcy5lc2F0Lmt1bGV1dmVuLmJlL35ib3NzZWxhZS9yaXBlbWQxNjAvcGRmL0FCLTk2MDEvQUItOTYwMS5wZGZcbiAqIEBtb2R1bGVcbiAqIEBkZXByZWNhdGVkXG4gKi9cbmNvbnN0IGxlZ2FjeV90c18xID0gcmVxdWlyZShcIi4vbGVnYWN5LmpzXCIpO1xuLyoqIEBkZXByZWNhdGVkIFVzZSBpbXBvcnQgZnJvbSBgbm9ibGUvaGFzaGVzL2xlZ2FjeWAgbW9kdWxlICovXG5leHBvcnRzLlJJUEVNRDE2MCA9IGxlZ2FjeV90c18xLlJJUEVNRDE2MDtcbi8qKiBAZGVwcmVjYXRlZCBVc2UgaW1wb3J0IGZyb20gYG5vYmxlL2hhc2hlcy9sZWdhY3lgIG1vZHVsZSAqL1xuZXhwb3J0cy5yaXBlbWQxNjAgPSBsZWdhY3lfdHNfMS5yaXBlbWQxNjA7XG4vLyMgc291cmNlTWFwcGluZ1VSTD1yaXBlbWQxNjAuanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@noble/hashes/ripemd160.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@noble/hashes/sha2.js":
/*!********************************************!*\
  !*** ./node_modules/@noble/hashes/sha2.js ***!
  \********************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.sha512_224 = exports.sha512_256 = exports.sha384 = exports.sha512 = exports.sha224 = exports.sha256 = exports.SHA512_256 = exports.SHA512_224 = exports.SHA384 = exports.SHA512 = exports.SHA224 = exports.SHA256 = void 0;\n/**\n * SHA2 hash function. A.k.a. sha256, sha384, sha512, sha512_224, sha512_256.\n * SHA256 is the fastest hash implementable in JS, even faster than Blake3.\n * Check out [RFC 4634](https://datatracker.ietf.org/doc/html/rfc4634) and\n * [FIPS 180-4](https://nvlpubs.nist.gov/nistpubs/FIPS/NIST.FIPS.180-4.pdf).\n * @module\n */\nconst _md_ts_1 = __webpack_require__(/*! ./_md.js */ \"(ssr)/./node_modules/@noble/hashes/_md.js\");\nconst u64 = __webpack_require__(/*! ./_u64.js */ \"(ssr)/./node_modules/@noble/hashes/_u64.js\");\nconst utils_ts_1 = __webpack_require__(/*! ./utils.js */ \"(ssr)/./node_modules/@noble/hashes/utils.js\");\n/**\n * Round constants:\n * First 32 bits of fractional parts of the cube roots of the first 64 primes 2..311)\n */\n// prettier-ignore\nconst SHA256_K = /* @__PURE__ */ Uint32Array.from([\n    0x428a2f98, 0x71374491, 0xb5c0fbcf, 0xe9b5dba5, 0x3956c25b, 0x59f111f1, 0x923f82a4, 0xab1c5ed5,\n    0xd807aa98, 0x12835b01, 0x243185be, 0x550c7dc3, 0x72be5d74, 0x80deb1fe, 0x9bdc06a7, 0xc19bf174,\n    0xe49b69c1, 0xefbe4786, 0x0fc19dc6, 0x240ca1cc, 0x2de92c6f, 0x4a7484aa, 0x5cb0a9dc, 0x76f988da,\n    0x983e5152, 0xa831c66d, 0xb00327c8, 0xbf597fc7, 0xc6e00bf3, 0xd5a79147, 0x06ca6351, 0x14292967,\n    0x27b70a85, 0x2e1b2138, 0x4d2c6dfc, 0x53380d13, 0x650a7354, 0x766a0abb, 0x81c2c92e, 0x92722c85,\n    0xa2bfe8a1, 0xa81a664b, 0xc24b8b70, 0xc76c51a3, 0xd192e819, 0xd6990624, 0xf40e3585, 0x106aa070,\n    0x19a4c116, 0x1e376c08, 0x2748774c, 0x34b0bcb5, 0x391c0cb3, 0x4ed8aa4a, 0x5b9cca4f, 0x682e6ff3,\n    0x748f82ee, 0x78a5636f, 0x84c87814, 0x8cc70208, 0x90befffa, 0xa4506ceb, 0xbef9a3f7, 0xc67178f2\n]);\n/** Reusable temporary buffer. \"W\" comes straight from spec. */\nconst SHA256_W = /* @__PURE__ */ new Uint32Array(64);\nclass SHA256 extends _md_ts_1.HashMD {\n    constructor(outputLen = 32) {\n        super(64, outputLen, 8, false);\n        // We cannot use array here since array allows indexing by variable\n        // which means optimizer/compiler cannot use registers.\n        this.A = _md_ts_1.SHA256_IV[0] | 0;\n        this.B = _md_ts_1.SHA256_IV[1] | 0;\n        this.C = _md_ts_1.SHA256_IV[2] | 0;\n        this.D = _md_ts_1.SHA256_IV[3] | 0;\n        this.E = _md_ts_1.SHA256_IV[4] | 0;\n        this.F = _md_ts_1.SHA256_IV[5] | 0;\n        this.G = _md_ts_1.SHA256_IV[6] | 0;\n        this.H = _md_ts_1.SHA256_IV[7] | 0;\n    }\n    get() {\n        const { A, B, C, D, E, F, G, H } = this;\n        return [A, B, C, D, E, F, G, H];\n    }\n    // prettier-ignore\n    set(A, B, C, D, E, F, G, H) {\n        this.A = A | 0;\n        this.B = B | 0;\n        this.C = C | 0;\n        this.D = D | 0;\n        this.E = E | 0;\n        this.F = F | 0;\n        this.G = G | 0;\n        this.H = H | 0;\n    }\n    process(view, offset) {\n        // Extend the first 16 words into the remaining 48 words w[16..63] of the message schedule array\n        for (let i = 0; i < 16; i++, offset += 4)\n            SHA256_W[i] = view.getUint32(offset, false);\n        for (let i = 16; i < 64; i++) {\n            const W15 = SHA256_W[i - 15];\n            const W2 = SHA256_W[i - 2];\n            const s0 = (0, utils_ts_1.rotr)(W15, 7) ^ (0, utils_ts_1.rotr)(W15, 18) ^ (W15 >>> 3);\n            const s1 = (0, utils_ts_1.rotr)(W2, 17) ^ (0, utils_ts_1.rotr)(W2, 19) ^ (W2 >>> 10);\n            SHA256_W[i] = (s1 + SHA256_W[i - 7] + s0 + SHA256_W[i - 16]) | 0;\n        }\n        // Compression function main loop, 64 rounds\n        let { A, B, C, D, E, F, G, H } = this;\n        for (let i = 0; i < 64; i++) {\n            const sigma1 = (0, utils_ts_1.rotr)(E, 6) ^ (0, utils_ts_1.rotr)(E, 11) ^ (0, utils_ts_1.rotr)(E, 25);\n            const T1 = (H + sigma1 + (0, _md_ts_1.Chi)(E, F, G) + SHA256_K[i] + SHA256_W[i]) | 0;\n            const sigma0 = (0, utils_ts_1.rotr)(A, 2) ^ (0, utils_ts_1.rotr)(A, 13) ^ (0, utils_ts_1.rotr)(A, 22);\n            const T2 = (sigma0 + (0, _md_ts_1.Maj)(A, B, C)) | 0;\n            H = G;\n            G = F;\n            F = E;\n            E = (D + T1) | 0;\n            D = C;\n            C = B;\n            B = A;\n            A = (T1 + T2) | 0;\n        }\n        // Add the compressed chunk to the current hash value\n        A = (A + this.A) | 0;\n        B = (B + this.B) | 0;\n        C = (C + this.C) | 0;\n        D = (D + this.D) | 0;\n        E = (E + this.E) | 0;\n        F = (F + this.F) | 0;\n        G = (G + this.G) | 0;\n        H = (H + this.H) | 0;\n        this.set(A, B, C, D, E, F, G, H);\n    }\n    roundClean() {\n        (0, utils_ts_1.clean)(SHA256_W);\n    }\n    destroy() {\n        this.set(0, 0, 0, 0, 0, 0, 0, 0);\n        (0, utils_ts_1.clean)(this.buffer);\n    }\n}\nexports.SHA256 = SHA256;\nclass SHA224 extends SHA256 {\n    constructor() {\n        super(28);\n        this.A = _md_ts_1.SHA224_IV[0] | 0;\n        this.B = _md_ts_1.SHA224_IV[1] | 0;\n        this.C = _md_ts_1.SHA224_IV[2] | 0;\n        this.D = _md_ts_1.SHA224_IV[3] | 0;\n        this.E = _md_ts_1.SHA224_IV[4] | 0;\n        this.F = _md_ts_1.SHA224_IV[5] | 0;\n        this.G = _md_ts_1.SHA224_IV[6] | 0;\n        this.H = _md_ts_1.SHA224_IV[7] | 0;\n    }\n}\nexports.SHA224 = SHA224;\n// SHA2-512 is slower than sha256 in js because u64 operations are slow.\n// Round contants\n// First 32 bits of the fractional parts of the cube roots of the first 80 primes 2..409\n// prettier-ignore\nconst K512 = /* @__PURE__ */ (() => u64.split([\n    '0x428a2f98d728ae22', '0x7137449123ef65cd', '0xb5c0fbcfec4d3b2f', '0xe9b5dba58189dbbc',\n    '0x3956c25bf348b538', '0x59f111f1b605d019', '0x923f82a4af194f9b', '0xab1c5ed5da6d8118',\n    '0xd807aa98a3030242', '0x12835b0145706fbe', '0x243185be4ee4b28c', '0x550c7dc3d5ffb4e2',\n    '0x72be5d74f27b896f', '0x80deb1fe3b1696b1', '0x9bdc06a725c71235', '0xc19bf174cf692694',\n    '0xe49b69c19ef14ad2', '0xefbe4786384f25e3', '0x0fc19dc68b8cd5b5', '0x240ca1cc77ac9c65',\n    '0x2de92c6f592b0275', '0x4a7484aa6ea6e483', '0x5cb0a9dcbd41fbd4', '0x76f988da831153b5',\n    '0x983e5152ee66dfab', '0xa831c66d2db43210', '0xb00327c898fb213f', '0xbf597fc7beef0ee4',\n    '0xc6e00bf33da88fc2', '0xd5a79147930aa725', '0x06ca6351e003826f', '0x142929670a0e6e70',\n    '0x27b70a8546d22ffc', '0x2e1b21385c26c926', '0x4d2c6dfc5ac42aed', '0x53380d139d95b3df',\n    '0x650a73548baf63de', '0x766a0abb3c77b2a8', '0x81c2c92e47edaee6', '0x92722c851482353b',\n    '0xa2bfe8a14cf10364', '0xa81a664bbc423001', '0xc24b8b70d0f89791', '0xc76c51a30654be30',\n    '0xd192e819d6ef5218', '0xd69906245565a910', '0xf40e35855771202a', '0x106aa07032bbd1b8',\n    '0x19a4c116b8d2d0c8', '0x1e376c085141ab53', '0x2748774cdf8eeb99', '0x34b0bcb5e19b48a8',\n    '0x391c0cb3c5c95a63', '0x4ed8aa4ae3418acb', '0x5b9cca4f7763e373', '0x682e6ff3d6b2b8a3',\n    '0x748f82ee5defb2fc', '0x78a5636f43172f60', '0x84c87814a1f0ab72', '0x8cc702081a6439ec',\n    '0x90befffa23631e28', '0xa4506cebde82bde9', '0xbef9a3f7b2c67915', '0xc67178f2e372532b',\n    '0xca273eceea26619c', '0xd186b8c721c0c207', '0xeada7dd6cde0eb1e', '0xf57d4f7fee6ed178',\n    '0x06f067aa72176fba', '0x0a637dc5a2c898a6', '0x113f9804bef90dae', '0x1b710b35131c471b',\n    '0x28db77f523047d84', '0x32caab7b40c72493', '0x3c9ebe0a15c9bebc', '0x431d67c49c100d4c',\n    '0x4cc5d4becb3e42b6', '0x597f299cfc657e2a', '0x5fcb6fab3ad6faec', '0x6c44198c4a475817'\n].map(n => BigInt(n))))();\nconst SHA512_Kh = /* @__PURE__ */ (() => K512[0])();\nconst SHA512_Kl = /* @__PURE__ */ (() => K512[1])();\n// Reusable temporary buffers\nconst SHA512_W_H = /* @__PURE__ */ new Uint32Array(80);\nconst SHA512_W_L = /* @__PURE__ */ new Uint32Array(80);\nclass SHA512 extends _md_ts_1.HashMD {\n    constructor(outputLen = 64) {\n        super(128, outputLen, 16, false);\n        // We cannot use array here since array allows indexing by variable\n        // which means optimizer/compiler cannot use registers.\n        // h -- high 32 bits, l -- low 32 bits\n        this.Ah = _md_ts_1.SHA512_IV[0] | 0;\n        this.Al = _md_ts_1.SHA512_IV[1] | 0;\n        this.Bh = _md_ts_1.SHA512_IV[2] | 0;\n        this.Bl = _md_ts_1.SHA512_IV[3] | 0;\n        this.Ch = _md_ts_1.SHA512_IV[4] | 0;\n        this.Cl = _md_ts_1.SHA512_IV[5] | 0;\n        this.Dh = _md_ts_1.SHA512_IV[6] | 0;\n        this.Dl = _md_ts_1.SHA512_IV[7] | 0;\n        this.Eh = _md_ts_1.SHA512_IV[8] | 0;\n        this.El = _md_ts_1.SHA512_IV[9] | 0;\n        this.Fh = _md_ts_1.SHA512_IV[10] | 0;\n        this.Fl = _md_ts_1.SHA512_IV[11] | 0;\n        this.Gh = _md_ts_1.SHA512_IV[12] | 0;\n        this.Gl = _md_ts_1.SHA512_IV[13] | 0;\n        this.Hh = _md_ts_1.SHA512_IV[14] | 0;\n        this.Hl = _md_ts_1.SHA512_IV[15] | 0;\n    }\n    // prettier-ignore\n    get() {\n        const { Ah, Al, Bh, Bl, Ch, Cl, Dh, Dl, Eh, El, Fh, Fl, Gh, Gl, Hh, Hl } = this;\n        return [Ah, Al, Bh, Bl, Ch, Cl, Dh, Dl, Eh, El, Fh, Fl, Gh, Gl, Hh, Hl];\n    }\n    // prettier-ignore\n    set(Ah, Al, Bh, Bl, Ch, Cl, Dh, Dl, Eh, El, Fh, Fl, Gh, Gl, Hh, Hl) {\n        this.Ah = Ah | 0;\n        this.Al = Al | 0;\n        this.Bh = Bh | 0;\n        this.Bl = Bl | 0;\n        this.Ch = Ch | 0;\n        this.Cl = Cl | 0;\n        this.Dh = Dh | 0;\n        this.Dl = Dl | 0;\n        this.Eh = Eh | 0;\n        this.El = El | 0;\n        this.Fh = Fh | 0;\n        this.Fl = Fl | 0;\n        this.Gh = Gh | 0;\n        this.Gl = Gl | 0;\n        this.Hh = Hh | 0;\n        this.Hl = Hl | 0;\n    }\n    process(view, offset) {\n        // Extend the first 16 words into the remaining 64 words w[16..79] of the message schedule array\n        for (let i = 0; i < 16; i++, offset += 4) {\n            SHA512_W_H[i] = view.getUint32(offset);\n            SHA512_W_L[i] = view.getUint32((offset += 4));\n        }\n        for (let i = 16; i < 80; i++) {\n            // s0 := (w[i-15] rightrotate 1) xor (w[i-15] rightrotate 8) xor (w[i-15] rightshift 7)\n            const W15h = SHA512_W_H[i - 15] | 0;\n            const W15l = SHA512_W_L[i - 15] | 0;\n            const s0h = u64.rotrSH(W15h, W15l, 1) ^ u64.rotrSH(W15h, W15l, 8) ^ u64.shrSH(W15h, W15l, 7);\n            const s0l = u64.rotrSL(W15h, W15l, 1) ^ u64.rotrSL(W15h, W15l, 8) ^ u64.shrSL(W15h, W15l, 7);\n            // s1 := (w[i-2] rightrotate 19) xor (w[i-2] rightrotate 61) xor (w[i-2] rightshift 6)\n            const W2h = SHA512_W_H[i - 2] | 0;\n            const W2l = SHA512_W_L[i - 2] | 0;\n            const s1h = u64.rotrSH(W2h, W2l, 19) ^ u64.rotrBH(W2h, W2l, 61) ^ u64.shrSH(W2h, W2l, 6);\n            const s1l = u64.rotrSL(W2h, W2l, 19) ^ u64.rotrBL(W2h, W2l, 61) ^ u64.shrSL(W2h, W2l, 6);\n            // SHA256_W[i] = s0 + s1 + SHA256_W[i - 7] + SHA256_W[i - 16];\n            const SUMl = u64.add4L(s0l, s1l, SHA512_W_L[i - 7], SHA512_W_L[i - 16]);\n            const SUMh = u64.add4H(SUMl, s0h, s1h, SHA512_W_H[i - 7], SHA512_W_H[i - 16]);\n            SHA512_W_H[i] = SUMh | 0;\n            SHA512_W_L[i] = SUMl | 0;\n        }\n        let { Ah, Al, Bh, Bl, Ch, Cl, Dh, Dl, Eh, El, Fh, Fl, Gh, Gl, Hh, Hl } = this;\n        // Compression function main loop, 80 rounds\n        for (let i = 0; i < 80; i++) {\n            // S1 := (e rightrotate 14) xor (e rightrotate 18) xor (e rightrotate 41)\n            const sigma1h = u64.rotrSH(Eh, El, 14) ^ u64.rotrSH(Eh, El, 18) ^ u64.rotrBH(Eh, El, 41);\n            const sigma1l = u64.rotrSL(Eh, El, 14) ^ u64.rotrSL(Eh, El, 18) ^ u64.rotrBL(Eh, El, 41);\n            //const T1 = (H + sigma1 + Chi(E, F, G) + SHA256_K[i] + SHA256_W[i]) | 0;\n            const CHIh = (Eh & Fh) ^ (~Eh & Gh);\n            const CHIl = (El & Fl) ^ (~El & Gl);\n            // T1 = H + sigma1 + Chi(E, F, G) + SHA512_K[i] + SHA512_W[i]\n            // prettier-ignore\n            const T1ll = u64.add5L(Hl, sigma1l, CHIl, SHA512_Kl[i], SHA512_W_L[i]);\n            const T1h = u64.add5H(T1ll, Hh, sigma1h, CHIh, SHA512_Kh[i], SHA512_W_H[i]);\n            const T1l = T1ll | 0;\n            // S0 := (a rightrotate 28) xor (a rightrotate 34) xor (a rightrotate 39)\n            const sigma0h = u64.rotrSH(Ah, Al, 28) ^ u64.rotrBH(Ah, Al, 34) ^ u64.rotrBH(Ah, Al, 39);\n            const sigma0l = u64.rotrSL(Ah, Al, 28) ^ u64.rotrBL(Ah, Al, 34) ^ u64.rotrBL(Ah, Al, 39);\n            const MAJh = (Ah & Bh) ^ (Ah & Ch) ^ (Bh & Ch);\n            const MAJl = (Al & Bl) ^ (Al & Cl) ^ (Bl & Cl);\n            Hh = Gh | 0;\n            Hl = Gl | 0;\n            Gh = Fh | 0;\n            Gl = Fl | 0;\n            Fh = Eh | 0;\n            Fl = El | 0;\n            ({ h: Eh, l: El } = u64.add(Dh | 0, Dl | 0, T1h | 0, T1l | 0));\n            Dh = Ch | 0;\n            Dl = Cl | 0;\n            Ch = Bh | 0;\n            Cl = Bl | 0;\n            Bh = Ah | 0;\n            Bl = Al | 0;\n            const All = u64.add3L(T1l, sigma0l, MAJl);\n            Ah = u64.add3H(All, T1h, sigma0h, MAJh);\n            Al = All | 0;\n        }\n        // Add the compressed chunk to the current hash value\n        ({ h: Ah, l: Al } = u64.add(this.Ah | 0, this.Al | 0, Ah | 0, Al | 0));\n        ({ h: Bh, l: Bl } = u64.add(this.Bh | 0, this.Bl | 0, Bh | 0, Bl | 0));\n        ({ h: Ch, l: Cl } = u64.add(this.Ch | 0, this.Cl | 0, Ch | 0, Cl | 0));\n        ({ h: Dh, l: Dl } = u64.add(this.Dh | 0, this.Dl | 0, Dh | 0, Dl | 0));\n        ({ h: Eh, l: El } = u64.add(this.Eh | 0, this.El | 0, Eh | 0, El | 0));\n        ({ h: Fh, l: Fl } = u64.add(this.Fh | 0, this.Fl | 0, Fh | 0, Fl | 0));\n        ({ h: Gh, l: Gl } = u64.add(this.Gh | 0, this.Gl | 0, Gh | 0, Gl | 0));\n        ({ h: Hh, l: Hl } = u64.add(this.Hh | 0, this.Hl | 0, Hh | 0, Hl | 0));\n        this.set(Ah, Al, Bh, Bl, Ch, Cl, Dh, Dl, Eh, El, Fh, Fl, Gh, Gl, Hh, Hl);\n    }\n    roundClean() {\n        (0, utils_ts_1.clean)(SHA512_W_H, SHA512_W_L);\n    }\n    destroy() {\n        (0, utils_ts_1.clean)(this.buffer);\n        this.set(0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0);\n    }\n}\nexports.SHA512 = SHA512;\nclass SHA384 extends SHA512 {\n    constructor() {\n        super(48);\n        this.Ah = _md_ts_1.SHA384_IV[0] | 0;\n        this.Al = _md_ts_1.SHA384_IV[1] | 0;\n        this.Bh = _md_ts_1.SHA384_IV[2] | 0;\n        this.Bl = _md_ts_1.SHA384_IV[3] | 0;\n        this.Ch = _md_ts_1.SHA384_IV[4] | 0;\n        this.Cl = _md_ts_1.SHA384_IV[5] | 0;\n        this.Dh = _md_ts_1.SHA384_IV[6] | 0;\n        this.Dl = _md_ts_1.SHA384_IV[7] | 0;\n        this.Eh = _md_ts_1.SHA384_IV[8] | 0;\n        this.El = _md_ts_1.SHA384_IV[9] | 0;\n        this.Fh = _md_ts_1.SHA384_IV[10] | 0;\n        this.Fl = _md_ts_1.SHA384_IV[11] | 0;\n        this.Gh = _md_ts_1.SHA384_IV[12] | 0;\n        this.Gl = _md_ts_1.SHA384_IV[13] | 0;\n        this.Hh = _md_ts_1.SHA384_IV[14] | 0;\n        this.Hl = _md_ts_1.SHA384_IV[15] | 0;\n    }\n}\nexports.SHA384 = SHA384;\n/**\n * Truncated SHA512/256 and SHA512/224.\n * SHA512_IV is XORed with 0xa5a5a5a5a5a5a5a5, then used as \"intermediary\" IV of SHA512/t.\n * Then t hashes string to produce result IV.\n * See `test/misc/sha2-gen-iv.js`.\n */\n/** SHA512/224 IV */\nconst T224_IV = /* @__PURE__ */ Uint32Array.from([\n    0x8c3d37c8, 0x19544da2, 0x73e19966, 0x89dcd4d6, 0x1dfab7ae, 0x32ff9c82, 0x679dd514, 0x582f9fcf,\n    0x0f6d2b69, 0x7bd44da8, 0x77e36f73, 0x04c48942, 0x3f9d85a8, 0x6a1d36c8, 0x1112e6ad, 0x91d692a1,\n]);\n/** SHA512/256 IV */\nconst T256_IV = /* @__PURE__ */ Uint32Array.from([\n    0x22312194, 0xfc2bf72c, 0x9f555fa3, 0xc84c64c2, 0x2393b86b, 0x6f53b151, 0x96387719, 0x5940eabd,\n    0x96283ee2, 0xa88effe3, 0xbe5e1e25, 0x53863992, 0x2b0199fc, 0x2c85b8aa, 0x0eb72ddc, 0x81c52ca2,\n]);\nclass SHA512_224 extends SHA512 {\n    constructor() {\n        super(28);\n        this.Ah = T224_IV[0] | 0;\n        this.Al = T224_IV[1] | 0;\n        this.Bh = T224_IV[2] | 0;\n        this.Bl = T224_IV[3] | 0;\n        this.Ch = T224_IV[4] | 0;\n        this.Cl = T224_IV[5] | 0;\n        this.Dh = T224_IV[6] | 0;\n        this.Dl = T224_IV[7] | 0;\n        this.Eh = T224_IV[8] | 0;\n        this.El = T224_IV[9] | 0;\n        this.Fh = T224_IV[10] | 0;\n        this.Fl = T224_IV[11] | 0;\n        this.Gh = T224_IV[12] | 0;\n        this.Gl = T224_IV[13] | 0;\n        this.Hh = T224_IV[14] | 0;\n        this.Hl = T224_IV[15] | 0;\n    }\n}\nexports.SHA512_224 = SHA512_224;\nclass SHA512_256 extends SHA512 {\n    constructor() {\n        super(32);\n        this.Ah = T256_IV[0] | 0;\n        this.Al = T256_IV[1] | 0;\n        this.Bh = T256_IV[2] | 0;\n        this.Bl = T256_IV[3] | 0;\n        this.Ch = T256_IV[4] | 0;\n        this.Cl = T256_IV[5] | 0;\n        this.Dh = T256_IV[6] | 0;\n        this.Dl = T256_IV[7] | 0;\n        this.Eh = T256_IV[8] | 0;\n        this.El = T256_IV[9] | 0;\n        this.Fh = T256_IV[10] | 0;\n        this.Fl = T256_IV[11] | 0;\n        this.Gh = T256_IV[12] | 0;\n        this.Gl = T256_IV[13] | 0;\n        this.Hh = T256_IV[14] | 0;\n        this.Hl = T256_IV[15] | 0;\n    }\n}\nexports.SHA512_256 = SHA512_256;\n/**\n * SHA2-256 hash function from RFC 4634.\n *\n * It is the fastest JS hash, even faster than Blake3.\n * To break sha256 using birthday attack, attackers need to try 2^128 hashes.\n * BTC network is doing 2^70 hashes/sec (2^95 hashes/year) as per 2025.\n */\nexports.sha256 = (0, utils_ts_1.createHasher)(() => new SHA256());\n/** SHA2-224 hash function from RFC 4634 */\nexports.sha224 = (0, utils_ts_1.createHasher)(() => new SHA224());\n/** SHA2-512 hash function from RFC 4634. */\nexports.sha512 = (0, utils_ts_1.createHasher)(() => new SHA512());\n/** SHA2-384 hash function from RFC 4634. */\nexports.sha384 = (0, utils_ts_1.createHasher)(() => new SHA384());\n/**\n * SHA2-512/256 \"truncated\" hash function, with improved resistance to length extension attacks.\n * See the paper on [truncated SHA512](https://eprint.iacr.org/2010/548.pdf).\n */\nexports.sha512_256 = (0, utils_ts_1.createHasher)(() => new SHA512_256());\n/**\n * SHA2-512/224 \"truncated\" hash function, with improved resistance to length extension attacks.\n * See the paper on [truncated SHA512](https://eprint.iacr.org/2010/548.pdf).\n */\nexports.sha512_224 = (0, utils_ts_1.createHasher)(() => new SHA512_224());\n//# sourceMappingURL=sha2.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@noble/hashes/sha2.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@noble/hashes/sha256.js":
/*!**********************************************!*\
  !*** ./node_modules/@noble/hashes/sha256.js ***!
  \**********************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.sha224 = exports.SHA224 = exports.sha256 = exports.SHA256 = void 0;\n/**\n * SHA2-256 a.k.a. sha256. In JS, it is the fastest hash, even faster than Blake3.\n *\n * To break sha256 using birthday attack, attackers need to try 2^128 hashes.\n * BTC network is doing 2^70 hashes/sec (2^95 hashes/year) as per 2025.\n *\n * Check out [FIPS 180-4](https://nvlpubs.nist.gov/nistpubs/FIPS/NIST.FIPS.180-4.pdf).\n * @module\n * @deprecated\n */\nconst sha2_ts_1 = __webpack_require__(/*! ./sha2.js */ \"(ssr)/./node_modules/@noble/hashes/sha2.js\");\n/** @deprecated Use import from `noble/hashes/sha2` module */\nexports.SHA256 = sha2_ts_1.SHA256;\n/** @deprecated Use import from `noble/hashes/sha2` module */\nexports.sha256 = sha2_ts_1.sha256;\n/** @deprecated Use import from `noble/hashes/sha2` module */\nexports.SHA224 = sha2_ts_1.SHA224;\n/** @deprecated Use import from `noble/hashes/sha2` module */\nexports.sha224 = sha2_ts_1.sha224;\n//# sourceMappingURL=sha256.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQG5vYmxlL2hhc2hlcy9zaGEyNTYuanMiLCJtYXBwaW5ncyI6IkFBQWE7QUFDYiw4Q0FBNkMsRUFBRSxhQUFhLEVBQUM7QUFDN0QsY0FBYyxHQUFHLGNBQWMsR0FBRyxjQUFjLEdBQUcsY0FBYztBQUNqRTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGtCQUFrQixtQkFBTyxDQUFDLDZEQUFXO0FBQ3JDO0FBQ0EsY0FBYztBQUNkO0FBQ0EsY0FBYztBQUNkO0FBQ0EsY0FBYztBQUNkO0FBQ0EsY0FBYztBQUNkIiwic291cmNlcyI6WyJGOlxcQUkgY2hhaW5cXGNoYWluc2lnaHQtcHJvZHVjdGlvblxcbm9kZV9tb2R1bGVzXFxAbm9ibGVcXGhhc2hlc1xcc2hhMjU2LmpzIl0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIHN0cmljdFwiO1xuT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsIFwiX19lc01vZHVsZVwiLCB7IHZhbHVlOiB0cnVlIH0pO1xuZXhwb3J0cy5zaGEyMjQgPSBleHBvcnRzLlNIQTIyNCA9IGV4cG9ydHMuc2hhMjU2ID0gZXhwb3J0cy5TSEEyNTYgPSB2b2lkIDA7XG4vKipcbiAqIFNIQTItMjU2IGEuay5hLiBzaGEyNTYuIEluIEpTLCBpdCBpcyB0aGUgZmFzdGVzdCBoYXNoLCBldmVuIGZhc3RlciB0aGFuIEJsYWtlMy5cbiAqXG4gKiBUbyBicmVhayBzaGEyNTYgdXNpbmcgYmlydGhkYXkgYXR0YWNrLCBhdHRhY2tlcnMgbmVlZCB0byB0cnkgMl4xMjggaGFzaGVzLlxuICogQlRDIG5ldHdvcmsgaXMgZG9pbmcgMl43MCBoYXNoZXMvc2VjICgyXjk1IGhhc2hlcy95ZWFyKSBhcyBwZXIgMjAyNS5cbiAqXG4gKiBDaGVjayBvdXQgW0ZJUFMgMTgwLTRdKGh0dHBzOi8vbnZscHVicy5uaXN0Lmdvdi9uaXN0cHVicy9GSVBTL05JU1QuRklQUy4xODAtNC5wZGYpLlxuICogQG1vZHVsZVxuICogQGRlcHJlY2F0ZWRcbiAqL1xuY29uc3Qgc2hhMl90c18xID0gcmVxdWlyZShcIi4vc2hhMi5qc1wiKTtcbi8qKiBAZGVwcmVjYXRlZCBVc2UgaW1wb3J0IGZyb20gYG5vYmxlL2hhc2hlcy9zaGEyYCBtb2R1bGUgKi9cbmV4cG9ydHMuU0hBMjU2ID0gc2hhMl90c18xLlNIQTI1Njtcbi8qKiBAZGVwcmVjYXRlZCBVc2UgaW1wb3J0IGZyb20gYG5vYmxlL2hhc2hlcy9zaGEyYCBtb2R1bGUgKi9cbmV4cG9ydHMuc2hhMjU2ID0gc2hhMl90c18xLnNoYTI1Njtcbi8qKiBAZGVwcmVjYXRlZCBVc2UgaW1wb3J0IGZyb20gYG5vYmxlL2hhc2hlcy9zaGEyYCBtb2R1bGUgKi9cbmV4cG9ydHMuU0hBMjI0ID0gc2hhMl90c18xLlNIQTIyNDtcbi8qKiBAZGVwcmVjYXRlZCBVc2UgaW1wb3J0IGZyb20gYG5vYmxlL2hhc2hlcy9zaGEyYCBtb2R1bGUgKi9cbmV4cG9ydHMuc2hhMjI0ID0gc2hhMl90c18xLnNoYTIyNDtcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPXNoYTI1Ni5qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@noble/hashes/sha256.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@noble/hashes/sha3.js":
/*!********************************************!*\
  !*** ./node_modules/@noble/hashes/sha3.js ***!
  \********************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.shake256 = exports.shake128 = exports.keccak_512 = exports.keccak_384 = exports.keccak_256 = exports.keccak_224 = exports.sha3_512 = exports.sha3_384 = exports.sha3_256 = exports.sha3_224 = exports.Keccak = void 0;\nexports.keccakP = keccakP;\n/**\n * SHA3 (keccak) hash function, based on a new \"Sponge function\" design.\n * Different from older hashes, the internal state is bigger than output size.\n *\n * Check out [FIPS-202](https://nvlpubs.nist.gov/nistpubs/FIPS/NIST.FIPS.202.pdf),\n * [Website](https://keccak.team/keccak.html),\n * [the differences between SHA-3 and Keccak](https://crypto.stackexchange.com/questions/15727/what-are-the-key-differences-between-the-draft-sha-3-standard-and-the-keccak-sub).\n *\n * Check out `sha3-addons` module for cSHAKE, k12, and others.\n * @module\n */\nconst _u64_ts_1 = __webpack_require__(/*! ./_u64.js */ \"(ssr)/./node_modules/@noble/hashes/_u64.js\");\n// prettier-ignore\nconst utils_ts_1 = __webpack_require__(/*! ./utils.js */ \"(ssr)/./node_modules/@noble/hashes/utils.js\");\n// No __PURE__ annotations in sha3 header:\n// EVERYTHING is in fact used on every export.\n// Various per round constants calculations\nconst _0n = BigInt(0);\nconst _1n = BigInt(1);\nconst _2n = BigInt(2);\nconst _7n = BigInt(7);\nconst _256n = BigInt(256);\nconst _0x71n = BigInt(0x71);\nconst SHA3_PI = [];\nconst SHA3_ROTL = [];\nconst _SHA3_IOTA = [];\nfor (let round = 0, R = _1n, x = 1, y = 0; round < 24; round++) {\n    // Pi\n    [x, y] = [y, (2 * x + 3 * y) % 5];\n    SHA3_PI.push(2 * (5 * y + x));\n    // Rotational\n    SHA3_ROTL.push((((round + 1) * (round + 2)) / 2) % 64);\n    // Iota\n    let t = _0n;\n    for (let j = 0; j < 7; j++) {\n        R = ((R << _1n) ^ ((R >> _7n) * _0x71n)) % _256n;\n        if (R & _2n)\n            t ^= _1n << ((_1n << /* @__PURE__ */ BigInt(j)) - _1n);\n    }\n    _SHA3_IOTA.push(t);\n}\nconst IOTAS = (0, _u64_ts_1.split)(_SHA3_IOTA, true);\nconst SHA3_IOTA_H = IOTAS[0];\nconst SHA3_IOTA_L = IOTAS[1];\n// Left rotation (without 0, 32, 64)\nconst rotlH = (h, l, s) => (s > 32 ? (0, _u64_ts_1.rotlBH)(h, l, s) : (0, _u64_ts_1.rotlSH)(h, l, s));\nconst rotlL = (h, l, s) => (s > 32 ? (0, _u64_ts_1.rotlBL)(h, l, s) : (0, _u64_ts_1.rotlSL)(h, l, s));\n/** `keccakf1600` internal function, additionally allows to adjust round count. */\nfunction keccakP(s, rounds = 24) {\n    const B = new Uint32Array(5 * 2);\n    // NOTE: all indices are x2 since we store state as u32 instead of u64 (bigints to slow in js)\n    for (let round = 24 - rounds; round < 24; round++) {\n        // Theta θ\n        for (let x = 0; x < 10; x++)\n            B[x] = s[x] ^ s[x + 10] ^ s[x + 20] ^ s[x + 30] ^ s[x + 40];\n        for (let x = 0; x < 10; x += 2) {\n            const idx1 = (x + 8) % 10;\n            const idx0 = (x + 2) % 10;\n            const B0 = B[idx0];\n            const B1 = B[idx0 + 1];\n            const Th = rotlH(B0, B1, 1) ^ B[idx1];\n            const Tl = rotlL(B0, B1, 1) ^ B[idx1 + 1];\n            for (let y = 0; y < 50; y += 10) {\n                s[x + y] ^= Th;\n                s[x + y + 1] ^= Tl;\n            }\n        }\n        // Rho (ρ) and Pi (π)\n        let curH = s[2];\n        let curL = s[3];\n        for (let t = 0; t < 24; t++) {\n            const shift = SHA3_ROTL[t];\n            const Th = rotlH(curH, curL, shift);\n            const Tl = rotlL(curH, curL, shift);\n            const PI = SHA3_PI[t];\n            curH = s[PI];\n            curL = s[PI + 1];\n            s[PI] = Th;\n            s[PI + 1] = Tl;\n        }\n        // Chi (χ)\n        for (let y = 0; y < 50; y += 10) {\n            for (let x = 0; x < 10; x++)\n                B[x] = s[y + x];\n            for (let x = 0; x < 10; x++)\n                s[y + x] ^= ~B[(x + 2) % 10] & B[(x + 4) % 10];\n        }\n        // Iota (ι)\n        s[0] ^= SHA3_IOTA_H[round];\n        s[1] ^= SHA3_IOTA_L[round];\n    }\n    (0, utils_ts_1.clean)(B);\n}\n/** Keccak sponge function. */\nclass Keccak extends utils_ts_1.Hash {\n    // NOTE: we accept arguments in bytes instead of bits here.\n    constructor(blockLen, suffix, outputLen, enableXOF = false, rounds = 24) {\n        super();\n        this.pos = 0;\n        this.posOut = 0;\n        this.finished = false;\n        this.destroyed = false;\n        this.enableXOF = false;\n        this.blockLen = blockLen;\n        this.suffix = suffix;\n        this.outputLen = outputLen;\n        this.enableXOF = enableXOF;\n        this.rounds = rounds;\n        // Can be passed from user as dkLen\n        (0, utils_ts_1.anumber)(outputLen);\n        // 1600 = 5x5 matrix of 64bit.  1600 bits === 200 bytes\n        // 0 < blockLen < 200\n        if (!(0 < blockLen && blockLen < 200))\n            throw new Error('only keccak-f1600 function is supported');\n        this.state = new Uint8Array(200);\n        this.state32 = (0, utils_ts_1.u32)(this.state);\n    }\n    clone() {\n        return this._cloneInto();\n    }\n    keccak() {\n        (0, utils_ts_1.swap32IfBE)(this.state32);\n        keccakP(this.state32, this.rounds);\n        (0, utils_ts_1.swap32IfBE)(this.state32);\n        this.posOut = 0;\n        this.pos = 0;\n    }\n    update(data) {\n        (0, utils_ts_1.aexists)(this);\n        data = (0, utils_ts_1.toBytes)(data);\n        (0, utils_ts_1.abytes)(data);\n        const { blockLen, state } = this;\n        const len = data.length;\n        for (let pos = 0; pos < len;) {\n            const take = Math.min(blockLen - this.pos, len - pos);\n            for (let i = 0; i < take; i++)\n                state[this.pos++] ^= data[pos++];\n            if (this.pos === blockLen)\n                this.keccak();\n        }\n        return this;\n    }\n    finish() {\n        if (this.finished)\n            return;\n        this.finished = true;\n        const { state, suffix, pos, blockLen } = this;\n        // Do the padding\n        state[pos] ^= suffix;\n        if ((suffix & 0x80) !== 0 && pos === blockLen - 1)\n            this.keccak();\n        state[blockLen - 1] ^= 0x80;\n        this.keccak();\n    }\n    writeInto(out) {\n        (0, utils_ts_1.aexists)(this, false);\n        (0, utils_ts_1.abytes)(out);\n        this.finish();\n        const bufferOut = this.state;\n        const { blockLen } = this;\n        for (let pos = 0, len = out.length; pos < len;) {\n            if (this.posOut >= blockLen)\n                this.keccak();\n            const take = Math.min(blockLen - this.posOut, len - pos);\n            out.set(bufferOut.subarray(this.posOut, this.posOut + take), pos);\n            this.posOut += take;\n            pos += take;\n        }\n        return out;\n    }\n    xofInto(out) {\n        // Sha3/Keccak usage with XOF is probably mistake, only SHAKE instances can do XOF\n        if (!this.enableXOF)\n            throw new Error('XOF is not possible for this instance');\n        return this.writeInto(out);\n    }\n    xof(bytes) {\n        (0, utils_ts_1.anumber)(bytes);\n        return this.xofInto(new Uint8Array(bytes));\n    }\n    digestInto(out) {\n        (0, utils_ts_1.aoutput)(out, this);\n        if (this.finished)\n            throw new Error('digest() was already called');\n        this.writeInto(out);\n        this.destroy();\n        return out;\n    }\n    digest() {\n        return this.digestInto(new Uint8Array(this.outputLen));\n    }\n    destroy() {\n        this.destroyed = true;\n        (0, utils_ts_1.clean)(this.state);\n    }\n    _cloneInto(to) {\n        const { blockLen, suffix, outputLen, rounds, enableXOF } = this;\n        to || (to = new Keccak(blockLen, suffix, outputLen, enableXOF, rounds));\n        to.state32.set(this.state32);\n        to.pos = this.pos;\n        to.posOut = this.posOut;\n        to.finished = this.finished;\n        to.rounds = rounds;\n        // Suffix can change in cSHAKE\n        to.suffix = suffix;\n        to.outputLen = outputLen;\n        to.enableXOF = enableXOF;\n        to.destroyed = this.destroyed;\n        return to;\n    }\n}\nexports.Keccak = Keccak;\nconst gen = (suffix, blockLen, outputLen) => (0, utils_ts_1.createHasher)(() => new Keccak(blockLen, suffix, outputLen));\n/** SHA3-224 hash function. */\nexports.sha3_224 = (() => gen(0x06, 144, 224 / 8))();\n/** SHA3-256 hash function. Different from keccak-256. */\nexports.sha3_256 = (() => gen(0x06, 136, 256 / 8))();\n/** SHA3-384 hash function. */\nexports.sha3_384 = (() => gen(0x06, 104, 384 / 8))();\n/** SHA3-512 hash function. */\nexports.sha3_512 = (() => gen(0x06, 72, 512 / 8))();\n/** keccak-224 hash function. */\nexports.keccak_224 = (() => gen(0x01, 144, 224 / 8))();\n/** keccak-256 hash function. Different from SHA3-256. */\nexports.keccak_256 = (() => gen(0x01, 136, 256 / 8))();\n/** keccak-384 hash function. */\nexports.keccak_384 = (() => gen(0x01, 104, 384 / 8))();\n/** keccak-512 hash function. */\nexports.keccak_512 = (() => gen(0x01, 72, 512 / 8))();\nconst genShake = (suffix, blockLen, outputLen) => (0, utils_ts_1.createXOFer)((opts = {}) => new Keccak(blockLen, suffix, opts.dkLen === undefined ? outputLen : opts.dkLen, true));\n/** SHAKE128 XOF with 128-bit security. */\nexports.shake128 = (() => genShake(0x1f, 168, 128 / 8))();\n/** SHAKE256 XOF with 256-bit security. */\nexports.shake256 = (() => genShake(0x1f, 136, 256 / 8))();\n//# sourceMappingURL=sha3.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@noble/hashes/sha3.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@noble/hashes/utils.js":
/*!*********************************************!*\
  !*** ./node_modules/@noble/hashes/utils.js ***!
  \*********************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\n/**\n * Utilities for hex, bytes, CSPRNG.\n * @module\n */\n/*! noble-hashes - MIT License (c) 2022 Paul Miller (paulmillr.com) */\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.wrapXOFConstructorWithOpts = exports.wrapConstructorWithOpts = exports.wrapConstructor = exports.Hash = exports.nextTick = exports.swap32IfBE = exports.byteSwapIfBE = exports.swap8IfBE = exports.isLE = void 0;\nexports.isBytes = isBytes;\nexports.anumber = anumber;\nexports.abytes = abytes;\nexports.ahash = ahash;\nexports.aexists = aexists;\nexports.aoutput = aoutput;\nexports.u8 = u8;\nexports.u32 = u32;\nexports.clean = clean;\nexports.createView = createView;\nexports.rotr = rotr;\nexports.rotl = rotl;\nexports.byteSwap = byteSwap;\nexports.byteSwap32 = byteSwap32;\nexports.bytesToHex = bytesToHex;\nexports.hexToBytes = hexToBytes;\nexports.asyncLoop = asyncLoop;\nexports.utf8ToBytes = utf8ToBytes;\nexports.bytesToUtf8 = bytesToUtf8;\nexports.toBytes = toBytes;\nexports.kdfInputToBytes = kdfInputToBytes;\nexports.concatBytes = concatBytes;\nexports.checkOpts = checkOpts;\nexports.createHasher = createHasher;\nexports.createOptHasher = createOptHasher;\nexports.createXOFer = createXOFer;\nexports.randomBytes = randomBytes;\n// We use WebCrypto aka globalThis.crypto, which exists in browsers and node.js 16+.\n// node.js versions earlier than v19 don't declare it in global scope.\n// For node.js, package.json#exports field mapping rewrites import\n// from `crypto` to `cryptoNode`, which imports native module.\n// Makes the utils un-importable in browsers without a bundler.\n// Once node.js 18 is deprecated (2025-04-30), we can just drop the import.\nconst crypto_1 = __webpack_require__(/*! @noble/hashes/crypto */ \"(ssr)/./node_modules/@noble/hashes/cryptoNode.js\");\n/** Checks if something is Uint8Array. Be careful: nodejs Buffer will return true. */\nfunction isBytes(a) {\n    return a instanceof Uint8Array || (ArrayBuffer.isView(a) && a.constructor.name === 'Uint8Array');\n}\n/** Asserts something is positive integer. */\nfunction anumber(n) {\n    if (!Number.isSafeInteger(n) || n < 0)\n        throw new Error('positive integer expected, got ' + n);\n}\n/** Asserts something is Uint8Array. */\nfunction abytes(b, ...lengths) {\n    if (!isBytes(b))\n        throw new Error('Uint8Array expected');\n    if (lengths.length > 0 && !lengths.includes(b.length))\n        throw new Error('Uint8Array expected of length ' + lengths + ', got length=' + b.length);\n}\n/** Asserts something is hash */\nfunction ahash(h) {\n    if (typeof h !== 'function' || typeof h.create !== 'function')\n        throw new Error('Hash should be wrapped by utils.createHasher');\n    anumber(h.outputLen);\n    anumber(h.blockLen);\n}\n/** Asserts a hash instance has not been destroyed / finished */\nfunction aexists(instance, checkFinished = true) {\n    if (instance.destroyed)\n        throw new Error('Hash instance has been destroyed');\n    if (checkFinished && instance.finished)\n        throw new Error('Hash#digest() has already been called');\n}\n/** Asserts output is properly-sized byte array */\nfunction aoutput(out, instance) {\n    abytes(out);\n    const min = instance.outputLen;\n    if (out.length < min) {\n        throw new Error('digestInto() expects output buffer of length at least ' + min);\n    }\n}\n/** Cast u8 / u16 / u32 to u8. */\nfunction u8(arr) {\n    return new Uint8Array(arr.buffer, arr.byteOffset, arr.byteLength);\n}\n/** Cast u8 / u16 / u32 to u32. */\nfunction u32(arr) {\n    return new Uint32Array(arr.buffer, arr.byteOffset, Math.floor(arr.byteLength / 4));\n}\n/** Zeroize a byte array. Warning: JS provides no guarantees. */\nfunction clean(...arrays) {\n    for (let i = 0; i < arrays.length; i++) {\n        arrays[i].fill(0);\n    }\n}\n/** Create DataView of an array for easy byte-level manipulation. */\nfunction createView(arr) {\n    return new DataView(arr.buffer, arr.byteOffset, arr.byteLength);\n}\n/** The rotate right (circular right shift) operation for uint32 */\nfunction rotr(word, shift) {\n    return (word << (32 - shift)) | (word >>> shift);\n}\n/** The rotate left (circular left shift) operation for uint32 */\nfunction rotl(word, shift) {\n    return (word << shift) | ((word >>> (32 - shift)) >>> 0);\n}\n/** Is current platform little-endian? Most are. Big-Endian platform: IBM */\nexports.isLE = (() => new Uint8Array(new Uint32Array([0x11223344]).buffer)[0] === 0x44)();\n/** The byte swap operation for uint32 */\nfunction byteSwap(word) {\n    return (((word << 24) & 0xff000000) |\n        ((word << 8) & 0xff0000) |\n        ((word >>> 8) & 0xff00) |\n        ((word >>> 24) & 0xff));\n}\n/** Conditionally byte swap if on a big-endian platform */\nexports.swap8IfBE = exports.isLE\n    ? (n) => n\n    : (n) => byteSwap(n);\n/** @deprecated */\nexports.byteSwapIfBE = exports.swap8IfBE;\n/** In place byte swap for Uint32Array */\nfunction byteSwap32(arr) {\n    for (let i = 0; i < arr.length; i++) {\n        arr[i] = byteSwap(arr[i]);\n    }\n    return arr;\n}\nexports.swap32IfBE = exports.isLE\n    ? (u) => u\n    : byteSwap32;\n// Built-in hex conversion https://caniuse.com/mdn-javascript_builtins_uint8array_fromhex\nconst hasHexBuiltin = /* @__PURE__ */ (() => \n// @ts-ignore\ntypeof Uint8Array.from([]).toHex === 'function' && typeof Uint8Array.fromHex === 'function')();\n// Array where index 0xf0 (240) is mapped to string 'f0'\nconst hexes = /* @__PURE__ */ Array.from({ length: 256 }, (_, i) => i.toString(16).padStart(2, '0'));\n/**\n * Convert byte array to hex string. Uses built-in function, when available.\n * @example bytesToHex(Uint8Array.from([0xca, 0xfe, 0x01, 0x23])) // 'cafe0123'\n */\nfunction bytesToHex(bytes) {\n    abytes(bytes);\n    // @ts-ignore\n    if (hasHexBuiltin)\n        return bytes.toHex();\n    // pre-caching improves the speed 6x\n    let hex = '';\n    for (let i = 0; i < bytes.length; i++) {\n        hex += hexes[bytes[i]];\n    }\n    return hex;\n}\n// We use optimized technique to convert hex string to byte array\nconst asciis = { _0: 48, _9: 57, A: 65, F: 70, a: 97, f: 102 };\nfunction asciiToBase16(ch) {\n    if (ch >= asciis._0 && ch <= asciis._9)\n        return ch - asciis._0; // '2' => 50-48\n    if (ch >= asciis.A && ch <= asciis.F)\n        return ch - (asciis.A - 10); // 'B' => 66-(65-10)\n    if (ch >= asciis.a && ch <= asciis.f)\n        return ch - (asciis.a - 10); // 'b' => 98-(97-10)\n    return;\n}\n/**\n * Convert hex string to byte array. Uses built-in function, when available.\n * @example hexToBytes('cafe0123') // Uint8Array.from([0xca, 0xfe, 0x01, 0x23])\n */\nfunction hexToBytes(hex) {\n    if (typeof hex !== 'string')\n        throw new Error('hex string expected, got ' + typeof hex);\n    // @ts-ignore\n    if (hasHexBuiltin)\n        return Uint8Array.fromHex(hex);\n    const hl = hex.length;\n    const al = hl / 2;\n    if (hl % 2)\n        throw new Error('hex string expected, got unpadded hex of length ' + hl);\n    const array = new Uint8Array(al);\n    for (let ai = 0, hi = 0; ai < al; ai++, hi += 2) {\n        const n1 = asciiToBase16(hex.charCodeAt(hi));\n        const n2 = asciiToBase16(hex.charCodeAt(hi + 1));\n        if (n1 === undefined || n2 === undefined) {\n            const char = hex[hi] + hex[hi + 1];\n            throw new Error('hex string expected, got non-hex character \"' + char + '\" at index ' + hi);\n        }\n        array[ai] = n1 * 16 + n2; // multiply first octet, e.g. 'a3' => 10*16+3 => 160 + 3 => 163\n    }\n    return array;\n}\n/**\n * There is no setImmediate in browser and setTimeout is slow.\n * Call of async fn will return Promise, which will be fullfiled only on\n * next scheduler queue processing step and this is exactly what we need.\n */\nconst nextTick = async () => { };\nexports.nextTick = nextTick;\n/** Returns control to thread each 'tick' ms to avoid blocking. */\nasync function asyncLoop(iters, tick, cb) {\n    let ts = Date.now();\n    for (let i = 0; i < iters; i++) {\n        cb(i);\n        // Date.now() is not monotonic, so in case if clock goes backwards we return return control too\n        const diff = Date.now() - ts;\n        if (diff >= 0 && diff < tick)\n            continue;\n        await (0, exports.nextTick)();\n        ts += diff;\n    }\n}\n/**\n * Converts string to bytes using UTF8 encoding.\n * @example utf8ToBytes('abc') // Uint8Array.from([97, 98, 99])\n */\nfunction utf8ToBytes(str) {\n    if (typeof str !== 'string')\n        throw new Error('string expected');\n    return new Uint8Array(new TextEncoder().encode(str)); // https://bugzil.la/1681809\n}\n/**\n * Converts bytes to string using UTF8 encoding.\n * @example bytesToUtf8(Uint8Array.from([97, 98, 99])) // 'abc'\n */\nfunction bytesToUtf8(bytes) {\n    return new TextDecoder().decode(bytes);\n}\n/**\n * Normalizes (non-hex) string or Uint8Array to Uint8Array.\n * Warning: when Uint8Array is passed, it would NOT get copied.\n * Keep in mind for future mutable operations.\n */\nfunction toBytes(data) {\n    if (typeof data === 'string')\n        data = utf8ToBytes(data);\n    abytes(data);\n    return data;\n}\n/**\n * Helper for KDFs: consumes uint8array or string.\n * When string is passed, does utf8 decoding, using TextDecoder.\n */\nfunction kdfInputToBytes(data) {\n    if (typeof data === 'string')\n        data = utf8ToBytes(data);\n    abytes(data);\n    return data;\n}\n/** Copies several Uint8Arrays into one. */\nfunction concatBytes(...arrays) {\n    let sum = 0;\n    for (let i = 0; i < arrays.length; i++) {\n        const a = arrays[i];\n        abytes(a);\n        sum += a.length;\n    }\n    const res = new Uint8Array(sum);\n    for (let i = 0, pad = 0; i < arrays.length; i++) {\n        const a = arrays[i];\n        res.set(a, pad);\n        pad += a.length;\n    }\n    return res;\n}\nfunction checkOpts(defaults, opts) {\n    if (opts !== undefined && {}.toString.call(opts) !== '[object Object]')\n        throw new Error('options should be object or undefined');\n    const merged = Object.assign(defaults, opts);\n    return merged;\n}\n/** For runtime check if class implements interface */\nclass Hash {\n}\nexports.Hash = Hash;\n/** Wraps hash function, creating an interface on top of it */\nfunction createHasher(hashCons) {\n    const hashC = (msg) => hashCons().update(toBytes(msg)).digest();\n    const tmp = hashCons();\n    hashC.outputLen = tmp.outputLen;\n    hashC.blockLen = tmp.blockLen;\n    hashC.create = () => hashCons();\n    return hashC;\n}\nfunction createOptHasher(hashCons) {\n    const hashC = (msg, opts) => hashCons(opts).update(toBytes(msg)).digest();\n    const tmp = hashCons({});\n    hashC.outputLen = tmp.outputLen;\n    hashC.blockLen = tmp.blockLen;\n    hashC.create = (opts) => hashCons(opts);\n    return hashC;\n}\nfunction createXOFer(hashCons) {\n    const hashC = (msg, opts) => hashCons(opts).update(toBytes(msg)).digest();\n    const tmp = hashCons({});\n    hashC.outputLen = tmp.outputLen;\n    hashC.blockLen = tmp.blockLen;\n    hashC.create = (opts) => hashCons(opts);\n    return hashC;\n}\nexports.wrapConstructor = createHasher;\nexports.wrapConstructorWithOpts = createOptHasher;\nexports.wrapXOFConstructorWithOpts = createXOFer;\n/** Cryptographically secure PRNG. Uses internal OS-level `crypto.getRandomValues`. */\nfunction randomBytes(bytesLength = 32) {\n    if (crypto_1.crypto && typeof crypto_1.crypto.getRandomValues === 'function') {\n        return crypto_1.crypto.getRandomValues(new Uint8Array(bytesLength));\n    }\n    // Legacy Node.js compatibility\n    if (crypto_1.crypto && typeof crypto_1.crypto.randomBytes === 'function') {\n        return Uint8Array.from(crypto_1.crypto.randomBytes(bytesLength));\n    }\n    throw new Error('crypto.getRandomValues must be defined');\n}\n//# sourceMappingURL=utils.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@noble/hashes/utils.js\n");

/***/ })

};
;