@tailwind base;
@tailwind components;
@tailwind utilities;

@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=JetBrains+Mono:wght@400;500;600&display=swap');

:root {
  --foreground-rgb: 255, 255, 255;
  --background-start-rgb: 15, 23, 42;
  --background-end-rgb: 2, 6, 23;
}

* {
  box-sizing: border-box;
  padding: 0;
  margin: 0;
}

html,
body {
  max-width: 100vw;
  overflow-x: hidden;
  font-family: 'Inter', sans-serif;
}

body {
  color: rgb(var(--foreground-rgb));
  background: linear-gradient(
      to bottom,
      transparent,
      rgb(var(--background-end-rgb))
    )
    rgb(var(--background-start-rgb));
  min-height: 100vh;
}

a {
  color: inherit;
  text-decoration: none;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #1e293b;
}

::-webkit-scrollbar-thumb {
  background: #ffd700;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #ffed4e;
}

/* Luxury animations */
.luxury-glow {
  box-shadow: 0 0 20px rgba(255, 215, 0, 0.3);
  transition: box-shadow 0.3s ease;
}

.luxury-glow:hover {
  box-shadow: 0 0 30px rgba(255, 215, 0, 0.5);
}

.text-glow {
  text-shadow: 0 0 10px rgba(255, 215, 0, 0.5);
}

/* Loading animations */
.loading-dots {
  display: inline-block;
}

.loading-dots::after {
  content: '';
  animation: dots 1.5s steps(5, end) infinite;
}

@keyframes dots {
  0%, 20% { content: ''; }
  40% { content: '.'; }
  60% { content: '..'; }
  80%, 100% { content: '...'; }
}

/* Glass morphism effect */
.glass {
  background: rgba(30, 41, 59, 0.7);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 215, 0, 0.1);
}

/* Button styles */
.btn-primary {
  @apply bg-gradient-to-r from-primary-500 to-primary-600 text-dark-900 font-semibold py-3 px-6 rounded-lg transition-all duration-300 hover:from-primary-400 hover:to-primary-500 hover:shadow-lg hover:shadow-primary-500/25;
}

.btn-secondary {
  @apply bg-dark-800 text-primary-500 border border-primary-500 font-semibold py-3 px-6 rounded-lg transition-all duration-300 hover:bg-primary-500 hover:text-dark-900;
}

.btn-ghost {
  @apply text-primary-500 font-semibold py-3 px-6 rounded-lg transition-all duration-300 hover:bg-primary-500/10;
}
