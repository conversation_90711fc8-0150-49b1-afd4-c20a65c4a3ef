'use client';

import { motion } from 'framer-motion';
import { Hero } from '@/components/Hero';
import { FeatureGrid } from '@/components/FeatureGrid';
import { LiveStats } from '@/components/LiveStats';
import { AIChat } from '@/components/AIChat';

export default function HomePage() {
  return (
    <div className="min-h-screen bg-luxury-gradient">
      {/* Hero Section */}
      <Hero />
      
      {/* Live Stats Dashboard */}
      <section className="py-20 px-4">
        <div className="max-w-7xl mx-auto">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="text-center mb-12"
          >
            <h2 className="text-4xl font-bold text-white mb-4">
              Real-time <span className="text-glow text-primary-500">Intelligence</span>
            </h2>
            <p className="text-dark-300 text-lg max-w-2xl mx-auto">
              Live market data, blockchain analytics, and AI insights at your fingertips
            </p>
          </motion.div>
          <LiveStats />
        </div>
      </section>

      {/* Feature Grid */}
      <section className="py-20 px-4">
        <div className="max-w-7xl mx-auto">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="text-center mb-12"
          >
            <h2 className="text-4xl font-bold text-white mb-4">
              Modular <span className="text-glow text-primary-500">AI Capabilities</span>
            </h2>
            <p className="text-dark-300 text-lg max-w-2xl mx-auto">
              Comprehensive suite of AI-powered tools for blockchain, finance, design, and more
            </p>
          </motion.div>
          <FeatureGrid />
        </div>
      </section>

      {/* AI Chat Interface */}
      <section className="py-20 px-4">
        <div className="max-w-4xl mx-auto">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="text-center mb-12"
          >
            <h2 className="text-4xl font-bold text-white mb-4">
              AI <span className="text-glow text-primary-500">Assistant</span>
            </h2>
            <p className="text-dark-300 text-lg max-w-2xl mx-auto">
              Interact with our intelligent agent for instant insights and assistance
            </p>
          </motion.div>
          <AIChat />
        </div>
      </section>
    </div>
  );
}
