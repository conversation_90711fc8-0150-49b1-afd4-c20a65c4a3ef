from fastapi import <PERSON><PERSON><PERSON>er, WebSocket, WebSocketDisconnect, Depends
from typing import Dict, List
import json
import asyncio
from datetime import datetime

router = APIRouter()

class WebSocketManager:
    def __init__(self):
        self.active_connections: Dict[str, WebSocket] = {}
        self.user_connections: Dict[str, List[str]] = {}  # user_id -> [client_ids]

    async def connect(self, websocket: WebSocket, client_id: str, user_id: str = None):
        await websocket.accept()
        self.active_connections[client_id] = websocket
        
        if user_id:
            if user_id not in self.user_connections:
                self.user_connections[user_id] = []
            self.user_connections[user_id].append(client_id)
        
        print(f"Client {client_id} connected")

    def disconnect(self, client_id: str, user_id: str = None):
        if client_id in self.active_connections:
            del self.active_connections[client_id]
        
        if user_id and user_id in self.user_connections:
            if client_id in self.user_connections[user_id]:
                self.user_connections[user_id].remove(client_id)
            if not self.user_connections[user_id]:
                del self.user_connections[user_id]
        
        print(f"Client {client_id} disconnected")

    async def send_personal_message(self, message: str, client_id: str):
        if client_id in self.active_connections:
            try:
                await self.active_connections[client_id].send_text(message)
            except:
                self.disconnect(client_id)

    async def send_to_user(self, message: str, user_id: str):
        if user_id in self.user_connections:
            for client_id in self.user_connections[user_id].copy():
                await self.send_personal_message(message, client_id)

    async def broadcast(self, message: str):
        for client_id in list(self.active_connections.keys()):
            await self.send_personal_message(message, client_id)

    async def send_json(self, data: dict, client_id: str):
        await self.send_personal_message(json.dumps(data), client_id)

    async def broadcast_json(self, data: dict):
        message = json.dumps(data)
        await self.broadcast(message)

# Global WebSocket manager
manager = WebSocketManager()

@router.websocket("/live-updates/{client_id}")
async def websocket_endpoint(websocket: WebSocket, client_id: str):
    await manager.connect(websocket, client_id)
    try:
        # Send welcome message
        await manager.send_json({
            "type": "connection",
            "message": "Connected to Chainsight live updates",
            "timestamp": datetime.now().isoformat(),
            "client_id": client_id
        }, client_id)
        
        # Start sending periodic updates
        update_task = asyncio.create_task(send_periodic_updates(client_id))
        
        while True:
            # Listen for client messages
            data = await websocket.receive_text()
            message_data = json.loads(data)
            
            # Handle different message types
            if message_data.get("type") == "subscribe":
                await handle_subscription(client_id, message_data)
            elif message_data.get("type") == "unsubscribe":
                await handle_unsubscription(client_id, message_data)
            elif message_data.get("type") == "ping":
                await manager.send_json({
                    "type": "pong",
                    "timestamp": datetime.now().isoformat()
                }, client_id)
            
    except WebSocketDisconnect:
        manager.disconnect(client_id)
        if 'update_task' in locals():
            update_task.cancel()

async def send_periodic_updates(client_id: str):
    """Send periodic market and system updates"""
    try:
        while True:
            # Market data updates
            market_data = {
                "type": "market_update",
                "data": {
                    "BTC": {"price": 43250 + (hash(str(datetime.now())) % 1000 - 500), "change": 2.4},
                    "ETH": {"price": 2680 + (hash(str(datetime.now())) % 100 - 50), "change": 1.8},
                    "USDT": {"price": 1.0, "change": 0.1}
                },
                "timestamp": datetime.now().isoformat()
            }
            await manager.send_json(market_data, client_id)
            
            # System stats
            system_stats = {
                "type": "system_stats",
                "data": {
                    "active_users": len(manager.user_connections),
                    "total_connections": len(manager.active_connections),
                    "server_load": 0.45,
                    "uptime": "99.9%"
                },
                "timestamp": datetime.now().isoformat()
            }
            await manager.send_json(system_stats, client_id)
            
            await asyncio.sleep(5)  # Update every 5 seconds
            
    except asyncio.CancelledError:
        pass

async def handle_subscription(client_id: str, message_data: dict):
    """Handle subscription requests"""
    subscription_type = message_data.get("subscription")
    
    if subscription_type == "crypto_prices":
        await manager.send_json({
            "type": "subscription_confirmed",
            "subscription": "crypto_prices",
            "message": "Subscribed to real-time crypto price updates",
            "timestamp": datetime.now().isoformat()
        }, client_id)
        
    elif subscription_type == "ai_chat":
        await manager.send_json({
            "type": "subscription_confirmed", 
            "subscription": "ai_chat",
            "message": "Subscribed to AI chat notifications",
            "timestamp": datetime.now().isoformat()
        }, client_id)
        
    elif subscription_type == "system_alerts":
        await manager.send_json({
            "type": "subscription_confirmed",
            "subscription": "system_alerts", 
            "message": "Subscribed to system alerts",
            "timestamp": datetime.now().isoformat()
        }, client_id)

async def handle_unsubscription(client_id: str, message_data: dict):
    """Handle unsubscription requests"""
    subscription_type = message_data.get("subscription")
    
    await manager.send_json({
        "type": "unsubscription_confirmed",
        "subscription": subscription_type,
        "message": f"Unsubscribed from {subscription_type}",
        "timestamp": datetime.now().isoformat()
    }, client_id)

@router.websocket("/ai-chat/{client_id}")
async def ai_chat_websocket(websocket: WebSocket, client_id: str):
    """Dedicated WebSocket for AI chat"""
    await manager.connect(websocket, client_id)
    try:
        await manager.send_json({
            "type": "chat_connected",
            "message": "Connected to AI chat",
            "timestamp": datetime.now().isoformat()
        }, client_id)
        
        while True:
            data = await websocket.receive_text()
            message_data = json.loads(data)
            
            if message_data.get("type") == "chat_message":
                # Simulate AI response
                user_message = message_data.get("message", "")
                
                # Send typing indicator
                await manager.send_json({
                    "type": "typing_indicator",
                    "typing": True,
                    "timestamp": datetime.now().isoformat()
                }, client_id)
                
                # Simulate processing delay
                await asyncio.sleep(1)
                
                # Generate AI response
                ai_response = generate_ai_response(user_message)
                
                # Send response
                await manager.send_json({
                    "type": "ai_response",
                    "message": ai_response,
                    "timestamp": datetime.now().isoformat(),
                    "session_id": message_data.get("session_id")
                }, client_id)
                
                # Stop typing indicator
                await manager.send_json({
                    "type": "typing_indicator",
                    "typing": False,
                    "timestamp": datetime.now().isoformat()
                }, client_id)
                
    except WebSocketDisconnect:
        manager.disconnect(client_id)

def generate_ai_response(message: str) -> str:
    """Generate AI response for WebSocket chat"""
    message_lower = message.lower()
    
    if "crypto" in message_lower or "bitcoin" in message_lower:
        return "I can help you analyze cryptocurrency markets and deploy smart contracts. Current Bitcoin is showing strong momentum. Would you like a detailed market analysis?"
    elif "price" in message_lower:
        return "I'm tracking real-time prices across all major cryptocurrencies. BTC is at $43,250 (+2.4%), ETH at $2,680 (+1.8%). Need specific price alerts?"
    elif "hello" in message_lower or "hi" in message_lower:
        return "Hello! I'm your Chainsight AI assistant. I can help with crypto analysis, smart contracts, market predictions, and more. What would you like to explore?"
    else:
        return "I understand you're looking for assistance. I can help with blockchain operations, market analysis, smart contract deployment, and real-time insights. How can I assist you today?"

@router.websocket("/market-data/{client_id}")
async def market_data_websocket(websocket: WebSocket, client_id: str):
    """Dedicated WebSocket for market data"""
    await manager.connect(websocket, client_id)
    try:
        while True:
            # Send real-time market data
            market_update = {
                "type": "market_tick",
                "data": {
                    "BTC": {
                        "price": 43250 + (hash(str(datetime.now())) % 1000 - 500),
                        "volume": 28500000000,
                        "change_24h": 2.4
                    },
                    "ETH": {
                        "price": 2680 + (hash(str(datetime.now())) % 100 - 50),
                        "volume": 15200000000,
                        "change_24h": 1.8
                    },
                    "USDT": {
                        "price": 1.0,
                        "volume": 45000000000,
                        "change_24h": 0.1
                    }
                },
                "timestamp": datetime.now().isoformat()
            }
            
            await manager.send_json(market_update, client_id)
            await asyncio.sleep(2)  # Update every 2 seconds
            
    except WebSocketDisconnect:
        manager.disconnect(client_id)

# Utility functions for broadcasting updates
async def broadcast_market_alert(symbol: str, price: float, change: float):
    """Broadcast market alerts to all connected clients"""
    alert = {
        "type": "market_alert",
        "symbol": symbol,
        "price": price,
        "change": change,
        "alert_type": "significant_move" if abs(change) > 5 else "price_update",
        "timestamp": datetime.now().isoformat()
    }
    await manager.broadcast_json(alert)

async def broadcast_system_notification(message: str, level: str = "info"):
    """Broadcast system notifications"""
    notification = {
        "type": "system_notification",
        "message": message,
        "level": level,  # info, warning, error
        "timestamp": datetime.now().isoformat()
    }
    await manager.broadcast_json(notification)

# Export manager for use in other modules
__all__ = ["manager", "broadcast_market_alert", "broadcast_system_notification"]
