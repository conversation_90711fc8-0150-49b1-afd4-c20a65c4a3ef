"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ HomePage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _components_Navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/Navigation */ \"(app-pages-browser)/./src/components/Navigation.tsx\");\n/* harmony import */ var _components_ModularDashboard__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ModularDashboard */ \"(app-pages-browser)/./src/components/ModularDashboard.tsx\");\n/* harmony import */ var _components_AIModuleSelector__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/AIModuleSelector */ \"(app-pages-browser)/./src/components/AIModuleSelector.tsx\");\n/* harmony import */ var _components_RealTimeNotifications__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/RealTimeNotifications */ \"(app-pages-browser)/./src/components/RealTimeNotifications.tsx\");\n/* harmony import */ var _components_SystemStatus__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/SystemStatus */ \"(app-pages-browser)/./src/components/SystemStatus.tsx\");\n/* harmony import */ var _components_ClientOnly__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ClientOnly */ \"(app-pages-browser)/./src/components/ClientOnly.tsx\");\n/* harmony import */ var _components_RealTimeChat__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/RealTimeChat */ \"(app-pages-browser)/./src/components/RealTimeChat.tsx\");\n/* harmony import */ var _store_useAppStore__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/store/useAppStore */ \"(app-pages-browser)/./src/store/useAppStore.ts\");\n/* harmony import */ var _hooks_useAIModules__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/hooks/useAIModules */ \"(app-pages-browser)/./src/hooks/useAIModules.ts\");\n/* harmony import */ var _hooks_useRealTimeData__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/hooks/useRealTimeData */ \"(app-pages-browser)/./src/hooks/useRealTimeData.ts\");\n/* harmony import */ var _lib_debug__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/lib/debug */ \"(app-pages-browser)/./src/lib/debug.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\nfunction HomePage() {\n    _s();\n    const [isChatOpen, setIsChatOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const { modules, isLoading: modulesLoading } = (0,_hooks_useAIModules__WEBPACK_IMPORTED_MODULE_10__.useAIModules)();\n    const { connectionStatus, marketData } = (0,_hooks_useRealTimeData__WEBPACK_IMPORTED_MODULE_11__.useRealTimeData)();\n    // Debug tracking\n    const { logMount, logUnmount, logError, logInfo } = (0,_lib_debug__WEBPACK_IMPORTED_MODULE_12__.useDebug)('HomePage');\n    // Global state\n    const { updateSettings, updateConnectionStatus } = (0,_store_useAppStore__WEBPACK_IMPORTED_MODULE_9__.useAppActions)();\n    const settings = (0,_store_useAppStore__WEBPACK_IMPORTED_MODULE_9__.useSettings)();\n    const activeModule = settings.activeModule;\n    const handleModuleSelect = (moduleId)=>{\n        updateSettings({\n            activeModule: moduleId\n        });\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"HomePage.useEffect\": ()=>{\n            try {\n                logMount();\n                // Initialize production platform\n                _lib_debug__WEBPACK_IMPORTED_MODULE_12__.chainSightDebugger.logInfo('🚀 Chainsight Production Platform Initialized');\n                logInfo('Platform initialization started');\n                // Performance tracking\n                _lib_debug__WEBPACK_IMPORTED_MODULE_12__.debugUtils.performanceMark('platform-init-start');\n                // Update connection status\n                updateConnectionStatus({\n                    api: 'connected',\n                    websocket: 'connected',\n                    blockchain: 'connected'\n                });\n                _lib_debug__WEBPACK_IMPORTED_MODULE_12__.debugUtils.performanceMark('platform-init-end');\n                _lib_debug__WEBPACK_IMPORTED_MODULE_12__.debugUtils.performanceMeasure('platform-init', 'platform-init-start', 'platform-init-end');\n                logInfo('Platform initialization completed', {\n                    modulesCount: modules.length,\n                    connectionStatus,\n                    chatOpen: isChatOpen\n                });\n            } catch (error) {\n                logError(error, 'Platform initialization failed');\n            }\n            return ({\n                \"HomePage.useEffect\": ()=>{\n                    logUnmount();\n                }\n            })[\"HomePage.useEffect\"];\n        }\n    }[\"HomePage.useEffect\"], [\n        connectionStatus,\n        updateConnectionStatus,\n        logMount,\n        logUnmount,\n        logError,\n        logInfo,\n        modules.length,\n        isChatOpen\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-br from-cosmic-dark via-cosmic-mid to-cosmic-light\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ClientOnly__WEBPACK_IMPORTED_MODULE_7__.ClientOnly, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Navigation__WEBPACK_IMPORTED_MODULE_2__.Navigation, {}, void 0, false, {\n                    fileName: \"F:\\\\AI chain\\\\chainsight-production\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 73,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"F:\\\\AI chain\\\\chainsight-production\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 72,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                className: \"pt-20\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                        className: \"relative overflow-hidden min-h-screen flex items-center justify-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute inset-0 bg-gradient-to-br from-cosmic-dark via-cosmic-mid to-cosmic-light\"\n                            }, void 0, false, {\n                                fileName: \"F:\\\\AI chain\\\\chainsight-production\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 80,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute top-[20%] left-[20%] w-96 h-96 bg-gradient-to-r from-neon-blue/20 to-neon-purple/20 rounded-full blur-3xl animate-pulse\"\n                            }, void 0, false, {\n                                fileName: \"F:\\\\AI chain\\\\chainsight-production\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 83,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute top-[80%] right-[20%] w-96 h-96 bg-gradient-to-r from-neon-pink/20 to-neon-orange/20 rounded-full blur-3xl animate-pulse\"\n                            }, void 0, false, {\n                                fileName: \"F:\\\\AI chain\\\\chainsight-production\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 84,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute top-[40%] right-[40%] w-96 h-96 bg-gradient-to-r from-neon-green/20 to-accent-cyan/20 rounded-full blur-3xl animate-pulse\"\n                            }, void 0, false, {\n                                fileName: \"F:\\\\AI chain\\\\chainsight-production\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 85,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative max-w-7xl mx-auto px-4 py-20 text-center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_13__.motion.div, {\n                                    initial: {\n                                        opacity: 0,\n                                        y: 20\n                                    },\n                                    animate: {\n                                        opacity: 1,\n                                        y: 0\n                                    },\n                                    className: \"space-y-12\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_13__.motion.h1, {\n                                            initial: {\n                                                opacity: 0,\n                                                y: 20\n                                            },\n                                            animate: {\n                                                opacity: 1,\n                                                y: 0\n                                            },\n                                            transition: {\n                                                delay: 0.4,\n                                                duration: 0.8\n                                            },\n                                            className: \"text-6xl md:text-8xl font-bold leading-tight float\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"block bg-gradient-to-r from-neon-blue via-neon-purple to-neon-pink bg-clip-text text-transparent\",\n                                                    children: \"CHAINSIGHT\"\n                                                }, void 0, false, {\n                                                    fileName: \"F:\\\\AI chain\\\\chainsight-production\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 100,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"block text-2xl md:text-4xl mt-4 neon-glow-blue\",\n                                                    children: \"by Connectouch\"\n                                                }, void 0, false, {\n                                                    fileName: \"F:\\\\AI chain\\\\chainsight-production\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 103,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"F:\\\\AI chain\\\\chainsight-production\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 94,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_13__.motion.p, {\n                                            initial: {\n                                                opacity: 0,\n                                                y: 20\n                                            },\n                                            animate: {\n                                                opacity: 1,\n                                                y: 0\n                                            },\n                                            transition: {\n                                                delay: 0.6,\n                                                duration: 0.8\n                                            },\n                                            className: \"text-xl md:text-2xl text-white/80 max-w-3xl mx-auto leading-relaxed\",\n                                            children: [\n                                                \"\\uD83D\\uDE80 Vibrant AI-Powered Platform with\",\n                                                ' ',\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"neon-glow-purple\",\n                                                    children: \"Real-Time Analytics\"\n                                                }, void 0, false, {\n                                                    fileName: \"F:\\\\AI chain\\\\chainsight-production\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 116,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \",\",\n                                                ' ',\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"neon-glow-pink\",\n                                                    children: \"Interactive Charts\"\n                                                }, void 0, false, {\n                                                    fileName: \"F:\\\\AI chain\\\\chainsight-production\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 117,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \", and\",\n                                                ' ',\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"neon-glow-blue\",\n                                                    children: \"Futuristic Design\"\n                                                }, void 0, false, {\n                                                    fileName: \"F:\\\\AI chain\\\\chainsight-production\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 118,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"F:\\\\AI chain\\\\chainsight-production\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 109,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_13__.motion.div, {\n                                            initial: {\n                                                opacity: 0,\n                                                y: 20\n                                            },\n                                            animate: {\n                                                opacity: 1,\n                                                y: 0\n                                            },\n                                            transition: {\n                                                delay: 0.8,\n                                                duration: 0.8\n                                            },\n                                            className: \"max-w-4xl mx-auto\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ClientOnly__WEBPACK_IMPORTED_MODULE_7__.ClientOnly, {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_AIModuleSelector__WEBPACK_IMPORTED_MODULE_4__.AIModuleSelector, {\n                                                    activeModule: activeModule,\n                                                    onModuleChange: handleModuleSelect,\n                                                    modules: modules,\n                                                    loading: modulesLoading\n                                                }, void 0, false, {\n                                                    fileName: \"F:\\\\AI chain\\\\chainsight-production\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 129,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"F:\\\\AI chain\\\\chainsight-production\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 128,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"F:\\\\AI chain\\\\chainsight-production\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 122,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"F:\\\\AI chain\\\\chainsight-production\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 88,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"F:\\\\AI chain\\\\chainsight-production\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 87,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"F:\\\\AI chain\\\\chainsight-production\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 78,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                        className: \"py-20\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"max-w-7xl mx-auto px-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ClientOnly__WEBPACK_IMPORTED_MODULE_7__.ClientOnly, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ModularDashboard__WEBPACK_IMPORTED_MODULE_3__.ModularDashboard, {\n                                    activeModule: activeModule,\n                                    marketData: marketData,\n                                    modules: modules\n                                }, void 0, false, {\n                                    fileName: \"F:\\\\AI chain\\\\chainsight-production\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 145,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"F:\\\\AI chain\\\\chainsight-production\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 144,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"F:\\\\AI chain\\\\chainsight-production\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 143,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"F:\\\\AI chain\\\\chainsight-production\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 142,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"fixed bottom-4 right-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ClientOnly__WEBPACK_IMPORTED_MODULE_7__.ClientOnly, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_SystemStatus__WEBPACK_IMPORTED_MODULE_6__.SystemStatus, {\n                                status: connectionStatus\n                            }, void 0, false, {\n                                fileName: \"F:\\\\AI chain\\\\chainsight-production\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 157,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"F:\\\\AI chain\\\\chainsight-production\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 156,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"F:\\\\AI chain\\\\chainsight-production\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 155,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ClientOnly__WEBPACK_IMPORTED_MODULE_7__.ClientOnly, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_RealTimeNotifications__WEBPACK_IMPORTED_MODULE_5__.RealTimeNotifications, {}, void 0, false, {\n                            fileName: \"F:\\\\AI chain\\\\chainsight-production\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 163,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"F:\\\\AI chain\\\\chainsight-production\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 162,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"F:\\\\AI chain\\\\chainsight-production\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 76,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ClientOnly__WEBPACK_IMPORTED_MODULE_7__.ClientOnly, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_RealTimeChat__WEBPACK_IMPORTED_MODULE_8__.RealTimeChat, {\n                    isOpen: isChatOpen,\n                    onToggle: ()=>setIsChatOpen(!isChatOpen)\n                }, void 0, false, {\n                    fileName: \"F:\\\\AI chain\\\\chainsight-production\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 169,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"F:\\\\AI chain\\\\chainsight-production\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 168,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"F:\\\\AI chain\\\\chainsight-production\\\\src\\\\app\\\\page.tsx\",\n        lineNumber: 71,\n        columnNumber: 5\n    }, this);\n}\n_s(HomePage, \"H2FMMNnNNrVkwL6ap/tnEFWyICw=\", false, function() {\n    return [\n        _hooks_useAIModules__WEBPACK_IMPORTED_MODULE_10__.useAIModules,\n        _hooks_useRealTimeData__WEBPACK_IMPORTED_MODULE_11__.useRealTimeData,\n        _lib_debug__WEBPACK_IMPORTED_MODULE_12__.useDebug,\n        _store_useAppStore__WEBPACK_IMPORTED_MODULE_9__.useAppActions,\n        _store_useAppStore__WEBPACK_IMPORTED_MODULE_9__.useSettings\n    ];\n});\n_c = HomePage;\nvar _c;\n$RefreshReg$(_c, \"HomePage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/page.tsx\n"));

/***/ })

});