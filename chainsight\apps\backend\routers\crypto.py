from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session
from pydantic import BaseModel
from typing import Optional, List
import json
import os
from web3 import Web3
from eth_account import Account
import requests

from db import get_db, TokenContract, User
from routers.auth import get_current_user

router = APIRouter()

# Web3 setup
ETHEREUM_RPC_URL = os.getenv("ETHEREUM_RPC_URL", "https://mainnet.infura.io/v3/your_key")
w3 = Web3(Web3.HTTPProvider(ETHEREUM_RPC_URL))

# Pydantic models
class TokenCreateRequest(BaseModel):
    name: str
    symbol: str
    total_supply: int
    decimals: int = 18
    network: str = "ethereum"

class TokenResponse(BaseModel):
    id: int
    contract_address: str
    token_name: str
    token_symbol: str
    total_supply: str
    network: str
    transaction_hash: str = None

class LiquidityPoolRequest(BaseModel):
    token_address: str
    eth_amount: float
    token_amount: int
    network: str = "ethereum"

class MarketDataResponse(BaseModel):
    symbol: str
    price: float
    change_24h: float
    volume_24h: float
    market_cap: float

# ERC20 Contract Template
ERC20_CONTRACT_SOURCE = '''
pragma solidity ^0.8.0;

interface IERC20 {
    function totalSupply() external view returns (uint256);
    function balanceOf(address account) external view returns (uint256);
    function transfer(address recipient, uint256 amount) external returns (bool);
    function allowance(address owner, address spender) external view returns (uint256);
    function approve(address spender, uint256 amount) external returns (bool);
    function transferFrom(address sender, address recipient, uint256 amount) external returns (bool);
    
    event Transfer(address indexed from, address indexed to, uint256 value);
    event Approval(address indexed owner, address indexed spender, uint256 value);
}

contract {token_name} is IERC20 {
    mapping(address => uint256) private _balances;
    mapping(address => mapping(address => uint256)) private _allowances;
    
    uint256 private _totalSupply;
    string private _name;
    string private _symbol;
    uint8 private _decimals;
    
    constructor() {
        _name = "{token_name}";
        _symbol = "{token_symbol}";
        _decimals = {decimals};
        _totalSupply = {total_supply} * 10**{decimals};
        _balances[msg.sender] = _totalSupply;
        emit Transfer(address(0), msg.sender, _totalSupply);
    }
    
    function name() public view returns (string memory) { return _name; }
    function symbol() public view returns (string memory) { return _symbol; }
    function decimals() public view returns (uint8) { return _decimals; }
    function totalSupply() public view override returns (uint256) { return _totalSupply; }
    function balanceOf(address account) public view override returns (uint256) { return _balances[account]; }
    
    function transfer(address recipient, uint256 amount) public override returns (bool) {
        _transfer(msg.sender, recipient, amount);
        return true;
    }
    
    function allowance(address owner, address spender) public view override returns (uint256) {
        return _allowances[owner][spender];
    }
    
    function approve(address spender, uint256 amount) public override returns (bool) {
        _approve(msg.sender, spender, amount);
        return true;
    }
    
    function transferFrom(address sender, address recipient, uint256 amount) public override returns (bool) {
        _transfer(sender, recipient, amount);
        uint256 currentAllowance = _allowances[sender][msg.sender];
        require(currentAllowance >= amount, "ERC20: transfer amount exceeds allowance");
        _approve(sender, msg.sender, currentAllowance - amount);
        return true;
    }
    
    function _transfer(address sender, address recipient, uint256 amount) internal {
        require(sender != address(0), "ERC20: transfer from the zero address");
        require(recipient != address(0), "ERC20: transfer to the zero address");
        uint256 senderBalance = _balances[sender];
        require(senderBalance >= amount, "ERC20: transfer amount exceeds balance");
        _balances[sender] = senderBalance - amount;
        _balances[recipient] += amount;
        emit Transfer(sender, recipient, amount);
    }
    
    function _approve(address owner, address spender, uint256 amount) internal {
        require(owner != address(0), "ERC20: approve from the zero address");
        require(spender != address(0), "ERC20: approve to the zero address");
        _allowances[owner][spender] = amount;
        emit Approval(owner, spender, amount);
    }
}
'''

@router.post("/create-token", response_model=TokenResponse)
async def create_token(
    token_request: TokenCreateRequest,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Create a new ERC20 token contract"""
    try:
        # Generate contract source code
        contract_source = ERC20_CONTRACT_SOURCE.format(
            token_name=token_request.name,
            token_symbol=token_request.symbol,
            decimals=token_request.decimals,
            total_supply=token_request.total_supply
        )
        
        # For demo purposes, we'll simulate contract deployment
        # In production, you would compile and deploy the contract
        mock_contract_address = f"0x{os.urandom(20).hex()}"
        mock_tx_hash = f"0x{os.urandom(32).hex()}"
        
        # Save to database
        db_token = TokenContract(
            user_id=current_user.id,
            contract_address=mock_contract_address,
            token_name=token_request.name,
            token_symbol=token_request.symbol,
            total_supply=str(token_request.total_supply),
            network=token_request.network
        )
        db.add(db_token)
        db.commit()
        db.refresh(db_token)
        
        return TokenResponse(
            id=db_token.id,
            contract_address=mock_contract_address,
            token_name=token_request.name,
            token_symbol=token_request.symbol,
            total_supply=str(token_request.total_supply),
            network=token_request.network,
            transaction_hash=mock_tx_hash
        )
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Token creation failed: {str(e)}")

@router.get("/tokens", response_model=List[TokenResponse])
async def get_user_tokens(
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Get all tokens created by the current user"""
    tokens = db.query(TokenContract).filter(TokenContract.user_id == current_user.id).all()
    return [
        TokenResponse(
            id=token.id,
            contract_address=token.contract_address,
            token_name=token.token_name,
            token_symbol=token.token_symbol,
            total_supply=token.total_supply,
            network=token.network
        )
        for token in tokens
    ]

@router.post("/create-liquidity-pool")
async def create_liquidity_pool(
    lp_request: LiquidityPoolRequest,
    current_user: User = Depends(get_current_user)
):
    """Create a liquidity pool for a token"""
    try:
        # Simulate LP creation
        mock_lp_address = f"0x{os.urandom(20).hex()}"
        mock_tx_hash = f"0x{os.urandom(32).hex()}"
        
        return {
            "message": "Liquidity pool created successfully",
            "lp_address": mock_lp_address,
            "transaction_hash": mock_tx_hash,
            "token_address": lp_request.token_address,
            "eth_amount": lp_request.eth_amount,
            "token_amount": lp_request.token_amount
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"LP creation failed: {str(e)}")

@router.get("/market-data/{symbol}", response_model=MarketDataResponse)
async def get_market_data(symbol: str):
    """Get real-time market data for a cryptocurrency"""
    try:
        # Mock market data - in production, integrate with CoinGecko/CoinMarketCap
        mock_data = {
            "BTC": {"price": 43250.0, "change_24h": 2.4, "volume_24h": 28500000000, "market_cap": ************},
            "ETH": {"price": 2680.0, "change_24h": 1.8, "volume_24h": 15200000000, "market_cap": ************},
            "USDT": {"price": 1.0, "change_24h": 0.1, "volume_24h": 45000000000, "market_cap": 95000000000},
        }
        
        data = mock_data.get(symbol.upper(), {
            "price": 100.0,
            "change_24h": 0.0,
            "volume_24h": 1000000,
            "market_cap": 10000000
        })
        
        return MarketDataResponse(
            symbol=symbol.upper(),
            **data
        )
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to fetch market data: {str(e)}")

@router.get("/gas-price")
async def get_gas_price():
    """Get current gas price"""
    try:
        # Mock gas price - in production, get from Web3
        return {
            "gas_price_gwei": 25,
            "gas_price_wei": 25000000000,
            "fast": 30,
            "standard": 25,
            "safe": 20
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to fetch gas price: {str(e)}")

@router.get("/wallet-balance/{address}")
async def get_wallet_balance(address: str):
    """Get wallet balance for an address"""
    try:
        # Mock balance - in production, query blockchain
        return {
            "address": address,
            "eth_balance": "1.5",
            "usd_value": "4020.00",
            "tokens": [
                {"symbol": "USDT", "balance": "1000.0", "usd_value": "1000.0"},
                {"symbol": "USDC", "balance": "500.0", "usd_value": "500.0"}
            ]
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to fetch wallet balance: {str(e)}")
