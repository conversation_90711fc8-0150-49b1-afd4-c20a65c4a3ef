export declare const swissdlt: {
    blockExplorers: {
        readonly default: {
            readonly name: "SwissDLT Explorer";
            readonly url: "https://explorer.swissdlt.ch";
        };
    };
    contracts?: import("../index.js").Prettify<{
        [key: string]: import("../../index.js").ChainContract | {
            [sourceId: number]: import("../../index.js").ChainContract | undefined;
        } | undefined;
    } & {
        ensRegistry?: import("../../index.js").ChainContract | undefined;
        ensUniversalResolver?: import("../../index.js").ChainContract | undefined;
        multicall3?: import("../../index.js").ChainContract | undefined;
        universalSignatureVerifier?: import("../../index.js").ChainContract | undefined;
    }> | undefined;
    id: 94;
    name: "SwissDLT Mainnet";
    nativeCurrency: {
        readonly decimals: 18;
        readonly name: "BCTS";
        readonly symbol: "BCTS";
    };
    rpcUrls: {
        readonly default: {
            readonly http: readonly ["https://rpc.swissdlt.ch"];
        };
    };
    sourceId?: number | undefined;
    testnet: false;
    custom?: Record<string, unknown> | undefined;
    fees?: import("../../index.js").ChainFees<undefined> | undefined;
    formatters?: undefined;
    serializers?: import("../../index.js").ChainSerializers<undefined, import("../../index.js").TransactionSerializable> | undefined;
};
//# sourceMappingURL=swissdlt.d.ts.map