# Database Configuration
DATABASE_URL=postgresql://chainsight:chainsight@localhost:5432/chainsight

# Redis Configuration
REDIS_URL=redis://localhost:6379

# JWT Configuration
JWT_SECRET=your-super-secret-jwt-key-here-change-in-production
JWT_ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30

# API Keys
OPENAI_API_KEY=your_openai_api_key_here
ETHEREUM_RPC_URL=https://mainnet.infura.io/v3/your_infura_key_here

# Server Configuration
BACKEND_PORT=8000
FRONTEND_URL=http://localhost:3000

# File Upload Configuration
MAX_FILE_SIZE=10485760  # 10MB
UPLOAD_DIR=./uploads

# Email Configuration (optional)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASSWORD=your_app_password

# External APIs
COINGECKO_API_KEY=your_coingecko_api_key
COINMARKETCAP_API_KEY=your_coinmarketcap_api_key

# Security
CORS_ORIGINS=http://localhost:3000,https://chainsight.com
ALLOWED_HOSTS=localhost,127.0.0.1,chainsight.com

# Logging
LOG_LEVEL=INFO
LOG_FILE=./logs/chainsight.log

# Development
DEBUG=True
RELOAD=True
