/* ==================== DOM UTILITIES ====================
   DOM manipulation and query utilities
   ================================================== */

class DOMUtils {
    /**
     * Enhanced querySelector with error handling
     * @param {string} selector - CSS selector
     * @param {Element} context - Context element (default: document)
     * @returns {Element|null}
     */
    static $(selector, context = document) {
        try {
            return context.querySelector(selector);
        } catch (error) {
            console.warn(`Invalid selector: ${selector}`, error);
            return null;
        }
    }

    /**
     * Enhanced querySelectorAll with error handling
     * @param {string} selector - CSS selector
     * @param {Element} context - Context element (default: document)
     * @returns {NodeList|Array}
     */
    static $$(selector, context = document) {
        try {
            return Array.from(context.querySelectorAll(selector));
        } catch (error) {
            console.warn(`Invalid selector: ${selector}`, error);
            return [];
        }
    }

    /**
     * Create element with attributes and content
     * @param {string} tag - HTML tag name
     * @param {Object} attributes - Element attributes
     * @param {string|Element|Array} content - Element content
     * @returns {Element}
     */
    static createElement(tag, attributes = {}, content = '') {
        const element = document.createElement(tag);
        
        // Set attributes
        Object.entries(attributes).forEach(([key, value]) => {
            if (key === 'className' || key === 'class') {
                element.className = value;
            } else if (key === 'dataset') {
                Object.entries(value).forEach(([dataKey, dataValue]) => {
                    element.dataset[dataKey] = dataValue;
                });
            } else if (key.startsWith('on') && typeof value === 'function') {
                element.addEventListener(key.slice(2), value);
            } else {
                element.setAttribute(key, value);
            }
        });

        // Set content
        if (typeof content === 'string') {
            element.innerHTML = content;
        } else if (content instanceof Element) {
            element.appendChild(content);
        } else if (Array.isArray(content)) {
            content.forEach(child => {
                if (typeof child === 'string') {
                    element.appendChild(document.createTextNode(child));
                } else if (child instanceof Element) {
                    element.appendChild(child);
                }
            });
        }

        return element;
    }

    /**
     * Add class(es) to element
     * @param {Element} element - Target element
     * @param {...string} classes - Classes to add
     */
    static addClass(element, ...classes) {
        if (element && element.classList) {
            element.classList.add(...classes);
        }
    }

    /**
     * Remove class(es) from element
     * @param {Element} element - Target element
     * @param {...string} classes - Classes to remove
     */
    static removeClass(element, ...classes) {
        if (element && element.classList) {
            element.classList.remove(...classes);
        }
    }

    /**
     * Toggle class(es) on element
     * @param {Element} element - Target element
     * @param {...string} classes - Classes to toggle
     */
    static toggleClass(element, ...classes) {
        if (element && element.classList) {
            classes.forEach(cls => element.classList.toggle(cls));
        }
    }

    /**
     * Check if element has class
     * @param {Element} element - Target element
     * @param {string} className - Class to check
     * @returns {boolean}
     */
    static hasClass(element, className) {
        return element && element.classList && element.classList.contains(className);
    }

    /**
     * Get/Set element attributes
     * @param {Element} element - Target element
     * @param {string|Object} attr - Attribute name or object of attributes
     * @param {*} value - Attribute value (if attr is string)
     * @returns {*}
     */
    static attr(element, attr, value) {
        if (!element) return null;

        if (typeof attr === 'object') {
            Object.entries(attr).forEach(([key, val]) => {
                element.setAttribute(key, val);
            });
            return element;
        }

        if (value !== undefined) {
            element.setAttribute(attr, value);
            return element;
        }

        return element.getAttribute(attr);
    }

    /**
     * Get/Set element data attributes
     * @param {Element} element - Target element
     * @param {string|Object} key - Data key or object of data
     * @param {*} value - Data value (if key is string)
     * @returns {*}
     */
    static data(element, key, value) {
        if (!element) return null;

        if (typeof key === 'object') {
            Object.entries(key).forEach(([k, v]) => {
                element.dataset[k] = v;
            });
            return element;
        }

        if (value !== undefined) {
            element.dataset[key] = value;
            return element;
        }

        return element.dataset[key];
    }

    /**
     * Show element
     * @param {Element} element - Target element
     * @param {string} display - Display value (default: 'block')
     */
    static show(element, display = 'block') {
        if (element) {
            element.style.display = display;
        }
    }

    /**
     * Hide element
     * @param {Element} element - Target element
     */
    static hide(element) {
        if (element) {
            element.style.display = 'none';
        }
    }

    /**
     * Toggle element visibility
     * @param {Element} element - Target element
     * @param {string} display - Display value when showing
     */
    static toggle(element, display = 'block') {
        if (!element) return;
        
        if (element.style.display === 'none' || !element.style.display) {
            this.show(element, display);
        } else {
            this.hide(element);
        }
    }

    /**
     * Get element position relative to viewport
     * @param {Element} element - Target element
     * @returns {Object}
     */
    static getPosition(element) {
        if (!element) return { top: 0, left: 0, width: 0, height: 0 };
        return element.getBoundingClientRect();
    }

    /**
     * Get element offset relative to document
     * @param {Element} element - Target element
     * @returns {Object}
     */
    static getOffset(element) {
        if (!element) return { top: 0, left: 0 };
        
        const rect = element.getBoundingClientRect();
        return {
            top: rect.top + window.pageYOffset,
            left: rect.left + window.pageXOffset
        };
    }

    /**
     * Check if element is in viewport
     * @param {Element} element - Target element
     * @param {number} threshold - Visibility threshold (0-1)
     * @returns {boolean}
     */
    static isInViewport(element, threshold = 0) {
        if (!element) return false;
        
        const rect = element.getBoundingClientRect();
        const windowHeight = window.innerHeight || document.documentElement.clientHeight;
        const windowWidth = window.innerWidth || document.documentElement.clientWidth;
        
        const vertInView = (rect.top <= windowHeight * (1 - threshold)) && 
                          ((rect.top + rect.height) >= windowHeight * threshold);
        const horInView = (rect.left <= windowWidth * (1 - threshold)) && 
                         ((rect.left + rect.width) >= windowWidth * threshold);
        
        return vertInView && horInView;
    }

    /**
     * Smooth scroll to element
     * @param {Element|string} target - Target element or selector
     * @param {Object} options - Scroll options
     */
    static scrollTo(target, options = {}) {
        const element = typeof target === 'string' ? this.$(target) : target;
        if (!element) return;

        const defaultOptions = {
            behavior: 'smooth',
            block: 'start',
            inline: 'nearest'
        };

        element.scrollIntoView({ ...defaultOptions, ...options });
    }

    /**
     * Debounced event listener
     * @param {Element} element - Target element
     * @param {string} event - Event type
     * @param {Function} handler - Event handler
     * @param {number} delay - Debounce delay in ms
     * @returns {Function} - Cleanup function
     */
    static onDebounced(element, event, handler, delay = 300) {
        let timeoutId;
        
        const debouncedHandler = (e) => {
            clearTimeout(timeoutId);
            timeoutId = setTimeout(() => handler(e), delay);
        };

        element.addEventListener(event, debouncedHandler);
        
        return () => {
            clearTimeout(timeoutId);
            element.removeEventListener(event, debouncedHandler);
        };
    }

    /**
     * Throttled event listener
     * @param {Element} element - Target element
     * @param {string} event - Event type
     * @param {Function} handler - Event handler
     * @param {number} delay - Throttle delay in ms
     * @returns {Function} - Cleanup function
     */
    static onThrottled(element, event, handler, delay = 100) {
        let lastCall = 0;
        
        const throttledHandler = (e) => {
            const now = Date.now();
            if (now - lastCall >= delay) {
                lastCall = now;
                handler(e);
            }
        };

        element.addEventListener(event, throttledHandler);
        
        return () => {
            element.removeEventListener(event, throttledHandler);
        };
    }

    /**
     * Wait for DOM to be ready
     * @param {Function} callback - Callback function
     */
    static ready(callback) {
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', callback);
        } else {
            callback();
        }
    }

    /**
     * Create document fragment from HTML string
     * @param {string} html - HTML string
     * @returns {DocumentFragment}
     */
    static createFragment(html) {
        const template = document.createElement('template');
        template.innerHTML = html.trim();
        return template.content;
    }

    /**
     * Insert element at specific position
     * @param {Element} element - Element to insert
     * @param {Element} target - Target element
     * @param {string} position - Position ('beforebegin', 'afterbegin', 'beforeend', 'afterend')
     */
    static insertAt(element, target, position = 'beforeend') {
        if (element && target) {
            target.insertAdjacentElement(position, element);
        }
    }
}

// Export for module systems
if (typeof module !== 'undefined' && module.exports) {
    module.exports = DOMUtils;
}

// Global assignment for browser
if (typeof window !== 'undefined') {
    window.DOMUtils = DOMUtils;
}
