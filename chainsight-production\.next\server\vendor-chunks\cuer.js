"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/cuer";
exports.ids = ["vendor-chunks/cuer"];
exports.modules = {

/***/ "(ssr)/./node_modules/cuer/_dist/Cuer.js":
/*!*****************************************!*\
  !*** ./node_modules/cuer/_dist/Cuer.js ***!
  \*****************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Cuer: () => (/* binding */ Cuer)\n/* harmony export */ });\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _QrCode_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./QrCode.js */ \"(ssr)/./node_modules/cuer/_dist/QrCode.js\");\n\n\n\n/**\n * Renders a QR code with a finder pattern, cells, and an `arena` (if provided).\n *\n * @params {@link Cuer.Props}\n * @returns A {@link React.ReactNode}\n */\nfunction Cuer(props) {\n    const { arena, ...rest } = props;\n    return ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(Cuer.Root, { ...rest, children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(Cuer.Finder, {}), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(Cuer.Cells, {}), arena && ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(Cuer.Arena, { children: typeof arena === 'string' ? ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"img\", { alt: \"Arena\", src: arena, style: {\n                        borderRadius: 1,\n                        height: '100%',\n                        objectFit: 'cover',\n                        width: '100%',\n                    } })) : (arena) }))] }));\n}\n(function (Cuer) {\n    Cuer.Context = react__WEBPACK_IMPORTED_MODULE_1__.createContext(null);\n    /**\n     * Root component for the QR code.\n     *\n     * @params {@link Root.Props}\n     * @returns A {@link React.ReactNode}\n     */\n    function Root(props) {\n        const { children, size = '100%', value, version, ...rest } = props;\n        // Check if the children contain an `Arena` component.\n        const hasArena = react__WEBPACK_IMPORTED_MODULE_1__.useMemo(() => (react__WEBPACK_IMPORTED_MODULE_1__.Children.map(children, (child) => {\n            if (!react__WEBPACK_IMPORTED_MODULE_1__.isValidElement(child))\n                return null;\n            if (typeof child.type === 'string')\n                return null;\n            if ('displayName' in child.type &&\n                child.type.displayName === 'Arena')\n                return true;\n            return null;\n        }) ?? []).some(Boolean), [children]);\n        // Create the QR code.\n        const qrcode = react__WEBPACK_IMPORTED_MODULE_1__.useMemo(() => {\n            let errorCorrection = props.errorCorrection;\n            // If the QR code has an arena, use a higher error correction level.\n            if (hasArena && errorCorrection === 'low')\n                errorCorrection = 'medium';\n            return _QrCode_js__WEBPACK_IMPORTED_MODULE_2__.create(value, {\n                errorCorrection,\n                version,\n            });\n        }, [value, hasArena, props.errorCorrection, version]);\n        const cellSize = 1;\n        const edgeSize = qrcode.edgeLength * cellSize;\n        const finderSize = (qrcode.finderLength * cellSize) / 2;\n        const arenaSize = hasArena ? Math.floor(edgeSize / 4) : 0;\n        const context = react__WEBPACK_IMPORTED_MODULE_1__.useMemo(() => ({ arenaSize, cellSize, edgeSize, qrcode, finderSize }), [arenaSize, edgeSize, qrcode, finderSize]);\n        return ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(Cuer.Context.Provider, { value: context, children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(\"svg\", { ...rest, width: size, height: size, viewBox: `0 0 ${edgeSize} ${edgeSize}`, xmlns: \"http://www.w3.org/2000/svg\", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"title\", { children: \"QR Code\" }), children] }) }));\n    }\n    Cuer.Root = Root;\n    (function (Root) {\n        Root.displayName = 'Root';\n    })(Root = Cuer.Root || (Cuer.Root = {}));\n    /**\n     * Finder component for the QR code. The finder pattern is the squares\n     * on the top left, top right, and bottom left of the QR code.\n     *\n     * @params {@link Finder.Props}\n     * @returns A {@link React.ReactNode}\n     */\n    function Finder(props) {\n        const { className, fill, innerClassName, radius = 0.25 } = props;\n        const { cellSize, edgeSize, finderSize } = react__WEBPACK_IMPORTED_MODULE_1__.useContext(Cuer.Context);\n        function Inner({ position }) {\n            let outerX = finderSize - (finderSize - cellSize) - cellSize / 2;\n            if (position === 'top-right')\n                outerX = edgeSize - finderSize - (finderSize - cellSize) - cellSize / 2;\n            let outerY = finderSize - (finderSize - cellSize) - cellSize / 2;\n            if (position === 'bottom-left')\n                outerY = edgeSize - finderSize - (finderSize - cellSize) - cellSize / 2;\n            let innerX = finderSize - cellSize * 1.5;\n            if (position === 'top-right')\n                innerX = edgeSize - finderSize - cellSize * 1.5;\n            let innerY = finderSize - cellSize * 1.5;\n            if (position === 'bottom-left')\n                innerY = edgeSize - finderSize - cellSize * 1.5;\n            return ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, { children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"rect\", { className: className, stroke: fill ?? 'currentColor', fill: \"transparent\", x: outerX, y: outerY, width: cellSize + (finderSize - cellSize) * 2, height: cellSize + (finderSize - cellSize) * 2, rx: 2 * radius * (finderSize - cellSize), ry: 2 * radius * (finderSize - cellSize), strokeWidth: cellSize }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"rect\", { className: innerClassName, fill: fill ?? 'currentColor', x: innerX, y: innerY, width: cellSize * 3, height: cellSize * 3, rx: 2 * radius * cellSize, ry: 2 * radius * cellSize })] }));\n        }\n        return ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, { children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(Inner, { position: \"top-left\" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(Inner, { position: \"top-right\" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(Inner, { position: \"bottom-left\" })] }));\n    }\n    Cuer.Finder = Finder;\n    (function (Finder) {\n        Finder.displayName = 'Finder';\n    })(Finder = Cuer.Finder || (Cuer.Finder = {}));\n    /**\n     * Cells for the QR code.\n     *\n     * @params {@link Cells.Props}\n     * @returns A {@link React.ReactNode}\n     */\n    function Cells(props) {\n        const { className, fill = 'currentColor', inset: inset_ = true, radius = 1, } = props;\n        const { arenaSize, cellSize, qrcode } = react__WEBPACK_IMPORTED_MODULE_1__.useContext(Cuer.Context);\n        const { edgeLength, finderLength } = qrcode;\n        const path = react__WEBPACK_IMPORTED_MODULE_1__.useMemo(() => {\n            let path = '';\n            for (let i = 0; i < qrcode.grid.length; i++) {\n                const row = qrcode.grid[i];\n                if (!row)\n                    continue;\n                for (let j = 0; j < row.length; j++) {\n                    const cell = row[j];\n                    if (!cell)\n                        continue;\n                    // Skip rendering dots in arena area.\n                    const start = edgeLength / 2 - arenaSize / 2;\n                    const end = start + arenaSize;\n                    if (i >= start && i <= end && j >= start && j <= end)\n                        continue;\n                    // Skip rendering dots in the finder pattern areas\n                    const topLeftFinder = i < finderLength && j < finderLength;\n                    const topRightFinder = i < finderLength && j >= edgeLength - finderLength;\n                    const bottomLeftFinder = i >= edgeLength - finderLength && j < finderLength;\n                    if (topLeftFinder || topRightFinder || bottomLeftFinder)\n                        continue;\n                    // Add inset for padding\n                    const inset = inset_ ? cellSize * 0.1 : 0;\n                    const innerSize = (cellSize - inset * 2) / 2;\n                    // Calculate center positions\n                    const cx = j * cellSize + cellSize / 2;\n                    const cy = i * cellSize + cellSize / 2;\n                    // Calculate edge positions\n                    const left = cx - innerSize;\n                    const right = cx + innerSize;\n                    const top = cy - innerSize;\n                    const bottom = cy + innerSize;\n                    // Apply corner radius (clamped to maximum of innerSize)\n                    const r = radius * innerSize;\n                    path += [\n                        `M ${left + r},${top}`,\n                        `L ${right - r},${top}`,\n                        `A ${r},${r} 0 0,1 ${right},${top + r}`,\n                        `L ${right},${bottom - r}`,\n                        `A ${r},${r} 0 0,1 ${right - r},${bottom}`,\n                        `L ${left + r},${bottom}`,\n                        `A ${r},${r} 0 0,1 ${left},${bottom - r}`,\n                        `L ${left},${top + r}`,\n                        `A ${r},${r} 0 0,1 ${left + r},${top}`,\n                        'z',\n                    ].join(' ');\n                }\n            }\n            return path;\n        }, [\n            arenaSize,\n            cellSize,\n            edgeLength,\n            finderLength,\n            qrcode.grid,\n            inset_,\n            radius,\n        ]);\n        return (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"path\", { className: className, d: path, fill: fill });\n    }\n    Cuer.Cells = Cells;\n    (function (Cells) {\n        Cells.displayName = 'Cells';\n    })(Cells = Cuer.Cells || (Cuer.Cells = {}));\n    /**\n     * Arena component for the QR code. The arena is the area in the center\n     * of the QR code that is not part of the finder pattern.\n     *\n     * @params {@link Arena.Props}\n     * @returns A {@link React.ReactNode}\n     */\n    function Arena(props) {\n        const { children } = props;\n        const { arenaSize, cellSize, edgeSize } = react__WEBPACK_IMPORTED_MODULE_1__.useContext(Cuer.Context);\n        const start = Math.ceil(edgeSize / 2 - arenaSize / 2);\n        const size = arenaSize + (arenaSize % 2);\n        const padding = cellSize / 2;\n        return ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"foreignObject\", { x: start, y: start, width: size, height: size, children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"div\", { style: {\n                    alignItems: 'center',\n                    display: 'flex',\n                    fontSize: 1,\n                    justifyContent: 'center',\n                    height: '100%',\n                    overflow: 'hidden',\n                    width: '100%',\n                    padding,\n                    boxSizing: 'border-box',\n                }, children: children }) }));\n    }\n    Cuer.Arena = Arena;\n    (function (Arena) {\n        Arena.displayName = 'Arena';\n    })(Arena = Cuer.Arena || (Cuer.Arena = {}));\n})(Cuer || (Cuer = {}));\n//# sourceMappingURL=Cuer.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvY3Vlci9fZGlzdC9DdWVyLmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7QUFBc0Y7QUFDdkQ7QUFDTztBQUN0QztBQUNBO0FBQ0E7QUFDQSxZQUFZO0FBQ1osZUFBZTtBQUNmO0FBQ087QUFDUCxZQUFZLGlCQUFpQjtBQUM3QixZQUFZLHVEQUFLLGNBQWMsb0JBQW9CLHNEQUFJLGdCQUFnQixHQUFHLHNEQUFJLGVBQWUsYUFBYSxzREFBSSxlQUFlLHVDQUF1QyxzREFBSSxVQUFVO0FBQ2xMO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsdUJBQXVCLGNBQWMsS0FBSztBQUMxQztBQUNBO0FBQ0EsbUJBQW1CLGdEQUFtQjtBQUN0QztBQUNBO0FBQ0E7QUFDQSxnQkFBZ0I7QUFDaEIsbUJBQW1CO0FBQ25CO0FBQ0E7QUFDQSxnQkFBZ0IsbURBQW1EO0FBQ25FO0FBQ0EseUJBQXlCLDBDQUFhLFFBQVEsMkNBQWM7QUFDNUQsaUJBQWlCLGlEQUFvQjtBQUNyQztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFNBQVM7QUFDVDtBQUNBLHVCQUF1QiwwQ0FBYTtBQUNwQztBQUNBO0FBQ0E7QUFDQTtBQUNBLG1CQUFtQiw4Q0FBYTtBQUNoQztBQUNBO0FBQ0EsYUFBYTtBQUNiLFNBQVM7QUFDVDtBQUNBO0FBQ0E7QUFDQTtBQUNBLHdCQUF3QiwwQ0FBYSxVQUFVLG1EQUFtRDtBQUNsRyxnQkFBZ0Isc0RBQUksMEJBQTBCLDBCQUEwQix1REFBSyxVQUFVLG9EQUFvRCxVQUFVLEVBQUUsU0FBUyxtREFBbUQsc0RBQUksWUFBWSxxQkFBcUIsY0FBYyxHQUFHO0FBQ3pRO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSyxxQ0FBcUM7QUFDMUM7QUFDQTtBQUNBO0FBQ0E7QUFDQSxnQkFBZ0I7QUFDaEIsbUJBQW1CO0FBQ25CO0FBQ0E7QUFDQSxnQkFBZ0IsaURBQWlEO0FBQ2pFLGdCQUFnQixpQ0FBaUMsRUFBRSw2Q0FBZ0I7QUFDbkUseUJBQXlCLFVBQVU7QUFDbkM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esb0JBQW9CLHVEQUFLLENBQUMsdURBQVMsSUFBSSxXQUFXLHNEQUFJLFdBQVcsMlNBQTJTLEdBQUcsc0RBQUksV0FBVyxnTEFBZ0wsSUFBSTtBQUNsakI7QUFDQSxnQkFBZ0IsdURBQUssQ0FBQyx1REFBUyxJQUFJLFdBQVcsc0RBQUksVUFBVSxzQkFBc0IsR0FBRyxzREFBSSxVQUFVLHVCQUF1QixHQUFHLHNEQUFJLFVBQVUseUJBQXlCLElBQUk7QUFDeEs7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLLDJDQUEyQztBQUNoRDtBQUNBO0FBQ0E7QUFDQSxnQkFBZ0I7QUFDaEIsbUJBQW1CO0FBQ25CO0FBQ0E7QUFDQSxnQkFBZ0Isc0VBQXNFO0FBQ3RGLGdCQUFnQiw4QkFBOEIsRUFBRSw2Q0FBZ0I7QUFDaEUsZ0JBQWdCLDJCQUEyQjtBQUMzQyxxQkFBcUIsMENBQWE7QUFDbEM7QUFDQSw0QkFBNEIsd0JBQXdCO0FBQ3BEO0FBQ0E7QUFDQTtBQUNBLGdDQUFnQyxnQkFBZ0I7QUFDaEQ7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSw2QkFBNkIsU0FBUyxHQUFHLElBQUk7QUFDN0MsNkJBQTZCLFVBQVUsR0FBRyxJQUFJO0FBQzlDLDZCQUE2QixFQUFFLEdBQUcsR0FBRyxRQUFRLE1BQU0sR0FBRyxRQUFRO0FBQzlELDZCQUE2QixNQUFNLEdBQUcsV0FBVztBQUNqRCw2QkFBNkIsRUFBRSxHQUFHLEdBQUcsUUFBUSxVQUFVLEdBQUcsT0FBTztBQUNqRSw2QkFBNkIsU0FBUyxHQUFHLE9BQU87QUFDaEQsNkJBQTZCLEVBQUUsR0FBRyxHQUFHLFFBQVEsS0FBSyxHQUFHLFdBQVc7QUFDaEUsNkJBQTZCLEtBQUssR0FBRyxRQUFRO0FBQzdDLDZCQUE2QixFQUFFLEdBQUcsR0FBRyxRQUFRLFNBQVMsR0FBRyxJQUFJO0FBQzdEO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxTQUFTO0FBQ1Q7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGVBQWUsc0RBQUksV0FBVywyQ0FBMkM7QUFDekU7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLLHdDQUF3QztBQUM3QztBQUNBO0FBQ0E7QUFDQTtBQUNBLGdCQUFnQjtBQUNoQixtQkFBbUI7QUFDbkI7QUFDQTtBQUNBLGdCQUFnQixXQUFXO0FBQzNCLGdCQUFnQixnQ0FBZ0MsRUFBRSw2Q0FBZ0I7QUFDbEU7QUFDQTtBQUNBO0FBQ0EsZ0JBQWdCLHNEQUFJLG9CQUFvQix5REFBeUQsc0RBQUksVUFBVTtBQUMvRztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxpQkFBaUIsc0JBQXNCLEdBQUc7QUFDMUM7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLLHdDQUF3QztBQUM3QyxDQUFDLG9CQUFvQjtBQUNyQiIsInNvdXJjZXMiOlsiRjpcXEFJIGNoYWluXFxjaGFpbnNpZ2h0LXByb2R1Y3Rpb25cXG5vZGVfbW9kdWxlc1xcY3VlclxcX2Rpc3RcXEN1ZXIuanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsganN4IGFzIF9qc3gsIGpzeHMgYXMgX2pzeHMsIEZyYWdtZW50IGFzIF9GcmFnbWVudCB9IGZyb20gXCJyZWFjdC9qc3gtcnVudGltZVwiO1xuaW1wb3J0ICogYXMgUmVhY3QgZnJvbSAncmVhY3QnO1xuaW1wb3J0ICogYXMgUXJDb2RlIGZyb20gJy4vUXJDb2RlLmpzJztcbi8qKlxuICogUmVuZGVycyBhIFFSIGNvZGUgd2l0aCBhIGZpbmRlciBwYXR0ZXJuLCBjZWxscywgYW5kIGFuIGBhcmVuYWAgKGlmIHByb3ZpZGVkKS5cbiAqXG4gKiBAcGFyYW1zIHtAbGluayBDdWVyLlByb3BzfVxuICogQHJldHVybnMgQSB7QGxpbmsgUmVhY3QuUmVhY3ROb2RlfVxuICovXG5leHBvcnQgZnVuY3Rpb24gQ3Vlcihwcm9wcykge1xuICAgIGNvbnN0IHsgYXJlbmEsIC4uLnJlc3QgfSA9IHByb3BzO1xuICAgIHJldHVybiAoX2pzeHMoQ3Vlci5Sb290LCB7IC4uLnJlc3QsIGNoaWxkcmVuOiBbX2pzeChDdWVyLkZpbmRlciwge30pLCBfanN4KEN1ZXIuQ2VsbHMsIHt9KSwgYXJlbmEgJiYgKF9qc3goQ3Vlci5BcmVuYSwgeyBjaGlsZHJlbjogdHlwZW9mIGFyZW5hID09PSAnc3RyaW5nJyA/IChfanN4KFwiaW1nXCIsIHsgYWx0OiBcIkFyZW5hXCIsIHNyYzogYXJlbmEsIHN0eWxlOiB7XG4gICAgICAgICAgICAgICAgICAgICAgICBib3JkZXJSYWRpdXM6IDEsXG4gICAgICAgICAgICAgICAgICAgICAgICBoZWlnaHQ6ICcxMDAlJyxcbiAgICAgICAgICAgICAgICAgICAgICAgIG9iamVjdEZpdDogJ2NvdmVyJyxcbiAgICAgICAgICAgICAgICAgICAgICAgIHdpZHRoOiAnMTAwJScsXG4gICAgICAgICAgICAgICAgICAgIH0gfSkpIDogKGFyZW5hKSB9KSldIH0pKTtcbn1cbihmdW5jdGlvbiAoQ3Vlcikge1xuICAgIEN1ZXIuQ29udGV4dCA9IFJlYWN0LmNyZWF0ZUNvbnRleHQobnVsbCk7XG4gICAgLyoqXG4gICAgICogUm9vdCBjb21wb25lbnQgZm9yIHRoZSBRUiBjb2RlLlxuICAgICAqXG4gICAgICogQHBhcmFtcyB7QGxpbmsgUm9vdC5Qcm9wc31cbiAgICAgKiBAcmV0dXJucyBBIHtAbGluayBSZWFjdC5SZWFjdE5vZGV9XG4gICAgICovXG4gICAgZnVuY3Rpb24gUm9vdChwcm9wcykge1xuICAgICAgICBjb25zdCB7IGNoaWxkcmVuLCBzaXplID0gJzEwMCUnLCB2YWx1ZSwgdmVyc2lvbiwgLi4ucmVzdCB9ID0gcHJvcHM7XG4gICAgICAgIC8vIENoZWNrIGlmIHRoZSBjaGlsZHJlbiBjb250YWluIGFuIGBBcmVuYWAgY29tcG9uZW50LlxuICAgICAgICBjb25zdCBoYXNBcmVuYSA9IFJlYWN0LnVzZU1lbW8oKCkgPT4gKFJlYWN0LkNoaWxkcmVuLm1hcChjaGlsZHJlbiwgKGNoaWxkKSA9PiB7XG4gICAgICAgICAgICBpZiAoIVJlYWN0LmlzVmFsaWRFbGVtZW50KGNoaWxkKSlcbiAgICAgICAgICAgICAgICByZXR1cm4gbnVsbDtcbiAgICAgICAgICAgIGlmICh0eXBlb2YgY2hpbGQudHlwZSA9PT0gJ3N0cmluZycpXG4gICAgICAgICAgICAgICAgcmV0dXJuIG51bGw7XG4gICAgICAgICAgICBpZiAoJ2Rpc3BsYXlOYW1lJyBpbiBjaGlsZC50eXBlICYmXG4gICAgICAgICAgICAgICAgY2hpbGQudHlwZS5kaXNwbGF5TmFtZSA9PT0gJ0FyZW5hJylcbiAgICAgICAgICAgICAgICByZXR1cm4gdHJ1ZTtcbiAgICAgICAgICAgIHJldHVybiBudWxsO1xuICAgICAgICB9KSA/PyBbXSkuc29tZShCb29sZWFuKSwgW2NoaWxkcmVuXSk7XG4gICAgICAgIC8vIENyZWF0ZSB0aGUgUVIgY29kZS5cbiAgICAgICAgY29uc3QgcXJjb2RlID0gUmVhY3QudXNlTWVtbygoKSA9PiB7XG4gICAgICAgICAgICBsZXQgZXJyb3JDb3JyZWN0aW9uID0gcHJvcHMuZXJyb3JDb3JyZWN0aW9uO1xuICAgICAgICAgICAgLy8gSWYgdGhlIFFSIGNvZGUgaGFzIGFuIGFyZW5hLCB1c2UgYSBoaWdoZXIgZXJyb3IgY29ycmVjdGlvbiBsZXZlbC5cbiAgICAgICAgICAgIGlmIChoYXNBcmVuYSAmJiBlcnJvckNvcnJlY3Rpb24gPT09ICdsb3cnKVxuICAgICAgICAgICAgICAgIGVycm9yQ29ycmVjdGlvbiA9ICdtZWRpdW0nO1xuICAgICAgICAgICAgcmV0dXJuIFFyQ29kZS5jcmVhdGUodmFsdWUsIHtcbiAgICAgICAgICAgICAgICBlcnJvckNvcnJlY3Rpb24sXG4gICAgICAgICAgICAgICAgdmVyc2lvbixcbiAgICAgICAgICAgIH0pO1xuICAgICAgICB9LCBbdmFsdWUsIGhhc0FyZW5hLCBwcm9wcy5lcnJvckNvcnJlY3Rpb24sIHZlcnNpb25dKTtcbiAgICAgICAgY29uc3QgY2VsbFNpemUgPSAxO1xuICAgICAgICBjb25zdCBlZGdlU2l6ZSA9IHFyY29kZS5lZGdlTGVuZ3RoICogY2VsbFNpemU7XG4gICAgICAgIGNvbnN0IGZpbmRlclNpemUgPSAocXJjb2RlLmZpbmRlckxlbmd0aCAqIGNlbGxTaXplKSAvIDI7XG4gICAgICAgIGNvbnN0IGFyZW5hU2l6ZSA9IGhhc0FyZW5hID8gTWF0aC5mbG9vcihlZGdlU2l6ZSAvIDQpIDogMDtcbiAgICAgICAgY29uc3QgY29udGV4dCA9IFJlYWN0LnVzZU1lbW8oKCkgPT4gKHsgYXJlbmFTaXplLCBjZWxsU2l6ZSwgZWRnZVNpemUsIHFyY29kZSwgZmluZGVyU2l6ZSB9KSwgW2FyZW5hU2l6ZSwgZWRnZVNpemUsIHFyY29kZSwgZmluZGVyU2l6ZV0pO1xuICAgICAgICByZXR1cm4gKF9qc3goQ3Vlci5Db250ZXh0LlByb3ZpZGVyLCB7IHZhbHVlOiBjb250ZXh0LCBjaGlsZHJlbjogX2pzeHMoXCJzdmdcIiwgeyAuLi5yZXN0LCB3aWR0aDogc2l6ZSwgaGVpZ2h0OiBzaXplLCB2aWV3Qm94OiBgMCAwICR7ZWRnZVNpemV9ICR7ZWRnZVNpemV9YCwgeG1sbnM6IFwiaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmdcIiwgY2hpbGRyZW46IFtfanN4KFwidGl0bGVcIiwgeyBjaGlsZHJlbjogXCJRUiBDb2RlXCIgfSksIGNoaWxkcmVuXSB9KSB9KSk7XG4gICAgfVxuICAgIEN1ZXIuUm9vdCA9IFJvb3Q7XG4gICAgKGZ1bmN0aW9uIChSb290KSB7XG4gICAgICAgIFJvb3QuZGlzcGxheU5hbWUgPSAnUm9vdCc7XG4gICAgfSkoUm9vdCA9IEN1ZXIuUm9vdCB8fCAoQ3Vlci5Sb290ID0ge30pKTtcbiAgICAvKipcbiAgICAgKiBGaW5kZXIgY29tcG9uZW50IGZvciB0aGUgUVIgY29kZS4gVGhlIGZpbmRlciBwYXR0ZXJuIGlzIHRoZSBzcXVhcmVzXG4gICAgICogb24gdGhlIHRvcCBsZWZ0LCB0b3AgcmlnaHQsIGFuZCBib3R0b20gbGVmdCBvZiB0aGUgUVIgY29kZS5cbiAgICAgKlxuICAgICAqIEBwYXJhbXMge0BsaW5rIEZpbmRlci5Qcm9wc31cbiAgICAgKiBAcmV0dXJucyBBIHtAbGluayBSZWFjdC5SZWFjdE5vZGV9XG4gICAgICovXG4gICAgZnVuY3Rpb24gRmluZGVyKHByb3BzKSB7XG4gICAgICAgIGNvbnN0IHsgY2xhc3NOYW1lLCBmaWxsLCBpbm5lckNsYXNzTmFtZSwgcmFkaXVzID0gMC4yNSB9ID0gcHJvcHM7XG4gICAgICAgIGNvbnN0IHsgY2VsbFNpemUsIGVkZ2VTaXplLCBmaW5kZXJTaXplIH0gPSBSZWFjdC51c2VDb250ZXh0KEN1ZXIuQ29udGV4dCk7XG4gICAgICAgIGZ1bmN0aW9uIElubmVyKHsgcG9zaXRpb24gfSkge1xuICAgICAgICAgICAgbGV0IG91dGVyWCA9IGZpbmRlclNpemUgLSAoZmluZGVyU2l6ZSAtIGNlbGxTaXplKSAtIGNlbGxTaXplIC8gMjtcbiAgICAgICAgICAgIGlmIChwb3NpdGlvbiA9PT0gJ3RvcC1yaWdodCcpXG4gICAgICAgICAgICAgICAgb3V0ZXJYID0gZWRnZVNpemUgLSBmaW5kZXJTaXplIC0gKGZpbmRlclNpemUgLSBjZWxsU2l6ZSkgLSBjZWxsU2l6ZSAvIDI7XG4gICAgICAgICAgICBsZXQgb3V0ZXJZID0gZmluZGVyU2l6ZSAtIChmaW5kZXJTaXplIC0gY2VsbFNpemUpIC0gY2VsbFNpemUgLyAyO1xuICAgICAgICAgICAgaWYgKHBvc2l0aW9uID09PSAnYm90dG9tLWxlZnQnKVxuICAgICAgICAgICAgICAgIG91dGVyWSA9IGVkZ2VTaXplIC0gZmluZGVyU2l6ZSAtIChmaW5kZXJTaXplIC0gY2VsbFNpemUpIC0gY2VsbFNpemUgLyAyO1xuICAgICAgICAgICAgbGV0IGlubmVyWCA9IGZpbmRlclNpemUgLSBjZWxsU2l6ZSAqIDEuNTtcbiAgICAgICAgICAgIGlmIChwb3NpdGlvbiA9PT0gJ3RvcC1yaWdodCcpXG4gICAgICAgICAgICAgICAgaW5uZXJYID0gZWRnZVNpemUgLSBmaW5kZXJTaXplIC0gY2VsbFNpemUgKiAxLjU7XG4gICAgICAgICAgICBsZXQgaW5uZXJZID0gZmluZGVyU2l6ZSAtIGNlbGxTaXplICogMS41O1xuICAgICAgICAgICAgaWYgKHBvc2l0aW9uID09PSAnYm90dG9tLWxlZnQnKVxuICAgICAgICAgICAgICAgIGlubmVyWSA9IGVkZ2VTaXplIC0gZmluZGVyU2l6ZSAtIGNlbGxTaXplICogMS41O1xuICAgICAgICAgICAgcmV0dXJuIChfanN4cyhfRnJhZ21lbnQsIHsgY2hpbGRyZW46IFtfanN4KFwicmVjdFwiLCB7IGNsYXNzTmFtZTogY2xhc3NOYW1lLCBzdHJva2U6IGZpbGwgPz8gJ2N1cnJlbnRDb2xvcicsIGZpbGw6IFwidHJhbnNwYXJlbnRcIiwgeDogb3V0ZXJYLCB5OiBvdXRlclksIHdpZHRoOiBjZWxsU2l6ZSArIChmaW5kZXJTaXplIC0gY2VsbFNpemUpICogMiwgaGVpZ2h0OiBjZWxsU2l6ZSArIChmaW5kZXJTaXplIC0gY2VsbFNpemUpICogMiwgcng6IDIgKiByYWRpdXMgKiAoZmluZGVyU2l6ZSAtIGNlbGxTaXplKSwgcnk6IDIgKiByYWRpdXMgKiAoZmluZGVyU2l6ZSAtIGNlbGxTaXplKSwgc3Ryb2tlV2lkdGg6IGNlbGxTaXplIH0pLCBfanN4KFwicmVjdFwiLCB7IGNsYXNzTmFtZTogaW5uZXJDbGFzc05hbWUsIGZpbGw6IGZpbGwgPz8gJ2N1cnJlbnRDb2xvcicsIHg6IGlubmVyWCwgeTogaW5uZXJZLCB3aWR0aDogY2VsbFNpemUgKiAzLCBoZWlnaHQ6IGNlbGxTaXplICogMywgcng6IDIgKiByYWRpdXMgKiBjZWxsU2l6ZSwgcnk6IDIgKiByYWRpdXMgKiBjZWxsU2l6ZSB9KV0gfSkpO1xuICAgICAgICB9XG4gICAgICAgIHJldHVybiAoX2pzeHMoX0ZyYWdtZW50LCB7IGNoaWxkcmVuOiBbX2pzeChJbm5lciwgeyBwb3NpdGlvbjogXCJ0b3AtbGVmdFwiIH0pLCBfanN4KElubmVyLCB7IHBvc2l0aW9uOiBcInRvcC1yaWdodFwiIH0pLCBfanN4KElubmVyLCB7IHBvc2l0aW9uOiBcImJvdHRvbS1sZWZ0XCIgfSldIH0pKTtcbiAgICB9XG4gICAgQ3Vlci5GaW5kZXIgPSBGaW5kZXI7XG4gICAgKGZ1bmN0aW9uIChGaW5kZXIpIHtcbiAgICAgICAgRmluZGVyLmRpc3BsYXlOYW1lID0gJ0ZpbmRlcic7XG4gICAgfSkoRmluZGVyID0gQ3Vlci5GaW5kZXIgfHwgKEN1ZXIuRmluZGVyID0ge30pKTtcbiAgICAvKipcbiAgICAgKiBDZWxscyBmb3IgdGhlIFFSIGNvZGUuXG4gICAgICpcbiAgICAgKiBAcGFyYW1zIHtAbGluayBDZWxscy5Qcm9wc31cbiAgICAgKiBAcmV0dXJucyBBIHtAbGluayBSZWFjdC5SZWFjdE5vZGV9XG4gICAgICovXG4gICAgZnVuY3Rpb24gQ2VsbHMocHJvcHMpIHtcbiAgICAgICAgY29uc3QgeyBjbGFzc05hbWUsIGZpbGwgPSAnY3VycmVudENvbG9yJywgaW5zZXQ6IGluc2V0XyA9IHRydWUsIHJhZGl1cyA9IDEsIH0gPSBwcm9wcztcbiAgICAgICAgY29uc3QgeyBhcmVuYVNpemUsIGNlbGxTaXplLCBxcmNvZGUgfSA9IFJlYWN0LnVzZUNvbnRleHQoQ3Vlci5Db250ZXh0KTtcbiAgICAgICAgY29uc3QgeyBlZGdlTGVuZ3RoLCBmaW5kZXJMZW5ndGggfSA9IHFyY29kZTtcbiAgICAgICAgY29uc3QgcGF0aCA9IFJlYWN0LnVzZU1lbW8oKCkgPT4ge1xuICAgICAgICAgICAgbGV0IHBhdGggPSAnJztcbiAgICAgICAgICAgIGZvciAobGV0IGkgPSAwOyBpIDwgcXJjb2RlLmdyaWQubGVuZ3RoOyBpKyspIHtcbiAgICAgICAgICAgICAgICBjb25zdCByb3cgPSBxcmNvZGUuZ3JpZFtpXTtcbiAgICAgICAgICAgICAgICBpZiAoIXJvdylcbiAgICAgICAgICAgICAgICAgICAgY29udGludWU7XG4gICAgICAgICAgICAgICAgZm9yIChsZXQgaiA9IDA7IGogPCByb3cubGVuZ3RoOyBqKyspIHtcbiAgICAgICAgICAgICAgICAgICAgY29uc3QgY2VsbCA9IHJvd1tqXTtcbiAgICAgICAgICAgICAgICAgICAgaWYgKCFjZWxsKVxuICAgICAgICAgICAgICAgICAgICAgICAgY29udGludWU7XG4gICAgICAgICAgICAgICAgICAgIC8vIFNraXAgcmVuZGVyaW5nIGRvdHMgaW4gYXJlbmEgYXJlYS5cbiAgICAgICAgICAgICAgICAgICAgY29uc3Qgc3RhcnQgPSBlZGdlTGVuZ3RoIC8gMiAtIGFyZW5hU2l6ZSAvIDI7XG4gICAgICAgICAgICAgICAgICAgIGNvbnN0IGVuZCA9IHN0YXJ0ICsgYXJlbmFTaXplO1xuICAgICAgICAgICAgICAgICAgICBpZiAoaSA+PSBzdGFydCAmJiBpIDw9IGVuZCAmJiBqID49IHN0YXJ0ICYmIGogPD0gZW5kKVxuICAgICAgICAgICAgICAgICAgICAgICAgY29udGludWU7XG4gICAgICAgICAgICAgICAgICAgIC8vIFNraXAgcmVuZGVyaW5nIGRvdHMgaW4gdGhlIGZpbmRlciBwYXR0ZXJuIGFyZWFzXG4gICAgICAgICAgICAgICAgICAgIGNvbnN0IHRvcExlZnRGaW5kZXIgPSBpIDwgZmluZGVyTGVuZ3RoICYmIGogPCBmaW5kZXJMZW5ndGg7XG4gICAgICAgICAgICAgICAgICAgIGNvbnN0IHRvcFJpZ2h0RmluZGVyID0gaSA8IGZpbmRlckxlbmd0aCAmJiBqID49IGVkZ2VMZW5ndGggLSBmaW5kZXJMZW5ndGg7XG4gICAgICAgICAgICAgICAgICAgIGNvbnN0IGJvdHRvbUxlZnRGaW5kZXIgPSBpID49IGVkZ2VMZW5ndGggLSBmaW5kZXJMZW5ndGggJiYgaiA8IGZpbmRlckxlbmd0aDtcbiAgICAgICAgICAgICAgICAgICAgaWYgKHRvcExlZnRGaW5kZXIgfHwgdG9wUmlnaHRGaW5kZXIgfHwgYm90dG9tTGVmdEZpbmRlcilcbiAgICAgICAgICAgICAgICAgICAgICAgIGNvbnRpbnVlO1xuICAgICAgICAgICAgICAgICAgICAvLyBBZGQgaW5zZXQgZm9yIHBhZGRpbmdcbiAgICAgICAgICAgICAgICAgICAgY29uc3QgaW5zZXQgPSBpbnNldF8gPyBjZWxsU2l6ZSAqIDAuMSA6IDA7XG4gICAgICAgICAgICAgICAgICAgIGNvbnN0IGlubmVyU2l6ZSA9IChjZWxsU2l6ZSAtIGluc2V0ICogMikgLyAyO1xuICAgICAgICAgICAgICAgICAgICAvLyBDYWxjdWxhdGUgY2VudGVyIHBvc2l0aW9uc1xuICAgICAgICAgICAgICAgICAgICBjb25zdCBjeCA9IGogKiBjZWxsU2l6ZSArIGNlbGxTaXplIC8gMjtcbiAgICAgICAgICAgICAgICAgICAgY29uc3QgY3kgPSBpICogY2VsbFNpemUgKyBjZWxsU2l6ZSAvIDI7XG4gICAgICAgICAgICAgICAgICAgIC8vIENhbGN1bGF0ZSBlZGdlIHBvc2l0aW9uc1xuICAgICAgICAgICAgICAgICAgICBjb25zdCBsZWZ0ID0gY3ggLSBpbm5lclNpemU7XG4gICAgICAgICAgICAgICAgICAgIGNvbnN0IHJpZ2h0ID0gY3ggKyBpbm5lclNpemU7XG4gICAgICAgICAgICAgICAgICAgIGNvbnN0IHRvcCA9IGN5IC0gaW5uZXJTaXplO1xuICAgICAgICAgICAgICAgICAgICBjb25zdCBib3R0b20gPSBjeSArIGlubmVyU2l6ZTtcbiAgICAgICAgICAgICAgICAgICAgLy8gQXBwbHkgY29ybmVyIHJhZGl1cyAoY2xhbXBlZCB0byBtYXhpbXVtIG9mIGlubmVyU2l6ZSlcbiAgICAgICAgICAgICAgICAgICAgY29uc3QgciA9IHJhZGl1cyAqIGlubmVyU2l6ZTtcbiAgICAgICAgICAgICAgICAgICAgcGF0aCArPSBbXG4gICAgICAgICAgICAgICAgICAgICAgICBgTSAke2xlZnQgKyByfSwke3RvcH1gLFxuICAgICAgICAgICAgICAgICAgICAgICAgYEwgJHtyaWdodCAtIHJ9LCR7dG9wfWAsXG4gICAgICAgICAgICAgICAgICAgICAgICBgQSAke3J9LCR7cn0gMCAwLDEgJHtyaWdodH0sJHt0b3AgKyByfWAsXG4gICAgICAgICAgICAgICAgICAgICAgICBgTCAke3JpZ2h0fSwke2JvdHRvbSAtIHJ9YCxcbiAgICAgICAgICAgICAgICAgICAgICAgIGBBICR7cn0sJHtyfSAwIDAsMSAke3JpZ2h0IC0gcn0sJHtib3R0b219YCxcbiAgICAgICAgICAgICAgICAgICAgICAgIGBMICR7bGVmdCArIHJ9LCR7Ym90dG9tfWAsXG4gICAgICAgICAgICAgICAgICAgICAgICBgQSAke3J9LCR7cn0gMCAwLDEgJHtsZWZ0fSwke2JvdHRvbSAtIHJ9YCxcbiAgICAgICAgICAgICAgICAgICAgICAgIGBMICR7bGVmdH0sJHt0b3AgKyByfWAsXG4gICAgICAgICAgICAgICAgICAgICAgICBgQSAke3J9LCR7cn0gMCAwLDEgJHtsZWZ0ICsgcn0sJHt0b3B9YCxcbiAgICAgICAgICAgICAgICAgICAgICAgICd6JyxcbiAgICAgICAgICAgICAgICAgICAgXS5qb2luKCcgJyk7XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgfVxuICAgICAgICAgICAgcmV0dXJuIHBhdGg7XG4gICAgICAgIH0sIFtcbiAgICAgICAgICAgIGFyZW5hU2l6ZSxcbiAgICAgICAgICAgIGNlbGxTaXplLFxuICAgICAgICAgICAgZWRnZUxlbmd0aCxcbiAgICAgICAgICAgIGZpbmRlckxlbmd0aCxcbiAgICAgICAgICAgIHFyY29kZS5ncmlkLFxuICAgICAgICAgICAgaW5zZXRfLFxuICAgICAgICAgICAgcmFkaXVzLFxuICAgICAgICBdKTtcbiAgICAgICAgcmV0dXJuIF9qc3goXCJwYXRoXCIsIHsgY2xhc3NOYW1lOiBjbGFzc05hbWUsIGQ6IHBhdGgsIGZpbGw6IGZpbGwgfSk7XG4gICAgfVxuICAgIEN1ZXIuQ2VsbHMgPSBDZWxscztcbiAgICAoZnVuY3Rpb24gKENlbGxzKSB7XG4gICAgICAgIENlbGxzLmRpc3BsYXlOYW1lID0gJ0NlbGxzJztcbiAgICB9KShDZWxscyA9IEN1ZXIuQ2VsbHMgfHwgKEN1ZXIuQ2VsbHMgPSB7fSkpO1xuICAgIC8qKlxuICAgICAqIEFyZW5hIGNvbXBvbmVudCBmb3IgdGhlIFFSIGNvZGUuIFRoZSBhcmVuYSBpcyB0aGUgYXJlYSBpbiB0aGUgY2VudGVyXG4gICAgICogb2YgdGhlIFFSIGNvZGUgdGhhdCBpcyBub3QgcGFydCBvZiB0aGUgZmluZGVyIHBhdHRlcm4uXG4gICAgICpcbiAgICAgKiBAcGFyYW1zIHtAbGluayBBcmVuYS5Qcm9wc31cbiAgICAgKiBAcmV0dXJucyBBIHtAbGluayBSZWFjdC5SZWFjdE5vZGV9XG4gICAgICovXG4gICAgZnVuY3Rpb24gQXJlbmEocHJvcHMpIHtcbiAgICAgICAgY29uc3QgeyBjaGlsZHJlbiB9ID0gcHJvcHM7XG4gICAgICAgIGNvbnN0IHsgYXJlbmFTaXplLCBjZWxsU2l6ZSwgZWRnZVNpemUgfSA9IFJlYWN0LnVzZUNvbnRleHQoQ3Vlci5Db250ZXh0KTtcbiAgICAgICAgY29uc3Qgc3RhcnQgPSBNYXRoLmNlaWwoZWRnZVNpemUgLyAyIC0gYXJlbmFTaXplIC8gMik7XG4gICAgICAgIGNvbnN0IHNpemUgPSBhcmVuYVNpemUgKyAoYXJlbmFTaXplICUgMik7XG4gICAgICAgIGNvbnN0IHBhZGRpbmcgPSBjZWxsU2l6ZSAvIDI7XG4gICAgICAgIHJldHVybiAoX2pzeChcImZvcmVpZ25PYmplY3RcIiwgeyB4OiBzdGFydCwgeTogc3RhcnQsIHdpZHRoOiBzaXplLCBoZWlnaHQ6IHNpemUsIGNoaWxkcmVuOiBfanN4KFwiZGl2XCIsIHsgc3R5bGU6IHtcbiAgICAgICAgICAgICAgICAgICAgYWxpZ25JdGVtczogJ2NlbnRlcicsXG4gICAgICAgICAgICAgICAgICAgIGRpc3BsYXk6ICdmbGV4JyxcbiAgICAgICAgICAgICAgICAgICAgZm9udFNpemU6IDEsXG4gICAgICAgICAgICAgICAgICAgIGp1c3RpZnlDb250ZW50OiAnY2VudGVyJyxcbiAgICAgICAgICAgICAgICAgICAgaGVpZ2h0OiAnMTAwJScsXG4gICAgICAgICAgICAgICAgICAgIG92ZXJmbG93OiAnaGlkZGVuJyxcbiAgICAgICAgICAgICAgICAgICAgd2lkdGg6ICcxMDAlJyxcbiAgICAgICAgICAgICAgICAgICAgcGFkZGluZyxcbiAgICAgICAgICAgICAgICAgICAgYm94U2l6aW5nOiAnYm9yZGVyLWJveCcsXG4gICAgICAgICAgICAgICAgfSwgY2hpbGRyZW46IGNoaWxkcmVuIH0pIH0pKTtcbiAgICB9XG4gICAgQ3Vlci5BcmVuYSA9IEFyZW5hO1xuICAgIChmdW5jdGlvbiAoQXJlbmEpIHtcbiAgICAgICAgQXJlbmEuZGlzcGxheU5hbWUgPSAnQXJlbmEnO1xuICAgIH0pKEFyZW5hID0gQ3Vlci5BcmVuYSB8fCAoQ3Vlci5BcmVuYSA9IHt9KSk7XG59KShDdWVyIHx8IChDdWVyID0ge30pKTtcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPUN1ZXIuanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/cuer/_dist/Cuer.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/cuer/_dist/QrCode.js":
/*!*******************************************!*\
  !*** ./node_modules/cuer/_dist/QrCode.js ***!
  \*******************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   create: () => (/* binding */ create)\n/* harmony export */ });\n/* harmony import */ var qr__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! qr */ \"(ssr)/./node_modules/qr/index.js\");\n\nfunction create(value, options = {}) {\n    const { errorCorrection, version } = options;\n    const grid = (0,qr__WEBPACK_IMPORTED_MODULE_0__.encodeQR)(value, 'raw', {\n        border: 0,\n        ecc: errorCorrection,\n        scale: 1,\n        version: version,\n    });\n    const finderLength = 7;\n    const edgeLength = grid.length;\n    return {\n        edgeLength,\n        finderLength,\n        grid,\n        value,\n    };\n}\n//# sourceMappingURL=QrCode.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvY3Vlci9fZGlzdC9RckNvZGUuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBOEI7QUFDdkIsbUNBQW1DO0FBQzFDLFlBQVksMkJBQTJCO0FBQ3ZDLGlCQUFpQiw0Q0FBUTtBQUN6QjtBQUNBO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsiRjpcXEFJIGNoYWluXFxjaGFpbnNpZ2h0LXByb2R1Y3Rpb25cXG5vZGVfbW9kdWxlc1xcY3VlclxcX2Rpc3RcXFFyQ29kZS5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBlbmNvZGVRUiB9IGZyb20gJ3FyJztcbmV4cG9ydCBmdW5jdGlvbiBjcmVhdGUodmFsdWUsIG9wdGlvbnMgPSB7fSkge1xuICAgIGNvbnN0IHsgZXJyb3JDb3JyZWN0aW9uLCB2ZXJzaW9uIH0gPSBvcHRpb25zO1xuICAgIGNvbnN0IGdyaWQgPSBlbmNvZGVRUih2YWx1ZSwgJ3JhdycsIHtcbiAgICAgICAgYm9yZGVyOiAwLFxuICAgICAgICBlY2M6IGVycm9yQ29ycmVjdGlvbixcbiAgICAgICAgc2NhbGU6IDEsXG4gICAgICAgIHZlcnNpb246IHZlcnNpb24sXG4gICAgfSk7XG4gICAgY29uc3QgZmluZGVyTGVuZ3RoID0gNztcbiAgICBjb25zdCBlZGdlTGVuZ3RoID0gZ3JpZC5sZW5ndGg7XG4gICAgcmV0dXJuIHtcbiAgICAgICAgZWRnZUxlbmd0aCxcbiAgICAgICAgZmluZGVyTGVuZ3RoLFxuICAgICAgICBncmlkLFxuICAgICAgICB2YWx1ZSxcbiAgICB9O1xufVxuLy8jIHNvdXJjZU1hcHBpbmdVUkw9UXJDb2RlLmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/cuer/_dist/QrCode.js\n");

/***/ })

};
;