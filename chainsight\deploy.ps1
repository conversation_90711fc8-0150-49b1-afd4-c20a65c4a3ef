# Chainsight by Connectouch - Deployment Script
# This script deploys the complete Chainsight platform

Write-Host "🚀 CHAINSIGHT DEPLOYMENT SCRIPT" -ForegroundColor Yellow
Write-Host "=================================" -ForegroundColor Yellow
Write-Host ""

# Function to check if a command exists
function Test-Command($cmdname) {
    return [bool](Get-Command -Name $cmdname -ErrorAction SilentlyContinue)
}

# Check prerequisites
Write-Host "📋 Checking Prerequisites..." -ForegroundColor Cyan
$prerequisites = @()

if (Test-Command "docker") {
    Write-Host "✅ Docker is installed" -ForegroundColor Green
} else {
    Write-Host "❌ Docker is not installed" -ForegroundColor Red
    $prerequisites += "Docker"
}

if (Test-Command "python") {
    Write-Host "✅ Python is installed" -ForegroundColor Green
} else {
    Write-Host "❌ Python is not installed" -ForegroundColor Red
    $prerequisites += "Python"
}

if ($prerequisites.Count -gt 0) {
    Write-Host "❌ Missing prerequisites: $($prerequisites -join ', ')" -ForegroundColor Red
    Write-Host "Please install the missing prerequisites and run this script again." -ForegroundColor Red
    exit 1
}

Write-Host ""

# Step 1: Backend Dependencies
Write-Host "🔧 Step 1: Installing Backend Dependencies..." -ForegroundColor Cyan
Set-Location "apps/backend"
try {
    pip install -r requirements_simple.txt
    Write-Host "✅ Backend dependencies installed successfully" -ForegroundColor Green
} catch {
    Write-Host "❌ Failed to install backend dependencies: $_" -ForegroundColor Red
    exit 1
}
Set-Location "../.."

# Step 2: Docker Images
Write-Host "🐳 Step 2: Building Docker Images..." -ForegroundColor Cyan
try {
    docker-compose -f docker-compose-simple.yml build
    Write-Host "✅ Docker images built successfully" -ForegroundColor Green
} catch {
    Write-Host "❌ Failed to build Docker images: $_" -ForegroundColor Red
    exit 1
}

# Step 3: Start Services
Write-Host "🚀 Step 3: Starting Services..." -ForegroundColor Cyan
try {
    docker-compose -f docker-compose-simple.yml up -d
    Write-Host "✅ Services started successfully" -ForegroundColor Green
} catch {
    Write-Host "❌ Failed to start services: $_" -ForegroundColor Red
    exit 1
}

# Step 4: Health Checks
Write-Host "🏥 Step 4: Running Health Checks..." -ForegroundColor Cyan
Start-Sleep -Seconds 10

# Check backend health
try {
    $healthResponse = Invoke-RestMethod -Uri "http://localhost:8000/health" -TimeoutSec 10
    if ($healthResponse.status -eq "healthy") {
        Write-Host "✅ Backend API is healthy" -ForegroundColor Green
    } else {
        Write-Host "❌ Backend API health check failed" -ForegroundColor Red
    }
} catch {
    Write-Host "❌ Backend API is not responding: $_" -ForegroundColor Red
}

# Check frontend
try {
    $frontendResponse = Invoke-WebRequest -Uri "http://localhost:3001" -TimeoutSec 10
    if ($frontendResponse.StatusCode -eq 200) {
        Write-Host "✅ Frontend is accessible" -ForegroundColor Green
    } else {
        Write-Host "❌ Frontend health check failed" -ForegroundColor Red
    }
} catch {
    Write-Host "❌ Frontend is not responding: $_" -ForegroundColor Red
}

# Step 5: API Tests
Write-Host "🧪 Step 5: Running API Tests..." -ForegroundColor Cyan

# Test market data API
try {
    $marketData = Invoke-RestMethod -Uri "http://localhost:8000/api/market-data" -TimeoutSec 10
    if ($marketData.data -and $marketData.data.Count -gt 0) {
        Write-Host "✅ Market Data API is working" -ForegroundColor Green
    } else {
        Write-Host "❌ Market Data API test failed" -ForegroundColor Red
    }
} catch {
    Write-Host "❌ Market Data API test failed: $_" -ForegroundColor Red
}

# Test chat API
try {
    $chatResponse = Invoke-RestMethod -Uri "http://localhost:8000/api/chat" -Method POST -ContentType "application/json" -Body '{"message": "Deployment test"}' -TimeoutSec 10
    if ($chatResponse.response) {
        Write-Host "✅ Chat API is working" -ForegroundColor Green
    } else {
        Write-Host "❌ Chat API test failed" -ForegroundColor Red
    }
} catch {
    Write-Host "❌ Chat API test failed: $_" -ForegroundColor Red
}

# Step 6: Display Results
Write-Host ""
Write-Host "🎉 DEPLOYMENT COMPLETE!" -ForegroundColor Yellow
Write-Host "========================" -ForegroundColor Yellow
Write-Host ""
Write-Host "🌐 Access URLs:" -ForegroundColor Cyan
Write-Host "   Frontend:  http://localhost:3001" -ForegroundColor White
Write-Host "   Backend:   http://localhost:8000" -ForegroundColor White
Write-Host "   API Docs:  http://localhost:8000/docs" -ForegroundColor White
Write-Host ""
Write-Host "🔧 Management Commands:" -ForegroundColor Cyan
Write-Host "   Stop:      docker-compose -f docker-compose-simple.yml down" -ForegroundColor White
Write-Host "   Restart:   docker-compose -f docker-compose-simple.yml restart" -ForegroundColor White
Write-Host "   Logs:      docker-compose -f docker-compose-simple.yml logs -f" -ForegroundColor White
Write-Host ""
Write-Host "Chainsight by Connectouch is now running!" -ForegroundColor Green
