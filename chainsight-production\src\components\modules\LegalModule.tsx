'use client';

import { motion } from 'framer-motion';
import { Shield, FileText, Users, AlertTriangle } from 'lucide-react';

export function LegalModule() {
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="space-y-6"
    >
      <div className="bg-white/10 backdrop-blur-xl rounded-2xl p-6 border border-white/20">
        <div className="flex items-center space-x-3 mb-6">
          <div className="w-12 h-12 bg-gradient-to-r from-blue-400 to-purple-500 rounded-xl flex items-center justify-center">
            <Shield className="w-6 h-6 text-white" />
          </div>
          <div>
            <h2 className="text-2xl font-bold text-white">Legal & HR AI</h2>
            <p className="text-gray-400">Compliance and human resources management</p>
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          <div className="bg-black/20 rounded-xl p-4">
            <div className="flex items-center space-x-3 mb-3">
              <FileText className="w-5 h-5 text-blue-400" />
              <span className="text-white font-medium">Documents</span>
            </div>
            <p className="text-2xl font-bold text-blue-400">24</p>
            <p className="text-sm text-gray-400">Pending review</p>
          </div>

          <div className="bg-black/20 rounded-xl p-4">
            <div className="flex items-center space-x-3 mb-3">
              <Users className="w-5 h-5 text-green-400" />
              <span className="text-white font-medium">Employees</span>
            </div>
            <p className="text-2xl font-bold text-green-400">156</p>
            <p className="text-sm text-gray-400">Active staff</p>
          </div>

          <div className="bg-black/20 rounded-xl p-4">
            <div className="flex items-center space-x-3 mb-3">
              <AlertTriangle className="w-5 h-5 text-yellow-400" />
              <span className="text-white font-medium">Compliance</span>
            </div>
            <p className="text-2xl font-bold text-yellow-400">98%</p>
            <p className="text-sm text-gray-400">Compliance rate</p>
          </div>
        </div>

        <div className="mt-6 p-4 bg-gradient-to-r from-blue-500/20 to-purple-500/20 rounded-xl border border-blue-500/30">
          <h3 className="text-white font-semibold mb-2">Legal AI Analysis</h3>
          <p className="text-gray-300 text-sm">
            All employment contracts are up to date. Recommend updating privacy policy 
            to comply with latest GDPR requirements. No critical compliance issues detected.
          </p>
        </div>
      </div>
    </motion.div>
  );
}
