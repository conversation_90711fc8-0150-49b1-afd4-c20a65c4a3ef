# 🚀 **CHAINSIGHT SYSTEM REQUIREMENTS**

## 📋 **COMPLETE DEPENDENCY LIST**

### **💻 System Requirements**

#### **Operating System**
- **Windows**: 10/11 (64-bit)
- **macOS**: 10.15+ (Catalina or later)
- **Linux**: Ubuntu 20.04+, CentOS 8+, or equivalent

#### **Hardware Requirements**
- **RAM**: 8GB minimum, 16GB recommended
- **Storage**: 10GB free space minimum
- **CPU**: Multi-core processor (4+ cores recommended)
- **GPU**: Optional (for AI/ML acceleration)

---

## 🛠️ **CORE RUNTIME DEPENDENCIES**

### **Node.js & Frontend**
```bash
# Node.js (Required: 18.0.0+)
node --version  # Should be 18.0.0 or higher
npm --version   # Should be 8.0.0 or higher

# Alternative: Use Yarn or PNPM
yarn --version  # 1.22.0+
pnpm --version  # 7.0.0+
```

### **Python & Backend**
```bash
# Python (Required: 3.9+)
python --version  # Should be 3.9.0 or higher
pip --version     # Should be 21.0.0 or higher

# Alternative: Use Poetry or Conda
poetry --version  # 1.4.0+
conda --version   # 4.12.0+
```

### **Database Systems**
```bash
# PostgreSQL (Required: 13+)
psql --version    # Should be 13.0 or higher

# Redis (Required: 6+)
redis-server --version  # Should be 6.0 or higher
```

---

## 📦 **FRONTEND DEPENDENCIES (Node.js)**

### **✅ INSTALLED DEPENDENCIES**

#### **Core Framework**
- ✅ `next@15.3.5` - React framework with SSR
- ✅ `react@19.0.0` - UI library
- ✅ `react-dom@19.0.0` - DOM rendering
- ✅ `typescript@5.x` - Type safety

#### **UI & Styling**
- ✅ `tailwindcss@4.x` - Utility-first CSS
- ✅ `framer-motion@12.23.0` - Animation library
- ✅ `lucide-react@0.525.0` - Icon library
- ✅ `class-variance-authority@0.7.1` - Component variants
- ✅ `clsx@2.1.1` - Conditional classes
- ✅ `tailwind-merge@3.3.1` - Tailwind utilities

#### **Charts & Visualization**
- ✅ `recharts@3.0.2` - React charts
- ✅ `chart.js@4.5.0` - Chart library
- ✅ `react-chartjs-2@5.3.0` - Chart.js React wrapper

#### **AI & API Integration**
- ✅ `openai@5.8.2` - OpenAI API client
- ✅ `axios@1.10.0` - HTTP client
- ✅ `swr@2.3.4` - Data fetching

#### **Real-time Communication**
- ✅ `socket.io-client@4.8.1` - WebSocket client
- ✅ `ws@8.18.3` - WebSocket library

#### **Web3 & Blockchain**
- ✅ `ethers@6.15.0` - Ethereum library
- ✅ `web3@4.16.0` - Web3 library
- ✅ `wagmi@2.15.6` - React hooks for Ethereum
- ✅ `@rainbow-me/rainbowkit@2.2.8` - Wallet connection
- ✅ `@web3modal/wagmi@5.1.11` - Web3 modal

#### **Forms & Validation**
- ✅ `react-hook-form@7.59.0` - Form handling
- ✅ `zod@3.25.71` - Schema validation
- ✅ `@hookform/resolvers@5.1.1` - Form resolvers

#### **State Management**
- ✅ `zustand@5.0.6` - State management
- ✅ `react-hot-toast@2.5.2` - Notifications

#### **Utilities**
- ✅ `date-fns@4.1.0` - Date utilities
- ✅ `uuid@11.1.0` - UUID generation
- ✅ `natural@8.1.0` - NLP processing
- ✅ `compromise@14.14.4` - Text processing
- ✅ `sentiment@5.0.2` - Sentiment analysis

---

## 🐍 **BACKEND DEPENDENCIES (Python)**

### **Core Framework**
- `fastapi==0.104.1` - Modern web framework
- `uvicorn[standard]==0.24.0` - ASGI server
- `pydantic==2.5.0` - Data validation

### **Database & Storage**
- `sqlalchemy==2.0.23` - SQL toolkit and ORM
- `alembic==1.12.1` - Database migrations
- `asyncpg==0.29.0` - PostgreSQL driver
- `redis==5.0.1` - Redis client

### **Authentication & Security**
- `python-jose[cryptography]==3.3.0` - JWT tokens
- `passlib[bcrypt]==1.7.4` - Password hashing
- `cryptography==41.0.7` - Cryptographic recipes

### **AI & Machine Learning**
- `openai==1.3.7` - OpenAI API
- `transformers==4.35.2` - Hugging Face models
- `torch==2.1.1` - PyTorch framework
- `scikit-learn==1.3.2` - ML algorithms
- `pandas==2.1.3` - Data manipulation
- `numpy==1.25.2` - Numerical computing

### **Blockchain & Web3**
- `web3==6.11.3` - Web3 Python library
- `eth-account==0.9.0` - Ethereum accounts
- `py-solc-x==1.12.0` - Solidity compiler

### **Finance APIs**
- `yfinance==0.2.22` - Yahoo Finance data
- `alpha-vantage==2.3.1` - Alpha Vantage API
- `ccxt==4.1.30` - Cryptocurrency exchange

### **Computer Vision**
- `opencv-python-headless==********` - Computer vision
- `Pillow==10.1.0` - Image processing
- `face-recognition==1.3.0` - Face recognition

### **Real-time & WebSocket**
- `websockets==12.0` - WebSocket server
- `python-socketio==5.10.0` - Socket.IO

---

## 🗄️ **DATABASE & INFRASTRUCTURE**

### **PostgreSQL Setup**
```bash
# Install PostgreSQL
# Windows: Download from postgresql.org
# macOS: brew install postgresql
# Ubuntu: sudo apt install postgresql postgresql-contrib

# Create database
createdb chainsight
```

### **Redis Setup**
```bash
# Install Redis
# Windows: Download from redis.io
# macOS: brew install redis
# Ubuntu: sudo apt install redis-server

# Start Redis
redis-server
```

### **Docker (Optional)**
```bash
# Install Docker Desktop
# Windows/macOS: Download from docker.com
# Ubuntu: sudo apt install docker.io docker-compose

# Verify installation
docker --version
docker-compose --version
```

---

## 🚀 **INSTALLATION COMMANDS**

### **Frontend Setup**
```bash
cd chainsight-production
npm install
npm run dev
```

### **Backend Setup**
```bash
# Create virtual environment
python -m venv venv
source venv/bin/activate  # Windows: venv\Scripts\activate

# Install dependencies
pip install -r backend-requirements.txt

# Run server
uvicorn main:app --reload
```

### **Database Setup**
```bash
# Initialize database
alembic upgrade head

# Create admin user (optional)
python scripts/create_admin.py
```

---

## 🔧 **ENVIRONMENT VARIABLES**

Create `.env` file:
```env
# Database
DATABASE_URL=postgresql://user:password@localhost:5432/chainsight
REDIS_URL=redis://localhost:6379

# API Keys
OPENAI_API_KEY=your_openai_key_here
ALPHA_VANTAGE_API_KEY=your_alpha_vantage_key

# Security
JWT_SECRET=your_jwt_secret_here
ENCRYPTION_KEY=your_encryption_key

# Blockchain
INFURA_PROJECT_ID=your_infura_id
ALCHEMY_API_KEY=your_alchemy_key
```

---

## ✅ **VERIFICATION CHECKLIST**

- [ ] Node.js 18+ installed
- [ ] Python 3.9+ installed
- [ ] PostgreSQL 13+ running
- [ ] Redis 6+ running
- [ ] All npm dependencies installed
- [ ] All pip dependencies installed
- [ ] Environment variables configured
- [ ] Database migrations applied
- [ ] Frontend running on localhost:3001
- [ ] Backend running on localhost:8000

---

## 🎯 **NEXT STEPS**

1. **Install System Dependencies**: Node.js, Python, PostgreSQL, Redis
2. **Clone Repository**: `git clone <repository>`
3. **Install Frontend**: `npm install`
4. **Install Backend**: `pip install -r backend-requirements.txt`
5. **Configure Environment**: Create `.env` file
6. **Initialize Database**: Run migrations
7. **Start Services**: Frontend + Backend + Database
8. **Verify Setup**: Check all endpoints working

**🎉 Your Chainsight platform will be ready to use!**
