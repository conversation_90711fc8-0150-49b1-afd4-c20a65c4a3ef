'use client';

import { motion } from 'framer-motion';
import { 
  Coins, 
  TrendingUp, 
  Bo<PERSON>, 
  Palette, 
  Users, 
  Shield,
  Camera,
  Zap
} from 'lucide-react';
import Link from 'next/link';

const features = [
  {
    icon: Coins,
    title: 'Crypto & Blockchain',
    description: 'ERC20 token creator, LP automation, smart contract deployment',
    href: '/crypto',
    color: 'from-yellow-500 to-orange-500',
  },
  {
    icon: TrendingUp,
    title: 'AI Finance',
    description: 'Predictive market analytics for stocks, crypto, and forex',
    href: '/finance',
    color: 'from-green-500 to-emerald-500',
  },
  {
    icon: Bo<PERSON>,
    title: 'Customer Support',
    description: 'Intelligent chatbot with API-integrated ticket system',
    href: '/support',
    color: 'from-blue-500 to-cyan-500',
  },
  {
    icon: Palette,
    title: 'Design Studio',
    description: 'Generate animations, Lottie/SVG export, live preview',
    href: '/design',
    color: 'from-purple-500 to-pink-500',
  },
  {
    icon: Users,
    title: 'HR & Legal AI',
    description: 'Resume parser, CV analysis, contract reviewer',
    href: '/hr-legal',
    color: 'from-indigo-500 to-purple-500',
  },
  {
    icon: Camera,
    title: 'Face Recognition',
    description: 'Real-time facial scan with age and emotion detection',
    href: '/face-scanner',
    color: 'from-red-500 to-pink-500',
  },
  {
    icon: Shield,
    title: 'Web3 Security',
    description: 'Smart contract auditing and security analysis',
    href: '/security',
    color: 'from-gray-500 to-slate-500',
  },
  {
    icon: Zap,
    title: 'Real-time Data',
    description: 'Live blockchain monitoring and instant notifications',
    href: '/realtime',
    color: 'from-yellow-400 to-yellow-600',
  },
];

export function FeatureGrid() {
  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
      {features.map((feature, index) => (
        <motion.div
          key={feature.title}
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ delay: index * 0.1, duration: 0.6 }}
          whileHover={{ y: -5, scale: 1.02 }}
          className="group"
        >
          <Link href={feature.href}>
            <div className="glass rounded-xl p-6 h-full transition-all duration-300 hover:border-primary-500/50 hover:shadow-lg hover:shadow-primary-500/10">
              <div className={`w-12 h-12 rounded-lg bg-gradient-to-r ${feature.color} flex items-center justify-center mb-4 group-hover:scale-110 transition-transform duration-300`}>
                <feature.icon className="w-6 h-6 text-white" />
              </div>
              
              <h3 className="text-xl font-semibold text-white mb-2 group-hover:text-primary-500 transition-colors duration-300">
                {feature.title}
              </h3>
              
              <p className="text-dark-300 text-sm leading-relaxed">
                {feature.description}
              </p>
              
              <div className="mt-4 flex items-center text-primary-500 text-sm font-medium opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                <span>Explore</span>
                <motion.div
                  className="ml-1"
                  animate={{ x: [0, 4, 0] }}
                  transition={{ duration: 1.5, repeat: Infinity }}
                >
                  →
                </motion.div>
              </div>
            </div>
          </Link>
        </motion.div>
      ))}
    </div>
  );
}
