version: '3.8'

services:
  # Backend API (using local Python)
  backend:
    build:
      context: ./apps/backend
      dockerfile: Dockerfile.simple
    container_name: chainsight-backend
    ports:
      - "8000:8000"
    environment:
      - PYTHONPATH=/app
      - ENVIRONMENT=development
    volumes:
      - ./apps/backend:/app
    command: python main_simple.py
    networks:
      - chainsight-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Frontend (using Node.js)
  frontend:
    build:
      context: ./demo
      dockerfile: Dockerfile.simple
    container_name: chainsight-frontend
    ports:
      - "3000:3000"
    volumes:
      - ./demo:/app
    command: python -m http.server 3000
    networks:
      - chainsight-network
    depends_on:
      - backend

networks:
  chainsight-network:
    driver: bridge
