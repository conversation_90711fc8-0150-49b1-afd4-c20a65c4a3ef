import { define<PERSON>hai<PERSON> } from '../../utils/chain/defineChain.js'

export const scrollSepolia = /*#__PURE__*/ defineChain({
  id: 534_351,
  name: '<PERSON><PERSON> Sepolia',
  nativeCurrency: { name: '<PERSON><PERSON>', symbol: 'ETH', decimals: 18 },
  rpcUrls: {
    default: {
      http: ['https://sepolia-rpc.scroll.io'],
    },
  },
  blockExplorers: {
    default: {
      name: 'Scrolls<PERSON>',
      url: 'https://sepolia.scrollscan.com',
      apiUrl: 'https://api-sepolia.scrollscan.com/api',
    },
  },
  contracts: {
    multicall3: {
      address: '******************************************',
      blockCreated: 9473,
    },
  },
  testnet: true,
})
