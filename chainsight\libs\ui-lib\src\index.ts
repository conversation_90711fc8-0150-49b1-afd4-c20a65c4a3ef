// UI Library - Main exports

// Core Components
export * from './components/Button';
export * from './components/Card';
export * from './components/Input';
export * from './components/Modal';
export * from './components/Dropdown';
export * from './components/Chart';
export * from './components/Table';
export * from './components/Badge';
export * from './components/Avatar';
export * from './components/Spinner';
export * from './components/Toast';
export * from './components/Tooltip';
export * from './components/Progress';
export * from './components/Tabs';
export * from './components/Accordion';
export * from './components/Slider';
export * from './components/Switch';
export * from './components/DatePicker';
export * from './components/FileUpload';
export * from './components/SearchBox';
export * from './components/Pagination';

// Layout components
export * from './layout/Container';
export * from './layout/Grid';
export * from './layout/Flex';
export * from './layout/Stack';
export * from './layout/Sidebar';
export * from './layout/Header';
export * from './layout/Footer';

// Specialized components
export * from './crypto/PriceChart';
export * from './crypto/TokenCard';
export * from './crypto/WalletConnect';
export * from './finance/StockChart';
export * from './finance/PortfolioCard';
export * from './ai/ChatInterface';
export * from './ai/MessageBubble';
export * from './design/AnimationPreview';
export * from './face/FaceDetectionBox';

// Hooks
export * from './hooks/useTheme';
export * from './hooks/useAnimation';
export * from './hooks/useLocalStorage';
export * from './hooks/useDebounce';
export * from './hooks/useWebSocket';

// Utils
export * from './utils/cn';
export * from './utils/colors';
export * from './utils/animations';
export * from './utils/formatters';

// Types
export * from './types/UITypes';

// Theme
export * from './theme/colors';
export * from './theme/typography';
export * from './theme/spacing';
export * from './theme/shadows';

// Constants
export const UI_VERSION = '1.0.0';

export const LUXURY_THEME = {
  colors: {
    primary: {
      50: '#fefce8',
      100: '#fef9c3',
      200: '#fef08a',
      300: '#fde047',
      400: '#facc15',
      500: '#eab308',
      600: '#ca8a04',
      700: '#a16207',
      800: '#854d0e',
      900: '#713f12',
    },
    dark: {
      50: '#f8fafc',
      100: '#f1f5f9',
      200: '#e2e8f0',
      300: '#cbd5e1',
      400: '#94a3b8',
      500: '#64748b',
      600: '#475569',
      700: '#334155',
      800: '#1e293b',
      900: '#0f172a',
    }
  },
  gradients: {
    luxury: 'linear-gradient(135deg, #0f172a 0%, #1e293b 50%, #334155 100%)',
    gold: 'linear-gradient(135deg, #fbbf24 0%, #f59e0b 50%, #d97706 100%)',
    accent: 'linear-gradient(135deg, #6366f1 0%, #8b5cf6 50%, #a855f7 100%)'
  },
  shadows: {
    luxury: '0 25px 50px -12px rgba(0, 0, 0, 0.25)',
    gold: '0 10px 25px -3px rgba(251, 191, 36, 0.1)',
    glow: '0 0 20px rgba(251, 191, 36, 0.3)'
  }
};

// Component variants
export const BUTTON_VARIANTS = {
  primary: 'bg-gradient-to-r from-yellow-400 to-yellow-600 text-black',
  secondary: 'bg-gray-800 text-white border border-gray-700',
  ghost: 'bg-transparent text-gray-300 hover:bg-gray-800',
  luxury: 'bg-gradient-to-r from-gray-900 to-black text-yellow-400 border border-yellow-400'
};

export const CARD_VARIANTS = {
  default: 'bg-gray-900 border border-gray-800',
  luxury: 'bg-gradient-to-br from-gray-900 to-black border border-yellow-400/20',
  glass: 'bg-black/20 backdrop-blur-lg border border-white/10',
  elevated: 'bg-gray-900 border border-gray-800 shadow-luxury'
};
