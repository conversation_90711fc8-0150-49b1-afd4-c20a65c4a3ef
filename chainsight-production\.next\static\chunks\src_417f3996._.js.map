{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///F:/AI%20chain/chainsight-production/src/components/Navigation.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { motion } from 'framer-motion';\nimport Link from 'next/link';\nimport {\n  Menu,\n  X,\n  Zap,\n  TrendingUp,\n  Bo<PERSON>,\n  Palette,\n  <PERSON>,\n  Shield,\n  Camera,\n  Coins,\n  Settings,\n  User\n} from 'lucide-react';\n\nconst navItems = [\n  { name: 'Dashboard', href: '/', icon: TrendingUp },\n  { name: 'Crypto Tools', href: '/crypto', icon: Coins },\n  { name: 'AI Finance', href: '/finance', icon: TrendingUp },\n  { name: 'Design Studio', href: '/design', icon: Palette },\n  { name: 'HR & Legal', href: '/hr-legal', icon: Shield },\n  { name: 'Face Scanner', href: '/face-scanner', icon: Camera },\n  { name: 'Support', href: '/support', icon: Bot },\n];\n\nexport function Navigation() {\n  const [isOpen, setIsOpen] = useState(false);\n\n  return (\n    <nav className=\"fixed top-0 w-full z-50 glass border-b border-primary-500/20\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className=\"flex justify-between items-center h-16\">\n          {/* Logo */}\n          <Link href=\"/\" className=\"flex items-center space-x-2\">\n            <motion.div\n              whileHover={{ rotate: 360 }}\n              transition={{ duration: 0.5 }}\n              className=\"w-8 h-8 bg-gold-gradient rounded-lg flex items-center justify-center\"\n            >\n              <Zap className=\"w-5 h-5 text-dark-900\" />\n            </motion.div>\n            <span className=\"text-xl font-bold text-white\">\n              Chain<span className=\"text-primary-500\">sight</span>\n            </span>\n          </Link>\n\n          {/* Desktop Navigation */}\n          <div className=\"hidden md:flex items-center space-x-8\">\n            {navItems.map((item) => (\n              <Link\n                key={item.name}\n                href={item.href}\n                className=\"flex items-center space-x-1 text-dark-300 hover:text-primary-500 transition-colors duration-200\"\n              >\n                <item.icon className=\"w-4 h-4\" />\n                <span>{item.name}</span>\n              </Link>\n            ))}\n          </div>\n\n          {/* User Menu */}\n          <div className=\"hidden md:flex items-center space-x-3\">\n            <button className=\"p-2 bg-white/10 rounded-lg hover:bg-white/20 transition-colors\">\n              <Settings className=\"w-4 h-4 text-gray-300\" />\n            </button>\n            <button className=\"p-2 bg-white/10 rounded-lg hover:bg-white/20 transition-colors\">\n              <User className=\"w-4 h-4 text-gray-300\" />\n            </button>\n            <div className=\"w-2 h-2 bg-green-400 rounded-full animate-pulse\" />\n          </div>\n\n          {/* Mobile menu button */}\n          <div className=\"md:hidden\">\n            <button\n              onClick={() => setIsOpen(!isOpen)}\n              className=\"text-dark-300 hover:text-primary-500 transition-colors duration-200\"\n            >\n              {isOpen ? <X className=\"w-6 h-6\" /> : <Menu className=\"w-6 h-6\" />}\n            </button>\n          </div>\n        </div>\n      </div>\n\n      {/* Mobile Navigation */}\n      {isOpen && (\n        <motion.div\n          initial={{ opacity: 0, y: -20 }}\n          animate={{ opacity: 1, y: 0 }}\n          exit={{ opacity: 0, y: -20 }}\n          className=\"md:hidden glass border-t border-primary-500/20\"\n        >\n          <div className=\"px-2 pt-2 pb-3 space-y-1\">\n            {navItems.map((item) => (\n              <Link\n                key={item.name}\n                href={item.href}\n                className=\"flex items-center space-x-2 px-3 py-2 text-dark-300 hover:text-primary-500 hover:bg-dark-800/50 rounded-md transition-all duration-200\"\n                onClick={() => setIsOpen(false)}\n              >\n                <item.icon className=\"w-4 h-4\" />\n                <span>{item.name}</span>\n              </Link>\n            ))}\n            <div className=\"pt-4 border-t border-primary-500/20\">\n              <button\n                onClick={() => {\n                  open();\n                  setIsOpen(false);\n                }}\n                className=\"w-full px-3 py-2 bg-primary-500 text-dark-900 rounded-md font-medium hover:bg-primary-400 transition-colors duration-200\"\n              >\n                {isConnected \n                  ? `${address?.slice(0, 6)}...${address?.slice(-4)}`\n                  : 'Connect Wallet'\n                }\n              </button>\n            </div>\n          </div>\n        </motion.div>\n      )}\n    </nav>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;AALA;;;;;AAoBA,MAAM,WAAW;IACf;QAAE,MAAM;QAAa,MAAM;QAAK,MAAM,qNAAA,CAAA,aAAU;IAAC;IACjD;QAAE,MAAM;QAAgB,MAAM;QAAW,MAAM,uMAAA,CAAA,QAAK;IAAC;IACrD;QAAE,MAAM;QAAc,MAAM;QAAY,MAAM,qNAAA,CAAA,aAAU;IAAC;IACzD;QAAE,MAAM;QAAiB,MAAM;QAAW,MAAM,2MAAA,CAAA,UAAO;IAAC;IACxD;QAAE,MAAM;QAAc,MAAM;QAAa,MAAM,yMAAA,CAAA,SAAM;IAAC;IACtD;QAAE,MAAM;QAAgB,MAAM;QAAiB,MAAM,yMAAA,CAAA,SAAM;IAAC;IAC5D;QAAE,MAAM;QAAW,MAAM;QAAY,MAAM,mMAAA,CAAA,MAAG;IAAC;CAChD;AAEM,SAAS;;IACd,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAErC,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC,+JAAA,CAAA,UAAI;4BAAC,MAAK;4BAAI,WAAU;;8CACvB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oCACT,YAAY;wCAAE,QAAQ;oCAAI;oCAC1B,YAAY;wCAAE,UAAU;oCAAI;oCAC5B,WAAU;8CAEV,cAAA,6LAAC,mMAAA,CAAA,MAAG;wCAAC,WAAU;;;;;;;;;;;8CAEjB,6LAAC;oCAAK,WAAU;;wCAA+B;sDACxC,6LAAC;4CAAK,WAAU;sDAAmB;;;;;;;;;;;;;;;;;;sCAK5C,6LAAC;4BAAI,WAAU;sCACZ,SAAS,GAAG,CAAC,CAAC,qBACb,6LAAC,+JAAA,CAAA,UAAI;oCAEH,MAAM,KAAK,IAAI;oCACf,WAAU;;sDAEV,6LAAC,KAAK,IAAI;4CAAC,WAAU;;;;;;sDACrB,6LAAC;sDAAM,KAAK,IAAI;;;;;;;mCALX,KAAK,IAAI;;;;;;;;;;sCAWpB,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAO,WAAU;8CAChB,cAAA,6LAAC,6MAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;;;;;;8CAEtB,6LAAC;oCAAO,WAAU;8CAChB,cAAA,6LAAC,qMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;;;;;;8CAElB,6LAAC;oCAAI,WAAU;;;;;;;;;;;;sCAIjB,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCACC,SAAS,IAAM,UAAU,CAAC;gCAC1B,WAAU;0CAET,uBAAS,6LAAC,+LAAA,CAAA,IAAC;oCAAC,WAAU;;;;;yDAAe,6LAAC,qMAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;YAO7D,wBACC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,SAAS;oBAAE,SAAS;oBAAG,GAAG,CAAC;gBAAG;gBAC9B,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAE;gBAC5B,MAAM;oBAAE,SAAS;oBAAG,GAAG,CAAC;gBAAG;gBAC3B,WAAU;0BAEV,cAAA,6LAAC;oBAAI,WAAU;;wBACZ,SAAS,GAAG,CAAC,CAAC,qBACb,6LAAC,+JAAA,CAAA,UAAI;gCAEH,MAAM,KAAK,IAAI;gCACf,WAAU;gCACV,SAAS,IAAM,UAAU;;kDAEzB,6LAAC,KAAK,IAAI;wCAAC,WAAU;;;;;;kDACrB,6LAAC;kDAAM,KAAK,IAAI;;;;;;;+BANX,KAAK,IAAI;;;;;sCASlB,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCACC,SAAS;oCACP;oCACA,UAAU;gCACZ;gCACA,WAAU;0CAET,cACG,GAAG,SAAS,MAAM,GAAG,GAAG,GAAG,EAAE,SAAS,MAAM,CAAC,IAAI,GACjD;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASpB;GAjGgB;KAAA", "debugId": null}}, {"offset": {"line": 334, "column": 0}, "map": {"version": 3, "sources": ["file:///F:/AI%20chain/chainsight-production/src/hooks/useCryptoData.ts"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect, useCallback } from 'react';\nimport axios from 'axios';\n\ninterface CryptoPrice {\n  id: string;\n  symbol: string;\n  name: string;\n  current_price: number;\n  price_change_percentage_24h: number;\n  market_cap: number;\n  volume_24h: number;\n}\n\ninterface Portfolio {\n  total: number;\n  change: number;\n  holdings: any[];\n}\n\ninterface MarketData {\n  chartData: Array<{ time: string; price: number }>;\n  volume: number;\n  marketCap: number;\n}\n\nexport function useCryptoData() {\n  const [prices, setPrices] = useState<CryptoPrice[]>([]);\n  const [portfolio, setPortfolio] = useState<Portfolio | null>(null);\n  const [marketData, setMarketData] = useState<MarketData | null>(null);\n  const [isLoading, setIsLoading] = useState(true);\n  const [error, setError] = useState<string | null>(null);\n\n  const fetchCryptoPrices = useCallback(async () => {\n    try {\n      // Using CoinGecko API (free tier)\n      const response = await axios.get(\n        'https://api.coingecko.com/api/v3/coins/markets',\n        {\n          params: {\n            vs_currency: 'usd',\n            ids: 'bitcoin,ethereum,binancecoin,cardano,solana,polkadot,dogecoin,avalanche-2,polygon,chainlink',\n            order: 'market_cap_desc',\n            per_page: 10,\n            page: 1,\n            sparkline: false,\n            price_change_percentage: '24h'\n          }\n        }\n      );\n\n      setPrices(response.data);\n      \n      // Generate mock portfolio data based on real prices\n      const totalValue = response.data.reduce((sum: number, coin: any) => {\n        return sum + (coin.current_price * Math.random() * 10);\n      }, 0);\n      \n      setPortfolio({\n        total: totalValue,\n        change: (Math.random() - 0.5) * 10, // Random change between -5% and +5%\n        holdings: response.data.slice(0, 5).map((coin: any) => ({\n          id: coin.id,\n          symbol: coin.symbol,\n          amount: Math.random() * 10,\n          value: coin.current_price * Math.random() * 10\n        }))\n      });\n\n    } catch (err) {\n      console.error('Failed to fetch crypto prices:', err);\n      setError('Failed to fetch real-time crypto data. Using demo data.');\n      \n      // Fallback to demo data\n      setPrices([\n        {\n          id: 'bitcoin',\n          symbol: 'btc',\n          name: 'Bitcoin',\n          current_price: 43250.00,\n          price_change_percentage_24h: 2.5,\n          market_cap: 850000000000,\n          volume_24h: 25000000000\n        },\n        {\n          id: 'ethereum',\n          symbol: 'eth',\n          name: 'Ethereum',\n          current_price: 2650.00,\n          price_change_percentage_24h: -1.2,\n          market_cap: 320000000000,\n          volume_24h: 15000000000\n        },\n        {\n          id: 'binancecoin',\n          symbol: 'bnb',\n          name: 'BNB',\n          current_price: 315.50,\n          price_change_percentage_24h: 0.8,\n          market_cap: 48000000000,\n          volume_24h: 1200000000\n        }\n      ]);\n      \n      setPortfolio({\n        total: 125000,\n        change: 5.2,\n        holdings: []\n      });\n    }\n  }, []);\n\n  const fetchMarketData = useCallback(async () => {\n    try {\n      // Generate realistic chart data\n      const now = Date.now();\n      const chartData = [];\n      let basePrice = 43250;\n      \n      for (let i = 23; i >= 0; i--) {\n        const time = new Date(now - i * 60 * 60 * 1000).toLocaleTimeString('en-US', { \n          hour: '2-digit', \n          minute: '2-digit' \n        });\n        \n        // Add some realistic price movement\n        basePrice += (Math.random() - 0.5) * 1000;\n        chartData.push({\n          time,\n          price: Math.round(basePrice)\n        });\n      }\n\n      setMarketData({\n        chartData,\n        volume: 25000000000,\n        marketCap: 850000000000\n      });\n\n    } catch (err) {\n      console.error('Failed to fetch market data:', err);\n    }\n  }, []);\n\n  const refreshData = useCallback(async () => {\n    setIsLoading(true);\n    setError(null);\n    \n    try {\n      await Promise.all([\n        fetchCryptoPrices(),\n        fetchMarketData()\n      ]);\n    } catch (err) {\n      console.error('Failed to refresh data:', err);\n    } finally {\n      setIsLoading(false);\n    }\n  }, [fetchCryptoPrices, fetchMarketData]);\n\n  useEffect(() => {\n    refreshData();\n    \n    // Set up real-time updates every 30 seconds\n    const interval = setInterval(refreshData, 30000);\n    \n    return () => clearInterval(interval);\n  }, [refreshData]);\n\n  return {\n    prices,\n    portfolio,\n    marketData,\n    isLoading,\n    error,\n    refreshData\n  };\n}\n"], "names": [], "mappings": ";;;AAEA;AACA;;AAHA;;;AA2BO,SAAS;;IACd,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB,EAAE;IACtD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAoB;IAC7D,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAqB;IAChE,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAElD,MAAM,oBAAoB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;wDAAE;YACpC,IAAI;gBACF,kCAAkC;gBAClC,MAAM,WAAW,MAAM,wIAAA,CAAA,UAAK,CAAC,GAAG,CAC9B,kDACA;oBACE,QAAQ;wBACN,aAAa;wBACb,KAAK;wBACL,OAAO;wBACP,UAAU;wBACV,MAAM;wBACN,WAAW;wBACX,yBAAyB;oBAC3B;gBACF;gBAGF,UAAU,SAAS,IAAI;gBAEvB,oDAAoD;gBACpD,MAAM,aAAa,SAAS,IAAI,CAAC,MAAM;+EAAC,CAAC,KAAa;wBACpD,OAAO,MAAO,KAAK,aAAa,GAAG,KAAK,MAAM,KAAK;oBACrD;8EAAG;gBAEH,aAAa;oBACX,OAAO;oBACP,QAAQ,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI;oBAChC,UAAU,SAAS,IAAI,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG;wEAAC,CAAC,OAAc,CAAC;gCACtD,IAAI,KAAK,EAAE;gCACX,QAAQ,KAAK,MAAM;gCACnB,QAAQ,KAAK,MAAM,KAAK;gCACxB,OAAO,KAAK,aAAa,GAAG,KAAK,MAAM,KAAK;4BAC9C,CAAC;;gBACH;YAEF,EAAE,OAAO,KAAK;gBACZ,QAAQ,KAAK,CAAC,kCAAkC;gBAChD,SAAS;gBAET,wBAAwB;gBACxB,UAAU;oBACR;wBACE,IAAI;wBACJ,QAAQ;wBACR,MAAM;wBACN,eAAe;wBACf,6BAA6B;wBAC7B,YAAY;wBACZ,YAAY;oBACd;oBACA;wBACE,IAAI;wBACJ,QAAQ;wBACR,MAAM;wBACN,eAAe;wBACf,6BAA6B,CAAC;wBAC9B,YAAY;wBACZ,YAAY;oBACd;oBACA;wBACE,IAAI;wBACJ,QAAQ;wBACR,MAAM;wBACN,eAAe;wBACf,6BAA6B;wBAC7B,YAAY;wBACZ,YAAY;oBACd;iBACD;gBAED,aAAa;oBACX,OAAO;oBACP,QAAQ;oBACR,UAAU,EAAE;gBACd;YACF;QACF;uDAAG,EAAE;IAEL,MAAM,kBAAkB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;sDAAE;YAClC,IAAI;gBACF,gCAAgC;gBAChC,MAAM,MAAM,KAAK,GAAG;gBACpB,MAAM,YAAY,EAAE;gBACpB,IAAI,YAAY;gBAEhB,IAAK,IAAI,IAAI,IAAI,KAAK,GAAG,IAAK;oBAC5B,MAAM,OAAO,IAAI,KAAK,MAAM,IAAI,KAAK,KAAK,MAAM,kBAAkB,CAAC,SAAS;wBAC1E,MAAM;wBACN,QAAQ;oBACV;oBAEA,oCAAoC;oBACpC,aAAa,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI;oBACrC,UAAU,IAAI,CAAC;wBACb;wBACA,OAAO,KAAK,KAAK,CAAC;oBACpB;gBACF;gBAEA,cAAc;oBACZ;oBACA,QAAQ;oBACR,WAAW;gBACb;YAEF,EAAE,OAAO,KAAK;gBACZ,QAAQ,KAAK,CAAC,gCAAgC;YAChD;QACF;qDAAG,EAAE;IAEL,MAAM,cAAc,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;kDAAE;YAC9B,aAAa;YACb,SAAS;YAET,IAAI;gBACF,MAAM,QAAQ,GAAG,CAAC;oBAChB;oBACA;iBACD;YACH,EAAE,OAAO,KAAK;gBACZ,QAAQ,KAAK,CAAC,2BAA2B;YAC3C,SAAU;gBACR,aAAa;YACf;QACF;iDAAG;QAAC;QAAmB;KAAgB;IAEvC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;mCAAE;YACR;YAEA,4CAA4C;YAC5C,MAAM,WAAW,YAAY,aAAa;YAE1C;2CAAO,IAAM,cAAc;;QAC7B;kCAAG;QAAC;KAAY;IAEhB,OAAO;QACL;QACA;QACA;QACA;QACA;QACA;IACF;AACF;GAvJgB", "debugId": null}}, {"offset": {"line": 504, "column": 0}, "map": {"version": 3, "sources": ["file:///F:/AI%20chain/chainsight-production/src/components/ui/LoadingSpinner.tsx"], "sourcesContent": ["'use client';\n\nimport { motion } from 'framer-motion';\n\ninterface LoadingSpinnerProps {\n  size?: 'sm' | 'md' | 'lg';\n  color?: string;\n  className?: string;\n}\n\nexport function LoadingSpinner({ \n  size = 'md', \n  color = 'text-yellow-400',\n  className = '' \n}: LoadingSpinnerProps) {\n  const sizeClasses = {\n    sm: 'w-4 h-4',\n    md: 'w-8 h-8',\n    lg: 'w-12 h-12'\n  };\n\n  return (\n    <motion.div\n      animate={{ rotate: 360 }}\n      transition={{ duration: 1, repeat: Infinity, ease: \"linear\" }}\n      className={`${sizeClasses[size]} ${color} ${className}`}\n    >\n      <svg\n        className=\"w-full h-full\"\n        viewBox=\"0 0 24 24\"\n        fill=\"none\"\n        xmlns=\"http://www.w3.org/2000/svg\"\n      >\n        <circle\n          cx=\"12\"\n          cy=\"12\"\n          r=\"10\"\n          stroke=\"currentColor\"\n          strokeWidth=\"2\"\n          strokeLinecap=\"round\"\n          strokeDasharray=\"31.416\"\n          strokeDashoffset=\"31.416\"\n          className=\"opacity-25\"\n        />\n        <circle\n          cx=\"12\"\n          cy=\"12\"\n          r=\"10\"\n          stroke=\"currentColor\"\n          strokeWidth=\"2\"\n          strokeLinecap=\"round\"\n          strokeDasharray=\"31.416\"\n          strokeDashoffset=\"23.562\"\n          className=\"opacity-75\"\n        />\n      </svg>\n    </motion.div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAUO,SAAS,eAAe,EAC7B,OAAO,IAAI,EACX,QAAQ,iBAAiB,EACzB,YAAY,EAAE,EACM;IACpB,MAAM,cAAc;QAClB,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,qBACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;QACT,SAAS;YAAE,QAAQ;QAAI;QACvB,YAAY;YAAE,UAAU;YAAG,QAAQ;YAAU,MAAM;QAAS;QAC5D,WAAW,GAAG,WAAW,CAAC,KAAK,CAAC,CAAC,EAAE,MAAM,CAAC,EAAE,WAAW;kBAEvD,cAAA,6LAAC;YACC,WAAU;YACV,SAAQ;YACR,MAAK;YACL,OAAM;;8BAEN,6LAAC;oBACC,IAAG;oBACH,IAAG;oBACH,GAAE;oBACF,QAAO;oBACP,aAAY;oBACZ,eAAc;oBACd,iBAAgB;oBAChB,kBAAiB;oBACjB,WAAU;;;;;;8BAEZ,6LAAC;oBACC,IAAG;oBACH,IAAG;oBACH,GAAE;oBACF,QAAO;oBACP,aAAY;oBACZ,eAAc;oBACd,iBAAgB;oBAChB,kBAAiB;oBACjB,WAAU;;;;;;;;;;;;;;;;;AAKpB;KAhDgB", "debugId": null}}, {"offset": {"line": 588, "column": 0}, "map": {"version": 3, "sources": ["file:///F:/AI%20chain/chainsight-production/src/components/modules/CryptoModule.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { motion } from 'framer-motion';\nimport { \n  TrendingUp, \n  TrendingDown, \n  DollarSign, \n  BarChart3, \n  AlertTriangle,\n  Zap,\n  Bitcoin,\n  Activity\n} from 'lucide-react';\nimport { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer } from 'recharts';\nimport { useCryptoData } from '@/hooks/useCryptoData';\nimport { LoadingSpinner } from '../ui/LoadingSpinner';\n\ninterface CryptoModuleProps {\n  data?: any;\n}\n\nexport function CryptoModule({ data }: CryptoModuleProps) {\n  const { \n    prices, \n    portfolio, \n    marketData, \n    isLoading, \n    error,\n    refreshData \n  } = useCryptoData();\n\n  const [selectedCrypto, setSelectedCrypto] = useState('bitcoin');\n  const [timeframe, setTimeframe] = useState('24h');\n\n  const cryptoList = [\n    { id: 'bitcoin', symbol: 'BTC', name: 'Bitcoin', color: 'text-orange-400' },\n    { id: 'ethereum', symbol: 'ETH', name: 'Ether<PERSON>', color: 'text-blue-400' },\n    { id: 'binancecoin', symbol: 'BNB', name: 'BNB', color: 'text-yellow-400' },\n    { id: 'cardano', symbol: 'ADA', name: 'Cardano', color: 'text-blue-300' },\n    { id: 'solana', symbol: 'SOL', name: 'Solana', color: 'text-purple-400' },\n  ];\n\n  if (isLoading) {\n    return (\n      <div className=\"bg-black/20 backdrop-blur-xl border border-white/10 rounded-2xl p-8\">\n        <div className=\"flex items-center justify-center h-64\">\n          <LoadingSpinner />\n          <span className=\"ml-3 text-white\">Loading crypto data...</span>\n        </div>\n      </div>\n    );\n  }\n\n  if (error) {\n    return (\n      <div className=\"bg-black/20 backdrop-blur-xl border border-white/10 rounded-2xl p-8\">\n        <div className=\"text-center\">\n          <AlertTriangle className=\"w-12 h-12 text-red-400 mx-auto mb-4\" />\n          <h3 className=\"text-xl font-semibold text-white mb-2\">Failed to Load Data</h3>\n          <p className=\"text-gray-400 mb-4\">{error}</p>\n          <button\n            onClick={refreshData}\n            className=\"px-4 py-2 bg-gradient-to-r from-orange-500 to-yellow-500 text-black font-semibold rounded-lg hover:shadow-lg transition-all\"\n          >\n            Retry\n          </button>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Portfolio Overview */}\n      <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4\">\n        <motion.div\n          initial={{ opacity: 0, y: 20 }}\n          animate={{ opacity: 1, y: 0 }}\n          className=\"bg-black/20 backdrop-blur-xl border border-white/10 rounded-xl p-6\"\n        >\n          <div className=\"flex items-center justify-between\">\n            <div>\n              <p className=\"text-gray-400 text-sm\">Portfolio Value</p>\n              <p className=\"text-2xl font-bold text-white\">\n                ${portfolio?.total?.toLocaleString() || '0'}\n              </p>\n            </div>\n            <DollarSign className=\"w-8 h-8 text-green-400\" />\n          </div>\n          <div className=\"flex items-center mt-2\">\n            {(portfolio?.change || 0) >= 0 ? (\n              <TrendingUp className=\"w-4 h-4 text-green-400 mr-1\" />\n            ) : (\n              <TrendingDown className=\"w-4 h-4 text-red-400 mr-1\" />\n            )}\n            <span className={`text-sm ${(portfolio?.change || 0) >= 0 ? 'text-green-400' : 'text-red-400'}`}>\n              {portfolio?.change?.toFixed(2) || '0'}%\n            </span>\n          </div>\n        </motion.div>\n\n        <motion.div\n          initial={{ opacity: 0, y: 20 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ delay: 0.1 }}\n          className=\"bg-black/20 backdrop-blur-xl border border-white/10 rounded-xl p-6\"\n        >\n          <div className=\"flex items-center justify-between\">\n            <div>\n              <p className=\"text-gray-400 text-sm\">Active Alerts</p>\n              <p className=\"text-2xl font-bold text-white\">{data?.alerts || 0}</p>\n            </div>\n            <AlertTriangle className=\"w-8 h-8 text-yellow-400\" />\n          </div>\n          <p className=\"text-xs text-gray-400 mt-2\">Price & volume alerts</p>\n        </motion.div>\n\n        <motion.div\n          initial={{ opacity: 0, y: 20 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ delay: 0.2 }}\n          className=\"bg-black/20 backdrop-blur-xl border border-white/10 rounded-xl p-6\"\n        >\n          <div className=\"flex items-center justify-between\">\n            <div>\n              <p className=\"text-gray-400 text-sm\">AI Analysis</p>\n              <p className=\"text-lg font-bold text-green-400\">Bullish</p>\n            </div>\n            <Activity className=\"w-8 h-8 text-blue-400\" />\n          </div>\n          <p className=\"text-xs text-gray-400 mt-2\">Market sentiment</p>\n        </motion.div>\n      </div>\n\n      {/* Crypto Price List */}\n      <motion.div\n        initial={{ opacity: 0, y: 20 }}\n        animate={{ opacity: 1, y: 0 }}\n        transition={{ delay: 0.3 }}\n        className=\"bg-black/20 backdrop-blur-xl border border-white/10 rounded-xl p-6\"\n      >\n        <div className=\"flex items-center justify-between mb-6\">\n          <h3 className=\"text-xl font-semibold text-white\">Live Prices</h3>\n          <button\n            onClick={refreshData}\n            className=\"p-2 bg-white/10 rounded-lg hover:bg-white/20 transition-colors\"\n          >\n            <Zap className=\"w-4 h-4 text-yellow-400\" />\n          </button>\n        </div>\n\n        <div className=\"space-y-4\">\n          {cryptoList.map((crypto, index) => {\n            const priceData = prices?.find(p => p.id === crypto.id);\n            const price = priceData?.current_price || 0;\n            const change = priceData?.price_change_percentage_24h || 0;\n\n            return (\n              <motion.div\n                key={crypto.id}\n                initial={{ opacity: 0, x: -20 }}\n                animate={{ opacity: 1, x: 0 }}\n                transition={{ delay: index * 0.1 }}\n                onClick={() => setSelectedCrypto(crypto.id)}\n                className={`\n                  p-4 rounded-lg border cursor-pointer transition-all\n                  ${selectedCrypto === crypto.id \n                    ? 'bg-white/10 border-white/20' \n                    : 'bg-white/5 border-white/10 hover:bg-white/10'\n                  }\n                `}\n              >\n                <div className=\"flex items-center justify-between\">\n                  <div className=\"flex items-center space-x-3\">\n                    <div className=\"w-10 h-10 bg-gradient-to-r from-orange-500 to-yellow-500 rounded-full flex items-center justify-center\">\n                      <span className=\"text-black font-bold text-sm\">{crypto.symbol}</span>\n                    </div>\n                    <div>\n                      <p className=\"font-semibold text-white\">{crypto.name}</p>\n                      <p className=\"text-sm text-gray-400\">{crypto.symbol}</p>\n                    </div>\n                  </div>\n                  \n                  <div className=\"text-right\">\n                    <p className=\"font-semibold text-white\">\n                      ${price.toLocaleString()}\n                    </p>\n                    <div className=\"flex items-center\">\n                      {change >= 0 ? (\n                        <TrendingUp className=\"w-3 h-3 text-green-400 mr-1\" />\n                      ) : (\n                        <TrendingDown className=\"w-3 h-3 text-red-400 mr-1\" />\n                      )}\n                      <span className={`text-sm ${change >= 0 ? 'text-green-400' : 'text-red-400'}`}>\n                        {change.toFixed(2)}%\n                      </span>\n                    </div>\n                  </div>\n                </div>\n              </motion.div>\n            );\n          })}\n        </div>\n      </motion.div>\n\n      {/* Price Chart */}\n      <motion.div\n        initial={{ opacity: 0, y: 20 }}\n        animate={{ opacity: 1, y: 0 }}\n        transition={{ delay: 0.4 }}\n        className=\"bg-black/20 backdrop-blur-xl border border-white/10 rounded-xl p-6\"\n      >\n        <div className=\"flex items-center justify-between mb-6\">\n          <h3 className=\"text-xl font-semibold text-white\">\n            {cryptoList.find(c => c.id === selectedCrypto)?.name} Price Chart\n          </h3>\n          \n          <div className=\"flex space-x-2\">\n            {['1h', '24h', '7d', '30d'].map((tf) => (\n              <button\n                key={tf}\n                onClick={() => setTimeframe(tf)}\n                className={`\n                  px-3 py-1 rounded-lg text-sm font-medium transition-colors\n                  ${timeframe === tf \n                    ? 'bg-gradient-to-r from-orange-500 to-yellow-500 text-black' \n                    : 'bg-white/10 text-gray-300 hover:bg-white/20'\n                  }\n                `}\n              >\n                {tf}\n              </button>\n            ))}\n          </div>\n        </div>\n\n        <div className=\"h-64\">\n          <ResponsiveContainer width=\"100%\" height=\"100%\">\n            <LineChart data={marketData?.chartData || []}>\n              <CartesianGrid strokeDasharray=\"3 3\" stroke=\"#374151\" />\n              <XAxis dataKey=\"time\" stroke=\"#9CA3AF\" />\n              <YAxis stroke=\"#9CA3AF\" />\n              <Tooltip \n                contentStyle={{ \n                  backgroundColor: 'rgba(0, 0, 0, 0.8)', \n                  border: '1px solid rgba(255, 255, 255, 0.1)',\n                  borderRadius: '8px'\n                }}\n              />\n              <Line \n                type=\"monotone\" \n                dataKey=\"price\" \n                stroke=\"#F59E0B\" \n                strokeWidth={2}\n                dot={false}\n              />\n            </LineChart>\n          </ResponsiveContainer>\n        </div>\n      </motion.div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAUA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;;;AAhBA;;;;;;;AAsBO,SAAS,aAAa,EAAE,IAAI,EAAqB;;IACtD,MAAM,EACJ,MAAM,EACN,SAAS,EACT,UAAU,EACV,SAAS,EACT,KAAK,EACL,WAAW,EACZ,GAAG,CAAA,GAAA,gIAAA,CAAA,gBAAa,AAAD;IAEhB,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,MAAM,aAAa;QACjB;YAAE,IAAI;YAAW,QAAQ;YAAO,MAAM;YAAW,OAAO;QAAkB;QAC1E;YAAE,IAAI;YAAY,QAAQ;YAAO,MAAM;YAAY,OAAO;QAAgB;QAC1E;YAAE,IAAI;YAAe,QAAQ;YAAO,MAAM;YAAO,OAAO;QAAkB;QAC1E;YAAE,IAAI;YAAW,QAAQ;YAAO,MAAM;YAAW,OAAO;QAAgB;QACxE;YAAE,IAAI;YAAU,QAAQ;YAAO,MAAM;YAAU,OAAO;QAAkB;KACzE;IAED,IAAI,WAAW;QACb,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,6IAAA,CAAA,iBAAc;;;;;kCACf,6LAAC;wBAAK,WAAU;kCAAkB;;;;;;;;;;;;;;;;;IAI1C;IAEA,IAAI,OAAO;QACT,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,2NAAA,CAAA,gBAAa;wBAAC,WAAU;;;;;;kCACzB,6LAAC;wBAAG,WAAU;kCAAwC;;;;;;kCACtD,6LAAC;wBAAE,WAAU;kCAAsB;;;;;;kCACnC,6LAAC;wBACC,SAAS;wBACT,WAAU;kCACX;;;;;;;;;;;;;;;;;IAMT;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAC5B,WAAU;;0CAEV,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;;0DACC,6LAAC;gDAAE,WAAU;0DAAwB;;;;;;0DACrC,6LAAC;gDAAE,WAAU;;oDAAgC;oDACzC,WAAW,OAAO,oBAAoB;;;;;;;;;;;;;kDAG5C,6LAAC,qNAAA,CAAA,aAAU;wCAAC,WAAU;;;;;;;;;;;;0CAExB,6LAAC;gCAAI,WAAU;;oCACZ,CAAC,WAAW,UAAU,CAAC,KAAK,kBAC3B,6LAAC,qNAAA,CAAA,aAAU;wCAAC,WAAU;;;;;6DAEtB,6LAAC,yNAAA,CAAA,eAAY;wCAAC,WAAU;;;;;;kDAE1B,6LAAC;wCAAK,WAAW,CAAC,QAAQ,EAAE,CAAC,WAAW,UAAU,CAAC,KAAK,IAAI,mBAAmB,gBAAgB;;4CAC5F,WAAW,QAAQ,QAAQ,MAAM;4CAAI;;;;;;;;;;;;;;;;;;;kCAK5C,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAC5B,YAAY;4BAAE,OAAO;wBAAI;wBACzB,WAAU;;0CAEV,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;;0DACC,6LAAC;gDAAE,WAAU;0DAAwB;;;;;;0DACrC,6LAAC;gDAAE,WAAU;0DAAiC,MAAM,UAAU;;;;;;;;;;;;kDAEhE,6LAAC,2NAAA,CAAA,gBAAa;wCAAC,WAAU;;;;;;;;;;;;0CAE3B,6LAAC;gCAAE,WAAU;0CAA6B;;;;;;;;;;;;kCAG5C,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAC5B,YAAY;4BAAE,OAAO;wBAAI;wBACzB,WAAU;;0CAEV,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;;0DACC,6LAAC;gDAAE,WAAU;0DAAwB;;;;;;0DACrC,6LAAC;gDAAE,WAAU;0DAAmC;;;;;;;;;;;;kDAElD,6LAAC,6MAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;;;;;;;0CAEtB,6LAAC;gCAAE,WAAU;0CAA6B;;;;;;;;;;;;;;;;;;0BAK9C,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAG;gBAC7B,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAE;gBAC5B,YAAY;oBAAE,OAAO;gBAAI;gBACzB,WAAU;;kCAEV,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAAmC;;;;;;0CACjD,6LAAC;gCACC,SAAS;gCACT,WAAU;0CAEV,cAAA,6LAAC,mMAAA,CAAA,MAAG;oCAAC,WAAU;;;;;;;;;;;;;;;;;kCAInB,6LAAC;wBAAI,WAAU;kCACZ,WAAW,GAAG,CAAC,CAAC,QAAQ;4BACvB,MAAM,YAAY,QAAQ,KAAK,CAAA,IAAK,EAAE,EAAE,KAAK,OAAO,EAAE;4BACtD,MAAM,QAAQ,WAAW,iBAAiB;4BAC1C,MAAM,SAAS,WAAW,+BAA+B;4BAEzD,qBACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gCAET,SAAS;oCAAE,SAAS;oCAAG,GAAG,CAAC;gCAAG;gCAC9B,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAC5B,YAAY;oCAAE,OAAO,QAAQ;gCAAI;gCACjC,SAAS,IAAM,kBAAkB,OAAO,EAAE;gCAC1C,WAAW,CAAC;;kBAEV,EAAE,mBAAmB,OAAO,EAAE,GAC1B,gCACA,+CACH;gBACH,CAAC;0CAED,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC;wDAAK,WAAU;kEAAgC,OAAO,MAAM;;;;;;;;;;;8DAE/D,6LAAC;;sEACC,6LAAC;4DAAE,WAAU;sEAA4B,OAAO,IAAI;;;;;;sEACpD,6LAAC;4DAAE,WAAU;sEAAyB,OAAO,MAAM;;;;;;;;;;;;;;;;;;sDAIvD,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAE,WAAU;;wDAA2B;wDACpC,MAAM,cAAc;;;;;;;8DAExB,6LAAC;oDAAI,WAAU;;wDACZ,UAAU,kBACT,6LAAC,qNAAA,CAAA,aAAU;4DAAC,WAAU;;;;;iFAEtB,6LAAC,yNAAA,CAAA,eAAY;4DAAC,WAAU;;;;;;sEAE1B,6LAAC;4DAAK,WAAW,CAAC,QAAQ,EAAE,UAAU,IAAI,mBAAmB,gBAAgB;;gEAC1E,OAAO,OAAO,CAAC;gEAAG;;;;;;;;;;;;;;;;;;;;;;;;;+BAnCtB,OAAO,EAAE;;;;;wBA0CpB;;;;;;;;;;;;0BAKJ,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAG;gBAC7B,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAE;gBAC5B,YAAY;oBAAE,OAAO;gBAAI;gBACzB,WAAU;;kCAEV,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;;oCACX,WAAW,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,iBAAiB;oCAAK;;;;;;;0CAGvD,6LAAC;gCAAI,WAAU;0CACZ;oCAAC;oCAAM;oCAAO;oCAAM;iCAAM,CAAC,GAAG,CAAC,CAAC,mBAC/B,6LAAC;wCAEC,SAAS,IAAM,aAAa;wCAC5B,WAAW,CAAC;;kBAEV,EAAE,cAAc,KACZ,8DACA,8CACH;gBACH,CAAC;kDAEA;uCAVI;;;;;;;;;;;;;;;;kCAgBb,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC,sKAAA,CAAA,sBAAmB;4BAAC,OAAM;4BAAO,QAAO;sCACvC,cAAA,6LAAC,wJAAA,CAAA,YAAS;gCAAC,MAAM,YAAY,aAAa,EAAE;;kDAC1C,6LAAC,gKAAA,CAAA,gBAAa;wCAAC,iBAAgB;wCAAM,QAAO;;;;;;kDAC5C,6LAAC,wJAAA,CAAA,QAAK;wCAAC,SAAQ;wCAAO,QAAO;;;;;;kDAC7B,6LAAC,wJAAA,CAAA,QAAK;wCAAC,QAAO;;;;;;kDACd,6LAAC,0JAAA,CAAA,UAAO;wCACN,cAAc;4CACZ,iBAAiB;4CACjB,QAAQ;4CACR,cAAc;wCAChB;;;;;;kDAEF,6LAAC,uJAAA,CAAA,OAAI;wCACH,MAAK;wCACL,SAAQ;wCACR,QAAO;wCACP,aAAa;wCACb,KAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQrB;GAjPgB;;QAQV,gIAAA,CAAA,gBAAa;;;KARH", "debugId": null}}, {"offset": {"line": 1330, "column": 0}, "map": {"version": 3, "sources": ["file:///F:/AI%20chain/chainsight-production/src/components/modules/FinanceModule.tsx"], "sourcesContent": ["'use client';\n\nimport { motion } from 'framer-motion';\nimport { TrendingUp, DollarSign, BarChart3, Pie<PERSON>hart } from 'lucide-react';\n\nexport function FinanceModule() {\n  return (\n    <motion.div\n      initial={{ opacity: 0, y: 20 }}\n      animate={{ opacity: 1, y: 0 }}\n      className=\"space-y-6\"\n    >\n      <div className=\"bg-white/10 backdrop-blur-xl rounded-2xl p-6 border border-white/20\">\n        <div className=\"flex items-center space-x-3 mb-6\">\n          <div className=\"w-12 h-12 bg-gradient-to-r from-green-400 to-blue-500 rounded-xl flex items-center justify-center\">\n            <TrendingUp className=\"w-6 h-6 text-white\" />\n          </div>\n          <div>\n            <h2 className=\"text-2xl font-bold text-white\">AI Finance</h2>\n            <p className=\"text-gray-400\">Advanced financial analysis and insights</p>\n          </div>\n        </div>\n\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\">\n          <div className=\"bg-black/20 rounded-xl p-4\">\n            <div className=\"flex items-center space-x-3 mb-3\">\n              <DollarSign className=\"w-5 h-5 text-green-400\" />\n              <span className=\"text-white font-medium\">Portfolio Value</span>\n            </div>\n            <p className=\"text-2xl font-bold text-green-400\">$125,430</p>\n            <p className=\"text-sm text-gray-400\">+12.5% this month</p>\n          </div>\n\n          <div className=\"bg-black/20 rounded-xl p-4\">\n            <div className=\"flex items-center space-x-3 mb-3\">\n              <BarChart3 className=\"w-5 h-5 text-blue-400\" />\n              <span className=\"text-white font-medium\">Market Analysis</span>\n            </div>\n            <p className=\"text-2xl font-bold text-blue-400\">Bullish</p>\n            <p className=\"text-sm text-gray-400\">Strong upward trend</p>\n          </div>\n\n          <div className=\"bg-black/20 rounded-xl p-4\">\n            <div className=\"flex items-center space-x-3 mb-3\">\n              <PieChart className=\"w-5 h-5 text-purple-400\" />\n              <span className=\"text-white font-medium\">Risk Score</span>\n            </div>\n            <p className=\"text-2xl font-bold text-purple-400\">7.2/10</p>\n            <p className=\"text-sm text-gray-400\">Moderate risk</p>\n          </div>\n        </div>\n\n        <div className=\"mt-6 p-4 bg-gradient-to-r from-green-500/20 to-blue-500/20 rounded-xl border border-green-500/30\">\n          <h3 className=\"text-white font-semibold mb-2\">AI Recommendation</h3>\n          <p className=\"text-gray-300 text-sm\">\n            Based on current market conditions and your portfolio performance, consider diversifying \n            into emerging tech stocks and reducing exposure to volatile assets.\n          </p>\n        </div>\n      </div>\n    </motion.div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAHA;;;;AAKO,SAAS;IACd,qBACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;QACT,SAAS;YAAE,SAAS;YAAG,GAAG;QAAG;QAC7B,SAAS;YAAE,SAAS;YAAG,GAAG;QAAE;QAC5B,WAAU;kBAEV,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC,qNAAA,CAAA,aAAU;gCAAC,WAAU;;;;;;;;;;;sCAExB,6LAAC;;8CACC,6LAAC;oCAAG,WAAU;8CAAgC;;;;;;8CAC9C,6LAAC;oCAAE,WAAU;8CAAgB;;;;;;;;;;;;;;;;;;8BAIjC,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,qNAAA,CAAA,aAAU;4CAAC,WAAU;;;;;;sDACtB,6LAAC;4CAAK,WAAU;sDAAyB;;;;;;;;;;;;8CAE3C,6LAAC;oCAAE,WAAU;8CAAoC;;;;;;8CACjD,6LAAC;oCAAE,WAAU;8CAAwB;;;;;;;;;;;;sCAGvC,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,qNAAA,CAAA,YAAS;4CAAC,WAAU;;;;;;sDACrB,6LAAC;4CAAK,WAAU;sDAAyB;;;;;;;;;;;;8CAE3C,6LAAC;oCAAE,WAAU;8CAAmC;;;;;;8CAChD,6LAAC;oCAAE,WAAU;8CAAwB;;;;;;;;;;;;sCAGvC,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,iNAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;sDACpB,6LAAC;4CAAK,WAAU;sDAAyB;;;;;;;;;;;;8CAE3C,6LAAC;oCAAE,WAAU;8CAAqC;;;;;;8CAClD,6LAAC;oCAAE,WAAU;8CAAwB;;;;;;;;;;;;;;;;;;8BAIzC,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAAgC;;;;;;sCAC9C,6LAAC;4BAAE,WAAU;sCAAwB;;;;;;;;;;;;;;;;;;;;;;;AAQ/C;KAzDgB", "debugId": null}}, {"offset": {"line": 1609, "column": 0}, "map": {"version": 3, "sources": ["file:///F:/AI%20chain/chainsight-production/src/components/modules/LegalModule.tsx"], "sourcesContent": ["'use client';\n\nimport { motion } from 'framer-motion';\nimport { Shield, FileText, Users, AlertTriangle } from 'lucide-react';\n\nexport function LegalModule() {\n  return (\n    <motion.div\n      initial={{ opacity: 0, y: 20 }}\n      animate={{ opacity: 1, y: 0 }}\n      className=\"space-y-6\"\n    >\n      <div className=\"bg-white/10 backdrop-blur-xl rounded-2xl p-6 border border-white/20\">\n        <div className=\"flex items-center space-x-3 mb-6\">\n          <div className=\"w-12 h-12 bg-gradient-to-r from-blue-400 to-purple-500 rounded-xl flex items-center justify-center\">\n            <Shield className=\"w-6 h-6 text-white\" />\n          </div>\n          <div>\n            <h2 className=\"text-2xl font-bold text-white\">Legal & HR AI</h2>\n            <p className=\"text-gray-400\">Compliance and human resources management</p>\n          </div>\n        </div>\n\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\">\n          <div className=\"bg-black/20 rounded-xl p-4\">\n            <div className=\"flex items-center space-x-3 mb-3\">\n              <FileText className=\"w-5 h-5 text-blue-400\" />\n              <span className=\"text-white font-medium\">Documents</span>\n            </div>\n            <p className=\"text-2xl font-bold text-blue-400\">24</p>\n            <p className=\"text-sm text-gray-400\">Pending review</p>\n          </div>\n\n          <div className=\"bg-black/20 rounded-xl p-4\">\n            <div className=\"flex items-center space-x-3 mb-3\">\n              <Users className=\"w-5 h-5 text-green-400\" />\n              <span className=\"text-white font-medium\">Employees</span>\n            </div>\n            <p className=\"text-2xl font-bold text-green-400\">156</p>\n            <p className=\"text-sm text-gray-400\">Active staff</p>\n          </div>\n\n          <div className=\"bg-black/20 rounded-xl p-4\">\n            <div className=\"flex items-center space-x-3 mb-3\">\n              <AlertTriangle className=\"w-5 h-5 text-yellow-400\" />\n              <span className=\"text-white font-medium\">Compliance</span>\n            </div>\n            <p className=\"text-2xl font-bold text-yellow-400\">98%</p>\n            <p className=\"text-sm text-gray-400\">Compliance rate</p>\n          </div>\n        </div>\n\n        <div className=\"mt-6 p-4 bg-gradient-to-r from-blue-500/20 to-purple-500/20 rounded-xl border border-blue-500/30\">\n          <h3 className=\"text-white font-semibold mb-2\">Legal AI Analysis</h3>\n          <p className=\"text-gray-300 text-sm\">\n            All employment contracts are up to date. Recommend updating privacy policy \n            to comply with latest GDPR requirements. No critical compliance issues detected.\n          </p>\n        </div>\n      </div>\n    </motion.div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAHA;;;;AAKO,SAAS;IACd,qBACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;QACT,SAAS;YAAE,SAAS;YAAG,GAAG;QAAG;QAC7B,SAAS;YAAE,SAAS;YAAG,GAAG;QAAE;QAC5B,WAAU;kBAEV,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC,yMAAA,CAAA,SAAM;gCAAC,WAAU;;;;;;;;;;;sCAEpB,6LAAC;;8CACC,6LAAC;oCAAG,WAAU;8CAAgC;;;;;;8CAC9C,6LAAC;oCAAE,WAAU;8CAAgB;;;;;;;;;;;;;;;;;;8BAIjC,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,iNAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;sDACpB,6LAAC;4CAAK,WAAU;sDAAyB;;;;;;;;;;;;8CAE3C,6LAAC;oCAAE,WAAU;8CAAmC;;;;;;8CAChD,6LAAC;oCAAE,WAAU;8CAAwB;;;;;;;;;;;;sCAGvC,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,uMAAA,CAAA,QAAK;4CAAC,WAAU;;;;;;sDACjB,6LAAC;4CAAK,WAAU;sDAAyB;;;;;;;;;;;;8CAE3C,6LAAC;oCAAE,WAAU;8CAAoC;;;;;;8CACjD,6LAAC;oCAAE,WAAU;8CAAwB;;;;;;;;;;;;sCAGvC,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,2NAAA,CAAA,gBAAa;4CAAC,WAAU;;;;;;sDACzB,6LAAC;4CAAK,WAAU;sDAAyB;;;;;;;;;;;;8CAE3C,6LAAC;oCAAE,WAAU;8CAAqC;;;;;;8CAClD,6LAAC;oCAAE,WAAU;8CAAwB;;;;;;;;;;;;;;;;;;8BAIzC,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAAgC;;;;;;sCAC9C,6LAAC;4BAAE,WAAU;sCAAwB;;;;;;;;;;;;;;;;;;;;;;;AAQ/C;KAzDgB", "debugId": null}}, {"offset": {"line": 1888, "column": 0}, "map": {"version": 3, "sources": ["file:///F:/AI%20chain/chainsight-production/src/components/modules/DesignModule.tsx"], "sourcesContent": ["'use client';\n\nimport { motion } from 'framer-motion';\nimport { Palette, Image, Layers, Sparkles } from 'lucide-react';\n\nexport function DesignModule() {\n  return (\n    <motion.div\n      initial={{ opacity: 0, y: 20 }}\n      animate={{ opacity: 1, y: 0 }}\n      className=\"space-y-6\"\n    >\n      <div className=\"bg-white/10 backdrop-blur-xl rounded-2xl p-6 border border-white/20\">\n        <div className=\"flex items-center space-x-3 mb-6\">\n          <div className=\"w-12 h-12 bg-gradient-to-r from-pink-400 to-purple-500 rounded-xl flex items-center justify-center\">\n            <Palette className=\"w-6 h-6 text-white\" />\n          </div>\n          <div>\n            <h2 className=\"text-2xl font-bold text-white\">Design Studio</h2>\n            <p className=\"text-gray-400\">AI-powered creative design tools</p>\n          </div>\n        </div>\n\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\">\n          <div className=\"bg-black/20 rounded-xl p-4\">\n            <div className=\"flex items-center space-x-3 mb-3\">\n              <Image className=\"w-5 h-5 text-pink-400\" />\n              <span className=\"text-white font-medium\">Projects</span>\n            </div>\n            <p className=\"text-2xl font-bold text-pink-400\">12</p>\n            <p className=\"text-sm text-gray-400\">Active designs</p>\n          </div>\n\n          <div className=\"bg-black/20 rounded-xl p-4\">\n            <div className=\"flex items-center space-x-3 mb-3\">\n              <Layers className=\"w-5 h-5 text-purple-400\" />\n              <span className=\"text-white font-medium\">Templates</span>\n            </div>\n            <p className=\"text-2xl font-bold text-purple-400\">48</p>\n            <p className=\"text-sm text-gray-400\">Available templates</p>\n          </div>\n\n          <div className=\"bg-black/20 rounded-xl p-4\">\n            <div className=\"flex items-center space-x-3 mb-3\">\n              <Sparkles className=\"w-5 h-5 text-yellow-400\" />\n              <span className=\"text-white font-medium\">AI Generated</span>\n            </div>\n            <p className=\"text-2xl font-bold text-yellow-400\">156</p>\n            <p className=\"text-sm text-gray-400\">This month</p>\n          </div>\n        </div>\n\n        <div className=\"mt-6 p-4 bg-gradient-to-r from-pink-500/20 to-purple-500/20 rounded-xl border border-pink-500/30\">\n          <h3 className=\"text-white font-semibold mb-2\">Design AI Suggestion</h3>\n          <p className=\"text-gray-300 text-sm\">\n            Trending design patterns suggest incorporating more minimalist elements with \n            bold color accents. Consider using glass morphism effects for modern appeal.\n          </p>\n        </div>\n      </div>\n    </motion.div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAHA;;;;AAKO,SAAS;IACd,qBACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;QACT,SAAS;YAAE,SAAS;YAAG,GAAG;QAAG;QAC7B,SAAS;YAAE,SAAS;YAAG,GAAG;QAAE;QAC5B,WAAU;kBAEV,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC,2MAAA,CAAA,UAAO;gCAAC,WAAU;;;;;;;;;;;sCAErB,6LAAC;;8CACC,6LAAC;oCAAG,WAAU;8CAAgC;;;;;;8CAC9C,6LAAC;oCAAE,WAAU;8CAAgB;;;;;;;;;;;;;;;;;;8BAIjC,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,uMAAA,CAAA,QAAK;4CAAC,WAAU;;;;;;sDACjB,6LAAC;4CAAK,WAAU;sDAAyB;;;;;;;;;;;;8CAE3C,6LAAC;oCAAE,WAAU;8CAAmC;;;;;;8CAChD,6LAAC;oCAAE,WAAU;8CAAwB;;;;;;;;;;;;sCAGvC,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,yMAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;sDAClB,6LAAC;4CAAK,WAAU;sDAAyB;;;;;;;;;;;;8CAE3C,6LAAC;oCAAE,WAAU;8CAAqC;;;;;;8CAClD,6LAAC;oCAAE,WAAU;8CAAwB;;;;;;;;;;;;sCAGvC,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,6MAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;sDACpB,6LAAC;4CAAK,WAAU;sDAAyB;;;;;;;;;;;;8CAE3C,6LAAC;oCAAE,WAAU;8CAAqC;;;;;;8CAClD,6LAAC;oCAAE,WAAU;8CAAwB;;;;;;;;;;;;;;;;;;8BAIzC,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAAgC;;;;;;sCAC9C,6LAAC;4BAAE,WAAU;sCAAwB;;;;;;;;;;;;;;;;;;;;;;;AAQ/C;KAzDgB", "debugId": null}}, {"offset": {"line": 2167, "column": 0}, "map": {"version": 3, "sources": ["file:///F:/AI%20chain/chainsight-production/src/components/modules/FacialModule.tsx"], "sourcesContent": ["'use client';\n\nimport { motion } from 'framer-motion';\nimport { Camera, Eye, Shield, Users } from 'lucide-react';\n\nexport function FacialModule() {\n  return (\n    <motion.div\n      initial={{ opacity: 0, y: 20 }}\n      animate={{ opacity: 1, y: 0 }}\n      className=\"space-y-6\"\n    >\n      <div className=\"bg-white/10 backdrop-blur-xl rounded-2xl p-6 border border-white/20\">\n        <div className=\"flex items-center space-x-3 mb-6\">\n          <div className=\"w-12 h-12 bg-gradient-to-r from-cyan-400 to-blue-500 rounded-xl flex items-center justify-center\">\n            <Camera className=\"w-6 h-6 text-white\" />\n          </div>\n          <div>\n            <h2 className=\"text-2xl font-bold text-white\">Facial Recognition</h2>\n            <p className=\"text-gray-400\">Advanced biometric analysis and security</p>\n          </div>\n        </div>\n\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\">\n          <div className=\"bg-black/20 rounded-xl p-4\">\n            <div className=\"flex items-center space-x-3 mb-3\">\n              <Eye className=\"w-5 h-5 text-cyan-400\" />\n              <span className=\"text-white font-medium\">Accuracy</span>\n            </div>\n            <p className=\"text-2xl font-bold text-cyan-400\">99.7%</p>\n            <p className=\"text-sm text-gray-400\">Recognition rate</p>\n          </div>\n\n          <div className=\"bg-black/20 rounded-xl p-4\">\n            <div className=\"flex items-center space-x-3 mb-3\">\n              <Users className=\"w-5 h-5 text-blue-400\" />\n              <span className=\"text-white font-medium\">Profiles</span>\n            </div>\n            <p className=\"text-2xl font-bold text-blue-400\">2,847</p>\n            <p className=\"text-sm text-gray-400\">Registered faces</p>\n          </div>\n\n          <div className=\"bg-black/20 rounded-xl p-4\">\n            <div className=\"flex items-center space-x-3 mb-3\">\n              <Shield className=\"w-5 h-5 text-green-400\" />\n              <span className=\"text-white font-medium\">Security</span>\n            </div>\n            <p className=\"text-2xl font-bold text-green-400\">Active</p>\n            <p className=\"text-sm text-gray-400\">Real-time monitoring</p>\n          </div>\n        </div>\n\n        <div className=\"mt-6 p-4 bg-gradient-to-r from-cyan-500/20 to-blue-500/20 rounded-xl border border-cyan-500/30\">\n          <h3 className=\"text-white font-semibold mb-2\">Biometric Analysis</h3>\n          <p className=\"text-gray-300 text-sm\">\n            Facial recognition system is operating at optimal performance. All security \n            protocols are active. No unauthorized access attempts detected in the last 24 hours.\n          </p>\n        </div>\n      </div>\n    </motion.div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAHA;;;;AAKO,SAAS;IACd,qBACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;QACT,SAAS;YAAE,SAAS;YAAG,GAAG;QAAG;QAC7B,SAAS;YAAE,SAAS;YAAG,GAAG;QAAE;QAC5B,WAAU;kBAEV,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC,yMAAA,CAAA,SAAM;gCAAC,WAAU;;;;;;;;;;;sCAEpB,6LAAC;;8CACC,6LAAC;oCAAG,WAAU;8CAAgC;;;;;;8CAC9C,6LAAC;oCAAE,WAAU;8CAAgB;;;;;;;;;;;;;;;;;;8BAIjC,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,mMAAA,CAAA,MAAG;4CAAC,WAAU;;;;;;sDACf,6LAAC;4CAAK,WAAU;sDAAyB;;;;;;;;;;;;8CAE3C,6LAAC;oCAAE,WAAU;8CAAmC;;;;;;8CAChD,6LAAC;oCAAE,WAAU;8CAAwB;;;;;;;;;;;;sCAGvC,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,uMAAA,CAAA,QAAK;4CAAC,WAAU;;;;;;sDACjB,6LAAC;4CAAK,WAAU;sDAAyB;;;;;;;;;;;;8CAE3C,6LAAC;oCAAE,WAAU;8CAAmC;;;;;;8CAChD,6LAAC;oCAAE,WAAU;8CAAwB;;;;;;;;;;;;sCAGvC,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,yMAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;sDAClB,6LAAC;4CAAK,WAAU;sDAAyB;;;;;;;;;;;;8CAE3C,6LAAC;oCAAE,WAAU;8CAAoC;;;;;;8CACjD,6LAAC;oCAAE,WAAU;8CAAwB;;;;;;;;;;;;;;;;;;8BAIzC,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAAgC;;;;;;sCAC9C,6LAAC;4BAAE,WAAU;sCAAwB;;;;;;;;;;;;;;;;;;;;;;;AAQ/C;KAzDgB", "debugId": null}}, {"offset": {"line": 2446, "column": 0}, "map": {"version": 3, "sources": ["file:///F:/AI%20chain/chainsight-production/src/components/modules/SupportModule.tsx"], "sourcesContent": ["'use client';\n\nimport { motion } from 'framer-motion';\nimport { MessageCircle, Users, Clock, CheckCircle } from 'lucide-react';\n\nexport function SupportModule() {\n  return (\n    <motion.div\n      initial={{ opacity: 0, y: 20 }}\n      animate={{ opacity: 1, y: 0 }}\n      className=\"space-y-6\"\n    >\n      <div className=\"bg-white/10 backdrop-blur-xl rounded-2xl p-6 border border-white/20\">\n        <div className=\"flex items-center space-x-3 mb-6\">\n          <div className=\"w-12 h-12 bg-gradient-to-r from-orange-400 to-red-500 rounded-xl flex items-center justify-center\">\n            <MessageCircle className=\"w-6 h-6 text-white\" />\n          </div>\n          <div>\n            <h2 className=\"text-2xl font-bold text-white\">Customer Support</h2>\n            <p className=\"text-gray-400\">AI-powered customer service and support</p>\n          </div>\n        </div>\n\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\">\n          <div className=\"bg-black/20 rounded-xl p-4\">\n            <div className=\"flex items-center space-x-3 mb-3\">\n              <MessageCircle className=\"w-5 h-5 text-orange-400\" />\n              <span className=\"text-white font-medium\">Active Tickets</span>\n            </div>\n            <p className=\"text-2xl font-bold text-orange-400\">23</p>\n            <p className=\"text-sm text-gray-400\">Pending resolution</p>\n          </div>\n\n          <div className=\"bg-black/20 rounded-xl p-4\">\n            <div className=\"flex items-center space-x-3 mb-3\">\n              <Clock className=\"w-5 h-5 text-blue-400\" />\n              <span className=\"text-white font-medium\">Avg Response</span>\n            </div>\n            <p className=\"text-2xl font-bold text-blue-400\">2.3m</p>\n            <p className=\"text-sm text-gray-400\">Response time</p>\n          </div>\n\n          <div className=\"bg-black/20 rounded-xl p-4\">\n            <div className=\"flex items-center space-x-3 mb-3\">\n              <CheckCircle className=\"w-5 h-5 text-green-400\" />\n              <span className=\"text-white font-medium\">Satisfaction</span>\n            </div>\n            <p className=\"text-2xl font-bold text-green-400\">4.8/5</p>\n            <p className=\"text-sm text-gray-400\">Customer rating</p>\n          </div>\n        </div>\n\n        <div className=\"mt-6 p-4 bg-gradient-to-r from-orange-500/20 to-red-500/20 rounded-xl border border-orange-500/30\">\n          <h3 className=\"text-white font-semibold mb-2\">Support AI Insights</h3>\n          <p className=\"text-gray-300 text-sm\">\n            Most common issues: Account access (32%), Payment processing (28%), Feature requests (21%). \n            AI chatbot successfully resolved 78% of inquiries without human intervention.\n          </p>\n        </div>\n      </div>\n    </motion.div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAHA;;;;AAKO,SAAS;IACd,qBACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;QACT,SAAS;YAAE,SAAS;YAAG,GAAG;QAAG;QAC7B,SAAS;YAAE,SAAS;YAAG,GAAG;QAAE;QAC5B,WAAU;kBAEV,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC,2NAAA,CAAA,gBAAa;gCAAC,WAAU;;;;;;;;;;;sCAE3B,6LAAC;;8CACC,6LAAC;oCAAG,WAAU;8CAAgC;;;;;;8CAC9C,6LAAC;oCAAE,WAAU;8CAAgB;;;;;;;;;;;;;;;;;;8BAIjC,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,2NAAA,CAAA,gBAAa;4CAAC,WAAU;;;;;;sDACzB,6LAAC;4CAAK,WAAU;sDAAyB;;;;;;;;;;;;8CAE3C,6LAAC;oCAAE,WAAU;8CAAqC;;;;;;8CAClD,6LAAC;oCAAE,WAAU;8CAAwB;;;;;;;;;;;;sCAGvC,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,uMAAA,CAAA,QAAK;4CAAC,WAAU;;;;;;sDACjB,6LAAC;4CAAK,WAAU;sDAAyB;;;;;;;;;;;;8CAE3C,6LAAC;oCAAE,WAAU;8CAAmC;;;;;;8CAChD,6LAAC;oCAAE,WAAU;8CAAwB;;;;;;;;;;;;sCAGvC,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,8NAAA,CAAA,cAAW;4CAAC,WAAU;;;;;;sDACvB,6LAAC;4CAAK,WAAU;sDAAyB;;;;;;;;;;;;8CAE3C,6LAAC;oCAAE,WAAU;8CAAoC;;;;;;8CACjD,6LAAC;oCAAE,WAAU;8CAAwB;;;;;;;;;;;;;;;;;;8BAIzC,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAAgC;;;;;;sCAC9C,6LAAC;4BAAE,WAAU;sCAAwB;;;;;;;;;;;;;;;;;;;;;;;AAQ/C;KAzDgB", "debugId": null}}, {"offset": {"line": 2724, "column": 0}, "map": {"version": 3, "sources": ["file:///F:/AI%20chain/chainsight-production/src/components/modules/Web3Module.tsx"], "sourcesContent": ["'use client';\n\nimport { motion } from 'framer-motion';\nimport { Zap, Globe, Code, Layers } from 'lucide-react';\n\nexport function Web3Module() {\n  return (\n    <motion.div\n      initial={{ opacity: 0, y: 20 }}\n      animate={{ opacity: 1, y: 0 }}\n      className=\"space-y-6\"\n    >\n      <div className=\"bg-white/10 backdrop-blur-xl rounded-2xl p-6 border border-white/20\">\n        <div className=\"flex items-center space-x-3 mb-6\">\n          <div className=\"w-12 h-12 bg-gradient-to-r from-purple-400 to-pink-500 rounded-xl flex items-center justify-center\">\n            <Globe className=\"w-6 h-6 text-white\" />\n          </div>\n          <div>\n            <h2 className=\"text-2xl font-bold text-white\">Web3 Development</h2>\n            <p className=\"text-gray-400\">Blockchain and DeFi development tools</p>\n          </div>\n        </div>\n\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\">\n          <div className=\"bg-black/20 rounded-xl p-4\">\n            <div className=\"flex items-center space-x-3 mb-3\">\n              <Code className=\"w-5 h-5 text-purple-400\" />\n              <span className=\"text-white font-medium\">Smart Contracts</span>\n            </div>\n            <p className=\"text-2xl font-bold text-purple-400\">8</p>\n            <p className=\"text-sm text-gray-400\">Deployed contracts</p>\n          </div>\n\n          <div className=\"bg-black/20 rounded-xl p-4\">\n            <div className=\"flex items-center space-x-3 mb-3\">\n              <Zap className=\"w-5 h-5 text-yellow-400\" />\n              <span className=\"text-white font-medium\">Gas Fees</span>\n            </div>\n            <p className=\"text-2xl font-bold text-yellow-400\">Optimal</p>\n            <p className=\"text-sm text-gray-400\">Current network</p>\n          </div>\n\n          <div className=\"bg-black/20 rounded-xl p-4\">\n            <div className=\"flex items-center space-x-3 mb-3\">\n              <Layers className=\"w-5 h-5 text-blue-400\" />\n              <span className=\"text-white font-medium\">DApps</span>\n            </div>\n            <p className=\"text-2xl font-bold text-blue-400\">5</p>\n            <p className=\"text-sm text-gray-400\">Active applications</p>\n          </div>\n        </div>\n\n        <div className=\"mt-6 p-4 bg-gradient-to-r from-purple-500/20 to-pink-500/20 rounded-xl border border-purple-500/30\">\n          <h3 className=\"text-white font-semibold mb-2\">Web3 AI Analysis</h3>\n          <p className=\"text-gray-300 text-sm\">\n            Ethereum network congestion is low. Optimal time for contract deployment. \n            Consider implementing Layer 2 solutions for reduced transaction costs.\n          </p>\n        </div>\n      </div>\n    </motion.div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAHA;;;;AAKO,SAAS;IACd,qBACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;QACT,SAAS;YAAE,SAAS;YAAG,GAAG;QAAG;QAC7B,SAAS;YAAE,SAAS;YAAG,GAAG;QAAE;QAC5B,WAAU;kBAEV,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC,uMAAA,CAAA,QAAK;gCAAC,WAAU;;;;;;;;;;;sCAEnB,6LAAC;;8CACC,6LAAC;oCAAG,WAAU;8CAAgC;;;;;;8CAC9C,6LAAC;oCAAE,WAAU;8CAAgB;;;;;;;;;;;;;;;;;;8BAIjC,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,qMAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;sDAChB,6LAAC;4CAAK,WAAU;sDAAyB;;;;;;;;;;;;8CAE3C,6LAAC;oCAAE,WAAU;8CAAqC;;;;;;8CAClD,6LAAC;oCAAE,WAAU;8CAAwB;;;;;;;;;;;;sCAGvC,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,mMAAA,CAAA,MAAG;4CAAC,WAAU;;;;;;sDACf,6LAAC;4CAAK,WAAU;sDAAyB;;;;;;;;;;;;8CAE3C,6LAAC;oCAAE,WAAU;8CAAqC;;;;;;8CAClD,6LAAC;oCAAE,WAAU;8CAAwB;;;;;;;;;;;;sCAGvC,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,yMAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;sDAClB,6LAAC;4CAAK,WAAU;sDAAyB;;;;;;;;;;;;8CAE3C,6LAAC;oCAAE,WAAU;8CAAmC;;;;;;8CAChD,6LAAC;oCAAE,WAAU;8CAAwB;;;;;;;;;;;;;;;;;;8BAIzC,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAAgC;;;;;;sCAC9C,6LAAC;4BAAE,WAAU;sCAAwB;;;;;;;;;;;;;;;;;;;;;;;AAQ/C;KAzDgB", "debugId": null}}, {"offset": {"line": 3003, "column": 0}, "map": {"version": 3, "sources": ["file:///F:/AI%20chain/chainsight-production/src/hooks/useOpenAI.ts"], "sourcesContent": ["'use client';\n\nimport { useState, useCallback, useEffect } from 'react';\nimport OpenAI from 'openai';\n\ninterface OpenAIConfig {\n  apiKey: string;\n  model: string;\n  maxTokens: number;\n  temperature: number;\n}\n\nexport function useOpenAI() {\n  const [isConnected, setIsConnected] = useState(false);\n  const [isLoading, setIsLoading] = useState(false);\n  const [error, setError] = useState<string | null>(null);\n  const [openai, setOpenai] = useState<OpenAI | null>(null);\n\n  useEffect(() => {\n    // Initialize OpenAI client\n    const initializeOpenAI = () => {\n      try {\n        const apiKey = process.env.NEXT_PUBLIC_OPENAI_API_KEY;\n        \n        if (!apiKey) {\n          setError('OpenAI API key not configured');\n          setIsConnected(false);\n          return;\n        }\n\n        const client = new OpenAI({\n          apiKey: apiKey,\n          dangerouslyAllowBrowser: true // Note: In production, API calls should go through your backend\n        });\n\n        setOpenai(client);\n        setIsConnected(true);\n        setError(null);\n        \n        console.log('✅ OpenAI client initialized successfully');\n      } catch (err) {\n        console.error('❌ Failed to initialize OpenAI:', err);\n        setError('Failed to initialize OpenAI client');\n        setIsConnected(false);\n      }\n    };\n\n    initializeOpenAI();\n  }, []);\n\n  const sendMessage = useCallback(async (\n    message: string, \n    systemPrompt?: string,\n    config?: Partial<OpenAIConfig>\n  ): Promise<string> => {\n    if (!openai) {\n      throw new Error('OpenAI client not initialized');\n    }\n\n    if (!isConnected) {\n      throw new Error('OpenAI client not connected');\n    }\n\n    setIsLoading(true);\n    setError(null);\n\n    try {\n      const defaultConfig: OpenAIConfig = {\n        apiKey: process.env.NEXT_PUBLIC_OPENAI_API_KEY || '',\n        model: 'gpt-4',\n        maxTokens: 1000,\n        temperature: 0.7\n      };\n\n      const finalConfig = { ...defaultConfig, ...config };\n\n      const messages: OpenAI.Chat.Completions.ChatCompletionMessageParam[] = [\n        {\n          role: 'system',\n          content: systemPrompt || 'You are Connectouch, a helpful AI assistant for the Chainsight platform. Provide professional, accurate, and concise responses.'\n        },\n        {\n          role: 'user',\n          content: message\n        }\n      ];\n\n      const completion = await openai.chat.completions.create({\n        model: finalConfig.model,\n        messages: messages,\n        max_tokens: finalConfig.maxTokens,\n        temperature: finalConfig.temperature,\n        stream: false\n      });\n\n      const response = completion.choices[0]?.message?.content;\n      \n      if (!response) {\n        throw new Error('No response received from OpenAI');\n      }\n\n      return response;\n\n    } catch (err: any) {\n      console.error('OpenAI API Error:', err);\n      \n      // Handle specific OpenAI errors\n      if (err.status === 401) {\n        setError('Invalid API key');\n        setIsConnected(false);\n      } else if (err.status === 429) {\n        setError('Rate limit exceeded');\n      } else if (err.status === 500) {\n        setError('OpenAI server error');\n      } else {\n        setError(err.message || 'Unknown error occurred');\n      }\n      \n      throw err;\n    } finally {\n      setIsLoading(false);\n    }\n  }, [openai, isConnected]);\n\n  const sendStreamMessage = useCallback(async (\n    message: string,\n    systemPrompt?: string,\n    onChunk?: (chunk: string) => void,\n    config?: Partial<OpenAIConfig>\n  ): Promise<string> => {\n    if (!openai) {\n      throw new Error('OpenAI client not initialized');\n    }\n\n    if (!isConnected) {\n      throw new Error('OpenAI client not connected');\n    }\n\n    setIsLoading(true);\n    setError(null);\n\n    try {\n      const defaultConfig: OpenAIConfig = {\n        apiKey: process.env.NEXT_PUBLIC_OPENAI_API_KEY || '',\n        model: 'gpt-4',\n        maxTokens: 1000,\n        temperature: 0.7\n      };\n\n      const finalConfig = { ...defaultConfig, ...config };\n\n      const messages: OpenAI.Chat.Completions.ChatCompletionMessageParam[] = [\n        {\n          role: 'system',\n          content: systemPrompt || 'You are Connectouch, a helpful AI assistant for the Chainsight platform. Provide professional, accurate, and concise responses.'\n        },\n        {\n          role: 'user',\n          content: message\n        }\n      ];\n\n      const stream = await openai.chat.completions.create({\n        model: finalConfig.model,\n        messages: messages,\n        max_tokens: finalConfig.maxTokens,\n        temperature: finalConfig.temperature,\n        stream: true\n      });\n\n      let fullResponse = '';\n\n      for await (const chunk of stream) {\n        const content = chunk.choices[0]?.delta?.content || '';\n        if (content) {\n          fullResponse += content;\n          onChunk?.(content);\n        }\n      }\n\n      return fullResponse;\n\n    } catch (err: any) {\n      console.error('OpenAI Streaming Error:', err);\n      setError(err.message || 'Streaming error occurred');\n      throw err;\n    } finally {\n      setIsLoading(false);\n    }\n  }, [openai, isConnected]);\n\n  const testConnection = useCallback(async (): Promise<boolean> => {\n    try {\n      await sendMessage('Hello', 'Respond with just \"Hello\" to test the connection.');\n      return true;\n    } catch (err) {\n      console.error('Connection test failed:', err);\n      return false;\n    }\n  }, [sendMessage]);\n\n  return {\n    sendMessage,\n    sendStreamMessage,\n    testConnection,\n    isConnected,\n    isLoading,\n    error\n  };\n}\n"], "names": [], "mappings": ";;;AAsBuB;AApBvB;AACA;AAAA;;AAHA;;;AAYO,SAAS;;IACd,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAClD,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAEpD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;+BAAE;YACR,2BAA2B;YAC3B,MAAM;wDAAmB;oBACvB,IAAI;wBACF,MAAM;wBAEN,uCAAa;;wBAIb;wBAEA,MAAM,SAAS,IAAI,yKAAA,CAAA,UAAM,CAAC;4BACxB,QAAQ;4BACR,yBAAyB,KAAK,gEAAgE;wBAChG;wBAEA,UAAU;wBACV,eAAe;wBACf,SAAS;wBAET,QAAQ,GAAG,CAAC;oBACd,EAAE,OAAO,KAAK;wBACZ,QAAQ,KAAK,CAAC,kCAAkC;wBAChD,SAAS;wBACT,eAAe;oBACjB;gBACF;;YAEA;QACF;8BAAG,EAAE;IAEL,MAAM,cAAc,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;8CAAE,OAC9B,SACA,cACA;YAEA,IAAI,CAAC,QAAQ;gBACX,MAAM,IAAI,MAAM;YAClB;YAEA,IAAI,CAAC,aAAa;gBAChB,MAAM,IAAI,MAAM;YAClB;YAEA,aAAa;YACb,SAAS;YAET,IAAI;gBACF,MAAM,gBAA8B;oBAClC,QAAQ,4MAA0C;oBAClD,OAAO;oBACP,WAAW;oBACX,aAAa;gBACf;gBAEA,MAAM,cAAc;oBAAE,GAAG,aAAa;oBAAE,GAAG,MAAM;gBAAC;gBAElD,MAAM,WAAiE;oBACrE;wBACE,MAAM;wBACN,SAAS,gBAAgB;oBAC3B;oBACA;wBACE,MAAM;wBACN,SAAS;oBACX;iBACD;gBAED,MAAM,aAAa,MAAM,OAAO,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC;oBACtD,OAAO,YAAY,KAAK;oBACxB,UAAU;oBACV,YAAY,YAAY,SAAS;oBACjC,aAAa,YAAY,WAAW;oBACpC,QAAQ;gBACV;gBAEA,MAAM,WAAW,WAAW,OAAO,CAAC,EAAE,EAAE,SAAS;gBAEjD,IAAI,CAAC,UAAU;oBACb,MAAM,IAAI,MAAM;gBAClB;gBAEA,OAAO;YAET,EAAE,OAAO,KAAU;gBACjB,QAAQ,KAAK,CAAC,qBAAqB;gBAEnC,gCAAgC;gBAChC,IAAI,IAAI,MAAM,KAAK,KAAK;oBACtB,SAAS;oBACT,eAAe;gBACjB,OAAO,IAAI,IAAI,MAAM,KAAK,KAAK;oBAC7B,SAAS;gBACX,OAAO,IAAI,IAAI,MAAM,KAAK,KAAK;oBAC7B,SAAS;gBACX,OAAO;oBACL,SAAS,IAAI,OAAO,IAAI;gBAC1B;gBAEA,MAAM;YACR,SAAU;gBACR,aAAa;YACf;QACF;6CAAG;QAAC;QAAQ;KAAY;IAExB,MAAM,oBAAoB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;oDAAE,OACpC,SACA,cACA,SACA;YAEA,IAAI,CAAC,QAAQ;gBACX,MAAM,IAAI,MAAM;YAClB;YAEA,IAAI,CAAC,aAAa;gBAChB,MAAM,IAAI,MAAM;YAClB;YAEA,aAAa;YACb,SAAS;YAET,IAAI;gBACF,MAAM,gBAA8B;oBAClC,QAAQ,4MAA0C;oBAClD,OAAO;oBACP,WAAW;oBACX,aAAa;gBACf;gBAEA,MAAM,cAAc;oBAAE,GAAG,aAAa;oBAAE,GAAG,MAAM;gBAAC;gBAElD,MAAM,WAAiE;oBACrE;wBACE,MAAM;wBACN,SAAS,gBAAgB;oBAC3B;oBACA;wBACE,MAAM;wBACN,SAAS;oBACX;iBACD;gBAED,MAAM,SAAS,MAAM,OAAO,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC;oBAClD,OAAO,YAAY,KAAK;oBACxB,UAAU;oBACV,YAAY,YAAY,SAAS;oBACjC,aAAa,YAAY,WAAW;oBACpC,QAAQ;gBACV;gBAEA,IAAI,eAAe;gBAEnB,WAAW,MAAM,SAAS,OAAQ;oBAChC,MAAM,UAAU,MAAM,OAAO,CAAC,EAAE,EAAE,OAAO,WAAW;oBACpD,IAAI,SAAS;wBACX,gBAAgB;wBAChB,UAAU;oBACZ;gBACF;gBAEA,OAAO;YAET,EAAE,OAAO,KAAU;gBACjB,QAAQ,KAAK,CAAC,2BAA2B;gBACzC,SAAS,IAAI,OAAO,IAAI;gBACxB,MAAM;YACR,SAAU;gBACR,aAAa;YACf;QACF;mDAAG;QAAC;QAAQ;KAAY;IAExB,MAAM,iBAAiB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;iDAAE;YACjC,IAAI;gBACF,MAAM,YAAY,SAAS;gBAC3B,OAAO;YACT,EAAE,OAAO,KAAK;gBACZ,QAAQ,KAAK,CAAC,2BAA2B;gBACzC,OAAO;YACT;QACF;gDAAG;QAAC;KAAY;IAEhB,OAAO;QACL;QACA;QACA;QACA;QACA;QACA;IACF;AACF;GArMgB", "debugId": null}}, {"offset": {"line": 3204, "column": 0}, "map": {"version": 3, "sources": ["file:///F:/AI%20chain/chainsight-production/src/components/AIChat.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useRef, useEffect } from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport { Send, Bot, User, Loader2, Zap } from 'lucide-react';\nimport { useOpenAI } from '@/hooks/useOpenAI';\n\ninterface Message {\n  id: string;\n  content: string;\n  sender: 'user' | 'ai';\n  timestamp: Date;\n  module?: string;\n}\n\ninterface AIChatProps {\n  module?: string;\n  context?: any;\n  className?: string;\n}\n\nexport function AIChat({ module = 'general', context, className = '' }: AIChatProps) {\n  const [messages, setMessages] = useState<Message[]>([\n    {\n      id: '1',\n      content: `Hello! I'm Connectouch, your Chainsight AI assistant specialized in ${module}. I can provide real-time analysis, insights, and assistance. What would you like to explore today?`,\n      sender: 'ai',\n      timestamp: new Date(),\n      module\n    },\n  ]);\n  const [input, setInput] = useState('');\n  const [isLoading, setIsLoading] = useState(false);\n  const messagesEndRef = useRef<HTMLDivElement>(null);\n  const { sendMessage, isConnected } = useOpenAI();\n\n  const scrollToBottom = () => {\n    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });\n  };\n\n  useEffect(() => {\n    scrollToBottom();\n  }, [messages]);\n\n  useEffect(() => {\n    // Update welcome message when module changes\n    if (module && messages.length === 1) {\n      setMessages([{\n        id: '1',\n        content: `Hello! I'm Connectouch, your Chainsight AI assistant specialized in ${module}. I can provide real-time analysis, insights, and assistance. What would you like to explore today?`,\n        sender: 'ai',\n        timestamp: new Date(),\n        module\n      }]);\n    }\n  }, [module]);\n\n  const handleSend = async () => {\n    if (!input.trim() || isLoading) return;\n\n    const userMessage: Message = {\n      id: Date.now().toString(),\n      content: input,\n      sender: 'user',\n      timestamp: new Date(),\n      module\n    };\n\n    setMessages(prev => [...prev, userMessage]);\n    const currentInput = input;\n    setInput('');\n    setIsLoading(true);\n\n    try {\n      // Create context-aware prompt\n      const systemPrompt = createSystemPrompt(module, context);\n      const response = await sendMessage(currentInput, systemPrompt);\n\n      const aiResponse: Message = {\n        id: (Date.now() + 1).toString(),\n        content: response,\n        sender: 'ai',\n        timestamp: new Date(),\n        module\n      };\n\n      setMessages(prev => [...prev, aiResponse]);\n    } catch (error) {\n      console.error('AI response error:', error);\n\n      // Fallback to contextual response\n      const aiResponse: Message = {\n        id: (Date.now() + 1).toString(),\n        content: generateContextualResponse(currentInput, module, context),\n        sender: 'ai',\n        timestamp: new Date(),\n        module\n      };\n\n      setMessages(prev => [...prev, aiResponse]);\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  const createSystemPrompt = (module: string, context: any): string => {\n    const basePrompt = \"You are Connectouch, an advanced AI assistant for the Chainsight platform. You provide professional, accurate, and helpful responses.\";\n\n    const modulePrompts = {\n      crypto: `${basePrompt} You specialize in cryptocurrency analysis, blockchain technology, and DeFi. Current market context: ${JSON.stringify(context)}`,\n      finance: `${basePrompt} You specialize in financial analysis, investment strategies, and market insights. Current data: ${JSON.stringify(context)}`,\n      legal: `${basePrompt} You specialize in legal document analysis, compliance, and HR management. Current context: ${JSON.stringify(context)}`,\n      design: `${basePrompt} You specialize in creative design, UI/UX, and visual content creation. Current context: ${JSON.stringify(context)}`,\n      facial: `${basePrompt} You specialize in facial recognition, biometric analysis, and computer vision. Current context: ${JSON.stringify(context)}`,\n      support: `${basePrompt} You specialize in customer support, ticket management, and user assistance. Current context: ${JSON.stringify(context)}`,\n      web3: `${basePrompt} You specialize in Web3 development, smart contracts, and DeFi protocols. Current context: ${JSON.stringify(context)}`,\n      general: `${basePrompt} You can assist with various topics across crypto, finance, legal, design, and technology.`\n    };\n\n    return modulePrompts[module as keyof typeof modulePrompts] || modulePrompts.general;\n  };\n\n  const generateContextualResponse = (userInput: string, module: string, context: any): string => {\n    const moduleResponses = {\n      crypto: [\n        `Based on current market data, I can see ${context?.portfolio?.total ? `your portfolio value of $${context.portfolio.total.toLocaleString()}` : 'interesting market trends'}. ${userInput.toLowerCase().includes('bitcoin') ? 'Bitcoin is showing strong momentum with institutional adoption.' : 'The crypto market is experiencing dynamic movements.'}`,\n        `Analyzing the blockchain data and current market sentiment... ${context?.alerts ? `You have ${context.alerts} active price alerts.` : 'Market volatility suggests a strategic approach.'} Would you like me to provide specific recommendations?`,\n        `The DeFi ecosystem is evolving rapidly. ${userInput.toLowerCase().includes('defi') ? 'Current yield farming opportunities show promising returns in established protocols.' : 'Smart contract interactions require careful gas optimization.'}`\n      ],\n      finance: [\n        `Market analysis indicates ${context?.analysis || 'mixed signals'}. ${userInput.toLowerCase().includes('stock') ? 'Equity markets are showing sector rotation patterns.' : 'Portfolio diversification remains crucial in current conditions.'}`,\n        `Financial modeling suggests ${context?.portfolio?.change ? `a ${context.portfolio.change}% portfolio adjustment` : 'strategic rebalancing'}. Risk management should be your priority.`,\n        `Based on economic indicators and ${context?.portfolio?.total ? `your $${context.portfolio.total.toLocaleString()} portfolio` : 'current market conditions'}, I recommend a balanced approach.`\n      ],\n      legal: [\n        `Document analysis complete. ${context?.documents ? `Found ${context.documents} documents requiring review.` : 'Legal compliance status appears current.'} ${userInput.toLowerCase().includes('contract') ? 'Contract terms show standard provisions with minor risk factors.' : 'Regulatory requirements are being met.'}`,\n        `Compliance status: ${context?.compliance || 'Under review'}. ${context?.contracts ? `${context.contracts} contracts need attention.` : 'All legal documents are properly structured.'}`\n      ],\n      design: [\n        `Creative analysis suggests ${userInput.toLowerCase().includes('logo') ? 'a modern, minimalist approach for your logo design.' : 'exploring contemporary design trends.'} ${context?.projects ? `Your ${context.projects} active projects` : 'Current design trends'} show promising directions.`,\n        `Design optimization complete. ${context?.templates ? `${context.templates} templates available` : 'Multiple design options'} for your consideration. Would you like me to generate variations?`\n      ],\n      facial: [\n        `Biometric analysis shows ${context?.accuracy ? `${context.accuracy}% accuracy` : 'high confidence levels'}. ${context?.recognitions ? `Processed ${context.recognitions} recognitions` : 'Facial recognition system is operating optimally'} with advanced security protocols.`,\n        `Computer vision processing complete. ${userInput.toLowerCase().includes('security') ? 'Security protocols are functioning within normal parameters.' : 'Facial detection algorithms are performing excellently.'}`\n      ],\n      support: [\n        `Support analysis indicates ${context?.tickets ? `${context.tickets} active tickets with ${context.resolved} resolved` : 'optimal support performance'}. Customer satisfaction: ${context?.satisfaction || '4.8'}/5.0.`,\n        `Ticket management system shows ${userInput.toLowerCase().includes('urgent') ? 'priority escalation protocols activated.' : 'normal processing times.'} How can I assist you further?`\n      ],\n      web3: [\n        `Web3 analysis shows ${context?.dapps ? `${context.dapps} active DApps` : 'strong blockchain activity'}. ${context?.gas === 'optimal' ? 'Gas fees are currently optimal for transactions.' : 'Network congestion may affect transaction costs.'}`,\n        `Smart contract deployment ready. ${context?.transactions ? `${context.transactions} transactions processed` : 'Blockchain network is stable'} with ${userInput.toLowerCase().includes('ethereum') ? 'Ethereum mainnet compatibility.' : 'multi-chain support.'}`\n      ]\n    };\n\n    const responses = moduleResponses[module as keyof typeof moduleResponses] || [\n      'I understand your question. Let me provide you with a comprehensive analysis based on the available data.',\n      'Based on current information and market conditions, I can offer several insights that might be helpful.',\n      'Your inquiry touches on important aspects. Let me break down the key points for you.'\n    ];\n\n    return responses[Math.floor(Math.random() * responses.length)];\n  };\n\n  const handleKeyPress = (e: React.KeyboardEvent) => {\n    if (e.key === 'Enter' && !e.shiftKey) {\n      e.preventDefault();\n      handleSend();\n    }\n  };\n\n  return (\n    <div className={`bg-black/20 backdrop-blur-xl border border-white/10 rounded-2xl overflow-hidden flex flex-col ${className}`}>\n      {/* Header */}\n      <div className=\"bg-black/30 border-b border-white/10 p-4\">\n        <div className=\"flex items-center space-x-3\">\n          <div className=\"w-10 h-10 bg-gradient-to-r from-yellow-400 to-orange-500 rounded-full flex items-center justify-center\">\n            <Bot className=\"w-5 h-5 text-black\" />\n          </div>\n          <div>\n            <h3 className=\"text-white font-semibold\">Connectouch AI</h3>\n            <p className=\"text-gray-400 text-sm capitalize\">{module} specialist</p>\n          </div>\n          <div className=\"ml-auto flex items-center space-x-2\">\n            <motion.div\n              animate={{ scale: [1, 1.2, 1] }}\n              transition={{ duration: 2, repeat: Infinity }}\n              className={`w-2 h-2 rounded-full ${isConnected ? 'bg-green-400' : 'bg-red-400'}`}\n            />\n            <span className={`text-xs ${isConnected ? 'text-green-400' : 'text-red-400'}`}>\n              {isConnected ? 'Connected' : 'Offline'}\n            </span>\n            {isConnected && <Zap className=\"w-3 h-3 text-yellow-400\" />}\n          </div>\n        </div>\n      </div>\n\n      {/* Messages */}\n      <div className=\"flex-1 overflow-y-auto p-4 space-y-4\">\n        <AnimatePresence>\n          {messages.map((message) => (\n            <motion.div\n              key={message.id}\n              initial={{ opacity: 0, y: 20 }}\n              animate={{ opacity: 1, y: 0 }}\n              exit={{ opacity: 0, y: -20 }}\n              className={`flex ${message.sender === 'user' ? 'justify-end' : 'justify-start'}`}\n            >\n              <div className={`flex items-start space-x-3 max-w-[85%] ${\n                message.sender === 'user' ? 'flex-row-reverse space-x-reverse' : ''\n              }`}>\n                <div className={`w-8 h-8 rounded-full flex items-center justify-center ${\n                  message.sender === 'user'\n                    ? 'bg-gradient-to-r from-blue-500 to-purple-500'\n                    : 'bg-gradient-to-r from-yellow-400 to-orange-500'\n                }`}>\n                  {message.sender === 'user' ? (\n                    <User className=\"w-4 h-4 text-white\" />\n                  ) : (\n                    <Bot className=\"w-4 h-4 text-black\" />\n                  )}\n                </div>\n\n                <div className={`rounded-xl p-4 ${\n                  message.sender === 'user'\n                    ? 'bg-gradient-to-r from-blue-500 to-purple-500 text-white'\n                    : 'bg-white/10 text-white border border-white/20'\n                }`}>\n                  <p className=\"text-sm leading-relaxed whitespace-pre-wrap\">{message.content}</p>\n                  <div className=\"flex items-center justify-between mt-3\">\n                    <p className={`text-xs ${\n                      message.sender === 'user' ? 'text-white/70' : 'text-gray-400'\n                    }`}>\n                      {message.timestamp.toLocaleTimeString()}\n                    </p>\n                    {message.module && (\n                      <span className={`text-xs px-2 py-1 rounded-full ${\n                        message.sender === 'user'\n                          ? 'bg-white/20 text-white/80'\n                          : 'bg-yellow-400/20 text-yellow-400'\n                      }`}>\n                        {message.module}\n                      </span>\n                    )}\n                  </div>\n                </div>\n              </div>\n            </motion.div>\n          ))}\n        </AnimatePresence>\n\n        {isLoading && (\n          <motion.div\n            initial={{ opacity: 0, y: 20 }}\n            animate={{ opacity: 1, y: 0 }}\n            className=\"flex justify-start\"\n          >\n            <div className=\"flex items-start space-x-3\">\n              <div className=\"w-8 h-8 bg-gradient-to-r from-yellow-400 to-orange-500 rounded-full flex items-center justify-center\">\n                <Bot className=\"w-4 h-4 text-black\" />\n              </div>\n              <div className=\"bg-white/10 border border-white/20 rounded-xl p-4\">\n                <div className=\"flex items-center space-x-2\">\n                  <Loader2 className=\"w-4 h-4 text-yellow-400 animate-spin\" />\n                  <span className=\"text-sm text-gray-300\">Connectouch is thinking...</span>\n                </div>\n              </div>\n            </div>\n          </motion.div>\n        )}\n\n        <div ref={messagesEndRef} />\n      </div>\n\n      {/* Input */}\n      <div className=\"border-t border-white/10 p-4\">\n        <div className=\"flex items-center space-x-3\">\n          <input\n            type=\"text\"\n            value={input}\n            onChange={(e) => setInput(e.target.value)}\n            onKeyPress={handleKeyPress}\n            placeholder={`Ask Connectouch about ${module}...`}\n            className=\"flex-1 bg-white/10 border border-white/20 rounded-xl px-4 py-3 text-white placeholder-gray-400 focus:outline-none focus:border-yellow-400 transition-colors duration-200\"\n            disabled={isLoading || !isConnected}\n          />\n          <button\n            onClick={handleSend}\n            disabled={!input.trim() || isLoading || !isConnected}\n            className=\"bg-gradient-to-r from-yellow-400 to-orange-500 text-black p-3 rounded-xl hover:shadow-lg disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200\"\n          >\n            <Send className=\"w-5 h-5\" />\n          </button>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;;;AALA;;;;;AAqBO,SAAS,OAAO,EAAE,SAAS,SAAS,EAAE,OAAO,EAAE,YAAY,EAAE,EAAe;;IACjF,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAa;QAClD;YACE,IAAI;YACJ,SAAS,CAAC,oEAAoE,EAAE,OAAO,mGAAmG,CAAC;YAC3L,QAAQ;YACR,WAAW,IAAI;YACf;QACF;KACD;IACD,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,iBAAiB,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAkB;IAC9C,MAAM,EAAE,WAAW,EAAE,WAAW,EAAE,GAAG,CAAA,GAAA,4HAAA,CAAA,YAAS,AAAD;IAE7C,MAAM,iBAAiB;QACrB,eAAe,OAAO,EAAE,eAAe;YAAE,UAAU;QAAS;IAC9D;IAEA,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;4BAAE;YACR;QACF;2BAAG;QAAC;KAAS;IAEb,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;4BAAE;YACR,6CAA6C;YAC7C,IAAI,UAAU,SAAS,MAAM,KAAK,GAAG;gBACnC,YAAY;oBAAC;wBACX,IAAI;wBACJ,SAAS,CAAC,oEAAoE,EAAE,OAAO,mGAAmG,CAAC;wBAC3L,QAAQ;wBACR,WAAW,IAAI;wBACf;oBACF;iBAAE;YACJ;QACF;2BAAG;QAAC;KAAO;IAEX,MAAM,aAAa;QACjB,IAAI,CAAC,MAAM,IAAI,MAAM,WAAW;QAEhC,MAAM,cAAuB;YAC3B,IAAI,KAAK,GAAG,GAAG,QAAQ;YACvB,SAAS;YACT,QAAQ;YACR,WAAW,IAAI;YACf;QACF;QAEA,YAAY,CAAA,OAAQ;mBAAI;gBAAM;aAAY;QAC1C,MAAM,eAAe;QACrB,SAAS;QACT,aAAa;QAEb,IAAI;YACF,8BAA8B;YAC9B,MAAM,eAAe,mBAAmB,QAAQ;YAChD,MAAM,WAAW,MAAM,YAAY,cAAc;YAEjD,MAAM,aAAsB;gBAC1B,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC,EAAE,QAAQ;gBAC7B,SAAS;gBACT,QAAQ;gBACR,WAAW,IAAI;gBACf;YACF;YAEA,YAAY,CAAA,OAAQ;uBAAI;oBAAM;iBAAW;QAC3C,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,sBAAsB;YAEpC,kCAAkC;YAClC,MAAM,aAAsB;gBAC1B,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC,EAAE,QAAQ;gBAC7B,SAAS,2BAA2B,cAAc,QAAQ;gBAC1D,QAAQ;gBACR,WAAW,IAAI;gBACf;YACF;YAEA,YAAY,CAAA,OAAQ;uBAAI;oBAAM;iBAAW;QAC3C,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,qBAAqB,CAAC,QAAgB;QAC1C,MAAM,aAAa;QAEnB,MAAM,gBAAgB;YACpB,QAAQ,GAAG,WAAW,qGAAqG,EAAE,KAAK,SAAS,CAAC,UAAU;YACtJ,SAAS,GAAG,WAAW,iGAAiG,EAAE,KAAK,SAAS,CAAC,UAAU;YACnJ,OAAO,GAAG,WAAW,4FAA4F,EAAE,KAAK,SAAS,CAAC,UAAU;YAC5I,QAAQ,GAAG,WAAW,yFAAyF,EAAE,KAAK,SAAS,CAAC,UAAU;YAC1I,QAAQ,GAAG,WAAW,iGAAiG,EAAE,KAAK,SAAS,CAAC,UAAU;YAClJ,SAAS,GAAG,WAAW,8FAA8F,EAAE,KAAK,SAAS,CAAC,UAAU;YAChJ,MAAM,GAAG,WAAW,2FAA2F,EAAE,KAAK,SAAS,CAAC,UAAU;YAC1I,SAAS,GAAG,WAAW,0FAA0F,CAAC;QACpH;QAEA,OAAO,aAAa,CAAC,OAAqC,IAAI,cAAc,OAAO;IACrF;IAEA,MAAM,6BAA6B,CAAC,WAAmB,QAAgB;QACrE,MAAM,kBAAkB;YACtB,QAAQ;gBACN,CAAC,wCAAwC,EAAE,SAAS,WAAW,QAAQ,CAAC,yBAAyB,EAAE,QAAQ,SAAS,CAAC,KAAK,CAAC,cAAc,IAAI,GAAG,4BAA4B,EAAE,EAAE,UAAU,WAAW,GAAG,QAAQ,CAAC,aAAa,oEAAoE,wDAAwD;gBAC1V,CAAC,8DAA8D,EAAE,SAAS,SAAS,CAAC,SAAS,EAAE,QAAQ,MAAM,CAAC,qBAAqB,CAAC,GAAG,mDAAmD,uDAAuD,CAAC;gBAClP,CAAC,wCAAwC,EAAE,UAAU,WAAW,GAAG,QAAQ,CAAC,UAAU,yFAAyF,iEAAiE;aACjP;YACD,SAAS;gBACP,CAAC,0BAA0B,EAAE,SAAS,YAAY,gBAAgB,EAAE,EAAE,UAAU,WAAW,GAAG,QAAQ,CAAC,WAAW,yDAAyD,oEAAoE;gBAC/O,CAAC,4BAA4B,EAAE,SAAS,WAAW,SAAS,CAAC,EAAE,EAAE,QAAQ,SAAS,CAAC,MAAM,CAAC,sBAAsB,CAAC,GAAG,wBAAwB,0CAA0C,CAAC;gBACvL,CAAC,iCAAiC,EAAE,SAAS,WAAW,QAAQ,CAAC,MAAM,EAAE,QAAQ,SAAS,CAAC,KAAK,CAAC,cAAc,GAAG,UAAU,CAAC,GAAG,4BAA4B,kCAAkC,CAAC;aAChM;YACD,OAAO;gBACL,CAAC,4BAA4B,EAAE,SAAS,YAAY,CAAC,MAAM,EAAE,QAAQ,SAAS,CAAC,4BAA4B,CAAC,GAAG,2CAA2C,CAAC,EAAE,UAAU,WAAW,GAAG,QAAQ,CAAC,cAAc,qEAAqE,0CAA0C;gBAC3T,CAAC,mBAAmB,EAAE,SAAS,cAAc,eAAe,EAAE,EAAE,SAAS,YAAY,GAAG,QAAQ,SAAS,CAAC,0BAA0B,CAAC,GAAG,gDAAgD;aACzL;YACD,QAAQ;gBACN,CAAC,2BAA2B,EAAE,UAAU,WAAW,GAAG,QAAQ,CAAC,UAAU,wDAAwD,wCAAwC,CAAC,EAAE,SAAS,WAAW,CAAC,KAAK,EAAE,QAAQ,QAAQ,CAAC,gBAAgB,CAAC,GAAG,wBAAwB,2BAA2B,CAAC;gBACjS,CAAC,8BAA8B,EAAE,SAAS,YAAY,GAAG,QAAQ,SAAS,CAAC,oBAAoB,CAAC,GAAG,0BAA0B,kEAAkE,CAAC;aACjM;YACD,QAAQ;gBACN,CAAC,yBAAyB,EAAE,SAAS,WAAW,GAAG,QAAQ,QAAQ,CAAC,UAAU,CAAC,GAAG,yBAAyB,EAAE,EAAE,SAAS,eAAe,CAAC,UAAU,EAAE,QAAQ,YAAY,CAAC,aAAa,CAAC,GAAG,mDAAmD,kCAAkC,CAAC;gBAChR,CAAC,qCAAqC,EAAE,UAAU,WAAW,GAAG,QAAQ,CAAC,cAAc,iEAAiE,2DAA2D;aACpN;YACD,SAAS;gBACP,CAAC,2BAA2B,EAAE,SAAS,UAAU,GAAG,QAAQ,OAAO,CAAC,qBAAqB,EAAE,QAAQ,QAAQ,CAAC,SAAS,CAAC,GAAG,8BAA8B,yBAAyB,EAAE,SAAS,gBAAgB,MAAM,KAAK,CAAC;gBACvN,CAAC,+BAA+B,EAAE,UAAU,WAAW,GAAG,QAAQ,CAAC,YAAY,6CAA6C,2BAA2B,8BAA8B,CAAC;aACvL;YACD,MAAM;gBACJ,CAAC,oBAAoB,EAAE,SAAS,QAAQ,GAAG,QAAQ,KAAK,CAAC,aAAa,CAAC,GAAG,6BAA6B,EAAE,EAAE,SAAS,QAAQ,YAAY,qDAAqD,oDAAoD;gBACjP,CAAC,iCAAiC,EAAE,SAAS,eAAe,GAAG,QAAQ,YAAY,CAAC,uBAAuB,CAAC,GAAG,+BAA+B,MAAM,EAAE,UAAU,WAAW,GAAG,QAAQ,CAAC,cAAc,oCAAoC,wBAAwB;aAClQ;QACH;QAEA,MAAM,YAAY,eAAe,CAAC,OAAuC,IAAI;YAC3E;YACA;YACA;SACD;QAED,OAAO,SAAS,CAAC,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,UAAU,MAAM,EAAE;IAChE;IAEA,MAAM,iBAAiB,CAAC;QACtB,IAAI,EAAE,GAAG,KAAK,WAAW,CAAC,EAAE,QAAQ,EAAE;YACpC,EAAE,cAAc;YAChB;QACF;IACF;IAEA,qBACE,6LAAC;QAAI,WAAW,CAAC,8FAA8F,EAAE,WAAW;;0BAE1H,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC,mMAAA,CAAA,MAAG;gCAAC,WAAU;;;;;;;;;;;sCAEjB,6LAAC;;8CACC,6LAAC;oCAAG,WAAU;8CAA2B;;;;;;8CACzC,6LAAC;oCAAE,WAAU;;wCAAoC;wCAAO;;;;;;;;;;;;;sCAE1D,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oCACT,SAAS;wCAAE,OAAO;4CAAC;4CAAG;4CAAK;yCAAE;oCAAC;oCAC9B,YAAY;wCAAE,UAAU;wCAAG,QAAQ;oCAAS;oCAC5C,WAAW,CAAC,qBAAqB,EAAE,cAAc,iBAAiB,cAAc;;;;;;8CAElF,6LAAC;oCAAK,WAAW,CAAC,QAAQ,EAAE,cAAc,mBAAmB,gBAAgB;8CAC1E,cAAc,cAAc;;;;;;gCAE9B,6BAAe,6LAAC,mMAAA,CAAA,MAAG;oCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;0BAMrC,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,4LAAA,CAAA,kBAAe;kCACb,SAAS,GAAG,CAAC,CAAC,wBACb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gCAET,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAG;gCAC7B,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAC5B,MAAM;oCAAE,SAAS;oCAAG,GAAG,CAAC;gCAAG;gCAC3B,WAAW,CAAC,KAAK,EAAE,QAAQ,MAAM,KAAK,SAAS,gBAAgB,iBAAiB;0CAEhF,cAAA,6LAAC;oCAAI,WAAW,CAAC,uCAAuC,EACtD,QAAQ,MAAM,KAAK,SAAS,qCAAqC,IACjE;;sDACA,6LAAC;4CAAI,WAAW,CAAC,sDAAsD,EACrE,QAAQ,MAAM,KAAK,SACf,iDACA,kDACJ;sDACC,QAAQ,MAAM,KAAK,uBAClB,6LAAC,qMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;qEAEhB,6LAAC,mMAAA,CAAA,MAAG;gDAAC,WAAU;;;;;;;;;;;sDAInB,6LAAC;4CAAI,WAAW,CAAC,eAAe,EAC9B,QAAQ,MAAM,KAAK,SACf,4DACA,iDACJ;;8DACA,6LAAC;oDAAE,WAAU;8DAA+C,QAAQ,OAAO;;;;;;8DAC3E,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAE,WAAW,CAAC,QAAQ,EACrB,QAAQ,MAAM,KAAK,SAAS,kBAAkB,iBAC9C;sEACC,QAAQ,SAAS,CAAC,kBAAkB;;;;;;wDAEtC,QAAQ,MAAM,kBACb,6LAAC;4DAAK,WAAW,CAAC,+BAA+B,EAC/C,QAAQ,MAAM,KAAK,SACf,8BACA,oCACJ;sEACC,QAAQ,MAAM;;;;;;;;;;;;;;;;;;;;;;;;+BAvCpB,QAAQ,EAAE;;;;;;;;;;oBAiDpB,2BACC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAC5B,WAAU;kCAEV,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC,mMAAA,CAAA,MAAG;wCAAC,WAAU;;;;;;;;;;;8CAEjB,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,oNAAA,CAAA,UAAO;gDAAC,WAAU;;;;;;0DACnB,6LAAC;gDAAK,WAAU;0DAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAOlD,6LAAC;wBAAI,KAAK;;;;;;;;;;;;0BAIZ,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BACC,MAAK;4BACL,OAAO;4BACP,UAAU,CAAC,IAAM,SAAS,EAAE,MAAM,CAAC,KAAK;4BACxC,YAAY;4BACZ,aAAa,CAAC,sBAAsB,EAAE,OAAO,GAAG,CAAC;4BACjD,WAAU;4BACV,UAAU,aAAa,CAAC;;;;;;sCAE1B,6LAAC;4BACC,SAAS;4BACT,UAAU,CAAC,MAAM,IAAI,MAAM,aAAa,CAAC;4BACzC,WAAU;sCAEV,cAAA,6LAAC,qMAAA,CAAA,OAAI;gCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAM5B;GArRgB;;QAauB,4HAAA,CAAA,YAAS;;;KAbhC", "debugId": null}}, {"offset": {"line": 3730, "column": 0}, "map": {"version": 3, "sources": ["file:///F:/AI%20chain/chainsight-production/src/components/ModularDashboard.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport { CryptoModule } from './modules/CryptoModule';\nimport { FinanceModule } from './modules/FinanceModule';\nimport { LegalModule } from './modules/LegalModule';\nimport { DesignModule } from './modules/DesignModule';\nimport { FacialModule } from './modules/FacialModule';\nimport { SupportModule } from './modules/SupportModule';\nimport { Web3Module } from './modules/Web3Module';\nimport { AIChat } from './AIChat';\nimport { LoadingSpinner } from './ui/LoadingSpinner';\n\ninterface ModularDashboardProps {\n  activeModule: string;\n  marketData?: any;\n  modules: any[];\n}\n\nconst moduleComponents = {\n  crypto: CryptoModule,\n  finance: FinanceModule,\n  legal: LegalModule,\n  design: DesignModule,\n  facial: FacialModule,\n  support: SupportModule,\n  web3: Web3Module,\n};\n\nexport function ModularDashboard({ activeModule, marketData, modules }: ModularDashboardProps) {\n  const [isLoading, setIsLoading] = useState(false);\n  const [moduleData, setModuleData] = useState<any>(null);\n\n  useEffect(() => {\n    // Load module-specific data when switching modules\n    const loadModuleData = async () => {\n      setIsLoading(true);\n      try {\n        // Simulate loading module data\n        await new Promise(resolve => setTimeout(resolve, 500));\n        \n        // Set module-specific data based on active module\n        switch (activeModule) {\n          case 'crypto':\n            setModuleData({\n              prices: marketData?.crypto || [],\n              portfolio: { total: 125000, change: 5.2 },\n              alerts: 3\n            });\n            break;\n          case 'finance':\n            setModuleData({\n              stocks: marketData?.stocks || [],\n              portfolio: { total: 250000, change: 2.1 },\n              analysis: 'bullish'\n            });\n            break;\n          case 'legal':\n            setModuleData({\n              documents: 12,\n              contracts: 5,\n              compliance: 'up-to-date'\n            });\n            break;\n          case 'design':\n            setModuleData({\n              projects: 8,\n              templates: 24,\n              assets: 156\n            });\n            break;\n          case 'facial':\n            setModuleData({\n              recognitions: 1247,\n              accuracy: 98.5,\n              models: 3\n            });\n            break;\n          case 'support':\n            setModuleData({\n              tickets: 23,\n              resolved: 18,\n              satisfaction: 4.8\n            });\n            break;\n          case 'web3':\n            setModuleData({\n              dapps: 15,\n              transactions: 1024,\n              gas: 'optimal'\n            });\n            break;\n          default:\n            setModuleData(null);\n        }\n      } catch (error) {\n        console.error('Failed to load module data:', error);\n      } finally {\n        setIsLoading(false);\n      }\n    };\n\n    loadModuleData();\n  }, [activeModule, marketData]);\n\n  const ActiveModuleComponent = moduleComponents[activeModule as keyof typeof moduleComponents];\n\n  return (\n    <div className=\"space-y-8\">\n      {/* Module Header */}\n      <motion.div\n        initial={{ opacity: 0, y: 20 }}\n        animate={{ opacity: 1, y: 0 }}\n        className=\"bg-black/20 backdrop-blur-xl border border-white/10 rounded-2xl p-6\"\n      >\n        <div className=\"flex items-center justify-between\">\n          <div>\n            <h2 className=\"text-2xl font-bold text-white capitalize\">\n              {activeModule} AI Module\n            </h2>\n            <p className=\"text-gray-400 mt-1\">\n              {getModuleDescription(activeModule)}\n            </p>\n          </div>\n          {isLoading && <LoadingSpinner />}\n        </div>\n      </motion.div>\n\n      {/* Module Content */}\n      <div className=\"grid grid-cols-1 lg:grid-cols-3 gap-8\">\n        {/* Main Module Interface */}\n        <div className=\"lg:col-span-2\">\n          <AnimatePresence mode=\"wait\">\n            <motion.div\n              key={activeModule}\n              initial={{ opacity: 0, x: 20 }}\n              animate={{ opacity: 1, x: 0 }}\n              exit={{ opacity: 0, x: -20 }}\n              transition={{ duration: 0.3 }}\n            >\n              {ActiveModuleComponent ? (\n                <ActiveModuleComponent data={moduleData} />\n              ) : (\n                <div className=\"bg-black/20 backdrop-blur-xl border border-white/10 rounded-2xl p-8 text-center\">\n                  <h3 className=\"text-xl font-semibold text-white mb-4\">\n                    Module Coming Soon\n                  </h3>\n                  <p className=\"text-gray-400\">\n                    The {activeModule} module is currently under development.\n                  </p>\n                </div>\n              )}\n            </motion.div>\n          </AnimatePresence>\n        </div>\n\n        {/* AI Chat Sidebar */}\n        <div className=\"lg:col-span-1\">\n          <motion.div\n            initial={{ opacity: 0, x: 20 }}\n            animate={{ opacity: 1, x: 0 }}\n            transition={{ delay: 0.2 }}\n          >\n            <AIChat \n              module={activeModule}\n              context={moduleData}\n              className=\"h-[600px]\"\n            />\n          </motion.div>\n        </div>\n      </div>\n    </div>\n  );\n}\n\nfunction getModuleDescription(module: string): string {\n  const descriptions = {\n    crypto: 'Advanced cryptocurrency analysis and blockchain intelligence',\n    finance: 'AI-powered financial analysis and market insights',\n    legal: 'Legal document analysis and compliance management',\n    design: 'Creative AI tools for design and visual content',\n    facial: 'Facial recognition and biometric analysis',\n    support: 'Intelligent customer support and ticket management',\n    web3: 'Web3 development tools and DeFi analytics'\n  };\n  \n  return descriptions[module as keyof typeof descriptions] || 'AI-powered module';\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAZA;;;;;;;;;;;;AAoBA,MAAM,mBAAmB;IACvB,QAAQ,gJAAA,CAAA,eAAY;IACpB,SAAS,iJAAA,CAAA,gBAAa;IACtB,OAAO,+IAAA,CAAA,cAAW;IAClB,QAAQ,gJAAA,CAAA,eAAY;IACpB,QAAQ,gJAAA,CAAA,eAAY;IACpB,SAAS,iJAAA,CAAA,gBAAa;IACtB,MAAM,8IAAA,CAAA,aAAU;AAClB;AAEO,SAAS,iBAAiB,EAAE,YAAY,EAAE,UAAU,EAAE,OAAO,EAAyB;;IAC3F,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAO;IAElD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;sCAAE;YACR,mDAAmD;YACnD,MAAM;6DAAiB;oBACrB,aAAa;oBACb,IAAI;wBACF,+BAA+B;wBAC/B,MAAM,IAAI;yEAAQ,CAAA,UAAW,WAAW,SAAS;;wBAEjD,kDAAkD;wBAClD,OAAQ;4BACN,KAAK;gCACH,cAAc;oCACZ,QAAQ,YAAY,UAAU,EAAE;oCAChC,WAAW;wCAAE,OAAO;wCAAQ,QAAQ;oCAAI;oCACxC,QAAQ;gCACV;gCACA;4BACF,KAAK;gCACH,cAAc;oCACZ,QAAQ,YAAY,UAAU,EAAE;oCAChC,WAAW;wCAAE,OAAO;wCAAQ,QAAQ;oCAAI;oCACxC,UAAU;gCACZ;gCACA;4BACF,KAAK;gCACH,cAAc;oCACZ,WAAW;oCACX,WAAW;oCACX,YAAY;gCACd;gCACA;4BACF,KAAK;gCACH,cAAc;oCACZ,UAAU;oCACV,WAAW;oCACX,QAAQ;gCACV;gCACA;4BACF,KAAK;gCACH,cAAc;oCACZ,cAAc;oCACd,UAAU;oCACV,QAAQ;gCACV;gCACA;4BACF,KAAK;gCACH,cAAc;oCACZ,SAAS;oCACT,UAAU;oCACV,cAAc;gCAChB;gCACA;4BACF,KAAK;gCACH,cAAc;oCACZ,OAAO;oCACP,cAAc;oCACd,KAAK;gCACP;gCACA;4BACF;gCACE,cAAc;wBAClB;oBACF,EAAE,OAAO,OAAO;wBACd,QAAQ,KAAK,CAAC,+BAA+B;oBAC/C,SAAU;wBACR,aAAa;oBACf;gBACF;;YAEA;QACF;qCAAG;QAAC;QAAc;KAAW;IAE7B,MAAM,wBAAwB,gBAAgB,CAAC,aAA8C;IAE7F,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAG;gBAC7B,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAE;gBAC5B,WAAU;0BAEV,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;;8CACC,6LAAC;oCAAG,WAAU;;wCACX;wCAAa;;;;;;;8CAEhB,6LAAC;oCAAE,WAAU;8CACV,qBAAqB;;;;;;;;;;;;wBAGzB,2BAAa,6LAAC,6IAAA,CAAA,iBAAc;;;;;;;;;;;;;;;;0BAKjC,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC,4LAAA,CAAA,kBAAe;4BAAC,MAAK;sCACpB,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gCAET,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAG;gCAC7B,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAC5B,MAAM;oCAAE,SAAS;oCAAG,GAAG,CAAC;gCAAG;gCAC3B,YAAY;oCAAE,UAAU;gCAAI;0CAE3B,sCACC,6LAAC;oCAAsB,MAAM;;;;;yDAE7B,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;sDAAwC;;;;;;sDAGtD,6LAAC;4CAAE,WAAU;;gDAAgB;gDACtB;gDAAa;;;;;;;;;;;;;+BAdnB;;;;;;;;;;;;;;;kCAuBX,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAC5B,YAAY;gCAAE,OAAO;4BAAI;sCAEzB,cAAA,6LAAC,+HAAA,CAAA,SAAM;gCACL,QAAQ;gCACR,SAAS;gCACT,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOxB;GAhJgB;KAAA;AAkJhB,SAAS,qBAAqB,MAAc;IAC1C,MAAM,eAAe;QACnB,QAAQ;QACR,SAAS;QACT,OAAO;QACP,QAAQ;QACR,QAAQ;QACR,SAAS;QACT,MAAM;IACR;IAEA,OAAO,YAAY,CAAC,OAAoC,IAAI;AAC9D", "debugId": null}}, {"offset": {"line": 4061, "column": 0}, "map": {"version": 3, "sources": ["file:///F:/AI%20chain/chainsight-production/src/components/AIModuleSelector.tsx"], "sourcesContent": ["'use client';\n\nimport { motion } from 'framer-motion';\nimport { \n  Bitcoin, \n  TrendingUp, \n  Scale, \n  Palette, \n  Scan, \n  MessageCircle, \n  Globe,\n  Zap\n} from 'lucide-react';\nimport { LoadingSpinner } from './ui/LoadingSpinner';\n\ninterface AIModuleSelectorProps {\n  modules: any[];\n  activeModule: string;\n  onModuleChange: (module: string) => void;\n  loading?: boolean;\n}\n\nconst moduleConfig = {\n  crypto: {\n    icon: Bitcoin,\n    name: 'Crypto & Blockchain',\n    description: 'Real-time crypto analysis',\n    color: 'from-orange-500 to-yellow-500',\n    status: 'online'\n  },\n  finance: {\n    icon: TrendingUp,\n    name: 'AI Finance',\n    description: 'Market intelligence',\n    color: 'from-green-500 to-emerald-500',\n    status: 'online'\n  },\n  legal: {\n    icon: Scale,\n    name: 'Legal & HR AI',\n    description: 'Document analysis',\n    color: 'from-blue-500 to-cyan-500',\n    status: 'online'\n  },\n  design: {\n    icon: Palette,\n    name: 'Design AI',\n    description: 'Creative assistance',\n    color: 'from-purple-500 to-pink-500',\n    status: 'online'\n  },\n  facial: {\n    icon: <PERSON>an,\n    name: 'Facial Recognition',\n    description: 'Biometric analysis',\n    color: 'from-red-500 to-rose-500',\n    status: 'online'\n  },\n  support: {\n    icon: MessageCircle,\n    name: 'Customer Support',\n    description: 'Intelligent assistance',\n    color: 'from-indigo-500 to-purple-500',\n    status: 'online'\n  },\n  web3: {\n    icon: Globe,\n    name: 'Web3 & DeFi',\n    description: 'Blockchain development',\n    color: 'from-teal-500 to-cyan-500',\n    status: 'online'\n  }\n};\n\nexport function AIModuleSelector({ \n  modules, \n  activeModule, \n  onModuleChange, \n  loading \n}: AIModuleSelectorProps) {\n  const moduleKeys = Object.keys(moduleConfig);\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Header */}\n      <div className=\"text-center\">\n        <motion.h2\n          initial={{ opacity: 0, y: 20 }}\n          animate={{ opacity: 1, y: 0 }}\n          className=\"text-3xl font-bold text-dark-100 mb-2\"\n        >\n          AI Module <span className=\"bg-gradient-to-r from-primary-400 to-primary-500 bg-clip-text text-transparent\">\n            Selection\n          </span>\n        </motion.h2>\n        <p className=\"text-dark-300\">\n          Choose your AI agent specialization\n        </p>\n      </div>\n\n      {/* Module Grid */}\n      <div className=\"grid grid-cols-2 md:grid-cols-4 lg:grid-cols-7 gap-4\">\n        {moduleKeys.map((moduleKey, index) => {\n          const config = moduleConfig[moduleKey as keyof typeof moduleConfig];\n          const Icon = config.icon;\n          const isActive = activeModule === moduleKey;\n          \n          return (\n            <motion.button\n              key={moduleKey}\n              initial={{ opacity: 0, y: 20 }}\n              animate={{ opacity: 1, y: 0 }}\n              transition={{ delay: index * 0.1 }}\n              onClick={() => onModuleChange(moduleKey)}\n              disabled={loading}\n              className={`\n                relative group p-4 rounded-2xl border transition-all duration-300 glass\n                ${isActive\n                  ? 'bg-gradient-to-r ' + config.color + ' border-primary-500/30 shadow-lg shadow-primary-500/20'\n                  : 'border-dark-700 hover:border-primary-500/30 hover:bg-dark-800/50'\n                }\n              `}\n            >\n              {/* Status Indicator */}\n              <div className=\"absolute top-2 right-2\">\n                <div className={`\n                  w-2 h-2 rounded-full\n                  ${config.status === 'online' ? 'bg-green-400' : 'bg-gray-400'}\n                  ${config.status === 'online' ? 'animate-pulse' : ''}\n                `} />\n              </div>\n\n              {/* Icon */}\n              <div className=\"flex flex-col items-center space-y-2\">\n                <div className={`\n                  p-3 rounded-xl transition-all duration-300\n                  ${isActive \n                    ? 'bg-white/20' \n                    : 'bg-white/10 group-hover:bg-white/15'\n                  }\n                `}>\n                  <Icon className={`\n                    w-6 h-6 transition-colors duration-300\n                    ${isActive ? 'text-white' : 'text-dark-300 group-hover:text-primary-400'}\n                  `} />\n                </div>\n\n                {/* Name */}\n                <div className=\"text-center\">\n                  <h3 className={`\n                    text-sm font-semibold transition-colors duration-300\n                    ${isActive ? 'text-white' : 'text-dark-200 group-hover:text-white'}\n                  `}>\n                    {config.name}\n                  </h3>\n                  <p className={`\n                    text-xs transition-colors duration-300\n                    ${isActive ? 'text-white/80' : 'text-dark-400 group-hover:text-dark-300'}\n                  `}>\n                    {config.description}\n                  </p>\n                </div>\n              </div>\n\n              {/* Active Indicator */}\n              {isActive && (\n                <motion.div\n                  layoutId=\"activeModule\"\n                  className=\"absolute inset-0 rounded-2xl border-2 border-primary-400/50\"\n                  transition={{ type: \"spring\", bounce: 0.2, duration: 0.6 }}\n                />\n              )}\n\n              {/* Loading Overlay */}\n              {loading && isActive && (\n                <div className=\"absolute inset-0 bg-black/20 rounded-2xl flex items-center justify-center\">\n                  <LoadingSpinner size=\"sm\" />\n                </div>\n              )}\n            </motion.button>\n          );\n        })}\n      </div>\n\n      {/* Active Module Info */}\n      <motion.div\n        key={activeModule}\n        initial={{ opacity: 0, y: 10 }}\n        animate={{ opacity: 1, y: 0 }}\n        className=\"bg-black/20 backdrop-blur-xl border border-white/10 rounded-xl p-4\"\n      >\n        <div className=\"flex items-center space-x-3\">\n          <div className=\"w-2 h-2 bg-green-400 rounded-full animate-pulse\" />\n          <span className=\"text-white font-medium\">\n            {moduleConfig[activeModule as keyof typeof moduleConfig]?.name} Module Active\n          </span>\n          <Zap className=\"w-4 h-4 text-yellow-400\" />\n        </div>\n      </motion.div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAUA;AAbA;;;;;AAsBA,MAAM,eAAe;IACnB,QAAQ;QACN,MAAM,2MAAA,CAAA,UAAO;QACb,MAAM;QACN,aAAa;QACb,OAAO;QACP,QAAQ;IACV;IACA,SAAS;QACP,MAAM,qNAAA,CAAA,aAAU;QAChB,MAAM;QACN,aAAa;QACb,OAAO;QACP,QAAQ;IACV;IACA,OAAO;QACL,MAAM,uMAAA,CAAA,QAAK;QACX,MAAM;QACN,aAAa;QACb,OAAO;QACP,QAAQ;IACV;IACA,QAAQ;QACN,MAAM,2MAAA,CAAA,UAAO;QACb,MAAM;QACN,aAAa;QACb,OAAO;QACP,QAAQ;IACV;IACA,QAAQ;QACN,MAAM,qMAAA,CAAA,OAAI;QACV,MAAM;QACN,aAAa;QACb,OAAO;QACP,QAAQ;IACV;IACA,SAAS;QACP,MAAM,2NAAA,CAAA,gBAAa;QACnB,MAAM;QACN,aAAa;QACb,OAAO;QACP,QAAQ;IACV;IACA,MAAM;QACJ,MAAM,uMAAA,CAAA,QAAK;QACX,MAAM;QACN,aAAa;QACb,OAAO;QACP,QAAQ;IACV;AACF;AAEO,SAAS,iBAAiB,EAC/B,OAAO,EACP,YAAY,EACZ,cAAc,EACd,OAAO,EACe;IACtB,MAAM,aAAa,OAAO,IAAI,CAAC;IAE/B,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,6LAAA,CAAA,SAAM,CAAC,EAAE;wBACR,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAC5B,WAAU;;4BACX;0CACW,6LAAC;gCAAK,WAAU;0CAAiF;;;;;;;;;;;;kCAI7G,6LAAC;wBAAE,WAAU;kCAAgB;;;;;;;;;;;;0BAM/B,6LAAC;gBAAI,WAAU;0BACZ,WAAW,GAAG,CAAC,CAAC,WAAW;oBAC1B,MAAM,SAAS,YAAY,CAAC,UAAuC;oBACnE,MAAM,OAAO,OAAO,IAAI;oBACxB,MAAM,WAAW,iBAAiB;oBAElC,qBACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;wBAEZ,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAC5B,YAAY;4BAAE,OAAO,QAAQ;wBAAI;wBACjC,SAAS,IAAM,eAAe;wBAC9B,UAAU;wBACV,WAAW,CAAC;;gBAEV,EAAE,WACE,sBAAsB,OAAO,KAAK,GAAG,2DACrC,mEACH;cACH,CAAC;;0CAGD,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAW,CAAC;;kBAEf,EAAE,OAAO,MAAM,KAAK,WAAW,iBAAiB,cAAc;kBAC9D,EAAE,OAAO,MAAM,KAAK,WAAW,kBAAkB,GAAG;gBACtD,CAAC;;;;;;;;;;;0CAIH,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAW,CAAC;;kBAEf,EAAE,WACE,gBACA,sCACH;gBACH,CAAC;kDACC,cAAA,6LAAC;4CAAK,WAAW,CAAC;;oBAEhB,EAAE,WAAW,eAAe,6CAA6C;kBAC3E,CAAC;;;;;;;;;;;kDAIH,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAG,WAAW,CAAC;;oBAEd,EAAE,WAAW,eAAe,uCAAuC;kBACrE,CAAC;0DACE,OAAO,IAAI;;;;;;0DAEd,6LAAC;gDAAE,WAAW,CAAC;;oBAEb,EAAE,WAAW,kBAAkB,0CAA0C;kBAC3E,CAAC;0DACE,OAAO,WAAW;;;;;;;;;;;;;;;;;;4BAMxB,0BACC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,UAAS;gCACT,WAAU;gCACV,YAAY;oCAAE,MAAM;oCAAU,QAAQ;oCAAK,UAAU;gCAAI;;;;;;4BAK5D,WAAW,0BACV,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC,6IAAA,CAAA,iBAAc;oCAAC,MAAK;;;;;;;;;;;;uBAnEpB;;;;;gBAwEX;;;;;;0BAIF,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gBAET,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAG;gBAC7B,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAE;gBAC5B,WAAU;0BAEV,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;;;;;sCACf,6LAAC;4BAAK,WAAU;;gCACb,YAAY,CAAC,aAA0C,EAAE;gCAAK;;;;;;;sCAEjE,6LAAC,mMAAA,CAAA,MAAG;4BAAC,WAAU;;;;;;;;;;;;eAVZ;;;;;;;;;;;AAeb;KA/HgB", "debugId": null}}, {"offset": {"line": 4389, "column": 0}, "map": {"version": 3, "sources": ["file:///F:/AI%20chain/chainsight-production/src/components/RealTimeNotifications.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport { X, CheckCircle, AlertTriangle, Info, TrendingUp } from 'lucide-react';\n\ninterface Notification {\n  id: string;\n  type: 'success' | 'warning' | 'info' | 'market';\n  title: string;\n  message: string;\n  timestamp: Date;\n  autoHide?: boolean;\n}\n\nexport function RealTimeNotifications() {\n  const [notifications, setNotifications] = useState<Notification[]>([]);\n\n  useEffect(() => {\n    // Simulate real-time notifications\n    const interval = setInterval(() => {\n      const notificationTypes = [\n        {\n          type: 'market' as const,\n          title: 'Price Alert',\n          message: 'Bitcoin reached $44,000 - 3% increase in the last hour',\n          autoHide: true\n        },\n        {\n          type: 'success' as const,\n          title: 'AI Analysis Complete',\n          message: 'Portfolio optimization suggestions are ready',\n          autoHide: true\n        },\n        {\n          type: 'info' as const,\n          title: 'Market Update',\n          message: 'Ethereum network congestion detected - gas fees elevated',\n          autoHide: true\n        },\n        {\n          type: 'warning' as const,\n          title: 'Risk Alert',\n          message: 'High volatility detected in DeFi markets',\n          autoHide: true\n        }\n      ];\n\n      // Randomly show notifications\n      if (Math.random() > 0.7) {\n        const randomNotification = notificationTypes[Math.floor(Math.random() * notificationTypes.length)];\n        const newNotification: Notification = {\n          id: Date.now().toString(),\n          ...randomNotification,\n          timestamp: new Date()\n        };\n\n        setNotifications(prev => [newNotification, ...prev.slice(0, 4)]); // Keep max 5 notifications\n      }\n    }, 15000); // Check every 15 seconds\n\n    // Add initial welcome notification\n    const welcomeNotification: Notification = {\n      id: 'welcome',\n      type: 'success',\n      title: 'Chainsight Initialized',\n      message: 'Production AI platform is ready. All modules online.',\n      timestamp: new Date(),\n      autoHide: false\n    };\n\n    setNotifications([welcomeNotification]);\n\n    return () => clearInterval(interval);\n  }, []);\n\n  useEffect(() => {\n    // Auto-hide notifications after 5 seconds\n    notifications.forEach(notification => {\n      if (notification.autoHide) {\n        setTimeout(() => {\n          removeNotification(notification.id);\n        }, 5000);\n      }\n    });\n  }, [notifications]);\n\n  const removeNotification = (id: string) => {\n    setNotifications(prev => prev.filter(n => n.id !== id));\n  };\n\n  const getIcon = (type: string) => {\n    switch (type) {\n      case 'success':\n        return <CheckCircle className=\"w-5 h-5 text-green-400\" />;\n      case 'warning':\n        return <AlertTriangle className=\"w-5 h-5 text-yellow-400\" />;\n      case 'info':\n        return <Info className=\"w-5 h-5 text-blue-400\" />;\n      case 'market':\n        return <TrendingUp className=\"w-5 h-5 text-orange-400\" />;\n      default:\n        return <Info className=\"w-5 h-5 text-gray-400\" />;\n    }\n  };\n\n  const getBackgroundColor = (type: string) => {\n    switch (type) {\n      case 'success':\n        return 'bg-green-500/10 border-green-500/20';\n      case 'warning':\n        return 'bg-yellow-500/10 border-yellow-500/20';\n      case 'info':\n        return 'bg-blue-500/10 border-blue-500/20';\n      case 'market':\n        return 'bg-orange-500/10 border-orange-500/20';\n      default:\n        return 'bg-gray-500/10 border-gray-500/20';\n    }\n  };\n\n  return (\n    <div className=\"fixed top-4 right-4 z-50 space-y-2 max-w-sm\">\n      <AnimatePresence>\n        {notifications.map((notification) => (\n          <motion.div\n            key={notification.id}\n            initial={{ opacity: 0, x: 300, scale: 0.9 }}\n            animate={{ opacity: 1, x: 0, scale: 1 }}\n            exit={{ opacity: 0, x: 300, scale: 0.9 }}\n            transition={{ type: \"spring\", bounce: 0.3 }}\n            className={`\n              backdrop-blur-xl border rounded-xl p-4 shadow-lg\n              ${getBackgroundColor(notification.type)}\n            `}\n          >\n            <div className=\"flex items-start space-x-3\">\n              <div className=\"flex-shrink-0 mt-0.5\">\n                {getIcon(notification.type)}\n              </div>\n              \n              <div className=\"flex-1 min-w-0\">\n                <div className=\"flex items-center justify-between\">\n                  <h4 className=\"text-sm font-semibold text-white\">\n                    {notification.title}\n                  </h4>\n                  <button\n                    onClick={() => removeNotification(notification.id)}\n                    className=\"text-gray-400 hover:text-white transition-colors\"\n                  >\n                    <X className=\"w-4 h-4\" />\n                  </button>\n                </div>\n                \n                <p className=\"text-sm text-gray-300 mt-1\">\n                  {notification.message}\n                </p>\n                \n                <p className=\"text-xs text-gray-400 mt-2\">\n                  {notification.timestamp.toLocaleTimeString()}\n                </p>\n              </div>\n            </div>\n          </motion.div>\n        ))}\n      </AnimatePresence>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;;;AAJA;;;;AAeO,SAAS;;IACd,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAkB,EAAE;IAErE,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;2CAAE;YACR,mCAAmC;YACnC,MAAM,WAAW;4DAAY;oBAC3B,MAAM,oBAAoB;wBACxB;4BACE,MAAM;4BACN,OAAO;4BACP,SAAS;4BACT,UAAU;wBACZ;wBACA;4BACE,MAAM;4BACN,OAAO;4BACP,SAAS;4BACT,UAAU;wBACZ;wBACA;4BACE,MAAM;4BACN,OAAO;4BACP,SAAS;4BACT,UAAU;wBACZ;wBACA;4BACE,MAAM;4BACN,OAAO;4BACP,SAAS;4BACT,UAAU;wBACZ;qBACD;oBAED,8BAA8B;oBAC9B,IAAI,KAAK,MAAM,KAAK,KAAK;wBACvB,MAAM,qBAAqB,iBAAiB,CAAC,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,kBAAkB,MAAM,EAAE;wBAClG,MAAM,kBAAgC;4BACpC,IAAI,KAAK,GAAG,GAAG,QAAQ;4BACvB,GAAG,kBAAkB;4BACrB,WAAW,IAAI;wBACjB;wBAEA;wEAAiB,CAAA,OAAQ;oCAAC;uCAAoB,KAAK,KAAK,CAAC,GAAG;iCAAG;wEAAG,2BAA2B;oBAC/F;gBACF;2DAAG,QAAQ,yBAAyB;YAEpC,mCAAmC;YACnC,MAAM,sBAAoC;gBACxC,IAAI;gBACJ,MAAM;gBACN,OAAO;gBACP,SAAS;gBACT,WAAW,IAAI;gBACf,UAAU;YACZ;YAEA,iBAAiB;gBAAC;aAAoB;YAEtC;mDAAO,IAAM,cAAc;;QAC7B;0CAAG,EAAE;IAEL,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;2CAAE;YACR,0CAA0C;YAC1C,cAAc,OAAO;mDAAC,CAAA;oBACpB,IAAI,aAAa,QAAQ,EAAE;wBACzB;+DAAW;gCACT,mBAAmB,aAAa,EAAE;4BACpC;8DAAG;oBACL;gBACF;;QACF;0CAAG;QAAC;KAAc;IAElB,MAAM,qBAAqB,CAAC;QAC1B,iBAAiB,CAAA,OAAQ,KAAK,MAAM,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;IACrD;IAEA,MAAM,UAAU,CAAC;QACf,OAAQ;YACN,KAAK;gBACH,qBAAO,6LAAC,8NAAA,CAAA,cAAW;oBAAC,WAAU;;;;;;YAChC,KAAK;gBACH,qBAAO,6LAAC,2NAAA,CAAA,gBAAa;oBAAC,WAAU;;;;;;YAClC,KAAK;gBACH,qBAAO,6LAAC,qMAAA,CAAA,OAAI;oBAAC,WAAU;;;;;;YACzB,KAAK;gBACH,qBAAO,6LAAC,qNAAA,CAAA,aAAU;oBAAC,WAAU;;;;;;YAC/B;gBACE,qBAAO,6LAAC,qMAAA,CAAA,OAAI;oBAAC,WAAU;;;;;;QAC3B;IACF;IAEA,MAAM,qBAAqB,CAAC;QAC1B,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC,4LAAA,CAAA,kBAAe;sBACb,cAAc,GAAG,CAAC,CAAC,6BAClB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBAET,SAAS;wBAAE,SAAS;wBAAG,GAAG;wBAAK,OAAO;oBAAI;oBAC1C,SAAS;wBAAE,SAAS;wBAAG,GAAG;wBAAG,OAAO;oBAAE;oBACtC,MAAM;wBAAE,SAAS;wBAAG,GAAG;wBAAK,OAAO;oBAAI;oBACvC,YAAY;wBAAE,MAAM;wBAAU,QAAQ;oBAAI;oBAC1C,WAAW,CAAC;;cAEV,EAAE,mBAAmB,aAAa,IAAI,EAAE;YAC1C,CAAC;8BAED,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CACZ,QAAQ,aAAa,IAAI;;;;;;0CAG5B,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAG,WAAU;0DACX,aAAa,KAAK;;;;;;0DAErB,6LAAC;gDACC,SAAS,IAAM,mBAAmB,aAAa,EAAE;gDACjD,WAAU;0DAEV,cAAA,6LAAC,+LAAA,CAAA,IAAC;oDAAC,WAAU;;;;;;;;;;;;;;;;;kDAIjB,6LAAC;wCAAE,WAAU;kDACV,aAAa,OAAO;;;;;;kDAGvB,6LAAC;wCAAE,WAAU;kDACV,aAAa,SAAS,CAAC,kBAAkB;;;;;;;;;;;;;;;;;;mBAjC3C,aAAa,EAAE;;;;;;;;;;;;;;;AA0ChC;GAzJgB;KAAA", "debugId": null}}, {"offset": {"line": 4684, "column": 0}, "map": {"version": 3, "sources": ["file:///F:/AI%20chain/chainsight-production/src/components/SystemStatus.tsx"], "sourcesContent": ["'use client';\n\nimport { motion } from 'framer-motion';\nimport { Wifi, WifiOff, Clock, Zap } from 'lucide-react';\n\ninterface SystemStatusProps {\n  status: 'connected' | 'disconnected' | 'connecting';\n}\n\nexport function SystemStatus({ status }: SystemStatusProps) {\n  const getStatusIcon = () => {\n    switch (status) {\n      case 'connected':\n        return <Wifi className=\"w-4 h-4 text-green-400\" />;\n      case 'disconnected':\n        return <WifiOff className=\"w-4 h-4 text-red-400\" />;\n      case 'connecting':\n        return <Zap className=\"w-4 h-4 text-yellow-400 animate-pulse\" />;\n      default:\n        return <WifiOff className=\"w-4 h-4 text-gray-400\" />;\n    }\n  };\n\n  const getStatusColor = () => {\n    switch (status) {\n      case 'connected':\n        return 'text-green-400';\n      case 'disconnected':\n        return 'text-red-400';\n      case 'connecting':\n        return 'text-yellow-400';\n      default:\n        return 'text-gray-400';\n    }\n  };\n\n  const getStatusText = () => {\n    switch (status) {\n      case 'connected':\n        return 'CONNECTED';\n      case 'disconnected':\n        return 'DISCONNECTED';\n      case 'connecting':\n        return 'CONNECTING...';\n      default:\n        return 'UNKNOWN';\n    }\n  };\n\n  return (\n    <div className=\"flex items-center space-x-4\">\n      {/* Connection Status */}\n      <motion.div\n        initial={{ opacity: 0, scale: 0.9 }}\n        animate={{ opacity: 1, scale: 1 }}\n        className=\"flex items-center space-x-2 glass rounded-full px-4 py-2\"\n      >\n        <motion.div\n          animate={status === 'connected' ? { scale: [1, 1.2, 1] } : {}}\n          transition={{ duration: 2, repeat: Infinity }}\n        >\n          {getStatusIcon()}\n        </motion.div>\n        <span className={`text-sm font-medium ${getStatusColor()}`}>\n          {getStatusText()}\n        </span>\n      </motion.div>\n\n      {/* Latency */}\n      {status.status === 'connected' && (\n        <motion.div\n          initial={{ opacity: 0, x: 20 }}\n          animate={{ opacity: 1, x: 0 }}\n          className=\"flex items-center space-x-2 bg-black/20 backdrop-blur-xl border border-white/10 rounded-full px-4 py-2\"\n        >\n          <Clock className=\"w-4 h-4 text-blue-400\" />\n          <span className=\"text-sm text-gray-300\">\n            {status.latency}ms\n          </span>\n        </motion.div>\n      )}\n\n      {/* Last Update */}\n      <motion.div\n        initial={{ opacity: 0, x: 20 }}\n        animate={{ opacity: 1, x: 0 }}\n        transition={{ delay: 0.1 }}\n        className=\"bg-black/20 backdrop-blur-xl border border-white/10 rounded-full px-4 py-2\"\n      >\n        <span className=\"text-sm text-gray-400\">\n          Last: {status.lastUpdate.toLocaleTimeString()}\n        </span>\n      </motion.div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAHA;;;;AASO,SAAS,aAAa,EAAE,MAAM,EAAqB;IACxD,MAAM,gBAAgB;QACpB,OAAQ;YACN,KAAK;gBACH,qBAAO,6LAAC,qMAAA,CAAA,OAAI;oBAAC,WAAU;;;;;;YACzB,KAAK;gBACH,qBAAO,6LAAC,+MAAA,CAAA,UAAO;oBAAC,WAAU;;;;;;YAC5B,KAAK;gBACH,qBAAO,6LAAC,mMAAA,CAAA,MAAG;oBAAC,WAAU;;;;;;YACxB;gBACE,qBAAO,6LAAC,+MAAA,CAAA,UAAO;oBAAC,WAAU;;;;;;QAC9B;IACF;IAEA,MAAM,iBAAiB;QACrB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;g<PERSON><PERSON>,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,gBAAgB;QACpB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,SAAS;oBAAE,SAAS;oBAAG,OAAO;gBAAI;gBAClC,SAAS;oBAAE,SAAS;oBAAG,OAAO;gBAAE;gBAChC,WAAU;;kCAEV,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS,WAAW,cAAc;4BAAE,OAAO;gCAAC;gCAAG;gCAAK;6BAAE;wBAAC,IAAI,CAAC;wBAC5D,YAAY;4BAAE,UAAU;4BAAG,QAAQ;wBAAS;kCAE3C;;;;;;kCAEH,6LAAC;wBAAK,WAAW,CAAC,oBAAoB,EAAE,kBAAkB;kCACvD;;;;;;;;;;;;YAKJ,OAAO,MAAM,KAAK,6BACjB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAG;gBAC7B,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAE;gBAC5B,WAAU;;kCAEV,6LAAC,uMAAA,CAAA,QAAK;wBAAC,WAAU;;;;;;kCACjB,6LAAC;wBAAK,WAAU;;4BACb,OAAO,OAAO;4BAAC;;;;;;;;;;;;;0BAMtB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAG;gBAC7B,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAE;gBAC5B,YAAY;oBAAE,OAAO;gBAAI;gBACzB,WAAU;0BAEV,cAAA,6LAAC;oBAAK,WAAU;;wBAAwB;wBAC/B,OAAO,UAAU,CAAC,kBAAkB;;;;;;;;;;;;;;;;;;AAKrD;KAtFgB", "debugId": null}}, {"offset": {"line": 4887, "column": 0}, "map": {"version": 3, "sources": ["file:///F:/AI%20chain/chainsight-production/src/hooks/useAIModules.ts"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\n\ninterface AIModule {\n  id: string;\n  name: string;\n  description: string;\n  status: 'online' | 'offline' | 'maintenance';\n  capabilities: string[];\n  version: string;\n}\n\nexport function useAIModules() {\n  const [modules, setModules] = useState<AIModule[]>([]);\n  const [isLoading, setIsLoading] = useState(true);\n  const [error, setError] = useState<string | null>(null);\n\n  useEffect(() => {\n    const loadModules = async () => {\n      try {\n        // Simulate API call to load module configurations\n        await new Promise(resolve => setTimeout(resolve, 1000));\n        \n        const moduleData: AIModule[] = [\n          {\n            id: 'crypto',\n            name: 'Crypto & Blockchain AI',\n            description: 'Advanced cryptocurrency analysis and blockchain intelligence',\n            status: 'online',\n            capabilities: [\n              'Real-time price analysis',\n              'Market sentiment analysis',\n              'Portfolio optimization',\n              'Risk assessment',\n              'Trading signals'\n            ],\n            version: '2.1.0'\n          },\n          {\n            id: 'finance',\n            name: 'AI Finance',\n            description: 'Comprehensive financial analysis and market insights',\n            status: 'online',\n            capabilities: [\n              'Stock market analysis',\n              'Financial planning',\n              'Risk management',\n              'Investment recommendations',\n              'Economic forecasting'\n            ],\n            version: '1.8.5'\n          },\n          {\n            id: 'legal',\n            name: 'Legal & HR AI',\n            description: 'Legal document analysis and HR management',\n            status: 'online',\n            capabilities: [\n              'Contract analysis',\n              'Compliance checking',\n              'Legal research',\n              'HR policy management',\n              'Document generation'\n            ],\n            version: '1.5.2'\n          },\n          {\n            id: 'design',\n            name: 'Design AI',\n            description: 'Creative AI tools for design and visual content',\n            status: 'online',\n            capabilities: [\n              'Logo generation',\n              'UI/UX design assistance',\n              'Color palette creation',\n              'Brand identity development',\n              'Visual content optimization'\n            ],\n            version: '2.0.1'\n          },\n          {\n            id: 'facial',\n            name: 'Facial Recognition',\n            description: 'Advanced facial recognition and biometric analysis',\n            status: 'online',\n            capabilities: [\n              'Face detection',\n              'Identity verification',\n              'Emotion analysis',\n              'Age estimation',\n              'Security monitoring'\n            ],\n            version: '3.2.0'\n          },\n          {\n            id: 'support',\n            name: 'Customer Support AI',\n            description: 'Intelligent customer support and ticket management',\n            status: 'online',\n            capabilities: [\n              'Automated responses',\n              'Ticket classification',\n              'Sentiment analysis',\n              'Knowledge base search',\n              'Escalation management'\n            ],\n            version: '1.9.3'\n          },\n          {\n            id: 'web3',\n            name: 'Web3 & DeFi',\n            description: 'Web3 development tools and DeFi analytics',\n            status: 'online',\n            capabilities: [\n              'Smart contract analysis',\n              'DeFi protocol monitoring',\n              'Gas optimization',\n              'Yield farming strategies',\n              'NFT analytics'\n            ],\n            version: '1.6.7'\n          }\n        ];\n\n        setModules(moduleData);\n        setIsLoading(false);\n      } catch (err) {\n        setError('Failed to load AI modules');\n        setIsLoading(false);\n      }\n    };\n\n    loadModules();\n  }, []);\n\n  return {\n    modules,\n    isLoading,\n    error\n  };\n}\n"], "names": [], "mappings": ";;;AAEA;;AAFA;;AAaO,SAAS;;IACd,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAc,EAAE;IACrD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAElD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;kCAAE;YACR,MAAM;sDAAc;oBAClB,IAAI;wBACF,kDAAkD;wBAClD,MAAM,IAAI;kEAAQ,CAAA,UAAW,WAAW,SAAS;;wBAEjD,MAAM,aAAyB;4BAC7B;gCACE,IAAI;gCACJ,MAAM;gCACN,aAAa;gCACb,QAAQ;gCACR,cAAc;oCACZ;oCACA;oCACA;oCACA;oCACA;iCACD;gCACD,SAAS;4BACX;4BACA;gCACE,IAAI;gCACJ,MAAM;gCACN,aAAa;gCACb,QAAQ;gCACR,cAAc;oCACZ;oCACA;oCACA;oCACA;oCACA;iCACD;gCACD,SAAS;4BACX;4BACA;gCACE,IAAI;gCACJ,MAAM;gCACN,aAAa;gCACb,QAAQ;gCACR,cAAc;oCACZ;oCACA;oCACA;oCACA;oCACA;iCACD;gCACD,SAAS;4BACX;4BACA;gCACE,IAAI;gCACJ,MAAM;gCACN,aAAa;gCACb,QAAQ;gCACR,cAAc;oCACZ;oCACA;oCACA;oCACA;oCACA;iCACD;gCACD,SAAS;4BACX;4BACA;gCACE,IAAI;gCACJ,MAAM;gCACN,aAAa;gCACb,QAAQ;gCACR,cAAc;oCACZ;oCACA;oCACA;oCACA;oCACA;iCACD;gCACD,SAAS;4BACX;4BACA;gCACE,IAAI;gCACJ,MAAM;gCACN,aAAa;gCACb,QAAQ;gCACR,cAAc;oCACZ;oCACA;oCACA;oCACA;oCACA;iCACD;gCACD,SAAS;4BACX;4BACA;gCACE,IAAI;gCACJ,MAAM;gCACN,aAAa;gCACb,QAAQ;gCACR,cAAc;oCACZ;oCACA;oCACA;oCACA;oCACA;iCACD;gCACD,SAAS;4BACX;yBACD;wBAED,WAAW;wBACX,aAAa;oBACf,EAAE,OAAO,KAAK;wBACZ,SAAS;wBACT,aAAa;oBACf;gBACF;;YAEA;QACF;iCAAG,EAAE;IAEL,OAAO;QACL;QACA;QACA;IACF;AACF;GAhIgB", "debugId": null}}, {"offset": {"line": 5035, "column": 0}, "map": {"version": 3, "sources": ["file:///F:/AI%20chain/chainsight-production/src/hooks/useRealTimeData.ts"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect, useRef } from 'react';\n\ninterface ConnectionStatus {\n  status: 'connected' | 'disconnected' | 'connecting';\n  lastUpdate: Date;\n  latency: number;\n}\n\ninterface MarketData {\n  crypto: any[];\n  stocks: any[];\n  forex: any[];\n  lastUpdate: Date;\n}\n\nexport function useRealTimeData() {\n  const [connectionStatus, setConnectionStatus] = useState<ConnectionStatus>({\n    status: 'connecting',\n    lastUpdate: new Date(),\n    latency: 0\n  });\n  \n  const [marketData, setMarketData] = useState<MarketData>({\n    crypto: [],\n    stocks: [],\n    forex: [],\n    lastUpdate: new Date()\n  });\n\n  const wsRef = useRef<WebSocket | null>(null);\n  const reconnectTimeoutRef = useRef<NodeJS.Timeout>();\n\n  const connectWebSocket = () => {\n    try {\n      // In production, this would connect to your real WebSocket server\n      // For now, we'll simulate real-time updates\n      setConnectionStatus(prev => ({ ...prev, status: 'connecting' }));\n      \n      // Simulate connection\n      setTimeout(() => {\n        setConnectionStatus({\n          status: 'connected',\n          lastUpdate: new Date(),\n          latency: Math.floor(Math.random() * 50) + 10\n        });\n      }, 1000);\n\n      // Simulate real-time market data updates\n      const interval = setInterval(() => {\n        const now = new Date();\n        \n        // Update connection status\n        setConnectionStatus(prev => ({\n          ...prev,\n          lastUpdate: now,\n          latency: Math.floor(Math.random() * 50) + 10\n        }));\n\n        // Update market data with realistic fluctuations\n        setMarketData(prev => ({\n          crypto: prev.crypto.map(item => ({\n            ...item,\n            price: item.price * (1 + (Math.random() - 0.5) * 0.02), // ±1% change\n            change: (Math.random() - 0.5) * 10 // ±5% change\n          })),\n          stocks: prev.stocks.map(item => ({\n            ...item,\n            price: item.price * (1 + (Math.random() - 0.5) * 0.01), // ±0.5% change\n            change: (Math.random() - 0.5) * 5 // ±2.5% change\n          })),\n          forex: prev.forex,\n          lastUpdate: now\n        }));\n      }, 5000); // Update every 5 seconds\n\n      return () => {\n        clearInterval(interval);\n      };\n\n    } catch (error) {\n      console.error('WebSocket connection failed:', error);\n      setConnectionStatus(prev => ({ ...prev, status: 'disconnected' }));\n      \n      // Retry connection after 5 seconds\n      reconnectTimeoutRef.current = setTimeout(connectWebSocket, 5000);\n    }\n  };\n\n  useEffect(() => {\n    // Initialize with some sample data\n    setMarketData({\n      crypto: [\n        { id: 'bitcoin', symbol: 'BTC', price: 43250, change: 2.5 },\n        { id: 'ethereum', symbol: 'ETH', price: 2650, change: -1.2 },\n        { id: 'binancecoin', symbol: 'BNB', price: 315, change: 0.8 }\n      ],\n      stocks: [\n        { symbol: 'AAPL', price: 185.50, change: 1.2 },\n        { symbol: 'GOOGL', price: 142.30, change: -0.5 },\n        { symbol: 'MSFT', price: 378.90, change: 0.8 }\n      ],\n      forex: [\n        { pair: 'EUR/USD', rate: 1.0875, change: 0.15 },\n        { pair: 'GBP/USD', rate: 1.2650, change: -0.25 },\n        { pair: 'USD/JPY', rate: 149.85, change: 0.35 }\n      ],\n      lastUpdate: new Date()\n    });\n\n    // Start WebSocket connection\n    const cleanup = connectWebSocket();\n\n    return () => {\n      if (cleanup) cleanup();\n      if (reconnectTimeoutRef.current) {\n        clearTimeout(reconnectTimeoutRef.current);\n      }\n      if (wsRef.current) {\n        wsRef.current.close();\n      }\n    };\n  }, []);\n\n  return {\n    connectionStatus,\n    marketData\n  };\n}\n"], "names": [], "mappings": ";;;AAEA;;AAFA;;AAiBO,SAAS;;IACd,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAoB;QACzE,QAAQ;QACR,YAAY,IAAI;QAChB,SAAS;IACX;IAEA,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAc;QACvD,QAAQ,EAAE;QACV,QAAQ,EAAE;QACV,OAAO,EAAE;QACT,YAAY,IAAI;IAClB;IAEA,MAAM,QAAQ,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAoB;IACvC,MAAM,sBAAsB,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD;IAEjC,MAAM,mBAAmB;QACvB,IAAI;YACF,kEAAkE;YAClE,4CAA4C;YAC5C,oBAAoB,CAAA,OAAQ,CAAC;oBAAE,GAAG,IAAI;oBAAE,QAAQ;gBAAa,CAAC;YAE9D,sBAAsB;YACtB,WAAW;gBACT,oBAAoB;oBAClB,QAAQ;oBACR,YAAY,IAAI;oBAChB,SAAS,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,MAAM;gBAC5C;YACF,GAAG;YAEH,yCAAyC;YACzC,MAAM,WAAW,YAAY;gBAC3B,MAAM,MAAM,IAAI;gBAEhB,2BAA2B;gBAC3B,oBAAoB,CAAA,OAAQ,CAAC;wBAC3B,GAAG,IAAI;wBACP,YAAY;wBACZ,SAAS,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,MAAM;oBAC5C,CAAC;gBAED,iDAAiD;gBACjD,cAAc,CAAA,OAAQ,CAAC;wBACrB,QAAQ,KAAK,MAAM,CAAC,GAAG,CAAC,CAAA,OAAQ,CAAC;gCAC/B,GAAG,IAAI;gCACP,OAAO,KAAK,KAAK,GAAG,CAAC,IAAI,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI,IAAI;gCACrD,QAAQ,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI,GAAG,aAAa;4BAClD,CAAC;wBACD,QAAQ,KAAK,MAAM,CAAC,GAAG,CAAC,CAAA,OAAQ,CAAC;gCAC/B,GAAG,IAAI;gCACP,OAAO,KAAK,KAAK,GAAG,CAAC,IAAI,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI,IAAI;gCACrD,QAAQ,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI,EAAE,eAAe;4BACnD,CAAC;wBACD,OAAO,KAAK,KAAK;wBACjB,YAAY;oBACd,CAAC;YACH,GAAG,OAAO,yBAAyB;YAEnC,OAAO;gBACL,cAAc;YAChB;QAEF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,gCAAgC;YAC9C,oBAAoB,CAAA,OAAQ,CAAC;oBAAE,GAAG,IAAI;oBAAE,QAAQ;gBAAe,CAAC;YAEhE,mCAAmC;YACnC,oBAAoB,OAAO,GAAG,WAAW,kBAAkB;QAC7D;IACF;IAEA,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;qCAAE;YACR,mCAAmC;YACnC,cAAc;gBACZ,QAAQ;oBACN;wBAAE,IAAI;wBAAW,QAAQ;wBAAO,OAAO;wBAAO,QAAQ;oBAAI;oBAC1D;wBAAE,IAAI;wBAAY,QAAQ;wBAAO,OAAO;wBAAM,QAAQ,CAAC;oBAAI;oBAC3D;wBAAE,IAAI;wBAAe,QAAQ;wBAAO,OAAO;wBAAK,QAAQ;oBAAI;iBAC7D;gBACD,QAAQ;oBACN;wBAAE,QAAQ;wBAAQ,OAAO;wBAAQ,QAAQ;oBAAI;oBAC7C;wBAAE,QAAQ;wBAAS,OAAO;wBAAQ,QAAQ,CAAC;oBAAI;oBAC/C;wBAAE,QAAQ;wBAAQ,OAAO;wBAAQ,QAAQ;oBAAI;iBAC9C;gBACD,OAAO;oBACL;wBAAE,MAAM;wBAAW,MAAM;wBAAQ,QAAQ;oBAAK;oBAC9C;wBAAE,MAAM;wBAAW,MAAM;wBAAQ,QAAQ,CAAC;oBAAK;oBAC/C;wBAAE,MAAM;wBAAW,MAAM;wBAAQ,QAAQ;oBAAK;iBAC/C;gBACD,YAAY,IAAI;YAClB;YAEA,6BAA6B;YAC7B,MAAM,UAAU;YAEhB;6CAAO;oBACL,IAAI,SAAS;oBACb,IAAI,oBAAoB,OAAO,EAAE;wBAC/B,aAAa,oBAAoB,OAAO;oBAC1C;oBACA,IAAI,MAAM,OAAO,EAAE;wBACjB,MAAM,OAAO,CAAC,KAAK;oBACrB;gBACF;;QACF;oCAAG,EAAE;IAEL,OAAO;QACL;QACA;IACF;AACF;GAhHgB", "debugId": null}}, {"offset": {"line": 5201, "column": 0}, "map": {"version": 3, "sources": ["file:///F:/AI%20chain/chainsight-production/src/app/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { motion } from 'framer-motion';\nimport { Navigation } from '@/components/Navigation';\nimport { ModularDashboard } from '@/components/ModularDashboard';\nimport { AIModuleSelector } from '@/components/AIModuleSelector';\nimport { RealTimeNotifications } from '@/components/RealTimeNotifications';\nimport { SystemStatus } from '@/components/SystemStatus';\nimport { useAIModules } from '@/hooks/useAIModules';\nimport { useRealTimeData } from '@/hooks/useRealTimeData';\n\nexport default function HomePage() {\n  const [activeModule, setActiveModule] = useState('crypto');\n  const { modules, isLoading: modulesLoading } = useAIModules();\n  const { connectionStatus, marketData } = useRealTimeData();\n\n  useEffect(() => {\n    // Initialize production platform\n    console.log('🚀 Chainsight Production Platform Initialized');\n  }, []);\n\n  return (\n    <div className=\"min-h-screen bg-dark-900\">\n      <Navigation />\n\n      <main className=\"pt-20\">\n        {/* Hero Section */}\n        <section className=\"relative overflow-hidden\">\n          <div className=\"absolute inset-0 bg-gradient-to-br from-primary-500/20 via-transparent to-primary-500/10\" />\n\n          <div className=\"relative max-w-7xl mx-auto px-4 py-20\">\n            <motion.div\n              initial={{ opacity: 0, y: 20 }}\n              animate={{ opacity: 1, y: 0 }}\n              className=\"text-center space-y-8\"\n            >\n              <div className=\"space-y-4\">\n                <h1 className=\"text-6xl md:text-8xl font-bold\">\n                  <span className=\"bg-gradient-to-r from-primary-400 via-primary-500 to-primary-600 bg-clip-text text-transparent\">\n                    CHAINSIGHT\n                  </span>\n                </h1>\n                <p className=\"text-xl md:text-2xl text-dark-300 max-w-3xl mx-auto\">\n                  The Ultimate Modular AI Agent Platform by{' '}\n                  <span className=\"text-primary-500 font-semibold\">Connectouch</span>\n                </p>\n              </div>\n\n              {/* Module Selector */}\n              <div className=\"max-w-4xl mx-auto\">\n                <AIModuleSelector\n                  activeModule={activeModule}\n                  onModuleChange={setActiveModule}\n                  modules={modules}\n                  loading={modulesLoading}\n                />\n              </div>\n            </motion.div>\n          </div>\n        </section>\n\n        {/* Dashboard Section */}\n        <section className=\"py-20\">\n          <div className=\"max-w-7xl mx-auto px-4\">\n            <ModularDashboard\n              activeModule={activeModule}\n              marketData={marketData}\n              modules={modules}\n            />\n          </div>\n        </section>\n\n        {/* System Status */}\n        <div className=\"fixed bottom-4 right-4\">\n          <SystemStatus status={connectionStatus} />\n        </div>\n\n        {/* Real-time Notifications */}\n        <RealTimeNotifications />\n      </main>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAVA;;;;;;;;;;AAYe,SAAS;;IACtB,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,EAAE,OAAO,EAAE,WAAW,cAAc,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,eAAY,AAAD;IAC1D,MAAM,EAAE,gBAAgB,EAAE,UAAU,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,kBAAe,AAAD;IAEvD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;8BAAE;YACR,iCAAiC;YACjC,QAAQ,GAAG,CAAC;QACd;6BAAG,EAAE;IAEL,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC,mIAAA,CAAA,aAAU;;;;;0BAEX,6LAAC;gBAAK,WAAU;;kCAEd,6LAAC;wBAAQ,WAAU;;0CACjB,6LAAC;gCAAI,WAAU;;;;;;0CAEf,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oCACT,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAG;oCAC7B,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAE;oCAC5B,WAAU;;sDAEV,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAG,WAAU;8DACZ,cAAA,6LAAC;wDAAK,WAAU;kEAAiG;;;;;;;;;;;8DAInH,6LAAC;oDAAE,WAAU;;wDAAsD;wDACvB;sEAC1C,6LAAC;4DAAK,WAAU;sEAAiC;;;;;;;;;;;;;;;;;;sDAKrD,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC,yIAAA,CAAA,mBAAgB;gDACf,cAAc;gDACd,gBAAgB;gDAChB,SAAS;gDACT,SAAS;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAQnB,6LAAC;wBAAQ,WAAU;kCACjB,cAAA,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC,yIAAA,CAAA,mBAAgB;gCACf,cAAc;gCACd,YAAY;gCACZ,SAAS;;;;;;;;;;;;;;;;kCAMf,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC,qIAAA,CAAA,eAAY;4BAAC,QAAQ;;;;;;;;;;;kCAIxB,6LAAC,8IAAA,CAAA,wBAAqB;;;;;;;;;;;;;;;;;AAI9B;GAvEwB;;QAEyB,+HAAA,CAAA,eAAY;QAClB,kIAAA,CAAA,kBAAe;;;KAHlC", "debugId": null}}]}