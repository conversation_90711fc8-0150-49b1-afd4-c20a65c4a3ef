from fastapi import <PERSON><PERSON><PERSON>, WebSocket, WebSocketDisconnect
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.gzip import GZipMiddleware
from contextlib import asynccontextmanager
import uvicorn
import os
from dotenv import load_dotenv

from routers import (
    auth,
    crypto,
    finance,
    ai_chat,
    hr_legal,
    design,
    face_recognition,
    websocket_router
)
from db import init_db
from services.websocket_manager import WebSocketManager

load_dotenv()

# WebSocket manager instance
websocket_manager = WebSocketManager()

@asynccontextmanager
async def lifespan(app: FastAPI):
    # Startup
    await init_db()
    print("🚀 Chainsight Backend Started")
    yield
    # Shutdown
    print("🛑 Chainsight Backend Stopped")

app = FastAPI(
    title="Chainsight by Connectouch API",
    description="Modular Fullstack AI Agent Backend",
    version="1.0.0",
    docs_url="/docs",
    redoc_url="/redoc",
    lifespan=lifespan
)

# Middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:3000", "https://chainsight.com"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)
app.add_middleware(GZipMiddleware, minimum_size=1000)

# Include routers
app.include_router(auth.router, prefix="/api/auth", tags=["Authentication"])
app.include_router(crypto.router, prefix="/api/crypto", tags=["Crypto & Blockchain"])
app.include_router(finance.router, prefix="/api/finance", tags=["AI Finance"])
app.include_router(ai_chat.router, prefix="/api/chat", tags=["AI Chat"])
app.include_router(hr_legal.router, prefix="/api/hr-legal", tags=["HR & Legal"])
app.include_router(design.router, prefix="/api/design", tags=["Design Studio"])
app.include_router(face_recognition.router, prefix="/api/face", tags=["Face Recognition"])
app.include_router(websocket_router.router, prefix="/ws", tags=["WebSocket"])

@app.get("/")
async def root():
    return {
        "message": "Chainsight by Connectouch API",
        "version": "1.0.0",
        "status": "operational",
        "features": [
            "Crypto & Blockchain Tools",
            "AI Finance Analytics",
            "Real-time Chat Assistant",
            "HR & Legal AI",
            "Design Studio",
            "Face Recognition",
            "Real-time WebSocket Updates"
        ]
    }

@app.get("/health")
async def health_check():
    return {"status": "healthy", "timestamp": "2025-07-02T13:54:00Z"}

# WebSocket endpoint for real-time updates
@app.websocket("/ws/{client_id}")
async def websocket_endpoint(websocket: WebSocket, client_id: str):
    await websocket_manager.connect(websocket, client_id)
    try:
        while True:
            data = await websocket.receive_text()
            await websocket_manager.send_personal_message(f"Echo: {data}", client_id)
    except WebSocketDisconnect:
        websocket_manager.disconnect(client_id)

if __name__ == "__main__":
    uvicorn.run(
        "main:app",
        host="0.0.0.0",
        port=int(os.getenv("BACKEND_PORT", 8000)),
        reload=True,
        log_level="info"
    )
