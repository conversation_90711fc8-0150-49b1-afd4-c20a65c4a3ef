import { useEffect, useRef, useState, useCallback } from 'react';
import { io, Socket } from 'socket.io-client';

export interface WebSocketConfig {
  url?: string;
  autoConnect?: boolean;
  reconnectAttempts?: number;
  reconnectDelay?: number;
}

export interface WebSocketState {
  connected: boolean;
  connecting: boolean;
  error: string | null;
  lastMessage: any;
  messageHistory: any[];
}

export interface UseWebSocketReturn extends WebSocketState {
  socket: Socket | null;
  connect: () => void;
  disconnect: () => void;
  subscribe: (event: string, callback: (data: any) => void) => void;
  unsubscribe: (event: string) => void;
  emit: (event: string, data?: any) => void;
  clearHistory: () => void;
}

const defaultConfig: WebSocketConfig = {
  url: process.env.NEXT_PUBLIC_WS_URL || 'ws://localhost:8000',
  autoConnect: true,
  reconnectAttempts: 5,
  reconnectDelay: 1000
};

export const useWebSocket = (config: WebSocketConfig = {}): UseWebSocketReturn => {
  const finalConfig = { ...defaultConfig, ...config };
  const socketRef = useRef<Socket | null>(null);
  const reconnectTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const reconnectAttemptsRef = useRef(0);

  const [state, setState] = useState<WebSocketState>({
    connected: false,
    connecting: false,
    error: null,
    lastMessage: null,
    messageHistory: []
  });

  const updateState = useCallback((updates: Partial<WebSocketState>) => {
    setState(prev => ({ ...prev, ...updates }));
  }, []);

  const connect = useCallback(() => {
    if (socketRef.current?.connected) return;

    updateState({ connecting: true, error: null });

    try {
      const socket = io(finalConfig.url!, {
        transports: ['websocket', 'polling'],
        upgrade: true,
        rememberUpgrade: true,
        timeout: 10000,
        forceNew: true
      });

      socket.on('connect', () => {
        console.log('WebSocket connected');
        reconnectAttemptsRef.current = 0;
        updateState({ 
          connected: true, 
          connecting: false, 
          error: null 
        });
      });

      socket.on('disconnect', (reason) => {
        console.log('WebSocket disconnected:', reason);
        updateState({ 
          connected: false, 
          connecting: false 
        });

        // Auto-reconnect logic
        if (reason === 'io server disconnect') {
          // Server initiated disconnect, don't reconnect
          return;
        }

        if (reconnectAttemptsRef.current < (finalConfig.reconnectAttempts || 5)) {
          reconnectAttemptsRef.current++;
          reconnectTimeoutRef.current = setTimeout(() => {
            console.log(`Reconnecting... Attempt ${reconnectAttemptsRef.current}`);
            connect();
          }, finalConfig.reconnectDelay! * reconnectAttemptsRef.current);
        } else {
          updateState({ 
            error: 'Failed to reconnect after maximum attempts' 
          });
        }
      });

      socket.on('connect_error', (error) => {
        console.error('WebSocket connection error:', error);
        updateState({ 
          connected: false, 
          connecting: false, 
          error: error.message 
        });
      });

      socket.on('error', (error) => {
        console.error('WebSocket error:', error);
        updateState({ error: error.message });
      });

      // Generic message handler for logging
      socket.onAny((event, data) => {
        updateState(prev => ({
          lastMessage: { event, data, timestamp: Date.now() },
          messageHistory: [
            ...prev.messageHistory.slice(-99), // Keep last 100 messages
            { event, data, timestamp: Date.now() }
          ]
        }));
      });

      socketRef.current = socket;
    } catch (error) {
      console.error('Failed to create WebSocket connection:', error);
      updateState({ 
        connecting: false, 
        error: error instanceof Error ? error.message : 'Connection failed' 
      });
    }
  }, [finalConfig, updateState]);

  const disconnect = useCallback(() => {
    if (reconnectTimeoutRef.current) {
      clearTimeout(reconnectTimeoutRef.current);
      reconnectTimeoutRef.current = null;
    }

    if (socketRef.current) {
      socketRef.current.disconnect();
      socketRef.current = null;
    }

    updateState({ 
      connected: false, 
      connecting: false, 
      error: null 
    });
  }, [updateState]);

  const subscribe = useCallback((event: string, callback: (data: any) => void) => {
    if (socketRef.current) {
      socketRef.current.on(event, callback);
    }
  }, []);

  const unsubscribe = useCallback((event: string) => {
    if (socketRef.current) {
      socketRef.current.off(event);
    }
  }, []);

  const emit = useCallback((event: string, data?: any) => {
    if (socketRef.current?.connected) {
      socketRef.current.emit(event, data);
    } else {
      console.warn('Cannot emit event: WebSocket not connected');
    }
  }, []);

  const clearHistory = useCallback(() => {
    updateState({ messageHistory: [] });
  }, [updateState]);

  // Auto-connect on mount if enabled
  useEffect(() => {
    if (finalConfig.autoConnect) {
      connect();
    }

    return () => {
      disconnect();
    };
  }, [finalConfig.autoConnect, connect, disconnect]);

  return {
    ...state,
    socket: socketRef.current,
    connect,
    disconnect,
    subscribe,
    unsubscribe,
    emit,
    clearHistory
  };
};

// Specialized hooks for different data streams
export const useMarketDataStream = () => {
  const ws = useWebSocket();
  const [marketData, setMarketData] = useState<any>({});

  useEffect(() => {
    if (ws.connected) {
      ws.subscribe('market_data', (data) => {
        setMarketData(prev => ({
          ...prev,
          [data.symbol]: data
        }));
      });

      // Request initial market data
      ws.emit('subscribe_market_data', { symbols: ['BTC', 'ETH', 'USDT'] });
    }

    return () => {
      ws.unsubscribe('market_data');
    };
  }, [ws.connected]);

  return { ...ws, marketData };
};

export const useChatStream = () => {
  const ws = useWebSocket();
  const [messages, setMessages] = useState<any[]>([]);

  useEffect(() => {
    if (ws.connected) {
      ws.subscribe('chat_message', (message) => {
        setMessages(prev => [...prev, message]);
      });

      ws.subscribe('chat_response', (response) => {
        setMessages(prev => [...prev, response]);
      });
    }

    return () => {
      ws.unsubscribe('chat_message');
      ws.unsubscribe('chat_response');
    };
  }, [ws.connected]);

  const sendMessage = useCallback((message: string, sessionId?: string) => {
    if (ws.connected) {
      const messageData = {
        content: message,
        sessionId: sessionId || 'default',
        timestamp: Date.now(),
        userId: 'user' // In production, get from auth context
      };
      
      ws.emit('send_message', messageData);
      setMessages(prev => [...prev, { ...messageData, role: 'user' }]);
    }
  }, [ws.connected, ws.emit]);

  return { ...ws, messages, sendMessage, clearMessages: () => setMessages([]) };
};

export const useSystemUpdates = () => {
  const ws = useWebSocket();
  const [notifications, setNotifications] = useState<any[]>([]);

  useEffect(() => {
    if (ws.connected) {
      ws.subscribe('system_update', (update) => {
        setNotifications(prev => [update, ...prev.slice(0, 49)]); // Keep last 50
      });

      ws.subscribe('notification', (notification) => {
        setNotifications(prev => [notification, ...prev.slice(0, 49)]);
      });
    }

    return () => {
      ws.unsubscribe('system_update');
      ws.unsubscribe('notification');
    };
  }, [ws.connected]);

  const markAsRead = useCallback((notificationId: string) => {
    setNotifications(prev => 
      prev.map(n => n.id === notificationId ? { ...n, read: true } : n)
    );
  }, []);

  const clearNotifications = useCallback(() => {
    setNotifications([]);
  }, []);

  return { 
    ...ws, 
    notifications, 
    unreadCount: notifications.filter(n => !n.read).length,
    markAsRead, 
    clearNotifications 
  };
};
