'use client';

import { motion } from 'framer-motion';
import { 
  Bitcoin, 
  TrendingUp, 
  Scale, 
  Palette, 
  Scan, 
  MessageCircle, 
  Globe,
  Zap
} from 'lucide-react';
import { LoadingSpinner } from './ui/LoadingSpinner';

interface AIModuleSelectorProps {
  modules: any[];
  activeModule: string;
  onModuleChange: (module: string) => void;
  loading?: boolean;
}

const moduleConfig = {
  crypto: {
    icon: Bitcoin,
    name: 'Crypto & Blockchain',
    description: '₿ Real-time crypto analysis',
    color: 'from-neon-blue to-cyan-400',
    status: 'online'
  },
  finance: {
    icon: TrendingUp,
    name: 'AI Finance',
    description: '📈 Market intelligence',
    color: 'from-neon-green to-emerald-400',
    status: 'online'
  },
  legal: {
    icon: Scale,
    name: 'Legal & HR AI',
    description: '⚖️ Document analysis',
    color: 'from-neon-orange to-amber-400',
    status: 'online'
  },
  design: {
    icon: Palette,
    name: 'Design AI',
    description: '🎨 Creative assistance',
    color: 'from-neon-pink to-rose-400',
    status: 'online'
  },
  facial: {
    icon: <PERSON><PERSON>,
    name: 'Facial Recognition',
    description: '👁️ Biometric analysis',
    color: 'from-neon-purple to-violet-400',
    status: 'online'
  },
  support: {
    icon: MessageCircle,
    name: 'Customer Support',
    description: '🎧 Intelligent assistance',
    color: 'from-accent-purple to-purple-400',
    status: 'online'
  },
  web3: {
    icon: Globe,
    name: 'Web3 & DeFi',
    description: '🌐 Blockchain development',
    color: 'from-accent-cyan to-teal-400',
    status: 'online'
  }
};

export function AIModuleSelector({ 
  modules, 
  activeModule, 
  onModuleChange, 
  loading 
}: AIModuleSelectorProps) {
  const moduleKeys = Object.keys(moduleConfig);

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="text-center">
        <motion.h2
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="text-3xl font-bold text-white mb-2 rainbow-pulse"
        >
          🤖 AI MODULE{' '}
          <span className="bg-gradient-to-r from-neon-blue via-neon-purple to-neon-pink bg-clip-text text-transparent">
            NEXUS
          </span>
        </motion.h2>
        <p className="text-white/80 text-lg">
          ⚡ Choose your AI agent specialization ⚡
        </p>
      </div>

      {/* Module Grid */}
      <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-7 gap-4">
        {moduleKeys.map((moduleKey, index) => {
          const config = moduleConfig[moduleKey as keyof typeof moduleConfig];
          const Icon = config.icon;
          const isActive = activeModule === moduleKey;
          
          return (
            <motion.button
              key={moduleKey}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: index * 0.1 }}
              onClick={() => onModuleChange(moduleKey)}
              disabled={loading}
              className={`
                relative group p-4 rounded-2xl border transition-all duration-300 glass float
                ${isActive
                  ? 'bg-gradient-to-r ' + config.color + ' border-white/30 shadow-lg shadow-neon-blue/20 scale-105'
                  : 'border-white/20 hover:border-neon-blue/50 hover:bg-white/10 hover:scale-105'
                }
              `}
            >
              {/* Status Indicator */}
              <div className="absolute top-2 right-2">
                <div className={`
                  w-3 h-3 rounded-full
                  ${config.status === 'online' ? 'bg-neon-green shadow-lg shadow-neon-green/50' : 'bg-gray-400'}
                  ${config.status === 'online' ? 'animate-pulse' : ''}
                `} />
              </div>

              {/* Icon */}
              <div className="flex flex-col items-center space-y-2">
                <div className={`
                  p-3 rounded-xl transition-all duration-300
                  ${isActive 
                    ? 'bg-white/20' 
                    : 'bg-white/10 group-hover:bg-white/15'
                  }
                `}>
                  <Icon className={`
                    w-6 h-6 transition-colors duration-300
                    ${isActive ? 'text-white drop-shadow-lg' : 'text-white/70 group-hover:text-white'}
                  `} />
                </div>

                {/* Name */}
                <div className="text-center">
                  <h3 className={`
                    text-sm font-semibold transition-colors duration-300
                    ${isActive ? 'text-white drop-shadow-lg' : 'text-white/90 group-hover:text-white'}
                  `}>
                    {config.name}
                  </h3>
                  <p className={`
                    text-xs transition-colors duration-300
                    ${isActive ? 'text-white/90' : 'text-white/70 group-hover:text-white/80'}
                  `}>
                    {config.description}
                  </p>
                </div>
              </div>

              {/* Active Indicator */}
              {isActive && (
                <motion.div
                  layoutId="activeModule"
                  className="absolute inset-0 rounded-2xl border-2 border-neon-blue/70 shadow-lg shadow-neon-blue/30"
                  transition={{ type: "spring", bounce: 0.2, duration: 0.6 }}
                />
              )}

              {/* Loading Overlay */}
              {loading && isActive && (
                <div className="absolute inset-0 bg-black/20 rounded-2xl flex items-center justify-center">
                  <LoadingSpinner size="sm" />
                </div>
              )}
            </motion.button>
          );
        })}
      </div>

      {/* Active Module Info */}
      <motion.div
        key={activeModule}
        initial={{ opacity: 0, y: 10 }}
        animate={{ opacity: 1, y: 0 }}
        className="glass border border-neon-blue/30 rounded-xl p-4 shadow-lg shadow-neon-blue/20"
      >
        <div className="flex items-center justify-center space-x-3">
          <div className="w-3 h-3 bg-neon-green rounded-full animate-pulse shadow-lg shadow-neon-green/50" />
          <span className="text-white font-medium neon-glow-blue">
            ⚡ {moduleConfig[activeModule as keyof typeof moduleConfig]?.name} Module Active ⚡
          </span>
          <Zap className="w-5 h-5 text-neon-orange animate-pulse" />
        </div>
      </motion.div>
    </div>
  );
}
