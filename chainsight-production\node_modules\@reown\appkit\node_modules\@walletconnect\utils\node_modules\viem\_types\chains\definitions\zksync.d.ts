export declare const zksync: {
    blockExplorers: {
        readonly default: {
            readonly name: "Etherscan";
            readonly url: "https://era.zksync.network/";
            readonly apiUrl: "https://api-era.zksync.network/api";
        };
        readonly native: {
            readonly name: "ZKsync Explorer";
            readonly url: "https://explorer.zksync.io/";
            readonly apiUrl: "https://block-explorer-api.mainnet.zksync.io/api";
        };
    };
    contracts: {
        readonly multicall3: {
            readonly address: "******************************************";
        };
        readonly universalSignatureVerifier: {
            readonly address: "******************************************";
            readonly blockCreated: 45659388;
        };
    };
    id: 324;
    name: "ZKsync Era";
    nativeCurrency: {
        readonly decimals: 18;
        readonly name: "<PERSON>ther";
        readonly symbol: "ETH";
    };
    rpcUrls: {
        readonly default: {
            readonly http: readonly ["https://mainnet.era.zksync.io"];
            readonly webSocket: readonly ["wss://mainnet.era.zksync.io/ws"];
        };
    };
    sourceId?: number | undefined;
    testnet?: boolean | undefined;
    custom: {
        readonly getEip712Domain: import("../../zksync/index.js").EIP712DomainFn<import("../index.js").ZkSyncTransactionSerializable, import("../index.js").ZkSyncEIP712TransactionSignable>;
    };
    fees?: import("../../index.js").ChainFees<undefined> | undefined;
    formatters: {
        readonly block: {
            exclude: [] | undefined;
            format: (args: import("../index.js").ZkSyncRpcBlock) => {
                baseFeePerGas: bigint | null;
                blobGasUsed: bigint;
                difficulty: bigint;
                excessBlobGas: bigint;
                extraData: import("../../index.js").Hex;
                gasLimit: bigint;
                gasUsed: bigint;
                hash: `0x${string}` | null;
                logsBloom: `0x${string}` | null;
                miner: import("abitype").Address;
                mixHash: import("../../index.js").Hash;
                nonce: `0x${string}` | null;
                number: bigint | null;
                parentBeaconBlockRoot?: import("../../index.js").Hex | undefined;
                parentHash: import("../../index.js").Hash;
                receiptsRoot: import("../../index.js").Hex;
                sealFields: import("../../index.js").Hex[];
                sha3Uncles: import("../../index.js").Hash;
                size: bigint;
                stateRoot: import("../../index.js").Hash;
                timestamp: bigint;
                totalDifficulty: bigint | null;
                transactions: `0x${string}`[] | import("../index.js").ZkSyncTransaction<boolean>[];
                transactionsRoot: import("../../index.js").Hash;
                uncles: import("../../index.js").Hash[];
                withdrawals?: import("../../index.js").Withdrawal[] | undefined;
                withdrawalsRoot?: import("../../index.js").Hex | undefined;
                l1BatchNumber: bigint | null;
                l1BatchTimestamp: bigint | null;
            } & {};
            type: "block";
        };
        readonly transaction: {
            exclude: [] | undefined;
            format: (args: import("../index.js").ZkSyncRpcTransaction) => ({
                r: import("../../index.js").Hex;
                s: import("../../index.js").Hex;
                v: bigint;
                to: import("abitype").Address | null;
                from: import("abitype").Address;
                gas: bigint;
                nonce: number;
                value: bigint;
                blockHash: `0x${string}` | null;
                blockNumber: bigint | null;
                hash: import("../../index.js").Hash;
                input: import("../../index.js").Hex;
                transactionIndex: number | null;
                typeHex: import("../../index.js").Hex | null;
                accessList?: undefined;
                authorizationList?: undefined;
                blobVersionedHashes?: undefined;
                chainId?: number | undefined;
                yParity?: undefined;
                type: "legacy";
                gasPrice: bigint;
                maxFeePerBlobGas?: undefined;
                maxFeePerGas?: undefined;
                maxPriorityFeePerGas?: undefined;
                l1BatchNumber: bigint | null;
                l1BatchTxIndex: bigint | null;
            } | {
                blockHash: `0x${string}` | null;
                blockNumber: bigint | null;
                from: import("abitype").Address;
                gas: bigint;
                hash: import("../../index.js").Hash;
                input: import("../../index.js").Hex;
                nonce: number;
                r: import("../../index.js").Hex;
                s: import("../../index.js").Hex;
                to: import("abitype").Address | null;
                transactionIndex: number | null;
                typeHex: import("../../index.js").Hex | null;
                v: bigint;
                value: bigint;
                yParity: number;
                accessList: import("../../index.js").AccessList;
                authorizationList?: undefined;
                blobVersionedHashes?: undefined;
                chainId: number;
                type: "eip2930";
                gasPrice: bigint;
                maxFeePerBlobGas?: undefined;
                maxFeePerGas?: undefined;
                maxPriorityFeePerGas?: undefined;
                l1BatchNumber: bigint | null;
                l1BatchTxIndex: bigint | null;
            } | {
                blockHash: `0x${string}` | null;
                blockNumber: bigint | null;
                from: import("abitype").Address;
                gas: bigint;
                hash: import("../../index.js").Hash;
                input: import("../../index.js").Hex;
                nonce: number;
                r: import("../../index.js").Hex;
                s: import("../../index.js").Hex;
                to: import("abitype").Address | null;
                transactionIndex: number | null;
                typeHex: import("../../index.js").Hex | null;
                v: bigint;
                value: bigint;
                yParity: number;
                accessList: import("../../index.js").AccessList;
                authorizationList?: undefined;
                blobVersionedHashes?: undefined;
                chainId: number;
                type: "eip1559";
                gasPrice?: undefined;
                maxFeePerBlobGas?: undefined;
                maxFeePerGas: bigint;
                maxPriorityFeePerGas: bigint;
                l1BatchNumber: bigint | null;
                l1BatchTxIndex: bigint | null;
            } | {
                blockHash: `0x${string}` | null;
                blockNumber: bigint | null;
                from: import("abitype").Address;
                gas: bigint;
                hash: import("../../index.js").Hash;
                input: import("../../index.js").Hex;
                nonce: number;
                r: import("../../index.js").Hex;
                s: import("../../index.js").Hex;
                to: import("abitype").Address | null;
                transactionIndex: number | null;
                typeHex: import("../../index.js").Hex | null;
                v: bigint;
                value: bigint;
                yParity: number;
                accessList: import("../../index.js").AccessList;
                authorizationList?: undefined;
                blobVersionedHashes: readonly import("../../index.js").Hex[];
                chainId: number;
                type: "eip4844";
                gasPrice?: undefined;
                maxFeePerBlobGas: bigint;
                maxFeePerGas: bigint;
                maxPriorityFeePerGas: bigint;
                l1BatchNumber: bigint | null;
                l1BatchTxIndex: bigint | null;
            } | {
                blockHash: `0x${string}` | null;
                blockNumber: bigint | null;
                from: import("abitype").Address;
                gas: bigint;
                hash: import("../../index.js").Hash;
                input: import("../../index.js").Hex;
                nonce: number;
                r: import("../../index.js").Hex;
                s: import("../../index.js").Hex;
                to: import("abitype").Address | null;
                transactionIndex: number | null;
                typeHex: import("../../index.js").Hex | null;
                v: bigint;
                value: bigint;
                yParity: number;
                accessList: import("../../index.js").AccessList;
                authorizationList: import("../../experimental/index.js").SignedAuthorizationList;
                blobVersionedHashes?: undefined;
                chainId: number;
                type: "eip7702";
                gasPrice?: undefined;
                maxFeePerBlobGas?: undefined;
                maxFeePerGas: bigint;
                maxPriorityFeePerGas: bigint;
                l1BatchNumber: bigint | null;
                l1BatchTxIndex: bigint | null;
            } | {
                blockHash: `0x${string}` | null;
                blockNumber: bigint | null;
                from: import("abitype").Address;
                gas: bigint;
                hash: import("../../index.js").Hash;
                input: import("../../index.js").Hex;
                nonce: number;
                r: import("../../index.js").Hex;
                s: import("../../index.js").Hex;
                to: import("abitype").Address | null;
                transactionIndex: number | null;
                typeHex: import("../../index.js").Hex | null;
                v: bigint;
                value: bigint;
                yParity: number;
                l1BatchNumber: bigint | null;
                l1BatchTxIndex: bigint | null;
                gasPrice?: undefined;
                maxFeePerBlobGas?: undefined;
                maxFeePerGas: bigint;
                maxPriorityFeePerGas: bigint;
                type: "priority";
            } | {
                blockHash: `0x${string}` | null;
                blockNumber: bigint | null;
                from: import("abitype").Address;
                gas: bigint;
                hash: import("../../index.js").Hash;
                input: import("../../index.js").Hex;
                nonce: number;
                r: import("../../index.js").Hex;
                s: import("../../index.js").Hex;
                to: import("abitype").Address | null;
                transactionIndex: number | null;
                typeHex: import("../../index.js").Hex | null;
                v: bigint;
                value: bigint;
                yParity: number;
                l1BatchNumber: bigint | null;
                l1BatchTxIndex: bigint | null;
                gasPrice?: undefined;
                maxFeePerBlobGas?: undefined;
                maxFeePerGas: bigint;
                maxPriorityFeePerGas: bigint;
                type: "eip712" | "priority";
            }) & {};
            type: "transaction";
        };
        readonly transactionReceipt: {
            exclude: [] | undefined;
            format: (args: import("../index.js").ZkSyncRpcTransactionReceipt) => {
                type: import("../index.js").ZkSyncTransactionType;
                to: import("abitype").Address | null;
                from: import("abitype").Address;
                blockHash: import("../../index.js").Hash;
                blockNumber: bigint;
                transactionIndex: number;
                status: "success" | "reverted";
                contractAddress: import("abitype").Address | null | undefined;
                logsBloom: import("../../index.js").Hex;
                blobGasUsed?: bigint | undefined;
                gasUsed: bigint;
                transactionHash: import("../../index.js").Hash;
                blobGasPrice?: bigint | undefined;
                cumulativeGasUsed: bigint;
                effectiveGasPrice: bigint;
                root?: import("../../index.js").Hash | undefined;
                l1BatchNumber: bigint | null;
                l1BatchTxIndex: bigint | null;
                logs: import("../index.js").ZkSyncLog[];
                l2ToL1Logs: import("../index.js").ZkSyncL2ToL1Log[];
            } & {};
            type: "transactionReceipt";
        };
        readonly transactionRequest: {
            exclude: ("paymaster" | "gasPerPubdata" | "factoryDeps" | "paymasterInput" | "customSignature")[] | undefined;
            format: (args: import("../index.js").ZkSyncTransactionRequest) => ({
                data?: import("../../index.js").Hex | undefined;
                from?: import("abitype").Address | undefined;
                gas?: `0x${string}` | undefined;
                nonce?: `0x${string}` | undefined;
                to?: import("abitype").Address | null | undefined;
                type?: "0x0" | undefined;
                value?: `0x${string}` | undefined;
                gasPrice?: `0x${string}` | undefined;
                maxFeePerBlobGas?: undefined;
                maxFeePerGas?: undefined;
                maxPriorityFeePerGas?: undefined;
                blobs?: undefined;
                accessList?: undefined;
                authorizationList?: undefined;
                blobVersionedHashes?: undefined;
                kzg?: undefined;
                sidecars?: undefined;
                eip712Meta?: undefined;
            } | {
                data?: import("../../index.js").Hex | undefined;
                from?: import("abitype").Address | undefined;
                gas?: `0x${string}` | undefined;
                nonce?: `0x${string}` | undefined;
                to?: import("abitype").Address | null | undefined;
                type?: "0x1" | undefined;
                value?: `0x${string}` | undefined;
                gasPrice?: `0x${string}` | undefined;
                maxFeePerBlobGas?: undefined;
                maxFeePerGas?: undefined;
                maxPriorityFeePerGas?: undefined;
                accessList?: import("../../index.js").AccessList | undefined;
                blobs?: undefined;
                authorizationList?: undefined;
                blobVersionedHashes?: undefined;
                kzg?: undefined;
                sidecars?: undefined;
                eip712Meta?: undefined;
            } | {
                data?: import("../../index.js").Hex | undefined;
                from?: import("abitype").Address | undefined;
                gas?: `0x${string}` | undefined;
                nonce?: `0x${string}` | undefined;
                to?: import("abitype").Address | null | undefined;
                type?: "0x2" | undefined;
                value?: `0x${string}` | undefined;
                gasPrice?: undefined;
                maxFeePerBlobGas?: undefined;
                maxFeePerGas?: `0x${string}` | undefined;
                maxPriorityFeePerGas?: `0x${string}` | undefined;
                accessList?: import("../../index.js").AccessList | undefined;
                blobs?: undefined;
                authorizationList?: undefined;
                blobVersionedHashes?: undefined;
                kzg?: undefined;
                sidecars?: undefined;
                eip712Meta?: undefined;
            } | {
                type?: "0x3" | undefined;
                data?: import("../../index.js").Hex | undefined;
                from?: import("abitype").Address | undefined;
                gas?: `0x${string}` | undefined;
                nonce?: `0x${string}` | undefined;
                value?: `0x${string}` | undefined;
                to: `0x${string}` | null;
                gasPrice?: undefined;
                maxFeePerGas?: `0x${string}` | undefined;
                maxPriorityFeePerGas?: `0x${string}` | undefined;
                maxFeePerBlobGas: `0x${string}`;
                accessList?: import("../../index.js").AccessList | undefined;
                blobs: readonly import("../../index.js").Hex[] | readonly import("../../index.js").ByteArray[];
                blobVersionedHashes?: readonly import("../../index.js").Hex[] | undefined;
                kzg?: import("../../index.js").Kzg | undefined;
                sidecars?: readonly import("../../index.js").BlobSidecar<import("../../index.js").Hex>[] | undefined;
                authorizationList?: undefined;
                eip712Meta?: undefined;
            } | {
                type?: "0x4" | undefined;
                gasPrice?: undefined;
                maxFeePerBlobGas?: undefined;
                maxFeePerGas?: `0x${string}` | undefined;
                maxPriorityFeePerGas?: `0x${string}` | undefined;
                to?: import("abitype").Address | null | undefined;
                data?: import("../../index.js").Hex | undefined;
                from?: import("abitype").Address | undefined;
                gas?: `0x${string}` | undefined;
                nonce?: `0x${string}` | undefined;
                value?: `0x${string}` | undefined;
                accessList?: import("../../index.js").AccessList | undefined;
                authorizationList?: import("../../experimental/index.js").RpcAuthorizationList | undefined;
                blobs?: undefined;
                blobVersionedHashes?: undefined;
                kzg?: undefined;
                sidecars?: undefined;
                eip712Meta?: undefined;
            } | {
                data?: import("../../index.js").Hex | undefined;
                from?: import("abitype").Address | undefined;
                gas?: `0x${string}` | undefined;
                nonce?: `0x${string}` | undefined;
                to?: import("abitype").Address | null | undefined;
                type: "0xff" | "0x71";
                value?: `0x${string}` | undefined;
                gasPrice?: undefined;
                maxFeePerBlobGas?: undefined;
                maxFeePerGas?: `0x${string}` | undefined;
                maxPriorityFeePerGas?: `0x${string}` | undefined;
                eip712Meta: import("../index.js").ZkSyncEip712Meta;
            }) & {
                paymaster: never;
                gasPerPubdata: never;
                factoryDeps: never;
                paymasterInput: never;
                customSignature: never;
            };
            type: "transactionRequest";
        };
    };
    serializers: {
        readonly transaction: typeof import("../../zksync/serializers.js").serializeTransaction;
    };
    readonly network: "zksync-era";
};
//# sourceMappingURL=zksync.d.ts.map