"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/SystemStatus.tsx":
/*!*****************************************!*\
  !*** ./src/components/SystemStatus.tsx ***!
  \*****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SystemStatus: () => (/* binding */ SystemStatus)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _barrel_optimize_names_Wifi_WifiOff_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! __barrel_optimize__?names=Wifi,WifiOff,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/wifi.js\");\n/* harmony import */ var _barrel_optimize_names_Wifi_WifiOff_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=Wifi,WifiOff,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/wifi-off.js\");\n/* harmony import */ var _barrel_optimize_names_Wifi_WifiOff_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Wifi,WifiOff,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/zap.js\");\n/* __next_internal_client_entry_do_not_use__ SystemStatus auto */ \n\n\nfunction SystemStatus(param) {\n    let { status } = param;\n    const getStatusIcon = ()=>{\n        switch(status){\n            case 'connected':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Wifi_WifiOff_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                    className: \"w-4 h-4 text-green-400\"\n                }, void 0, false, {\n                    fileName: \"F:\\\\AI chain\\\\chainsight-production\\\\src\\\\components\\\\SystemStatus.tsx\",\n                    lineNumber: 14,\n                    columnNumber: 16\n                }, this);\n            case 'disconnected':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Wifi_WifiOff_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                    className: \"w-4 h-4 text-red-400\"\n                }, void 0, false, {\n                    fileName: \"F:\\\\AI chain\\\\chainsight-production\\\\src\\\\components\\\\SystemStatus.tsx\",\n                    lineNumber: 16,\n                    columnNumber: 16\n                }, this);\n            case 'connecting':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Wifi_WifiOff_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                    className: \"w-4 h-4 text-yellow-400 animate-pulse\"\n                }, void 0, false, {\n                    fileName: \"F:\\\\AI chain\\\\chainsight-production\\\\src\\\\components\\\\SystemStatus.tsx\",\n                    lineNumber: 18,\n                    columnNumber: 16\n                }, this);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Wifi_WifiOff_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                    className: \"w-4 h-4 text-gray-400\"\n                }, void 0, false, {\n                    fileName: \"F:\\\\AI chain\\\\chainsight-production\\\\src\\\\components\\\\SystemStatus.tsx\",\n                    lineNumber: 20,\n                    columnNumber: 16\n                }, this);\n        }\n    };\n    const getStatusColor = ()=>{\n        switch(status){\n            case 'connected':\n                return 'text-green-400';\n            case 'disconnected':\n                return 'text-red-400';\n            case 'connecting':\n                return 'text-yellow-400';\n            default:\n                return 'text-gray-400';\n        }\n    };\n    const getStatusText = ()=>{\n        switch(status){\n            case 'connected':\n                return 'CONNECTED';\n            case 'disconnected':\n                return 'DISCONNECTED';\n            case 'connecting':\n                return 'CONNECTING...';\n            default:\n                return 'UNKNOWN';\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex items-center space-x-4\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                initial: {\n                    opacity: 0,\n                    scale: 0.9\n                },\n                animate: {\n                    opacity: 1,\n                    scale: 1\n                },\n                className: \"flex items-center space-x-2 glass rounded-full px-4 py-2\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                        animate: status === 'connected' ? {\n                            scale: [\n                                1,\n                                1.2,\n                                1\n                            ]\n                        } : {},\n                        transition: {\n                            duration: 2,\n                            repeat: Infinity\n                        },\n                        children: getStatusIcon()\n                    }, void 0, false, {\n                        fileName: \"F:\\\\AI chain\\\\chainsight-production\\\\src\\\\components\\\\SystemStatus.tsx\",\n                        lineNumber: 58,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"text-sm font-medium \".concat(getStatusColor()),\n                        children: getStatusText()\n                    }, void 0, false, {\n                        fileName: \"F:\\\\AI chain\\\\chainsight-production\\\\src\\\\components\\\\SystemStatus.tsx\",\n                        lineNumber: 64,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"F:\\\\AI chain\\\\chainsight-production\\\\src\\\\components\\\\SystemStatus.tsx\",\n                lineNumber: 53,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                initial: {\n                    opacity: 0,\n                    x: 20\n                },\n                animate: {\n                    opacity: 1,\n                    x: 0\n                },\n                transition: {\n                    delay: 0.1\n                },\n                className: \"bg-black/20 backdrop-blur-xl border border-white/10 rounded-full px-4 py-2\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    className: \"text-sm text-gray-400\",\n                    children: [\n                        \"Last: \",\n                        new Date().toLocaleTimeString()\n                    ]\n                }, void 0, true, {\n                    fileName: \"F:\\\\AI chain\\\\chainsight-production\\\\src\\\\components\\\\SystemStatus.tsx\",\n                    lineNumber: 76,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"F:\\\\AI chain\\\\chainsight-production\\\\src\\\\components\\\\SystemStatus.tsx\",\n                lineNumber: 70,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"F:\\\\AI chain\\\\chainsight-production\\\\src\\\\components\\\\SystemStatus.tsx\",\n        lineNumber: 51,\n        columnNumber: 5\n    }, this);\n}\n_c = SystemStatus;\nvar _c;\n$RefreshReg$(_c, \"SystemStatus\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/SystemStatus.tsx\n"));

/***/ })

});