'use client';

import { motion } from 'framer-motion';
import { MessageCircle, Users, Clock, CheckCircle } from 'lucide-react';

export function SupportModule() {
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="space-y-6"
    >
      <div className="bg-white/10 backdrop-blur-xl rounded-2xl p-6 border border-white/20">
        <div className="flex items-center space-x-3 mb-6">
          <div className="w-12 h-12 bg-gradient-to-r from-orange-400 to-red-500 rounded-xl flex items-center justify-center">
            <MessageCircle className="w-6 h-6 text-white" />
          </div>
          <div>
            <h2 className="text-2xl font-bold text-white">Customer Support</h2>
            <p className="text-gray-400">AI-powered customer service and support</p>
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          <div className="bg-black/20 rounded-xl p-4">
            <div className="flex items-center space-x-3 mb-3">
              <MessageCircle className="w-5 h-5 text-orange-400" />
              <span className="text-white font-medium">Active Tickets</span>
            </div>
            <p className="text-2xl font-bold text-orange-400">23</p>
            <p className="text-sm text-gray-400">Pending resolution</p>
          </div>

          <div className="bg-black/20 rounded-xl p-4">
            <div className="flex items-center space-x-3 mb-3">
              <Clock className="w-5 h-5 text-blue-400" />
              <span className="text-white font-medium">Avg Response</span>
            </div>
            <p className="text-2xl font-bold text-blue-400">2.3m</p>
            <p className="text-sm text-gray-400">Response time</p>
          </div>

          <div className="bg-black/20 rounded-xl p-4">
            <div className="flex items-center space-x-3 mb-3">
              <CheckCircle className="w-5 h-5 text-green-400" />
              <span className="text-white font-medium">Satisfaction</span>
            </div>
            <p className="text-2xl font-bold text-green-400">4.8/5</p>
            <p className="text-sm text-gray-400">Customer rating</p>
          </div>
        </div>

        <div className="mt-6 p-4 bg-gradient-to-r from-orange-500/20 to-red-500/20 rounded-xl border border-orange-500/30">
          <h3 className="text-white font-semibold mb-2">Support AI Insights</h3>
          <p className="text-gray-300 text-sm">
            Most common issues: Account access (32%), Payment processing (28%), Feature requests (21%). 
            AI chatbot successfully resolved 78% of inquiries without human intervention.
          </p>
        </div>
      </div>
    </motion.div>
  );
}
