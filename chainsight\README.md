# Chainsight by Connectouch

**Modular Fullstack AI Agent with Luxury Black UI and Real-time Interactivity**

## 🚀 Features

- **Crypto + Blockchain**: ERC20 creator, LP automation, contract deployer
- **Web3**: dApp interface, wallet connect, on-chain TX listener
- **AI Finance**: Predictive market analytics (stocks/crypto/forex)
- **Customer Support**: Functional chatbot UI, API-integrated ticket system
- **HR + Legal AI**: Resume/CV parser, contract reviewer
- **Design**: Generate animation (Lottie/SVG), live preview/export
- **Facial Recognition**: Real-time scan and attribute extraction
- **Premium UI**: Luxury black theme with gold highlights

## 🏗️ Architecture

```
chainsight/
├── apps/           # Applications (frontend/backend)
├── packages/       # Shared libraries (AI, crypto, finance)
├── plugins/        # Modular plugins (animation, chatbot, web3)
├── libs/           # Utilities (UI, API, DB)
└── deployments/    # Docker & deployment configs
```

## 🛠️ Tech Stack

- **Frontend**: Next.js + TailwindCSS + Framer Motion
- **Backend**: FastAPI (Python)
- **AI**: Transformers, scikit-learn, OpenAI API
- **Blockchain**: Ethers.js, Solidity, Hardhat
- **Real-time**: WebSocket
- **Database**: PostgreSQL + Redis

## 🚀 Quick Start

```bash
# Install dependencies
npm install

# Start development
npm run dev

# Build for production
npm run build

# Docker deployment
npm run docker:up
```

## 📦 Development

```bash
# Frontend only
cd apps/frontend && npm run dev

# Backend only
cd apps/backend && python main.py

# All services
npm run dev
```

## 🔧 Environment Setup

Copy `.env.example` to `.env` and configure your API keys and database connections.

## 📚 Documentation

- [Frontend Guide](./apps/frontend/README.md)
- [Backend API](./apps/backend/README.md)
- [Plugin Development](./plugins/README.md)

## 🤝 Contributing

1. Fork the repository
2. Create feature branch
3. Commit changes
4. Push to branch
5. Create Pull Request

## 📄 License

MIT License - see LICENSE file for details.
