{"compilerOptions": {"target": "es5", "lib": ["dom", "dom.iterable", "es6"], "allowJs": true, "skipLibCheck": true, "strict": true, "noEmit": true, "esModuleInterop": true, "module": "esnext", "moduleResolution": "bundler", "resolveJsonModule": true, "isolatedModules": true, "jsx": "preserve", "incremental": true, "plugins": [{"name": "next"}], "baseUrl": ".", "paths": {"@/*": ["./*"], "@/components/*": ["./components/*"], "@/hooks/*": ["./hooks/*"], "@/utils/*": ["./utils/*"], "@/styles/*": ["./styles/*"], "@/lib/*": ["./lib/*"], "@chainsight/ui-lib": ["../../libs/ui-lib/src"], "@chainsight/api-sdk": ["../../libs/api-sdk/src"]}}, "include": ["next-env.d.ts", "**/*.ts", "**/*.tsx", ".next/types/**/*.ts"], "exclude": ["node_modules"]}