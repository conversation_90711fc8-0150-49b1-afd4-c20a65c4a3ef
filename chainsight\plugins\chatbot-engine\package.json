{"name": "@chainsight/chatbot-engine", "version": "1.0.0", "description": "Intelligent chatbot engine plugin for customer support and AI interactions", "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"build": "tsc", "dev": "tsc --watch", "test": "jest", "lint": "eslint src/**/*.ts", "clean": "rm -rf dist"}, "dependencies": {"@chainsight/ai-core": "workspace:*", "openai": "^4.20.1", "natural": "^6.5.0", "compromise": "^14.10.0", "sentiment": "^5.0.2", "socket.io": "^4.7.2", "uuid": "^9.0.0"}, "devDependencies": {"@types/node": "^20.5.0", "@types/uuid": "^9.0.2", "typescript": "^5.1.6", "jest": "^29.6.2", "@types/jest": "^29.5.4", "eslint": "^8.47.0", "@typescript-eslint/eslint-plugin": "^6.4.0", "@typescript-eslint/parser": "^6.4.0"}, "keywords": ["chatbot", "ai", "customer-support", "nlp", "conversation", "chainsight"], "author": "Connectouch", "license": "MIT"}