"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_reown_appkit-ui_dist_esm_src_assets_svg_qr-code_js"],{

/***/ "(app-pages-browser)/./node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/qr-code.js":
/*!**************************************************************************!*\
  !*** ./node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/qr-code.js ***!
  \**************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   qrCodeIcon: () => (/* binding */ qrCodeIcon)\n/* harmony export */ });\n/* harmony import */ var lit__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! lit */ \"(app-pages-browser)/./node_modules/lit/index.js\");\n\nconst qrCodeIcon = (0,lit__WEBPACK_IMPORTED_MODULE_0__.svg) `<svg fill=\"none\" viewBox=\"0 0 20 20\">\n  <path\n    fill=\"currentColor\"\n    d=\"M3 6a3 3 0 0 1 3-3h1a1 1 0 1 0 0-2H6a5 5 0 0 0-5 5v1a1 1 0 0 0 2 0V6ZM13 1a1 1 0 1 0 0 2h1a3 3 0 0 1 3 3v1a1 1 0 1 0 2 0V6a5 5 0 0 0-5-5h-1ZM3 13a1 1 0 1 0-2 0v1a5 5 0 0 0 5 5h1a1 1 0 1 0 0-2H6a3 3 0 0 1-3-3v-1ZM19 13a1 1 0 1 0-2 0v1a3 3 0 0 1-3 3h-1a1 1 0 1 0 0 2h1.01a5 5 0 0 0 5-5v-1ZM5.3 6.36c-.04.2-.04.43-.04.89s0 .7.05.89c.14.52.54.92 1.06 1.06.19.05.42.05.89.05.46 0 .7 0 .88-.05A1.5 1.5 0 0 0 9.2 8.14c.06-.2.06-.43.06-.89s0-.7-.06-.89A1.5 1.5 0 0 0 8.14 5.3c-.19-.05-.42-.05-.88-.05-.47 0-.7 0-.9.05a1.5 1.5 0 0 0-1.05 1.06ZM10.8 6.36c-.04.2-.04.43-.04.89s0 .7.05.89c.14.52.54.92 1.06 1.06.19.05.42.05.89.05.46 0 .7 0 .88-.05a1.5 1.5 0 0 0 1.06-1.06c.06-.2.06-.43.06-.89s0-.7-.06-.89a1.5 1.5 0 0 0-1.06-1.06c-.19-.05-.42-.05-.88-.05-.47 0-.7 0-.9.05a1.5 1.5 0 0 0-1.05 1.06ZM5.26 12.75c0-.46 0-.7.05-.89a1.5 1.5 0 0 1 1.06-1.06c.19-.05.42-.05.89-.05.46 0 .7 0 .88.05.52.14.93.54 1.06 1.06.06.2.06.43.06.89s0 .7-.06.89a1.5 1.5 0 0 1-1.06 1.06c-.19.05-.42.05-.88.05-.47 0-.7 0-.9-.05a1.5 1.5 0 0 1-1.05-1.06c-.05-.2-.05-.43-.05-.89ZM10.8 11.86c-.04.2-.04.43-.04.89s0 .7.05.89c.14.52.54.92 1.06 1.06.19.05.42.05.89.05.46 0 .7 0 .88-.05a1.5 1.5 0 0 0 1.06-1.06c.06-.2.06-.43.06-.89s0-.7-.06-.89a1.5 1.5 0 0 0-1.06-1.06c-.19-.05-.42-.05-.88-.05-.47 0-.7 0-.9.05a1.5 1.5 0 0 0-1.05 1.06Z\"\n  />\n</svg>`;\n//# sourceMappingURL=qr-code.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/qr-code.js\n"));

/***/ })

}]);