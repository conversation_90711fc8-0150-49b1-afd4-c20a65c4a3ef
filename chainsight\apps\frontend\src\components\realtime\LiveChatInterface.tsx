'use client';

import React, { useState, useRef, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Send, Bot, User, Loader2, MessageCircle, Minimize2, Maximize2 } from 'lucide-react';
import { useChatStream } from '@/hooks/useWebSocket';

interface Message {
  id: string;
  content: string;
  role: 'user' | 'assistant';
  timestamp: number;
  sessionId?: string;
  confidence?: number;
  intent?: string;
}

interface LiveChatInterfaceProps {
  sessionId?: string;
  placeholder?: string;
  maxHeight?: number;
  showConfidence?: boolean;
  minimizable?: boolean;
  autoFocus?: boolean;
}

const LiveChatInterface: React.FC<LiveChatInterfaceProps> = ({
  sessionId = 'default',
  placeholder = 'Ask me anything about crypto, finance, design, or documents...',
  maxHeight = 500,
  showConfidence = false,
  minimizable = true,
  autoFocus = true
}) => {
  const { connected, messages, sendMessage, clearMessages } = useChatStream();
  const [inputValue, setInputValue] = useState('');
  const [isMinimized, setIsMinimized] = useState(false);
  const [isTyping, setIsTyping] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);

  // Auto-scroll to bottom when new messages arrive
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [messages]);

  // Auto-focus input when connected
  useEffect(() => {
    if (connected && autoFocus && !isMinimized) {
      inputRef.current?.focus();
    }
  }, [connected, autoFocus, isMinimized]);

  // Simulate typing indicator
  useEffect(() => {
    const lastMessage = messages[messages.length - 1];
    if (lastMessage && lastMessage.role === 'user') {
      setIsTyping(true);
      const timeout = setTimeout(() => setIsTyping(false), 2000);
      return () => clearTimeout(timeout);
    }
  }, [messages]);

  const handleSendMessage = () => {
    if (!inputValue.trim() || !connected) return;

    sendMessage(inputValue, sessionId);
    setInputValue('');
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  const formatTime = (timestamp: number): string => {
    return new Date(timestamp).toLocaleTimeString([], { 
      hour: '2-digit', 
      minute: '2-digit' 
    });
  };

  const getConfidenceColor = (confidence: number): string => {
    if (confidence >= 0.8) return 'text-green-400';
    if (confidence >= 0.6) return 'text-yellow-400';
    return 'text-red-400';
  };

  if (isMinimized) {
    return (
      <motion.div
        initial={{ scale: 0 }}
        animate={{ scale: 1 }}
        className="fixed bottom-4 right-4 z-50"
      >
        <button
          onClick={() => setIsMinimized(false)}
          className="bg-gradient-to-r from-yellow-400 to-yellow-600 text-black p-3 rounded-full shadow-lg hover:shadow-xl transition-all duration-200"
        >
          <MessageCircle className="w-6 h-6" />
          {messages.length > 0 && (
            <div className="absolute -top-2 -right-2 bg-red-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center">
              {messages.length}
            </div>
          )}
        </button>
      </motion.div>
    );
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="bg-gradient-to-br from-gray-900 to-black border border-yellow-400/20 rounded-xl shadow-luxury overflow-hidden"
      style={{ maxHeight }}
    >
      {/* Header */}
      <div className="bg-gray-800/50 border-b border-gray-700 p-4 flex items-center justify-between">
        <div className="flex items-center space-x-2">
          <Bot className="w-5 h-5 text-yellow-400" />
          <span className="font-semibold text-white">Chainsight AI Assistant</span>
          {connected ? (
            <div className="flex items-center space-x-1 text-green-400">
              <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse" />
              <span className="text-xs">Online</span>
            </div>
          ) : (
            <div className="flex items-center space-x-1 text-red-400">
              <div className="w-2 h-2 bg-red-400 rounded-full" />
              <span className="text-xs">Offline</span>
            </div>
          )}
        </div>
        
        <div className="flex items-center space-x-2">
          <button
            onClick={clearMessages}
            className="text-gray-400 hover:text-white text-sm px-2 py-1 rounded transition-colors"
          >
            Clear
          </button>
          {minimizable && (
            <button
              onClick={() => setIsMinimized(true)}
              className="text-gray-400 hover:text-white p-1 rounded transition-colors"
            >
              <Minimize2 className="w-4 h-4" />
            </button>
          )}
        </div>
      </div>

      {/* Messages */}
      <div className="flex-1 overflow-y-auto p-4 space-y-4" style={{ maxHeight: maxHeight - 120 }}>
        <AnimatePresence>
          {messages.map((message, index) => (
            <motion.div
              key={`${message.timestamp}-${index}`}
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -10 }}
              className={`flex ${message.role === 'user' ? 'justify-end' : 'justify-start'}`}
            >
              <div className={`max-w-[80%] ${message.role === 'user' ? 'order-2' : 'order-1'}`}>
                <div
                  className={`rounded-lg p-3 ${
                    message.role === 'user'
                      ? 'bg-yellow-400 text-black'
                      : 'bg-gray-800 text-white border border-gray-700'
                  }`}
                >
                  <div className="text-sm">{message.content}</div>
                  
                  {/* Message metadata */}
                  <div className="flex items-center justify-between mt-2 text-xs opacity-70">
                    <span>{formatTime(message.timestamp)}</span>
                    {message.role === 'assistant' && showConfidence && message.confidence && (
                      <span className={getConfidenceColor(message.confidence)}>
                        {(message.confidence * 100).toFixed(0)}% confidence
                      </span>
                    )}
                  </div>
                </div>
                
                {/* Avatar */}
                <div className={`flex items-center mt-1 ${message.role === 'user' ? 'justify-end' : 'justify-start'}`}>
                  <div className={`w-6 h-6 rounded-full flex items-center justify-center ${
                    message.role === 'user' ? 'bg-yellow-400 text-black' : 'bg-gray-700 text-yellow-400'
                  }`}>
                    {message.role === 'user' ? <User className="w-3 h-3" /> : <Bot className="w-3 h-3" />}
                  </div>
                </div>
              </div>
            </motion.div>
          ))}
        </AnimatePresence>

        {/* Typing Indicator */}
        <AnimatePresence>
          {isTyping && (
            <motion.div
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -10 }}
              className="flex justify-start"
            >
              <div className="bg-gray-800 border border-gray-700 rounded-lg p-3 max-w-[80%]">
                <div className="flex items-center space-x-2">
                  <div className="flex space-x-1">
                    <div className="w-2 h-2 bg-yellow-400 rounded-full animate-bounce" />
                    <div className="w-2 h-2 bg-yellow-400 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }} />
                    <div className="w-2 h-2 bg-yellow-400 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }} />
                  </div>
                  <span className="text-gray-400 text-sm">AI is thinking...</span>
                </div>
              </div>
            </motion.div>
          )}
        </AnimatePresence>

        <div ref={messagesEndRef} />
      </div>

      {/* Input */}
      <div className="border-t border-gray-700 p-4">
        <div className="flex items-center space-x-2">
          <input
            ref={inputRef}
            type="text"
            value={inputValue}
            onChange={(e) => setInputValue(e.target.value)}
            onKeyPress={handleKeyPress}
            placeholder={connected ? placeholder : 'Connecting...'}
            disabled={!connected}
            className="flex-1 bg-gray-800 border border-gray-700 rounded-lg px-3 py-2 text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-yellow-400 focus:border-yellow-400 disabled:opacity-50"
          />
          <button
            onClick={handleSendMessage}
            disabled={!connected || !inputValue.trim()}
            className="bg-yellow-400 text-black p-2 rounded-lg hover:bg-yellow-500 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {isTyping ? (
              <Loader2 className="w-5 h-5 animate-spin" />
            ) : (
              <Send className="w-5 h-5" />
            )}
          </button>
        </div>
        
        {!connected && (
          <div className="text-red-400 text-xs mt-2">
            Connection lost. Trying to reconnect...
          </div>
        )}
      </div>
    </motion.div>
  );
};

export default LiveChatInterface;
