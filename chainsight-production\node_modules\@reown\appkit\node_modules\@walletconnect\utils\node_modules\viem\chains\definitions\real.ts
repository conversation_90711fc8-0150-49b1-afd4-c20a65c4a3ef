import { define<PERSON>hain } from '../../utils/chain/defineChain.js'

export const real = /*#__PURE__*/ defineChain({
  id: 111188,
  name: 're.al',
  nativeCurrency: {
    name: 'reETH',
    decimals: 18,
    symbol: 'reETH',
  },
  rpcUrls: {
    default: { http: ['https://real.drpc.org'] },
  },
  blockExplorers: {
    default: {
      name: 're.al Explorer',
      url: 'https://explorer.re.al',
      apiUrl: 'https://explorer.re.al/api/v2',
    },
  },
  contracts: {
    multicall3: {
      address: '******************************************',
      blockCreated: 695,
    },
  },
})
