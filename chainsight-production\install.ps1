# ===================================
# CHAINSIGHT INSTALLATION SCRIPT
# ===================================

Write-Host "🚀 CHAINSIGHT INSTALLATION SCRIPT" -ForegroundColor Cyan
Write-Host "===================================" -ForegroundColor Cyan

# Check Node.js
Write-Host "`n📦 Checking Node.js..." -ForegroundColor Yellow
try {
    $nodeVersion = node --version
    Write-Host "✅ Node.js found: $nodeVersion" -ForegroundColor Green
    
    if ([version]($nodeVersion -replace 'v', '') -lt [version]"18.0.0") {
        Write-Host "❌ Node.js 18.0.0+ required. Please upgrade." -ForegroundColor Red
        exit 1
    }
} catch {
    Write-Host "❌ Node.js not found. Please install Node.js 18+" -ForegroundColor Red
    Write-Host "   Download from: https://nodejs.org/" -ForegroundColor Yellow
    exit 1
}

# Check Python
Write-Host "`n🐍 Checking Python..." -ForegroundColor Yellow
try {
    $pythonVersion = python --version
    Write-Host "✅ Python found: $pythonVersion" -ForegroundColor Green
} catch {
    Write-Host "❌ Python not found. Please install Python 3.9+" -ForegroundColor Red
    Write-Host "   Download from: https://python.org/" -ForegroundColor Yellow
    exit 1
}

# Install Frontend Dependencies
Write-Host "`n📦 Installing Frontend Dependencies..." -ForegroundColor Yellow
Write-Host "This may take a few minutes..." -ForegroundColor Gray

try {
    npm install
    Write-Host "✅ Frontend dependencies installed successfully!" -ForegroundColor Green
} catch {
    Write-Host "❌ Failed to install frontend dependencies" -ForegroundColor Red
    exit 1
}

# Check if backend requirements exist
if (Test-Path "backend-requirements.txt") {
    Write-Host "`n🐍 Installing Backend Dependencies..." -ForegroundColor Yellow
    Write-Host "Creating Python virtual environment..." -ForegroundColor Gray
    
    try {
        python -m venv venv
        Write-Host "✅ Virtual environment created" -ForegroundColor Green
        
        # Activate virtual environment
        if (Test-Path "venv\Scripts\activate.ps1") {
            & "venv\Scripts\activate.ps1"
            Write-Host "✅ Virtual environment activated" -ForegroundColor Green
        }
        
        # Install Python dependencies
        pip install -r backend-requirements.txt
        Write-Host "✅ Backend dependencies installed successfully!" -ForegroundColor Green
        
    } catch {
        Write-Host "❌ Failed to install backend dependencies" -ForegroundColor Red
        Write-Host "   You can install manually with:" -ForegroundColor Yellow
        Write-Host "   python -m venv venv" -ForegroundColor Gray
        Write-Host "   venv\Scripts\activate" -ForegroundColor Gray
        Write-Host "   pip install -r backend-requirements.txt" -ForegroundColor Gray
    }
}

# Create .env template
Write-Host "`n⚙️ Creating environment template..." -ForegroundColor Yellow
$envTemplate = @"
# ===================================
# CHAINSIGHT ENVIRONMENT VARIABLES
# ===================================

# OpenAI API (Required for AI features)
OPENAI_API_KEY=your_openai_api_key_here

# Database (Optional - uses SQLite by default)
DATABASE_URL=postgresql://user:password@localhost:5432/chainsight
REDIS_URL=redis://localhost:6379

# Security (Generate secure keys)
JWT_SECRET=your_jwt_secret_here
ENCRYPTION_KEY=your_encryption_key_here

# Finance APIs (Optional)
ALPHA_VANTAGE_API_KEY=your_alpha_vantage_key
YAHOO_FINANCE_API_KEY=your_yahoo_finance_key

# Blockchain APIs (Optional)
INFURA_PROJECT_ID=your_infura_project_id
ALCHEMY_API_KEY=your_alchemy_api_key
ETHERSCAN_API_KEY=your_etherscan_api_key

# Email (Optional)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASSWORD=your_app_password

# Development
NODE_ENV=development
DEBUG=true
"@

if (-not (Test-Path ".env")) {
    $envTemplate | Out-File -FilePath ".env" -Encoding UTF8
    Write-Host "✅ .env template created" -ForegroundColor Green
    Write-Host "   Please edit .env file with your API keys" -ForegroundColor Yellow
} else {
    Write-Host "✅ .env file already exists" -ForegroundColor Green
}

# Installation Summary
Write-Host "`n🎉 INSTALLATION COMPLETE!" -ForegroundColor Green
Write-Host "=========================" -ForegroundColor Green

Write-Host "`n📋 NEXT STEPS:" -ForegroundColor Cyan
Write-Host "1. Edit .env file with your API keys" -ForegroundColor White
Write-Host "2. Start the development server:" -ForegroundColor White
Write-Host "   npm run dev" -ForegroundColor Gray
Write-Host "3. Open http://localhost:3001 in your browser" -ForegroundColor White

Write-Host "`n🔧 OPTIONAL SETUP:" -ForegroundColor Cyan
Write-Host "• Install PostgreSQL for production database" -ForegroundColor White
Write-Host "• Install Redis for caching and real-time features" -ForegroundColor White
Write-Host "• Get API keys for full functionality:" -ForegroundColor White
Write-Host "  - OpenAI API (AI features)" -ForegroundColor Gray
Write-Host "  - Alpha Vantage (Finance data)" -ForegroundColor Gray
Write-Host "  - Infura/Alchemy (Blockchain)" -ForegroundColor Gray

Write-Host "`n🚀 START DEVELOPMENT:" -ForegroundColor Cyan
Write-Host "npm run dev" -ForegroundColor Yellow

Write-Host "`n📚 DOCUMENTATION:" -ForegroundColor Cyan
Write-Host "See SYSTEM_REQUIREMENTS.md for detailed setup" -ForegroundColor White

Write-Host "`n✨ Happy coding with Chainsight!" -ForegroundColor Magenta
