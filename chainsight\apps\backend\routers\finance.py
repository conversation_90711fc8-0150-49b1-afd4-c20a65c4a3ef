from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session
from pydantic import BaseModel
from typing import List, Dict, Any
import yfinance as yf
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import json

from db import get_db, MarketPrediction, User
from routers.auth import get_current_user

router = APIRouter()

# Pydantic models
class PredictionRequest(BaseModel):
    symbol: str
    prediction_type: str  # 'price', 'trend', 'volatility'
    days_ahead: int = 7

class PredictionResponse(BaseModel):
    symbol: str
    prediction_type: str
    predicted_value: float
    confidence_score: float
    target_date: datetime
    analysis: Dict[str, Any]

class MarketAnalysis(BaseModel):
    symbol: str
    current_price: float
    trend: str
    volatility: float
    support_levels: List[float]
    resistance_levels: List[float]
    recommendation: str

@router.post("/predict", response_model=PredictionResponse)
async def create_prediction(
    request: PredictionRequest,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Create a market prediction using AI models"""
    try:
        # Get historical data
        ticker = yf.Ticker(request.symbol)
        hist = ticker.history(period="1y")
        
        if hist.empty:
            raise HTTPException(status_code=404, detail="Symbol not found")
        
        # Generate prediction based on type
        if request.prediction_type == "price":
            predicted_value, confidence = predict_price(hist, request.days_ahead)
        elif request.prediction_type == "trend":
            predicted_value, confidence = predict_trend(hist, request.days_ahead)
        elif request.prediction_type == "volatility":
            predicted_value, confidence = predict_volatility(hist, request.days_ahead)
        else:
            raise HTTPException(status_code=400, detail="Invalid prediction type")
        
        # Save prediction to database
        target_date = datetime.utcnow() + timedelta(days=request.days_ahead)
        prediction = MarketPrediction(
            symbol=request.symbol,
            prediction_type=request.prediction_type,
            predicted_value=predicted_value,
            confidence_score=confidence,
            target_date=target_date
        )
        db.add(prediction)
        db.commit()
        
        # Generate analysis
        analysis = generate_analysis(hist, request.symbol)
        
        return PredictionResponse(
            symbol=request.symbol,
            prediction_type=request.prediction_type,
            predicted_value=predicted_value,
            confidence_score=confidence,
            target_date=target_date,
            analysis=analysis
        )
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Prediction failed: {str(e)}")

def predict_price(hist: pd.DataFrame, days_ahead: int) -> tuple:
    """Predict future price using simple moving average and trend analysis"""
    prices = hist['Close'].values
    
    # Calculate moving averages
    ma_short = np.mean(prices[-10:])  # 10-day MA
    ma_long = np.mean(prices[-30:])   # 30-day MA
    
    # Calculate trend
    trend = (ma_short - ma_long) / ma_long
    
    # Simple prediction: current price + trend * days
    current_price = prices[-1]
    predicted_price = current_price * (1 + trend * days_ahead / 30)
    
    # Confidence based on volatility (lower volatility = higher confidence)
    volatility = np.std(prices[-30:]) / np.mean(prices[-30:])
    confidence = max(0.5, 1 - volatility * 2)
    
    return float(predicted_price), float(confidence)

def predict_trend(hist: pd.DataFrame, days_ahead: int) -> tuple:
    """Predict trend direction (1 = up, 0 = sideways, -1 = down)"""
    prices = hist['Close'].values
    
    # Calculate price momentum
    momentum = (prices[-1] - prices[-10]) / prices[-10]
    
    # Calculate volume trend
    volumes = hist['Volume'].values
    volume_trend = (np.mean(volumes[-5:]) - np.mean(volumes[-15:-5])) / np.mean(volumes[-15:-5])
    
    # Combine signals
    if momentum > 0.02 and volume_trend > 0:
        trend = 1  # Bullish
        confidence = 0.8
    elif momentum < -0.02 and volume_trend > 0:
        trend = -1  # Bearish
        confidence = 0.8
    else:
        trend = 0  # Sideways
        confidence = 0.6
    
    return float(trend), float(confidence)

def predict_volatility(hist: pd.DataFrame, days_ahead: int) -> tuple:
    """Predict future volatility"""
    prices = hist['Close'].values
    returns = np.diff(np.log(prices))
    
    # Calculate historical volatility
    current_vol = np.std(returns[-30:]) * np.sqrt(252)  # Annualized
    
    # Simple volatility prediction (mean reversion)
    long_term_vol = np.std(returns) * np.sqrt(252)
    predicted_vol = current_vol * 0.7 + long_term_vol * 0.3
    
    confidence = 0.7  # Volatility is harder to predict
    
    return float(predicted_vol), float(confidence)

def generate_analysis(hist: pd.DataFrame, symbol: str) -> Dict[str, Any]:
    """Generate comprehensive market analysis"""
    prices = hist['Close'].values
    volumes = hist['Volume'].values
    
    current_price = prices[-1]
    
    # Support and resistance levels
    highs = hist['High'].rolling(window=20).max().dropna()
    lows = hist['Low'].rolling(window=20).min().dropna()
    
    resistance_levels = sorted(highs.tail(10).unique(), reverse=True)[:3]
    support_levels = sorted(lows.tail(10).unique())[:3]
    
    # Technical indicators
    sma_20 = np.mean(prices[-20:])
    sma_50 = np.mean(prices[-50:])
    
    # RSI calculation
    delta = np.diff(prices)
    gain = np.where(delta > 0, delta, 0)
    loss = np.where(delta < 0, -delta, 0)
    avg_gain = np.mean(gain[-14:])
    avg_loss = np.mean(loss[-14:])
    rs = avg_gain / avg_loss if avg_loss != 0 else 100
    rsi = 100 - (100 / (1 + rs))
    
    return {
        "current_price": float(current_price),
        "sma_20": float(sma_20),
        "sma_50": float(sma_50),
        "rsi": float(rsi),
        "support_levels": [float(x) for x in support_levels],
        "resistance_levels": [float(x) for x in resistance_levels],
        "volume_trend": "increasing" if np.mean(volumes[-5:]) > np.mean(volumes[-15:-5]) else "decreasing",
        "volatility": float(np.std(prices[-30:]) / np.mean(prices[-30:])),
        "recommendation": get_recommendation(current_price, sma_20, sma_50, rsi)
    }

def get_recommendation(price: float, sma_20: float, sma_50: float, rsi: float) -> str:
    """Generate trading recommendation"""
    if price > sma_20 > sma_50 and rsi < 70:
        return "BUY"
    elif price < sma_20 < sma_50 and rsi > 30:
        return "SELL"
    elif rsi > 80:
        return "OVERBOUGHT"
    elif rsi < 20:
        return "OVERSOLD"
    else:
        return "HOLD"

@router.get("/analyze/{symbol}", response_model=MarketAnalysis)
async def analyze_market(symbol: str):
    """Get comprehensive market analysis for a symbol"""
    try:
        ticker = yf.Ticker(symbol)
        hist = ticker.history(period="6mo")
        
        if hist.empty:
            raise HTTPException(status_code=404, detail="Symbol not found")
        
        analysis = generate_analysis(hist, symbol)
        
        return MarketAnalysis(
            symbol=symbol,
            current_price=analysis["current_price"],
            trend="bullish" if analysis["sma_20"] > analysis["sma_50"] else "bearish",
            volatility=analysis["volatility"],
            support_levels=analysis["support_levels"],
            resistance_levels=analysis["resistance_levels"],
            recommendation=analysis["recommendation"]
        )
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Analysis failed: {str(e)}")

@router.get("/predictions")
async def get_predictions(
    symbol: str = None,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Get historical predictions"""
    query = db.query(MarketPrediction)
    
    if symbol:
        query = query.filter(MarketPrediction.symbol == symbol)
    
    predictions = query.order_by(MarketPrediction.prediction_date.desc()).limit(50).all()
    
    return {"predictions": [
        {
            "id": p.id,
            "symbol": p.symbol,
            "prediction_type": p.prediction_type,
            "predicted_value": p.predicted_value,
            "confidence_score": p.confidence_score,
            "prediction_date": p.prediction_date,
            "target_date": p.target_date,
            "actual_value": p.actual_value
        }
        for p in predictions
    ]}

@router.get("/portfolio-analysis")
async def analyze_portfolio(
    symbols: str,  # Comma-separated symbols
    current_user: User = Depends(get_current_user)
):
    """Analyze a portfolio of assets"""
    try:
        symbol_list = [s.strip().upper() for s in symbols.split(',')]
        portfolio_analysis = {}
        
        for symbol in symbol_list:
            ticker = yf.Ticker(symbol)
            hist = ticker.history(period="3mo")
            
            if not hist.empty:
                analysis = generate_analysis(hist, symbol)
                portfolio_analysis[symbol] = analysis
        
        # Calculate portfolio metrics
        total_value = sum(a["current_price"] for a in portfolio_analysis.values())
        avg_volatility = np.mean([a["volatility"] for a in portfolio_analysis.values()])
        
        return {
            "portfolio": portfolio_analysis,
            "summary": {
                "total_symbols": len(portfolio_analysis),
                "total_value": total_value,
                "average_volatility": avg_volatility,
                "diversification_score": min(1.0, len(portfolio_analysis) / 10)
            }
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Portfolio analysis failed: {str(e)}")
