from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session
from pydantic import BaseModel
from typing import List, Optional
import openai
import os
from datetime import datetime

from db import get_db, ChatMessage, User
from routers.auth import get_current_user

router = APIRouter()

# OpenAI setup
openai.api_key = os.getenv("OPENAI_API_KEY")

# Pydantic models
class ChatRequest(BaseModel):
    message: str
    session_id: Optional[str] = None

class ChatResponse(BaseModel):
    response: str
    session_id: str
    timestamp: datetime

class ChatHistory(BaseModel):
    id: int
    message: str
    response: str
    timestamp: datetime

@router.post("/send", response_model=ChatResponse)
async def send_message(
    chat_request: ChatRequest,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Send a message to the AI assistant"""
    try:
        # Generate session ID if not provided
        session_id = chat_request.session_id or f"session_{current_user.id}_{int(datetime.now().timestamp())}"
        
        # Get AI response
        ai_response = await get_ai_response(chat_request.message, current_user.username)
        
        # Save to database
        chat_message = ChatMessage(
            user_id=current_user.id,
            message=chat_request.message,
            response=ai_response,
            session_id=session_id
        )
        db.add(chat_message)
        db.commit()
        
        return ChatResponse(
            response=ai_response,
            session_id=session_id,
            timestamp=datetime.utcnow()
        )
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Chat failed: {str(e)}")

async def get_ai_response(message: str, username: str) -> str:
    """Generate AI response using OpenAI or fallback responses"""
    try:
        # Try OpenAI first
        if openai.api_key and openai.api_key != "your_openai_api_key_here":
            response = openai.ChatCompletion.create(
                model="gpt-3.5-turbo",
                messages=[
                    {
                        "role": "system",
                        "content": f"You are Chainsight AI, a luxury AI assistant for {username}. You specialize in blockchain, crypto, finance, design, HR, and legal analysis. Provide helpful, accurate, and professional responses."
                    },
                    {
                        "role": "user",
                        "content": message
                    }
                ],
                max_tokens=500,
                temperature=0.7
            )
            return response.choices[0].message.content
        else:
            # Fallback intelligent responses
            return generate_fallback_response(message)
            
    except Exception as e:
        print(f"OpenAI error: {e}")
        return generate_fallback_response(message)

def generate_fallback_response(message: str) -> str:
    """Generate intelligent fallback responses"""
    message_lower = message.lower()
    
    # Crypto/Blockchain responses
    if any(word in message_lower for word in ['crypto', 'bitcoin', 'ethereum', 'token', 'blockchain']):
        return "I can help you with cryptocurrency analysis and blockchain operations. Current market conditions show strong momentum in DeFi and Layer 2 solutions. Would you like me to analyze specific tokens or help you deploy a smart contract?"
    
    # Finance responses
    elif any(word in message_lower for word in ['stock', 'market', 'finance', 'trading', 'investment']):
        return "Based on current market analysis, I'm seeing increased volatility in tech stocks and growing interest in sustainable investments. I can provide detailed predictions and risk assessments for your portfolio. What specific assets would you like me to analyze?"
    
    # Design responses
    elif any(word in message_lower for word in ['design', 'animation', 'lottie', 'svg', 'ui', 'ux']):
        return "I can help you create stunning animations and design assets. Our design studio supports Lottie animations, SVG graphics, and real-time previews. Would you like me to generate a custom animation or help with UI design?"
    
    # HR/Legal responses
    elif any(word in message_lower for word in ['resume', 'cv', 'contract', 'legal', 'hr', 'hiring']):
        return "I can analyze resumes, review legal contracts, and provide HR insights. My AI can extract key information from documents and identify potential risks or opportunities. What document would you like me to review?"
    
    # Face recognition responses
    elif any(word in message_lower for word in ['face', 'facial', 'recognition', 'emotion', 'age']):
        return "Our facial recognition system can detect age, emotions, and various facial attributes with high accuracy. The system processes images in real-time and provides detailed analysis. Would you like to start a facial analysis session?"
    
    # General greeting
    elif any(word in message_lower for word in ['hello', 'hi', 'hey', 'greetings']):
        return "Hello! I'm your Chainsight AI assistant. I can help you with cryptocurrency analysis, market predictions, smart contract deployment, design creation, document analysis, and much more. What would you like to explore today?"
    
    # Default response
    else:
        return "I understand you're looking for assistance. As your Chainsight AI, I can help with blockchain operations, financial analysis, design creation, document review, and real-time market insights. Could you provide more details about what you'd like to accomplish?"

@router.get("/history", response_model=List[ChatHistory])
async def get_chat_history(
    session_id: Optional[str] = None,
    limit: int = 50,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Get chat history for the current user"""
    query = db.query(ChatMessage).filter(ChatMessage.user_id == current_user.id)
    
    if session_id:
        query = query.filter(ChatMessage.session_id == session_id)
    
    messages = query.order_by(ChatMessage.timestamp.desc()).limit(limit).all()
    
    return [
        ChatHistory(
            id=msg.id,
            message=msg.message,
            response=msg.response,
            timestamp=msg.timestamp
        )
        for msg in messages
    ]

@router.delete("/history/{session_id}")
async def delete_chat_session(
    session_id: str,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Delete a chat session"""
    deleted_count = db.query(ChatMessage).filter(
        ChatMessage.user_id == current_user.id,
        ChatMessage.session_id == session_id
    ).delete()
    
    db.commit()
    
    return {"message": f"Deleted {deleted_count} messages from session {session_id}"}

@router.get("/sessions")
async def get_chat_sessions(
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Get all chat sessions for the current user"""
    sessions = db.query(ChatMessage.session_id).filter(
        ChatMessage.user_id == current_user.id
    ).distinct().all()
    
    return {"sessions": [session[0] for session in sessions]}
