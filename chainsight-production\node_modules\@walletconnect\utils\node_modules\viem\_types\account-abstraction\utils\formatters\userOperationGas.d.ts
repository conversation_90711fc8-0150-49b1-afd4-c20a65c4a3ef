import type { ErrorType } from '../../../errors/utils.js';
import type { RpcEstimateUserOperationGasReturnType } from '../../types/rpc.js';
import type { EstimateUserOperationGasReturnType } from '../../types/userOperation.js';
export type FormatUserOperationGasErrorType = ErrorType;
export declare function formatUserOperationGas(parameters: RpcEstimateUserOperationGasReturnType): EstimateUserOperationGasReturnType;
//# sourceMappingURL=userOperationGas.d.ts.map