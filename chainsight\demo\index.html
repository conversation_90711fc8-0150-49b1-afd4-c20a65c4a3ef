<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Chainsight by Connectouch - Live Demo</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdn.socket.io/4.7.0/socket.io.min.js"></script>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Orbitron:wght@400;700;900&family=Rajdhani:wght@300;400;500;600;700&display=swap');

        :root {
            --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            --secondary-gradient: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            --accent-gradient: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            --success-gradient: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
            --warning-gradient: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
            --dark-gradient: linear-gradient(135deg, #0c0c0c 0%, #1a1a2e 50%, #16213e 100%);
            --glass-bg: rgba(255, 255, 255, 0.1);
            --glass-border: rgba(255, 255, 255, 0.2);
            --neon-blue: #00d4ff;
            --neon-purple: #b537f2;
            --neon-pink: #ff006e;
            --neon-green: #39ff14;
            --neon-orange: #ff8c00;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            background: var(--dark-gradient);
            color: #ffffff;
            font-family: 'Rajdhani', sans-serif;
            overflow-x: hidden;
            position: relative;
        }

        body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background:
                radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.3) 0%, transparent 50%),
                radial-gradient(circle at 40% 40%, rgba(120, 219, 255, 0.2) 0%, transparent 50%);
            pointer-events: none;
            z-index: -1;
        }

        .orbitron { font-family: 'Orbitron', monospace; }

        .gradient-text {
            background: var(--accent-gradient);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .glass-effect {
            background: var(--glass-bg);
            backdrop-filter: blur(20px);
            border: 1px solid var(--glass-border);
            box-shadow:
                0 8px 32px rgba(0, 0, 0, 0.3),
                inset 0 1px 0 rgba(255, 255, 255, 0.1);
        }

        .neon-glow {
            box-shadow:
                0 0 5px currentColor,
                0 0 10px currentColor,
                0 0 15px currentColor,
                0 0 20px currentColor;
        }

        .pulse-rainbow {
            animation: pulse-rainbow 3s infinite;
        }

        @keyframes pulse-rainbow {
            0% {
                box-shadow: 0 0 5px var(--neon-blue), 0 0 10px var(--neon-blue), 0 0 15px var(--neon-blue);
                transform: scale(1);
            }
            25% {
                box-shadow: 0 0 5px var(--neon-purple), 0 0 10px var(--neon-purple), 0 0 15px var(--neon-purple);
                transform: scale(1.02);
            }
            50% {
                box-shadow: 0 0 5px var(--neon-pink), 0 0 10px var(--neon-pink), 0 0 15px var(--neon-pink);
                transform: scale(1.05);
            }
            75% {
                box-shadow: 0 0 5px var(--neon-green), 0 0 10px var(--neon-green), 0 0 15px var(--neon-green);
                transform: scale(1.02);
            }
            100% {
                box-shadow: 0 0 5px var(--neon-blue), 0 0 10px var(--neon-blue), 0 0 15px var(--neon-blue);
                transform: scale(1);
            }
        }

        .floating {
            animation: floating 6s ease-in-out infinite;
        }

        @keyframes floating {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-10px); }
        }

        .slide-in {
            animation: slideIn 0.8s ease-out;
        }

        @keyframes slideIn {
            from { opacity: 0; transform: translateY(30px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .btn-futuristic {
            background: var(--primary-gradient);
            border: none;
            padding: 12px 24px;
            border-radius: 25px;
            color: white;
            font-weight: 600;
            font-family: 'Orbitron', monospace;
            text-transform: uppercase;
            letter-spacing: 1px;
            cursor: pointer;
            position: relative;
            overflow: hidden;
            transition: all 0.3s ease;
            box-shadow:
                0 4px 15px rgba(102, 126, 234, 0.4),
                inset 0 1px 0 rgba(255, 255, 255, 0.2);
        }

        .btn-futuristic:hover {
            transform: translateY(-2px);
            box-shadow:
                0 8px 25px rgba(102, 126, 234, 0.6),
                0 0 20px rgba(102, 126, 234, 0.4);
        }

        .btn-futuristic::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
            transition: left 0.5s;
        }

        .btn-futuristic:hover::before {
            left: 100%;
        }

        .typing-indicator {
            display: inline-block;
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: var(--accent-gradient);
            animation: typing 1.4s infinite ease-in-out;
        }

        .typing-indicator:nth-child(1) { animation-delay: -0.32s; }
        .typing-indicator:nth-child(2) { animation-delay: -0.16s; }
        .typing-indicator:nth-child(3) { animation-delay: 0s; }

        @keyframes typing {
            0%, 80%, 100% {
                transform: scale(0);
                opacity: 0.5;
                background: var(--neon-blue);
            }
            40% {
                transform: scale(1);
                opacity: 1;
                background: var(--neon-pink);
            }
        }

        .market-card {
            background: var(--glass-bg);
            backdrop-filter: blur(20px);
            border: 1px solid var(--glass-border);
            border-radius: 15px;
            padding: 20px;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .market-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: var(--accent-gradient);
            transform: scaleX(0);
            transition: transform 0.3s ease;
        }

        .market-card:hover::before {
            transform: scaleX(1);
        }

        .market-card:hover {
            transform: translateY(-5px);
            box-shadow:
                0 10px 30px rgba(0, 0, 0, 0.3),
                0 0 20px rgba(79, 172, 254, 0.2);
        }

        .crypto-icon {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            font-size: 18px;
            color: white;
            text-shadow: 0 0 10px currentColor;
        }

        .btc-icon { background: linear-gradient(135deg, #f7931e 0%, #ff6b35 100%); }
        .eth-icon { background: linear-gradient(135deg, #627eea 0%, #3c3c3d 100%); }
        .usdt-icon { background: linear-gradient(135deg, #26a17b 0%, #50af95 100%); }

        .status-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            display: inline-block;
            margin-right: 8px;
            animation: pulse-status 2s infinite;
        }

        .status-online { background: var(--neon-green); }
        .status-warning { background: var(--neon-orange); }
        .status-error { background: var(--neon-pink); }

        @keyframes pulse-status {
            0%, 100% { opacity: 1; transform: scale(1); }
            50% { opacity: 0.7; transform: scale(1.1); }
        }

        .notification-badge {
            background: var(--secondary-gradient);
            color: white;
            border-radius: 50%;
            width: 24px;
            height: 24px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            font-weight: bold;
            animation: bounce 2s infinite;
        }

        @keyframes bounce {
            0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
            40% { transform: translateY(-5px); }
            60% { transform: translateY(-3px); }
        }

        .chat-message {
            animation: messageSlide 0.5s ease-out;
        }

        @keyframes messageSlide {
            from { opacity: 0; transform: translateX(-20px); }
            to { opacity: 1; transform: translateX(0); }
        }

        .system-status-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
            gap: 20px;
        }

        .status-card {
            text-align: center;
            padding: 20px;
            background: var(--glass-bg);
            backdrop-filter: blur(20px);
            border: 1px solid var(--glass-border);
            border-radius: 15px;
            transition: all 0.3s ease;
        }

        .status-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 5px 20px rgba(0, 0, 0, 0.2);
        }

        .status-icon {
            font-size: 2rem;
            margin-bottom: 10px;
            display: block;
        }

        .connection-pulse {
            animation: connectionPulse 1.5s infinite;
        }

        @keyframes connectionPulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }
    </style>
</head>
<body class="min-h-screen">
    <!-- Header -->
    <header class="glass-effect border-b border-white/10 p-6 slide-in">
        <div class="container mx-auto flex items-center justify-between">
            <div class="flex items-center space-x-4">
                <div class="w-14 h-14 rounded-xl flex items-center justify-center pulse-rainbow floating" style="background: var(--primary-gradient);">
                    <span class="text-white font-bold text-2xl orbitron">C</span>
                </div>
                <div>
                    <h1 class="text-3xl font-bold gradient-text orbitron">CHAINSIGHT</h1>
                    <p class="text-sm text-blue-300 font-medium">by Connectouch • Next-Gen AI Platform</p>
                </div>
            </div>
            <div class="flex items-center space-x-6">
                <div id="connection-status" class="flex items-center space-x-3 glass-effect px-4 py-2 rounded-full">
                    <div class="status-indicator status-online connection-pulse"></div>
                    <span class="text-sm font-medium">CONNECTED</span>
                </div>
                <div class="glass-effect px-4 py-2 rounded-full">
                    <span class="text-sm text-gray-300">Backend:</span>
                    <span class="gradient-text font-bold ml-2">ONLINE</span>
                </div>
            </div>
        </div>
    </header>

    <!-- Main Dashboard -->
    <main class="container mx-auto p-8 grid grid-cols-1 lg:grid-cols-3 gap-8">
        <!-- Market Data Widget -->
        <div class="lg:col-span-2 glass-effect rounded-2xl p-8 slide-in floating">
            <div class="flex items-center justify-between mb-6">
                <div>
                    <h2 class="text-2xl font-bold gradient-text orbitron">LIVE MARKET DATA</h2>
                    <p class="text-blue-300 text-sm mt-1">Real-time cryptocurrency updates</p>
                </div>
                <div class="glass-effect px-4 py-2 rounded-full">
                    <div class="flex items-center space-x-2">
                        <div class="status-indicator status-online"></div>
                        <span class="text-sm font-medium">STREAMING</span>
                    </div>
                </div>
            </div>
            <div id="market-data" class="space-y-4">
                <!-- Market data will be populated here -->
            </div>
        </div>

        <!-- AI Chat Interface -->
        <div class="glass-effect rounded-2xl p-6 flex flex-col h-[500px] slide-in">
            <div class="flex items-center justify-between mb-6">
                <div>
                    <h2 class="text-xl font-bold gradient-text orbitron">AI NEXUS</h2>
                    <p class="text-purple-300 text-sm">Advanced Neural Assistant</p>
                </div>
                <div id="ai-status" class="glass-effect px-3 py-1 rounded-full">
                    <div class="flex items-center space-x-2">
                        <div class="status-indicator status-online"></div>
                        <span class="text-xs font-medium">NEURAL ACTIVE</span>
                    </div>
                </div>
            </div>
            <div id="chat-messages" class="flex-1 overflow-y-auto space-y-4 mb-4 pr-2">
                <div class="glass-effect rounded-xl p-4 chat-message">
                    <div class="text-xs text-purple-300 mb-2 orbitron">AI NEXUS • NEURAL CORE</div>
                    <div class="text-white">🚀 Welcome to Chainsight! I'm your advanced AI assistant powered by quantum neural networks. How can I assist you today?</div>
                </div>
            </div>
            <div class="flex space-x-3">
                <input
                    id="chat-input"
                    type="text"
                    placeholder="Enter your query..."
                    class="flex-1 glass-effect border border-white/20 rounded-xl px-4 py-3 text-white placeholder-gray-400 focus:border-blue-400 focus:outline-none focus:ring-2 focus:ring-blue-400/20 transition-all"
                >
                <button
                    id="send-btn"
                    class="btn-futuristic"
                >
                    TRANSMIT
                </button>
            </div>
        </div>

        <!-- Notifications Panel -->
        <div class="glass-effect rounded-2xl p-6 slide-in">
            <div class="flex items-center justify-between mb-6">
                <div>
                    <h2 class="text-xl font-bold gradient-text orbitron">ALERTS</h2>
                    <p class="text-pink-300 text-sm">System Notifications</p>
                </div>
                <div id="notification-count" class="notification-badge">3</div>
            </div>
            <div id="notifications" class="space-y-4">
                <!-- Notifications will be populated here -->
            </div>
        </div>

        <!-- System Status -->
        <div class="lg:col-span-2 glass-effect rounded-2xl p-8 slide-in">
            <div class="mb-6">
                <h2 class="text-2xl font-bold gradient-text orbitron mb-2">SYSTEM MATRIX</h2>
                <p class="text-green-300 text-sm">Real-time Infrastructure Monitoring</p>
            </div>
            <div class="system-status-grid">
                <div class="status-card">
                    <div class="status-icon" style="color: var(--neon-green);">⚡</div>
                    <div class="text-sm font-medium text-green-300">Backend API</div>
                    <div class="text-xs text-gray-400 mt-1">OPTIMAL</div>
                </div>
                <div class="status-card">
                    <div class="status-icon" style="color: var(--neon-blue);">🔗</div>
                    <div class="text-sm font-medium text-blue-300">WebSocket</div>
                    <div class="text-xs text-gray-400 mt-1">CONNECTED</div>
                </div>
                <div class="status-card">
                    <div class="status-icon" style="color: var(--neon-purple);">🧠</div>
                    <div class="text-sm font-medium text-purple-300">AI Services</div>
                    <div class="text-xs text-gray-400 mt-1">NEURAL ACTIVE</div>
                </div>
                <div class="status-card">
                    <div class="status-icon" style="color: var(--neon-orange);">📊</div>
                    <div class="text-sm font-medium text-orange-300">Real-time Data</div>
                    <div class="text-xs text-gray-400 mt-1">STREAMING</div>
                </div>
            </div>
        </div>
    </main>

    <script>
        // WebSocket connection to backend
        let socket;
        let isConnected = false;

        // Initialize WebSocket connection
        function initWebSocket() {
            try {
                socket = io('http://localhost:8000');
                
                socket.on('connect', () => {
                    isConnected = true;
                    updateConnectionStatus(true);
                    console.log('Connected to Chainsight backend');
                });

                socket.on('disconnect', () => {
                    isConnected = false;
                    updateConnectionStatus(false);
                    console.log('Disconnected from backend');
                });

                socket.on('market_data_update', (data) => {
                    updateMarketData(data);
                });

                socket.on('chat_response', (data) => {
                    addChatMessage('AI Assistant', data.message, 'ai');
                });

                socket.on('notification', (data) => {
                    addNotification(data);
                });

            } catch (error) {
                console.log('WebSocket connection failed, using mock data');
                useMockData();
            }
        }

        // Update connection status
        function updateConnectionStatus(connected) {
            const statusEl = document.getElementById('connection-status');
            const indicator = statusEl.querySelector('div');
            const text = statusEl.querySelector('span');
            
            if (connected) {
                indicator.className = 'w-3 h-3 bg-green-500 rounded-full pulse-gold';
                text.textContent = 'Connected';
            } else {
                indicator.className = 'w-3 h-3 bg-red-500 rounded-full';
                text.textContent = 'Disconnected';
            }
        }

        // Update market data
        function updateMarketData(data) {
            const container = document.getElementById('market-data');
            container.innerHTML = '';

            const marketData = data || [
                { symbol: 'BTC', price: 45000, change: 2.5, volume: '1.2B', icon: 'btc-icon' },
                { symbol: 'ETH', price: 3200, change: -1.2, volume: '800M', icon: 'eth-icon' },
                { symbol: 'USDT', price: 1.0, change: 0.0, volume: '2.1B', icon: 'usdt-icon' }
            ];

            marketData.forEach((item, index) => {
                const changeColor = item.change >= 0 ? 'text-green-400' : 'text-red-400';
                const changeSymbol = item.change >= 0 ? '+' : '';
                const trendIcon = item.change >= 0 ? '📈' : '📉';

                setTimeout(() => {
                    container.innerHTML += `
                        <div class="market-card slide-in">
                            <div class="flex items-center justify-between">
                                <div class="flex items-center space-x-4">
                                    <div class="crypto-icon ${item.icon}">
                                        ${item.symbol.charAt(0)}
                                    </div>
                                    <div>
                                        <div class="font-bold text-lg orbitron">${item.symbol}</div>
                                        <div class="text-sm text-gray-400">Volume: ${item.volume}</div>
                                    </div>
                                </div>
                                <div class="text-right">
                                    <div class="font-bold text-xl text-white">$${item.price.toLocaleString()}</div>
                                    <div class="flex items-center justify-end space-x-1">
                                        <span class="${changeColor} text-sm font-medium">${changeSymbol}${item.change}%</span>
                                        <span class="text-sm">${trendIcon}</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    `;
                }, index * 200);
            });
        }

        // Add chat message
        function addChatMessage(sender, message, type = 'user') {
            const container = document.getElementById('chat-messages');
            const isAI = type === 'ai';
            const bgClass = isAI ? 'glass-effect' : 'glass-effect border-blue-400/30';
            const senderColor = isAI ? 'text-purple-300' : 'text-blue-300';
            const senderPrefix = isAI ? '🤖 AI NEXUS' : '👤 USER';

            container.innerHTML += `
                <div class="${bgClass} rounded-xl p-4 chat-message">
                    <div class="text-xs ${senderColor} mb-2 orbitron">${senderPrefix} • ${new Date().toLocaleTimeString()}</div>
                    <div class="text-white">${message}</div>
                </div>
            `;

            container.scrollTop = container.scrollHeight;
        }

        // Add notification
        function addNotification(data) {
            const container = document.getElementById('notifications');
            const priorityColors = {
                'high': 'border-red-400 bg-red-500/10',
                'medium': 'border-yellow-400 bg-yellow-500/10',
                'low': 'border-blue-400 bg-blue-500/10',
                'info': 'border-green-400 bg-green-500/10'
            };
            const priorityIcons = {
                'high': '🚨',
                'medium': '⚠️',
                'low': 'ℹ️',
                'info': '✅'
            };

            const colorClass = priorityColors[data.priority] || priorityColors['info'];
            const icon = priorityIcons[data.priority] || priorityIcons['info'];

            container.innerHTML += `
                <div class="glass-effect border-l-4 ${colorClass} p-4 rounded-xl slide-in">
                    <div class="flex items-start space-x-3">
                        <span class="text-lg">${icon}</span>
                        <div class="flex-1">
                            <div class="font-semibold text-sm text-white">${data.title}</div>
                            <div class="text-xs text-gray-300 mt-1">${data.message}</div>
                            <div class="text-xs text-gray-500 mt-2">${new Date().toLocaleTimeString()}</div>
                        </div>
                    </div>
                </div>
            `;
        }

        // Use mock data when WebSocket fails
        function useMockData() {
            updateConnectionStatus(false);
            updateMarketData();
            
            // Simulate periodic updates
            setInterval(() => {
                const mockData = [
                    { symbol: 'BTC', price: 45000 + Math.random() * 1000, change: (Math.random() - 0.5) * 10, volume: '1.2B' },
                    { symbol: 'ETH', price: 3200 + Math.random() * 200, change: (Math.random() - 0.5) * 8, volume: '800M' },
                    { symbol: 'USDT', price: 1.0, change: 0.0, volume: '2.1B' }
                ];
                updateMarketData(mockData);
            }, 5000);
        }

        // Chat functionality
        document.getElementById('send-btn').addEventListener('click', sendMessage);
        document.getElementById('chat-input').addEventListener('keypress', (e) => {
            if (e.key === 'Enter') sendMessage();
        });

        function sendMessage() {
            const input = document.getElementById('chat-input');
            const message = input.value.trim();

            if (message) {
                addChatMessage('USER', message, 'user');
                input.value = '';

                // Show typing indicator
                const container = document.getElementById('chat-messages');
                container.innerHTML += `
                    <div class="glass-effect rounded-xl p-4 chat-message" id="typing-indicator">
                        <div class="text-xs text-purple-300 mb-2 orbitron">🤖 AI NEXUS • PROCESSING</div>
                        <div class="flex items-center space-x-2">
                            <span class="text-white">Neural networks analyzing</span>
                            <div class="flex space-x-1">
                                <div class="typing-indicator"></div>
                                <div class="typing-indicator"></div>
                                <div class="typing-indicator"></div>
                            </div>
                        </div>
                    </div>
                `;
                container.scrollTop = container.scrollHeight;

                // Simulate AI response with more dynamic responses
                const responses = [
                    `🚀 Quantum analysis complete! Your query "${message}" has been processed through our neural matrix. Chainsight's AI systems are operating at peak efficiency.`,
                    `⚡ Neural pathways activated! I've analyzed your input "${message}" using advanced algorithms. How else can I assist you today?`,
                    `🧠 Cognitive processing successful! Your message "${message}" has been interpreted by our AI core. What would you like to explore next?`,
                    `🔮 Predictive analysis engaged! Based on "${message}", I recommend exploring our advanced features. Need more assistance?`
                ];

                setTimeout(() => {
                    document.getElementById('typing-indicator')?.remove();
                    const randomResponse = responses[Math.floor(Math.random() * responses.length)];
                    addChatMessage('AI NEXUS', randomResponse, 'ai');
                }, 2500);
            }
        }

        // Initialize the application
        document.addEventListener('DOMContentLoaded', () => {
            initWebSocket();

            // Add some initial notifications with staggered timing
            setTimeout(() => {
                addNotification({
                    title: 'Quantum Core Online',
                    message: 'Chainsight neural networks fully operational',
                    priority: 'info'
                });
            }, 500);

            setTimeout(() => {
                addNotification({
                    title: 'Market Surge Detected',
                    message: 'BTC momentum increased by 2.5% - AI recommends monitoring',
                    priority: 'medium'
                });
            }, 1000);

            setTimeout(() => {
                addNotification({
                    title: 'Neural Enhancement',
                    message: 'Advanced AI models deployed - Enhanced prediction accuracy',
                    priority: 'high'
                });
            }, 1500);

            // Add some visual flair
            setTimeout(() => {
                const header = document.querySelector('header');
                header.style.transform = 'translateY(0)';
                header.style.opacity = '1';
            }, 100);
        });
    </script>
</body>
</html>
