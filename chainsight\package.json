{"name": "chainsight-by-connectouch", "version": "1.0.0", "description": "Modular Fullstack AI Agent with luxury black UI and real-time interactivity", "private": true, "workspaces": ["apps/*", "packages/*", "plugins/*", "libs/*"], "scripts": {"dev": "turbo run dev", "build": "turbo run build", "test": "turbo run test", "lint": "turbo run lint", "clean": "turbo run clean", "docker:build": "docker-compose build", "docker:up": "docker-compose up -d", "docker:down": "docker-compose down"}, "devDependencies": {"turbo": "^1.10.16", "@types/node": "^20.8.0", "typescript": "^5.2.2"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}}