{"name": "@chainsight/ai-core", "version": "1.0.0", "description": "Core AI functionality for Chainsight platform", "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"build": "tsc", "dev": "tsc --watch", "test": "jest", "lint": "eslint src/**/*.ts", "clean": "rm -rf dist"}, "dependencies": {"openai": "^4.20.1", "@tensorflow/tfjs": "^4.10.0", "@tensorflow/tfjs-node": "^4.10.0", "natural": "^6.5.0", "compromise": "^14.10.0", "sentiment": "^5.0.2", "axios": "^1.5.0"}, "devDependencies": {"@types/node": "^20.5.0", "typescript": "^5.1.6", "jest": "^29.6.2", "@types/jest": "^29.5.4", "eslint": "^8.47.0", "@typescript-eslint/eslint-plugin": "^6.4.0", "@typescript-eslint/parser": "^6.4.0"}, "keywords": ["ai", "machine-learning", "nlp", "chainsight"], "author": "Connectouch", "license": "MIT"}