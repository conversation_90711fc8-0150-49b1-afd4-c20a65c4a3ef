"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/store/useAppStore.ts":
/*!**********************************!*\
  !*** ./src/store/useAppStore.ts ***!
  \**********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useAppActions: () => (/* binding */ useAppActions),\n/* harmony export */   useAppStore: () => (/* binding */ useAppStore),\n/* harmony export */   useConnectionStatus: () => (/* binding */ useConnectionStatus),\n/* harmony export */   useError: () => (/* binding */ useError),\n/* harmony export */   useIsAuthenticated: () => (/* binding */ useIsAuthenticated),\n/* harmony export */   useIsLoading: () => (/* binding */ useIsLoading),\n/* harmony export */   useNotifications: () => (/* binding */ useNotifications),\n/* harmony export */   useSettings: () => (/* binding */ useSettings),\n/* harmony export */   useUnreadNotifications: () => (/* binding */ useUnreadNotifications),\n/* harmony export */   useUser: () => (/* binding */ useUser)\n/* harmony export */ });\n/* harmony import */ var zustand__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! zustand */ \"(app-pages-browser)/./node_modules/zustand/esm/react.mjs\");\n/* harmony import */ var zustand_middleware__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! zustand/middleware */ \"(app-pages-browser)/./node_modules/zustand/esm/middleware.mjs\");\n\n\n\n// Safe storage for SSR\nconst safeStorage = ()=>{\n    if (false) {}\n    return localStorage;\n};\nconst initialState = {\n    user: null,\n    isAuthenticated: false,\n    settings: {\n        sidebarCollapsed: false,\n        activeModule: 'crypto',\n        autoRefresh: true,\n        refreshInterval: 30000,\n        enableAnimations: true,\n        enableSounds: true\n    },\n    notifications: [],\n    connectionStatus: {\n        api: 'disconnected',\n        websocket: 'disconnected',\n        blockchain: 'disconnected'\n    },\n    isLoading: false,\n    loadingMessage: '',\n    error: null\n};\nconst useAppStore = (0,zustand__WEBPACK_IMPORTED_MODULE_0__.create)()((0,zustand_middleware__WEBPACK_IMPORTED_MODULE_1__.subscribeWithSelector)((0,zustand_middleware__WEBPACK_IMPORTED_MODULE_1__.persist)((set, get)=>({\n        ...initialState,\n        setUser: (user)=>set({\n                user,\n                isAuthenticated: !!user\n            }),\n        setAuthenticated: (authenticated)=>set({\n                isAuthenticated: authenticated\n            }),\n        updateSettings: (newSettings)=>set((state)=>({\n                    settings: {\n                        ...state.settings,\n                        ...newSettings\n                    }\n                })),\n        addNotification: (notification)=>set((state)=>({\n                    notifications: [\n                        {\n                            ...notification,\n                            id: Date.now().toString() + Math.random().toString(36).substr(2, 9),\n                            timestamp: Date.now(),\n                            read: false\n                        },\n                        ...state.notifications\n                    ].slice(0, 50)\n                })),\n        removeNotification: (id)=>set((state)=>({\n                    notifications: state.notifications.filter((n)=>n.id !== id)\n                })),\n        markNotificationRead: (id)=>set((state)=>({\n                    notifications: state.notifications.map((n)=>n.id === id ? {\n                            ...n,\n                            read: true\n                        } : n)\n                })),\n        clearNotifications: ()=>set({\n                notifications: []\n            }),\n        updateConnectionStatus: (status)=>set((state)=>({\n                    connectionStatus: {\n                        ...state.connectionStatus,\n                        ...status\n                    }\n                })),\n        setLoading: function(loading) {\n            let message = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : '';\n            return set({\n                isLoading: loading,\n                loadingMessage: message\n            });\n        },\n        setError: (error)=>set({\n                error\n            }),\n        reset: ()=>set(initialState)\n    }), {\n    name: 'chainsight-app-store',\n    storage: (0,zustand_middleware__WEBPACK_IMPORTED_MODULE_1__.createJSONStorage)(()=>localStorage),\n    partialize: (state)=>({\n            user: state.user,\n            isAuthenticated: state.isAuthenticated,\n            settings: state.settings,\n            notifications: state.notifications.filter((n)=>n.persistent)\n        })\n})));\n// Selectors for better performance\nconst useUser = ()=>useAppStore({\n        \"useUser.useAppStore\": (state)=>state.user\n    }[\"useUser.useAppStore\"]);\nconst useIsAuthenticated = ()=>useAppStore({\n        \"useIsAuthenticated.useAppStore\": (state)=>state.isAuthenticated\n    }[\"useIsAuthenticated.useAppStore\"]);\nconst useSettings = ()=>useAppStore({\n        \"useSettings.useAppStore\": (state)=>state.settings\n    }[\"useSettings.useAppStore\"]);\nconst useNotifications = ()=>useAppStore({\n        \"useNotifications.useAppStore\": (state)=>state.notifications\n    }[\"useNotifications.useAppStore\"]);\nconst useUnreadNotifications = ()=>useAppStore({\n        \"useUnreadNotifications.useAppStore\": (state)=>state.notifications.filter({\n                \"useUnreadNotifications.useAppStore\": (n)=>!n.read\n            }[\"useUnreadNotifications.useAppStore\"])\n    }[\"useUnreadNotifications.useAppStore\"]);\nconst useConnectionStatus = ()=>useAppStore({\n        \"useConnectionStatus.useAppStore\": (state)=>state.connectionStatus\n    }[\"useConnectionStatus.useAppStore\"]);\nconst useIsLoading = ()=>useAppStore({\n        \"useIsLoading.useAppStore\": (state)=>state.isLoading\n    }[\"useIsLoading.useAppStore\"]);\nconst useError = ()=>useAppStore({\n        \"useError.useAppStore\": (state)=>state.error\n    }[\"useError.useAppStore\"]);\n// Action selectors\nconst useAppActions = ()=>useAppStore({\n        \"useAppActions.useAppStore\": (state)=>({\n                setUser: state.setUser,\n                setAuthenticated: state.setAuthenticated,\n                updateSettings: state.updateSettings,\n                addNotification: state.addNotification,\n                removeNotification: state.removeNotification,\n                markNotificationRead: state.markNotificationRead,\n                clearNotifications: state.clearNotifications,\n                updateConnectionStatus: state.updateConnectionStatus,\n                setLoading: state.setLoading,\n                setError: state.setError,\n                reset: state.reset\n            })\n    }[\"useAppActions.useAppStore\"]);\n// Subscribe to changes for side effects\nuseAppStore.subscribe((state)=>state.connectionStatus, (connectionStatus)=>{\n    // Auto-add notifications for connection changes\n    const { addNotification } = useAppStore.getState();\n    Object.entries(connectionStatus).forEach((param)=>{\n        let [service, status] = param;\n        if (status === 'connected') {\n            addNotification({\n                type: 'success',\n                title: 'Connection Established',\n                message: \"\".concat(service.toUpperCase(), \" connection is now active\"),\n                read: false\n            });\n        } else if (status === 'error') {\n            addNotification({\n                type: 'error',\n                title: 'Connection Error',\n                message: \"Failed to connect to \".concat(service.toUpperCase()),\n                read: false\n            });\n        }\n    });\n});\n// Auto-remove old notifications\nuseAppStore.subscribe((state)=>state.notifications, (notifications)=>{\n    const now = Date.now();\n    const oneHour = 60 * 60 * 1000;\n    const expiredNotifications = notifications.filter((n)=>!n.persistent && now - n.timestamp > oneHour);\n    if (expiredNotifications.length > 0) {\n        const { removeNotification } = useAppStore.getState();\n        expiredNotifications.forEach((n)=>removeNotification(n.id));\n    }\n});\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9zdG9yZS91c2VBcHBTdG9yZS50cyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7QUFBaUM7QUFDK0I7QUFDTDtBQUUzRCx1QkFBdUI7QUFDdkIsTUFBTUksY0FBYztJQUNsQixJQUFJLEtBQTZCLEVBQUUsRUFPbEM7SUFDRCxPQUFPSTtBQUNUO0FBdUVBLE1BQU1DLGVBQWU7SUFDbkJDLE1BQU07SUFDTkMsaUJBQWlCO0lBQ2pCQyxVQUFVO1FBQ1JDLGtCQUFrQjtRQUNsQkMsY0FBYztRQUNkQyxhQUFhO1FBQ2JDLGlCQUFpQjtRQUNqQkMsa0JBQWtCO1FBQ2xCQyxjQUFjO0lBQ2hCO0lBQ0FDLGVBQWUsRUFBRTtJQUNqQkMsa0JBQWtCO1FBQ2hCQyxLQUFLO1FBQ0xDLFdBQVc7UUFDWEMsWUFBWTtJQUNkO0lBQ0FDLFdBQVc7SUFDWEMsZ0JBQWdCO0lBQ2hCQyxPQUFPO0FBQ1Q7QUFFTyxNQUFNQyxjQUFjM0IsK0NBQU1BLEdBQy9CRyx5RUFBcUJBLENBQ25CRiwyREFBT0EsQ0FDTCxDQUFDMkIsS0FBS0MsTUFBUztRQUNiLEdBQUdwQixZQUFZO1FBRWZxQixTQUFTLENBQUNwQixPQUFTa0IsSUFBSTtnQkFBRWxCO2dCQUFNQyxpQkFBaUIsQ0FBQyxDQUFDRDtZQUFLO1FBRXZEcUIsa0JBQWtCLENBQUNDLGdCQUFrQkosSUFBSTtnQkFBRWpCLGlCQUFpQnFCO1lBQWM7UUFFMUVDLGdCQUFnQixDQUFDQyxjQUNmTixJQUFJLENBQUNPLFFBQVc7b0JBQ2R2QixVQUFVO3dCQUFFLEdBQUd1QixNQUFNdkIsUUFBUTt3QkFBRSxHQUFHc0IsV0FBVztvQkFBQztnQkFDaEQ7UUFFRkUsaUJBQWlCLENBQUNDLGVBQ2hCVCxJQUFJLENBQUNPLFFBQVc7b0JBQ2RoQixlQUFlO3dCQUNiOzRCQUNFLEdBQUdrQixZQUFZOzRCQUNmQyxJQUFJQyxLQUFLQyxHQUFHLEdBQUdDLFFBQVEsS0FBS0MsS0FBS0MsTUFBTSxHQUFHRixRQUFRLENBQUMsSUFBSUcsTUFBTSxDQUFDLEdBQUc7NEJBQ2pFQyxXQUFXTixLQUFLQyxHQUFHOzRCQUNuQk0sTUFBTTt3QkFDUjsyQkFDR1gsTUFBTWhCLGFBQWE7cUJBQ3ZCLENBQUM0QixLQUFLLENBQUMsR0FBRztnQkFDYjtRQUVGQyxvQkFBb0IsQ0FBQ1YsS0FDbkJWLElBQUksQ0FBQ08sUUFBVztvQkFDZGhCLGVBQWVnQixNQUFNaEIsYUFBYSxDQUFDOEIsTUFBTSxDQUFDLENBQUNDLElBQU1BLEVBQUVaLEVBQUUsS0FBS0E7Z0JBQzVEO1FBRUZhLHNCQUFzQixDQUFDYixLQUNyQlYsSUFBSSxDQUFDTyxRQUFXO29CQUNkaEIsZUFBZWdCLE1BQU1oQixhQUFhLENBQUNpQyxHQUFHLENBQUMsQ0FBQ0YsSUFDdENBLEVBQUVaLEVBQUUsS0FBS0EsS0FBSzs0QkFBRSxHQUFHWSxDQUFDOzRCQUFFSixNQUFNO3dCQUFLLElBQUlJO2dCQUV6QztRQUVGRyxvQkFBb0IsSUFBTXpCLElBQUk7Z0JBQUVULGVBQWUsRUFBRTtZQUFDO1FBRWxEbUMsd0JBQXdCLENBQUNDLFNBQ3ZCM0IsSUFBSSxDQUFDTyxRQUFXO29CQUNkZixrQkFBa0I7d0JBQUUsR0FBR2UsTUFBTWYsZ0JBQWdCO3dCQUFFLEdBQUdtQyxNQUFNO29CQUFDO2dCQUMzRDtRQUVGQyxZQUFZLFNBQUNDO2dCQUFTQywyRUFBVTttQkFDOUI5QixJQUFJO2dCQUFFSixXQUFXaUM7Z0JBQVNoQyxnQkFBZ0JpQztZQUFROztRQUVwREMsVUFBVSxDQUFDakMsUUFBVUUsSUFBSTtnQkFBRUY7WUFBTTtRQUVqQ2tDLE9BQU8sSUFBTWhDLElBQUluQjtJQUNuQixJQUNBO0lBQ0VvRCxNQUFNO0lBQ05DLFNBQVM1RCxxRUFBaUJBLENBQUMsSUFBTU07SUFDakN1RCxZQUFZLENBQUM1QixRQUFXO1lBQ3RCekIsTUFBTXlCLE1BQU16QixJQUFJO1lBQ2hCQyxpQkFBaUJ3QixNQUFNeEIsZUFBZTtZQUN0Q0MsVUFBVXVCLE1BQU12QixRQUFRO1lBQ3hCTyxlQUFlZ0IsTUFBTWhCLGFBQWEsQ0FBQzhCLE1BQU0sQ0FBQ0MsQ0FBQUEsSUFBS0EsRUFBRWMsVUFBVTtRQUM3RDtBQUNGLEtBR0o7QUFFRixtQ0FBbUM7QUFDNUIsTUFBTUMsVUFBVSxJQUFNdEM7K0JBQVksQ0FBQ1EsUUFBVUEsTUFBTXpCLElBQUk7OEJBQUU7QUFDekQsTUFBTXdELHFCQUFxQixJQUFNdkM7MENBQVksQ0FBQ1EsUUFBVUEsTUFBTXhCLGVBQWU7eUNBQUU7QUFDL0UsTUFBTXdELGNBQWMsSUFBTXhDO21DQUFZLENBQUNRLFFBQVVBLE1BQU12QixRQUFRO2tDQUFFO0FBQ2pFLE1BQU13RCxtQkFBbUIsSUFBTXpDO3dDQUFZLENBQUNRLFFBQVVBLE1BQU1oQixhQUFhO3VDQUFFO0FBQzNFLE1BQU1rRCx5QkFBeUIsSUFDcEMxQzs4Q0FBWSxDQUFDUSxRQUFVQSxNQUFNaEIsYUFBYSxDQUFDOEIsTUFBTTtzREFBQ0MsQ0FBQUEsSUFBSyxDQUFDQSxFQUFFSixJQUFJOzs2Q0FBRztBQUM1RCxNQUFNd0Isc0JBQXNCLElBQU0zQzsyQ0FBWSxDQUFDUSxRQUFVQSxNQUFNZixnQkFBZ0I7MENBQUU7QUFDakYsTUFBTW1ELGVBQWUsSUFBTTVDO29DQUFZLENBQUNRLFFBQVVBLE1BQU1YLFNBQVM7bUNBQUU7QUFDbkUsTUFBTWdELFdBQVcsSUFBTTdDO2dDQUFZLENBQUNRLFFBQVVBLE1BQU1ULEtBQUs7K0JBQUU7QUFFbEUsbUJBQW1CO0FBQ1osTUFBTStDLGdCQUFnQixJQUFNOUM7cUNBQVksQ0FBQ1EsUUFBVztnQkFDekRMLFNBQVNLLE1BQU1MLE9BQU87Z0JBQ3RCQyxrQkFBa0JJLE1BQU1KLGdCQUFnQjtnQkFDeENFLGdCQUFnQkUsTUFBTUYsY0FBYztnQkFDcENHLGlCQUFpQkQsTUFBTUMsZUFBZTtnQkFDdENZLG9CQUFvQmIsTUFBTWEsa0JBQWtCO2dCQUM1Q0csc0JBQXNCaEIsTUFBTWdCLG9CQUFvQjtnQkFDaERFLG9CQUFvQmxCLE1BQU1rQixrQkFBa0I7Z0JBQzVDQyx3QkFBd0JuQixNQUFNbUIsc0JBQXNCO2dCQUNwREUsWUFBWXJCLE1BQU1xQixVQUFVO2dCQUM1QkcsVUFBVXhCLE1BQU13QixRQUFRO2dCQUN4QkMsT0FBT3pCLE1BQU15QixLQUFLO1lBQ3BCO29DQUFJO0FBRUosd0NBQXdDO0FBQ3hDakMsWUFBWStDLFNBQVMsQ0FDbkIsQ0FBQ3ZDLFFBQVVBLE1BQU1mLGdCQUFnQixFQUNqQyxDQUFDQTtJQUNDLGdEQUFnRDtJQUNoRCxNQUFNLEVBQUVnQixlQUFlLEVBQUUsR0FBR1QsWUFBWWdELFFBQVE7SUFFaERDLE9BQU9DLE9BQU8sQ0FBQ3pELGtCQUFrQjBELE9BQU8sQ0FBQztZQUFDLENBQUNDLFNBQVN4QixPQUFPO1FBQ3pELElBQUlBLFdBQVcsYUFBYTtZQUMxQm5CLGdCQUFnQjtnQkFDZDRDLE1BQU07Z0JBQ05DLE9BQU87Z0JBQ1B2QixTQUFTLEdBQXlCLE9BQXRCcUIsUUFBUUcsV0FBVyxJQUFHO2dCQUNsQ3BDLE1BQU07WUFDUjtRQUNGLE9BQU8sSUFBSVMsV0FBVyxTQUFTO1lBQzdCbkIsZ0JBQWdCO2dCQUNkNEMsTUFBTTtnQkFDTkMsT0FBTztnQkFDUHZCLFNBQVMsd0JBQThDLE9BQXRCcUIsUUFBUUcsV0FBVztnQkFDcERwQyxNQUFNO1lBQ1I7UUFDRjtJQUNGO0FBQ0Y7QUFHRixnQ0FBZ0M7QUFDaENuQixZQUFZK0MsU0FBUyxDQUNuQixDQUFDdkMsUUFBVUEsTUFBTWhCLGFBQWEsRUFDOUIsQ0FBQ0E7SUFDQyxNQUFNcUIsTUFBTUQsS0FBS0MsR0FBRztJQUNwQixNQUFNMkMsVUFBVSxLQUFLLEtBQUs7SUFFMUIsTUFBTUMsdUJBQXVCakUsY0FBYzhCLE1BQU0sQ0FDL0MsQ0FBQ0MsSUFBTSxDQUFDQSxFQUFFYyxVQUFVLElBQUl4QixNQUFNVSxFQUFFTCxTQUFTLEdBQUdzQztJQUc5QyxJQUFJQyxxQkFBcUJDLE1BQU0sR0FBRyxHQUFHO1FBQ25DLE1BQU0sRUFBRXJDLGtCQUFrQixFQUFFLEdBQUdyQixZQUFZZ0QsUUFBUTtRQUNuRFMscUJBQXFCTixPQUFPLENBQUMsQ0FBQzVCLElBQU1GLG1CQUFtQkUsRUFBRVosRUFBRTtJQUM3RDtBQUNGIiwic291cmNlcyI6WyJGOlxcQUkgY2hhaW5cXGNoYWluc2lnaHQtcHJvZHVjdGlvblxcc3JjXFxzdG9yZVxcdXNlQXBwU3RvcmUudHMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgY3JlYXRlIH0gZnJvbSAnenVzdGFuZCc7XG5pbXBvcnQgeyBwZXJzaXN0LCBjcmVhdGVKU09OU3RvcmFnZSB9IGZyb20gJ3p1c3RhbmQvbWlkZGxld2FyZSc7XG5pbXBvcnQgeyBzdWJzY3JpYmVXaXRoU2VsZWN0b3IgfSBmcm9tICd6dXN0YW5kL21pZGRsZXdhcmUnO1xuXG4vLyBTYWZlIHN0b3JhZ2UgZm9yIFNTUlxuY29uc3Qgc2FmZVN0b3JhZ2UgPSAoKSA9PiB7XG4gIGlmICh0eXBlb2Ygd2luZG93ID09PSAndW5kZWZpbmVkJykge1xuICAgIC8vIFJldHVybiBhIG1vY2sgc3RvcmFnZSBmb3IgU1NSXG4gICAgcmV0dXJuIHtcbiAgICAgIGdldEl0ZW06ICgpID0+IG51bGwsXG4gICAgICBzZXRJdGVtOiAoKSA9PiB7fSxcbiAgICAgIHJlbW92ZUl0ZW06ICgpID0+IHt9LFxuICAgIH07XG4gIH1cbiAgcmV0dXJuIGxvY2FsU3RvcmFnZTtcbn07XG5cbi8vIFR5cGVzXG5pbnRlcmZhY2UgVXNlciB7XG4gIGlkOiBzdHJpbmc7XG4gIG5hbWU6IHN0cmluZztcbiAgZW1haWw6IHN0cmluZztcbiAgYXZhdGFyPzogc3RyaW5nO1xuICBwcmVmZXJlbmNlczoge1xuICAgIHRoZW1lOiAnZGFyaycgfCAnbGlnaHQnO1xuICAgIGxhbmd1YWdlOiBzdHJpbmc7XG4gICAgdGltZXpvbmU6IHN0cmluZztcbiAgfTtcbn1cblxuaW50ZXJmYWNlIE5vdGlmaWNhdGlvbiB7XG4gIGlkOiBzdHJpbmc7XG4gIHR5cGU6ICdpbmZvJyB8ICdzdWNjZXNzJyB8ICd3YXJuaW5nJyB8ICdlcnJvcic7XG4gIHRpdGxlOiBzdHJpbmc7XG4gIG1lc3NhZ2U6IHN0cmluZztcbiAgdGltZXN0YW1wOiBudW1iZXI7XG4gIHJlYWQ6IGJvb2xlYW47XG4gIHBlcnNpc3RlbnQ/OiBib29sZWFuO1xufVxuXG5pbnRlcmZhY2UgQXBwU2V0dGluZ3Mge1xuICBzaWRlYmFyQ29sbGFwc2VkOiBib29sZWFuO1xuICBhY3RpdmVNb2R1bGU6IHN0cmluZztcbiAgYXV0b1JlZnJlc2g6IGJvb2xlYW47XG4gIHJlZnJlc2hJbnRlcnZhbDogbnVtYmVyO1xuICBlbmFibGVBbmltYXRpb25zOiBib29sZWFuO1xuICBlbmFibGVTb3VuZHM6IGJvb2xlYW47XG59XG5cbmludGVyZmFjZSBDb25uZWN0aW9uU3RhdHVzIHtcbiAgYXBpOiAnY29ubmVjdGVkJyB8ICdkaXNjb25uZWN0ZWQnIHwgJ2Vycm9yJztcbiAgd2Vic29ja2V0OiAnY29ubmVjdGVkJyB8ICdkaXNjb25uZWN0ZWQnIHwgJ2Vycm9yJztcbiAgYmxvY2tjaGFpbjogJ2Nvbm5lY3RlZCcgfCAnZGlzY29ubmVjdGVkJyB8ICdlcnJvcic7XG59XG5cbmludGVyZmFjZSBBcHBTdGF0ZSB7XG4gIC8vIFVzZXIgc3RhdGVcbiAgdXNlcjogVXNlciB8IG51bGw7XG4gIGlzQXV0aGVudGljYXRlZDogYm9vbGVhbjtcbiAgXG4gIC8vIFVJIHN0YXRlXG4gIHNldHRpbmdzOiBBcHBTZXR0aW5ncztcbiAgbm90aWZpY2F0aW9uczogTm90aWZpY2F0aW9uW107XG4gIGNvbm5lY3Rpb25TdGF0dXM6IENvbm5lY3Rpb25TdGF0dXM7XG4gIFxuICAvLyBMb2FkaW5nIHN0YXRlc1xuICBpc0xvYWRpbmc6IGJvb2xlYW47XG4gIGxvYWRpbmdNZXNzYWdlOiBzdHJpbmc7XG4gIFxuICAvLyBFcnJvciBzdGF0ZVxuICBlcnJvcjogc3RyaW5nIHwgbnVsbDtcbiAgXG4gIC8vIEFjdGlvbnNcbiAgc2V0VXNlcjogKHVzZXI6IFVzZXIgfCBudWxsKSA9PiB2b2lkO1xuICBzZXRBdXRoZW50aWNhdGVkOiAoYXV0aGVudGljYXRlZDogYm9vbGVhbikgPT4gdm9pZDtcbiAgdXBkYXRlU2V0dGluZ3M6IChzZXR0aW5nczogUGFydGlhbDxBcHBTZXR0aW5ncz4pID0+IHZvaWQ7XG4gIGFkZE5vdGlmaWNhdGlvbjogKG5vdGlmaWNhdGlvbjogT21pdDxOb3RpZmljYXRpb24sICdpZCcgfCAndGltZXN0YW1wJz4pID0+IHZvaWQ7XG4gIHJlbW92ZU5vdGlmaWNhdGlvbjogKGlkOiBzdHJpbmcpID0+IHZvaWQ7XG4gIG1hcmtOb3RpZmljYXRpb25SZWFkOiAoaWQ6IHN0cmluZykgPT4gdm9pZDtcbiAgY2xlYXJOb3RpZmljYXRpb25zOiAoKSA9PiB2b2lkO1xuICB1cGRhdGVDb25uZWN0aW9uU3RhdHVzOiAoc3RhdHVzOiBQYXJ0aWFsPENvbm5lY3Rpb25TdGF0dXM+KSA9PiB2b2lkO1xuICBzZXRMb2FkaW5nOiAobG9hZGluZzogYm9vbGVhbiwgbWVzc2FnZT86IHN0cmluZykgPT4gdm9pZDtcbiAgc2V0RXJyb3I6IChlcnJvcjogc3RyaW5nIHwgbnVsbCkgPT4gdm9pZDtcbiAgcmVzZXQ6ICgpID0+IHZvaWQ7XG59XG5cbmNvbnN0IGluaXRpYWxTdGF0ZSA9IHtcbiAgdXNlcjogbnVsbCxcbiAgaXNBdXRoZW50aWNhdGVkOiBmYWxzZSxcbiAgc2V0dGluZ3M6IHtcbiAgICBzaWRlYmFyQ29sbGFwc2VkOiBmYWxzZSxcbiAgICBhY3RpdmVNb2R1bGU6ICdjcnlwdG8nLFxuICAgIGF1dG9SZWZyZXNoOiB0cnVlLFxuICAgIHJlZnJlc2hJbnRlcnZhbDogMzAwMDAsXG4gICAgZW5hYmxlQW5pbWF0aW9uczogdHJ1ZSxcbiAgICBlbmFibGVTb3VuZHM6IHRydWUsXG4gIH0sXG4gIG5vdGlmaWNhdGlvbnM6IFtdLFxuICBjb25uZWN0aW9uU3RhdHVzOiB7XG4gICAgYXBpOiAnZGlzY29ubmVjdGVkJyBhcyBjb25zdCxcbiAgICB3ZWJzb2NrZXQ6ICdkaXNjb25uZWN0ZWQnIGFzIGNvbnN0LFxuICAgIGJsb2NrY2hhaW46ICdkaXNjb25uZWN0ZWQnIGFzIGNvbnN0LFxuICB9LFxuICBpc0xvYWRpbmc6IGZhbHNlLFxuICBsb2FkaW5nTWVzc2FnZTogJycsXG4gIGVycm9yOiBudWxsLFxufTtcblxuZXhwb3J0IGNvbnN0IHVzZUFwcFN0b3JlID0gY3JlYXRlPEFwcFN0YXRlPigpKFxuICBzdWJzY3JpYmVXaXRoU2VsZWN0b3IoXG4gICAgcGVyc2lzdChcbiAgICAgIChzZXQsIGdldCkgPT4gKHtcbiAgICAgICAgLi4uaW5pdGlhbFN0YXRlLFxuICAgICAgICBcbiAgICAgICAgc2V0VXNlcjogKHVzZXIpID0+IHNldCh7IHVzZXIsIGlzQXV0aGVudGljYXRlZDogISF1c2VyIH0pLFxuICAgICAgICBcbiAgICAgICAgc2V0QXV0aGVudGljYXRlZDogKGF1dGhlbnRpY2F0ZWQpID0+IHNldCh7IGlzQXV0aGVudGljYXRlZDogYXV0aGVudGljYXRlZCB9KSxcbiAgICAgICAgXG4gICAgICAgIHVwZGF0ZVNldHRpbmdzOiAobmV3U2V0dGluZ3MpID0+XG4gICAgICAgICAgc2V0KChzdGF0ZSkgPT4gKHtcbiAgICAgICAgICAgIHNldHRpbmdzOiB7IC4uLnN0YXRlLnNldHRpbmdzLCAuLi5uZXdTZXR0aW5ncyB9LFxuICAgICAgICAgIH0pKSxcbiAgICAgICAgXG4gICAgICAgIGFkZE5vdGlmaWNhdGlvbjogKG5vdGlmaWNhdGlvbikgPT5cbiAgICAgICAgICBzZXQoKHN0YXRlKSA9PiAoe1xuICAgICAgICAgICAgbm90aWZpY2F0aW9uczogW1xuICAgICAgICAgICAgICB7XG4gICAgICAgICAgICAgICAgLi4ubm90aWZpY2F0aW9uLFxuICAgICAgICAgICAgICAgIGlkOiBEYXRlLm5vdygpLnRvU3RyaW5nKCkgKyBNYXRoLnJhbmRvbSgpLnRvU3RyaW5nKDM2KS5zdWJzdHIoMiwgOSksXG4gICAgICAgICAgICAgICAgdGltZXN0YW1wOiBEYXRlLm5vdygpLFxuICAgICAgICAgICAgICAgIHJlYWQ6IGZhbHNlLFxuICAgICAgICAgICAgICB9LFxuICAgICAgICAgICAgICAuLi5zdGF0ZS5ub3RpZmljYXRpb25zLFxuICAgICAgICAgICAgXS5zbGljZSgwLCA1MCksIC8vIEtlZXAgb25seSBsYXN0IDUwIG5vdGlmaWNhdGlvbnNcbiAgICAgICAgICB9KSksXG4gICAgICAgIFxuICAgICAgICByZW1vdmVOb3RpZmljYXRpb246IChpZCkgPT5cbiAgICAgICAgICBzZXQoKHN0YXRlKSA9PiAoe1xuICAgICAgICAgICAgbm90aWZpY2F0aW9uczogc3RhdGUubm90aWZpY2F0aW9ucy5maWx0ZXIoKG4pID0+IG4uaWQgIT09IGlkKSxcbiAgICAgICAgICB9KSksXG4gICAgICAgIFxuICAgICAgICBtYXJrTm90aWZpY2F0aW9uUmVhZDogKGlkKSA9PlxuICAgICAgICAgIHNldCgoc3RhdGUpID0+ICh7XG4gICAgICAgICAgICBub3RpZmljYXRpb25zOiBzdGF0ZS5ub3RpZmljYXRpb25zLm1hcCgobikgPT5cbiAgICAgICAgICAgICAgbi5pZCA9PT0gaWQgPyB7IC4uLm4sIHJlYWQ6IHRydWUgfSA6IG5cbiAgICAgICAgICAgICksXG4gICAgICAgICAgfSkpLFxuICAgICAgICBcbiAgICAgICAgY2xlYXJOb3RpZmljYXRpb25zOiAoKSA9PiBzZXQoeyBub3RpZmljYXRpb25zOiBbXSB9KSxcbiAgICAgICAgXG4gICAgICAgIHVwZGF0ZUNvbm5lY3Rpb25TdGF0dXM6IChzdGF0dXMpID0+XG4gICAgICAgICAgc2V0KChzdGF0ZSkgPT4gKHtcbiAgICAgICAgICAgIGNvbm5lY3Rpb25TdGF0dXM6IHsgLi4uc3RhdGUuY29ubmVjdGlvblN0YXR1cywgLi4uc3RhdHVzIH0sXG4gICAgICAgICAgfSkpLFxuICAgICAgICBcbiAgICAgICAgc2V0TG9hZGluZzogKGxvYWRpbmcsIG1lc3NhZ2UgPSAnJykgPT5cbiAgICAgICAgICBzZXQoeyBpc0xvYWRpbmc6IGxvYWRpbmcsIGxvYWRpbmdNZXNzYWdlOiBtZXNzYWdlIH0pLFxuICAgICAgICBcbiAgICAgICAgc2V0RXJyb3I6IChlcnJvcikgPT4gc2V0KHsgZXJyb3IgfSksXG4gICAgICAgIFxuICAgICAgICByZXNldDogKCkgPT4gc2V0KGluaXRpYWxTdGF0ZSksXG4gICAgICB9KSxcbiAgICAgIHtcbiAgICAgICAgbmFtZTogJ2NoYWluc2lnaHQtYXBwLXN0b3JlJyxcbiAgICAgICAgc3RvcmFnZTogY3JlYXRlSlNPTlN0b3JhZ2UoKCkgPT4gbG9jYWxTdG9yYWdlKSxcbiAgICAgICAgcGFydGlhbGl6ZTogKHN0YXRlKSA9PiAoe1xuICAgICAgICAgIHVzZXI6IHN0YXRlLnVzZXIsXG4gICAgICAgICAgaXNBdXRoZW50aWNhdGVkOiBzdGF0ZS5pc0F1dGhlbnRpY2F0ZWQsXG4gICAgICAgICAgc2V0dGluZ3M6IHN0YXRlLnNldHRpbmdzLFxuICAgICAgICAgIG5vdGlmaWNhdGlvbnM6IHN0YXRlLm5vdGlmaWNhdGlvbnMuZmlsdGVyKG4gPT4gbi5wZXJzaXN0ZW50KSxcbiAgICAgICAgfSksXG4gICAgICB9XG4gICAgKVxuICApXG4pO1xuXG4vLyBTZWxlY3RvcnMgZm9yIGJldHRlciBwZXJmb3JtYW5jZVxuZXhwb3J0IGNvbnN0IHVzZVVzZXIgPSAoKSA9PiB1c2VBcHBTdG9yZSgoc3RhdGUpID0+IHN0YXRlLnVzZXIpO1xuZXhwb3J0IGNvbnN0IHVzZUlzQXV0aGVudGljYXRlZCA9ICgpID0+IHVzZUFwcFN0b3JlKChzdGF0ZSkgPT4gc3RhdGUuaXNBdXRoZW50aWNhdGVkKTtcbmV4cG9ydCBjb25zdCB1c2VTZXR0aW5ncyA9ICgpID0+IHVzZUFwcFN0b3JlKChzdGF0ZSkgPT4gc3RhdGUuc2V0dGluZ3MpO1xuZXhwb3J0IGNvbnN0IHVzZU5vdGlmaWNhdGlvbnMgPSAoKSA9PiB1c2VBcHBTdG9yZSgoc3RhdGUpID0+IHN0YXRlLm5vdGlmaWNhdGlvbnMpO1xuZXhwb3J0IGNvbnN0IHVzZVVucmVhZE5vdGlmaWNhdGlvbnMgPSAoKSA9PiBcbiAgdXNlQXBwU3RvcmUoKHN0YXRlKSA9PiBzdGF0ZS5ub3RpZmljYXRpb25zLmZpbHRlcihuID0+ICFuLnJlYWQpKTtcbmV4cG9ydCBjb25zdCB1c2VDb25uZWN0aW9uU3RhdHVzID0gKCkgPT4gdXNlQXBwU3RvcmUoKHN0YXRlKSA9PiBzdGF0ZS5jb25uZWN0aW9uU3RhdHVzKTtcbmV4cG9ydCBjb25zdCB1c2VJc0xvYWRpbmcgPSAoKSA9PiB1c2VBcHBTdG9yZSgoc3RhdGUpID0+IHN0YXRlLmlzTG9hZGluZyk7XG5leHBvcnQgY29uc3QgdXNlRXJyb3IgPSAoKSA9PiB1c2VBcHBTdG9yZSgoc3RhdGUpID0+IHN0YXRlLmVycm9yKTtcblxuLy8gQWN0aW9uIHNlbGVjdG9yc1xuZXhwb3J0IGNvbnN0IHVzZUFwcEFjdGlvbnMgPSAoKSA9PiB1c2VBcHBTdG9yZSgoc3RhdGUpID0+ICh7XG4gIHNldFVzZXI6IHN0YXRlLnNldFVzZXIsXG4gIHNldEF1dGhlbnRpY2F0ZWQ6IHN0YXRlLnNldEF1dGhlbnRpY2F0ZWQsXG4gIHVwZGF0ZVNldHRpbmdzOiBzdGF0ZS51cGRhdGVTZXR0aW5ncyxcbiAgYWRkTm90aWZpY2F0aW9uOiBzdGF0ZS5hZGROb3RpZmljYXRpb24sXG4gIHJlbW92ZU5vdGlmaWNhdGlvbjogc3RhdGUucmVtb3ZlTm90aWZpY2F0aW9uLFxuICBtYXJrTm90aWZpY2F0aW9uUmVhZDogc3RhdGUubWFya05vdGlmaWNhdGlvblJlYWQsXG4gIGNsZWFyTm90aWZpY2F0aW9uczogc3RhdGUuY2xlYXJOb3RpZmljYXRpb25zLFxuICB1cGRhdGVDb25uZWN0aW9uU3RhdHVzOiBzdGF0ZS51cGRhdGVDb25uZWN0aW9uU3RhdHVzLFxuICBzZXRMb2FkaW5nOiBzdGF0ZS5zZXRMb2FkaW5nLFxuICBzZXRFcnJvcjogc3RhdGUuc2V0RXJyb3IsXG4gIHJlc2V0OiBzdGF0ZS5yZXNldCxcbn0pKTtcblxuLy8gU3Vic2NyaWJlIHRvIGNoYW5nZXMgZm9yIHNpZGUgZWZmZWN0c1xudXNlQXBwU3RvcmUuc3Vic2NyaWJlKFxuICAoc3RhdGUpID0+IHN0YXRlLmNvbm5lY3Rpb25TdGF0dXMsXG4gIChjb25uZWN0aW9uU3RhdHVzKSA9PiB7XG4gICAgLy8gQXV0by1hZGQgbm90aWZpY2F0aW9ucyBmb3IgY29ubmVjdGlvbiBjaGFuZ2VzXG4gICAgY29uc3QgeyBhZGROb3RpZmljYXRpb24gfSA9IHVzZUFwcFN0b3JlLmdldFN0YXRlKCk7XG4gICAgXG4gICAgT2JqZWN0LmVudHJpZXMoY29ubmVjdGlvblN0YXR1cykuZm9yRWFjaCgoW3NlcnZpY2UsIHN0YXR1c10pID0+IHtcbiAgICAgIGlmIChzdGF0dXMgPT09ICdjb25uZWN0ZWQnKSB7XG4gICAgICAgIGFkZE5vdGlmaWNhdGlvbih7XG4gICAgICAgICAgdHlwZTogJ3N1Y2Nlc3MnLFxuICAgICAgICAgIHRpdGxlOiAnQ29ubmVjdGlvbiBFc3RhYmxpc2hlZCcsXG4gICAgICAgICAgbWVzc2FnZTogYCR7c2VydmljZS50b1VwcGVyQ2FzZSgpfSBjb25uZWN0aW9uIGlzIG5vdyBhY3RpdmVgLFxuICAgICAgICAgIHJlYWQ6IGZhbHNlLFxuICAgICAgICB9KTtcbiAgICAgIH0gZWxzZSBpZiAoc3RhdHVzID09PSAnZXJyb3InKSB7XG4gICAgICAgIGFkZE5vdGlmaWNhdGlvbih7XG4gICAgICAgICAgdHlwZTogJ2Vycm9yJyxcbiAgICAgICAgICB0aXRsZTogJ0Nvbm5lY3Rpb24gRXJyb3InLFxuICAgICAgICAgIG1lc3NhZ2U6IGBGYWlsZWQgdG8gY29ubmVjdCB0byAke3NlcnZpY2UudG9VcHBlckNhc2UoKX1gLFxuICAgICAgICAgIHJlYWQ6IGZhbHNlLFxuICAgICAgICB9KTtcbiAgICAgIH1cbiAgICB9KTtcbiAgfVxuKTtcblxuLy8gQXV0by1yZW1vdmUgb2xkIG5vdGlmaWNhdGlvbnNcbnVzZUFwcFN0b3JlLnN1YnNjcmliZShcbiAgKHN0YXRlKSA9PiBzdGF0ZS5ub3RpZmljYXRpb25zLFxuICAobm90aWZpY2F0aW9ucykgPT4ge1xuICAgIGNvbnN0IG5vdyA9IERhdGUubm93KCk7XG4gICAgY29uc3Qgb25lSG91ciA9IDYwICogNjAgKiAxMDAwO1xuICAgIFxuICAgIGNvbnN0IGV4cGlyZWROb3RpZmljYXRpb25zID0gbm90aWZpY2F0aW9ucy5maWx0ZXIoXG4gICAgICAobikgPT4gIW4ucGVyc2lzdGVudCAmJiBub3cgLSBuLnRpbWVzdGFtcCA+IG9uZUhvdXJcbiAgICApO1xuICAgIFxuICAgIGlmIChleHBpcmVkTm90aWZpY2F0aW9ucy5sZW5ndGggPiAwKSB7XG4gICAgICBjb25zdCB7IHJlbW92ZU5vdGlmaWNhdGlvbiB9ID0gdXNlQXBwU3RvcmUuZ2V0U3RhdGUoKTtcbiAgICAgIGV4cGlyZWROb3RpZmljYXRpb25zLmZvckVhY2goKG4pID0+IHJlbW92ZU5vdGlmaWNhdGlvbihuLmlkKSk7XG4gICAgfVxuICB9XG4pO1xuIl0sIm5hbWVzIjpbImNyZWF0ZSIsInBlcnNpc3QiLCJjcmVhdGVKU09OU3RvcmFnZSIsInN1YnNjcmliZVdpdGhTZWxlY3RvciIsInNhZmVTdG9yYWdlIiwiZ2V0SXRlbSIsInNldEl0ZW0iLCJyZW1vdmVJdGVtIiwibG9jYWxTdG9yYWdlIiwiaW5pdGlhbFN0YXRlIiwidXNlciIsImlzQXV0aGVudGljYXRlZCIsInNldHRpbmdzIiwic2lkZWJhckNvbGxhcHNlZCIsImFjdGl2ZU1vZHVsZSIsImF1dG9SZWZyZXNoIiwicmVmcmVzaEludGVydmFsIiwiZW5hYmxlQW5pbWF0aW9ucyIsImVuYWJsZVNvdW5kcyIsIm5vdGlmaWNhdGlvbnMiLCJjb25uZWN0aW9uU3RhdHVzIiwiYXBpIiwid2Vic29ja2V0IiwiYmxvY2tjaGFpbiIsImlzTG9hZGluZyIsImxvYWRpbmdNZXNzYWdlIiwiZXJyb3IiLCJ1c2VBcHBTdG9yZSIsInNldCIsImdldCIsInNldFVzZXIiLCJzZXRBdXRoZW50aWNhdGVkIiwiYXV0aGVudGljYXRlZCIsInVwZGF0ZVNldHRpbmdzIiwibmV3U2V0dGluZ3MiLCJzdGF0ZSIsImFkZE5vdGlmaWNhdGlvbiIsIm5vdGlmaWNhdGlvbiIsImlkIiwiRGF0ZSIsIm5vdyIsInRvU3RyaW5nIiwiTWF0aCIsInJhbmRvbSIsInN1YnN0ciIsInRpbWVzdGFtcCIsInJlYWQiLCJzbGljZSIsInJlbW92ZU5vdGlmaWNhdGlvbiIsImZpbHRlciIsIm4iLCJtYXJrTm90aWZpY2F0aW9uUmVhZCIsIm1hcCIsImNsZWFyTm90aWZpY2F0aW9ucyIsInVwZGF0ZUNvbm5lY3Rpb25TdGF0dXMiLCJzdGF0dXMiLCJzZXRMb2FkaW5nIiwibG9hZGluZyIsIm1lc3NhZ2UiLCJzZXRFcnJvciIsInJlc2V0IiwibmFtZSIsInN0b3JhZ2UiLCJwYXJ0aWFsaXplIiwicGVyc2lzdGVudCIsInVzZVVzZXIiLCJ1c2VJc0F1dGhlbnRpY2F0ZWQiLCJ1c2VTZXR0aW5ncyIsInVzZU5vdGlmaWNhdGlvbnMiLCJ1c2VVbnJlYWROb3RpZmljYXRpb25zIiwidXNlQ29ubmVjdGlvblN0YXR1cyIsInVzZUlzTG9hZGluZyIsInVzZUVycm9yIiwidXNlQXBwQWN0aW9ucyIsInN1YnNjcmliZSIsImdldFN0YXRlIiwiT2JqZWN0IiwiZW50cmllcyIsImZvckVhY2giLCJzZXJ2aWNlIiwidHlwZSIsInRpdGxlIiwidG9VcHBlckNhc2UiLCJvbmVIb3VyIiwiZXhwaXJlZE5vdGlmaWNhdGlvbnMiLCJsZW5ndGgiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/store/useAppStore.ts\n"));

/***/ })

});