export declare const scrollSepolia: {
    blockExplorers: {
        readonly default: {
            readonly name: "Scrolls<PERSON>";
            readonly url: "https://sepolia.scrollscan.com";
            readonly apiUrl: "https://api-sepolia.scrollscan.com/api";
        };
    };
    contracts: {
        readonly multicall3: {
            readonly address: "******************************************";
            readonly blockCreated: 9473;
        };
    };
    id: 534351;
    name: "Scroll <PERSON><PERSON>";
    nativeCurrency: {
        readonly name: "<PERSON><PERSON>";
        readonly symbol: "ETH";
        readonly decimals: 18;
    };
    rpcUrls: {
        readonly default: {
            readonly http: readonly ["https://sepolia-rpc.scroll.io"];
        };
    };
    sourceId?: number | undefined;
    testnet: true;
    custom?: Record<string, unknown> | undefined;
    fees?: import("../../index.js").ChainFees<undefined> | undefined;
    formatters?: undefined;
    serializers?: import("../../index.js").ChainSerializers<undefined, import("../../index.js").TransactionSerializable> | undefined;
};
//# sourceMappingURL=scrollSepolia.d.ts.map