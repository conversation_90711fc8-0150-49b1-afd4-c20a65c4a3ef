'use client';

import { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { Navigation } from '@/components/Navigation';
import { ModularDashboard } from '@/components/ModularDashboard';
import { AIModuleSelector } from '@/components/AIModuleSelector';
import { RealTimeNotifications } from '@/components/RealTimeNotifications';
import { SystemStatus } from '@/components/SystemStatus';
import { ClientOnly } from '@/components/ClientOnly';
import { RealTimeChat } from '@/components/RealTimeChat';
import { useAppActions } from '@/store/useAppStore';
import { useAIModules } from '@/hooks/useAIModules';
import { useRealTimeData } from '@/hooks/useRealTimeData';
import { debugger, debugUtils, useDebug } from '@/lib/debug';

export default function HomePage() {
  const [isChatOpen, setIsChatOpen] = useState(false);
  const { modules, isLoading: modulesLoading } = useAIModules();
  const { connectionStatus, marketData } = useRealTimeData();

  // Debug tracking
  const { logMount, logUnmount, logError, logInfo } = useDebug('HomePage');

  // Global state
  const { settings, updateSettings, updateConnectionStatus } = useAppActions();
  const activeModule = settings.activeModule;

  const handleModuleSelect = (moduleId: string) => {
    updateSettings({ activeModule: moduleId });
  };

  useEffect(() => {
    try {
      logMount();

      // Initialize production platform
      debugger.logInfo('🚀 Chainsight Production Platform Initialized');
      logInfo('Platform initialization started');

      // Performance tracking
      debugUtils.performanceMark('platform-init-start');

      // Update connection status
      updateConnectionStatus({
        api: connectionStatus === 'connected' ? 'connected' : 'disconnected',
        websocket: 'connected', // From real-time chat
        blockchain: 'connected', // From Web3 integration
      });

      debugUtils.performanceMark('platform-init-end');
      debugUtils.performanceMeasure('platform-init', 'platform-init-start', 'platform-init-end');

      logInfo('Platform initialization completed', {
        modulesCount: modules.length,
        connectionStatus,
        chatOpen: isChatOpen
      });

    } catch (error) {
      logError(error, 'Platform initialization failed');
    }

    return () => {
      logUnmount();
    };
  }, [connectionStatus, updateConnectionStatus, logMount, logUnmount, logError, logInfo, modules.length, isChatOpen]);

  return (
    <div className="min-h-screen bg-gradient-to-br from-cosmic-dark via-cosmic-mid to-cosmic-light">
      <ClientOnly>
        <Navigation />
      </ClientOnly>

      <main className="pt-20">
        {/* Hero Section */}
        <section className="relative overflow-hidden min-h-screen flex items-center justify-center">
          {/* Multi-layer Background Effects */}
          <div className="absolute inset-0 bg-gradient-to-br from-cosmic-dark via-cosmic-mid to-cosmic-light" />

          {/* Floating Color Orbs */}
          <div className="absolute top-[20%] left-[20%] w-96 h-96 bg-gradient-to-r from-neon-blue/20 to-neon-purple/20 rounded-full blur-3xl animate-pulse" />
          <div className="absolute top-[80%] right-[20%] w-96 h-96 bg-gradient-to-r from-neon-pink/20 to-neon-orange/20 rounded-full blur-3xl animate-pulse" />
          <div className="absolute top-[40%] right-[40%] w-96 h-96 bg-gradient-to-r from-neon-green/20 to-accent-cyan/20 rounded-full blur-3xl animate-pulse" />

          <div className="relative max-w-7xl mx-auto px-4 py-20 text-center">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              className="space-y-12"
            >
              {/* Main heading with rainbow pulse */}
              <motion.h1
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.4, duration: 0.8 }}
                className="text-6xl md:text-8xl font-bold leading-tight float"
              >
                <span className="block bg-gradient-to-r from-neon-blue via-neon-purple to-neon-pink bg-clip-text text-transparent">
                  CHAINSIGHT
                </span>
                <span className="block text-2xl md:text-4xl mt-4 neon-glow-blue">
                  by Connectouch
                </span>
              </motion.h1>

              {/* Subtitle */}
              <motion.p
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.6, duration: 0.8 }}
                className="text-xl md:text-2xl text-white/80 max-w-3xl mx-auto leading-relaxed"
              >
                🚀 Vibrant AI-Powered Platform with{' '}
                <span className="neon-glow-purple">Real-Time Analytics</span>,{' '}
                <span className="neon-glow-pink">Interactive Charts</span>, and{' '}
                <span className="neon-glow-blue">Futuristic Design</span>
              </motion.p>

              {/* Module Selector */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.8, duration: 0.8 }}
                className="max-w-4xl mx-auto"
              >
                <ClientOnly>
                  <AIModuleSelector
                    activeModule={activeModule}
                    onModuleChange={handleModuleSelect}
                    modules={modules}
                    loading={modulesLoading}
                  />
                </ClientOnly>
              </motion.div>
            </motion.div>
          </div>
        </section>

        {/* Dashboard Section */}
        <section className="py-20">
          <div className="max-w-7xl mx-auto px-4">
            <ClientOnly>
              <ModularDashboard
                activeModule={activeModule}
                marketData={marketData}
                modules={modules}
              />
            </ClientOnly>
          </div>
        </section>

        {/* System Status */}
        <div className="fixed bottom-4 right-4">
          <ClientOnly>
            <SystemStatus status={connectionStatus} />
          </ClientOnly>
        </div>

        {/* Real-time Notifications */}
        <ClientOnly>
          <RealTimeNotifications />
        </ClientOnly>
      </main>

      {/* Real-time Chat */}
      <ClientOnly>
        <RealTimeChat
          isOpen={isChatOpen}
          onToggle={() => setIsChatOpen(!isChatOpen)}
        />
      </ClientOnly>
    </div>
  );
}
