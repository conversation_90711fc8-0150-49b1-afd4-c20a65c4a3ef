import type { Address } from 'abitype'

import type { Byte<PERSON>rray, Hash, Hex, Signature } from '../../types/misc.js'
import { type GetAddressErrorType, getAddress } from '../address/getAddress.js'
import {
  type IsAddressEqualErrorType,
  isAddressEqual,
} from '../address/isAddressEqual.js'

import type { ErrorType } from '../../errors/utils.js'
import {
  type RecoverAddressErrorType,
  recoverAddress,
} from './recoverAddress.js'

export type VerifyHashParameters = {
  /** The address that signed the original message. */
  address: Address
  /** The hash to be verified. */
  hash: Hash
  /** The signature that was generated by signing the message with the address's private key. */
  signature: Hex | ByteArray | Signature
}

export type VerifyHashReturnType = boolean

export type VerifyHashErrorType =
  | IsAddressEqualErrorType
  | GetAddressErrorType
  | RecoverAddressErrorType
  | ErrorType

/**
 * Verify that a message was signed by the provided address.
 *
 * Note:  Only supports Externally Owned Accounts. Does not support Contract Accounts.
 *        It is highly recommended to use `publicClient.verifyHash` instead to ensure
 *        wallet interoperability.
 *
 * - Docs {@link https://viem.sh/docs/utilities/verifyHash}
 *
 * @param parameters - {@link VerifyHashParameters}
 * @returns Whether or not the signature is valid. {@link VerifyHashReturnType}
 */
export async function verifyHash({
  address,
  hash,
  signature,
}: VerifyHashParameters): Promise<VerifyHashReturnType> {
  return isAddressEqual(
    getAddress(address),
    await recoverAddress({ hash, signature }),
  )
}
