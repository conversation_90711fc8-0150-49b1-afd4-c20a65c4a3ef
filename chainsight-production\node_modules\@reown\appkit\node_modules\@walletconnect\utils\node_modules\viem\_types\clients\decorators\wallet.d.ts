import type { <PERSON><PERSON>, Address, TypedData } from 'abitype';
import type { Account } from '../../accounts/types.js';
import { type GetChainIdReturnType } from '../../actions/public/getChainId.js';
import { type AddChainParameters } from '../../actions/wallet/addChain.js';
import { type DeployContractParameters, type DeployContractReturnType } from '../../actions/wallet/deployContract.js';
import { type GetAddressesReturnType } from '../../actions/wallet/getAddresses.js';
import { type GetPermissionsReturnType } from '../../actions/wallet/getPermissions.js';
import { type PrepareTransactionRequestParameters, type PrepareTransactionRequestRequest, type PrepareTransactionRequestReturnType } from '../../actions/wallet/prepareTransactionRequest.js';
import { type RequestAddressesReturnType } from '../../actions/wallet/requestAddresses.js';
import { type RequestPermissionsParameters, type RequestPermissionsReturnType } from '../../actions/wallet/requestPermissions.js';
import { type SendRawTransactionParameters, type SendRawTransactionReturnType } from '../../actions/wallet/sendRawTransaction.js';
import { type SendTransactionParameters, type SendTransactionRequest, type SendTransactionReturnType } from '../../actions/wallet/sendTransaction.js';
import { type SignMessageParameters, type SignMessageReturnType } from '../../actions/wallet/signMessage.js';
import { type SignTransactionParameters, type SignTransactionRequest, type SignTransactionReturnType } from '../../actions/wallet/signTransaction.js';
import { type SignTypedDataParameters, type SignTypedDataReturnType } from '../../actions/wallet/signTypedData.js';
import { type SwitchChainParameters } from '../../actions/wallet/switchChain.js';
import { type WatchAssetParameters, type WatchAssetReturnType } from '../../actions/wallet/watchAsset.js';
import { type WriteContractParameters, type WriteContractReturnType } from '../../actions/wallet/writeContract.js';
import type { Chain } from '../../types/chain.js';
import type { ContractFunctionArgs, ContractFunctionName } from '../../types/contract.js';
import type { Client } from '../createClient.js';
import type { Transport } from '../transports/createTransport.js';
export type WalletActions<chain extends Chain | undefined = Chain | undefined, account extends Account | undefined = Account | undefined> = {
    /**
     * Adds an EVM chain to the wallet.
     *
     * - Docs: https://viem.sh/docs/actions/wallet/addChain
     * - JSON-RPC Methods: [`eth_addEthereumChain`](https://eips.ethereum.org/EIPS/eip-3085)
     *
     * @param args - {@link AddChainParameters}
     *
     * @example
     * import { createWalletClient, custom } from 'viem'
     * import { optimism } from 'viem/chains'
     *
     * const client = createWalletClient({
     *   transport: custom(window.ethereum),
     * })
     * await client.addChain({ chain: optimism })
     */
    addChain: (args: AddChainParameters) => Promise<void>;
    /**
     * Deploys a contract to the network, given bytecode and constructor arguments.
     *
     * - Docs: https://viem.sh/docs/contract/deployContract
     * - Examples: https://stackblitz.com/github/wevm/viem/tree/main/examples/contracts_deploying-contracts
     *
     * @param args - {@link DeployContractParameters}
     * @returns The [Transaction](https://viem.sh/docs/glossary/terms#transaction) hash. {@link DeployContractReturnType}
     *
     * @example
     * import { createWalletClient, http } from 'viem'
     * import { privateKeyToAccount } from 'viem/accounts'
     * import { mainnet } from 'viem/chains'
     *
     * const client = createWalletClient({
     *   account: privateKeyToAccount('0x…'),
     *   chain: mainnet,
     *   transport: http(),
     * })
     * const hash = await client.deployContract({
     *   abi: [],
     *   account: '0x…,
     *   bytecode: '0x608060405260405161083e38038061083e833981016040819052610...',
     * })
     */
    deployContract: <const abi extends Abi | readonly unknown[], chainOverride extends Chain | undefined>(args: DeployContractParameters<abi, chain, account, chainOverride>) => Promise<DeployContractReturnType>;
    /**
     * Returns a list of account addresses owned by the wallet or client.
     *
     * - Docs: https://viem.sh/docs/actions/wallet/getAddresses
     * - JSON-RPC Methods: [`eth_accounts`](https://ethereum.org/en/developers/docs/apis/json-rpc/#eth_accounts)
     *
     * @returns List of account addresses owned by the wallet or client. {@link GetAddressesReturnType}
     *
     * @example
     * import { createWalletClient, custom } from 'viem'
     * import { mainnet } from 'viem/chains'
     *
     * const client = createWalletClient({
     *   chain: mainnet,
     *   transport: custom(window.ethereum),
     * })
     * const accounts = await client.getAddresses()
     */
    getAddresses: () => Promise<GetAddressesReturnType>;
    /**
     * Returns the chain ID associated with the current network.
     *
     * - Docs: https://viem.sh/docs/actions/public/getChainId
     * - JSON-RPC Methods: [`eth_chainId`](https://ethereum.org/en/developers/docs/apis/json-rpc/#eth_chainid)
     *
     * @returns The current chain ID. {@link GetChainIdReturnType}
     *
     * @example
     * import { createWalletClient, http } from 'viem'
     * import { mainnet } from 'viem/chains'
     *
     * const client = createWalletClient({
     *   chain: mainnet,
     *   transport: custom(window.ethereum),
     * })
     * const chainId = await client.getChainId()
     * // 1
     */
    getChainId: () => Promise<GetChainIdReturnType>;
    /**
     * Gets the wallets current permissions.
     *
     * - Docs: https://viem.sh/docs/actions/wallet/getPermissions
     * - JSON-RPC Methods: [`wallet_getPermissions`](https://eips.ethereum.org/EIPS/eip-2255)
     *
     * @returns The wallet permissions. {@link GetPermissionsReturnType}
     *
     * @example
     * import { createWalletClient, custom } from 'viem'
     * import { mainnet } from 'viem/chains'
     *
     * const client = createWalletClient({
     *   chain: mainnet,
     *   transport: custom(window.ethereum),
     * })
     * const permissions = await client.getPermissions()
     */
    getPermissions: () => Promise<GetPermissionsReturnType>;
    /**
     * Prepares a transaction request for signing.
     *
     * - Docs: https://viem.sh/docs/actions/wallet/prepareTransactionRequest
     *
     * @param args - {@link PrepareTransactionRequestParameters}
     * @returns The transaction request. {@link PrepareTransactionRequestReturnType}
     *
     * @example
     * import { createWalletClient, custom } from 'viem'
     * import { mainnet } from 'viem/chains'
     *
     * const client = createWalletClient({
     *   chain: mainnet,
     *   transport: custom(window.ethereum),
     * })
     * const request = await client.prepareTransactionRequest({
     *   account: '******************************************',
     *   to: '******************************************',
     *   value: 1n,
     * })
     *
     * @example
     * // Account Hoisting
     * import { createWalletClient, http } from 'viem'
     * import { privateKeyToAccount } from 'viem/accounts'
     * import { mainnet } from 'viem/chains'
     *
     * const client = createWalletClient({
     *   account: privateKeyToAccount('0x…'),
     *   chain: mainnet,
     *   transport: custom(window.ethereum),
     * })
     * const request = await client.prepareTransactionRequest({
     *   to: '******************************************',
     *   value: 1n,
     * })
     */
    prepareTransactionRequest: <const request extends PrepareTransactionRequestRequest<chain, chainOverride>, chainOverride extends Chain | undefined = undefined, accountOverride extends Account | Address | undefined = undefined>(args: PrepareTransactionRequestParameters<chain, account, chainOverride, accountOverride, request>) => Promise<PrepareTransactionRequestReturnType<chain, account, chainOverride, accountOverride, request>>;
    /**
     * Requests a list of accounts managed by a wallet.
     *
     * - Docs: https://viem.sh/docs/actions/wallet/requestAddresses
     * - JSON-RPC Methods: [`eth_requestAccounts`](https://eips.ethereum.org/EIPS/eip-1102)
     *
     * Sends a request to the wallet, asking for permission to access the user's accounts. After the user accepts the request, it will return a list of accounts (addresses).
     *
     * This API can be useful for dapps that need to access the user's accounts in order to execute transactions or interact with smart contracts.
     *
     * @returns List of accounts managed by a wallet {@link RequestAddressesReturnType}
     *
     * @example
     * import { createWalletClient, custom } from 'viem'
     * import { mainnet } from 'viem/chains'
     *
     * const client = createWalletClient({
     *   chain: mainnet,
     *   transport: custom(window.ethereum),
     * })
     * const accounts = await client.requestAddresses()
     */
    requestAddresses: () => Promise<RequestAddressesReturnType>;
    /**
     * Requests permissions for a wallet.
     *
     * - Docs: https://viem.sh/docs/actions/wallet/requestPermissions
     * - JSON-RPC Methods: [`wallet_requestPermissions`](https://eips.ethereum.org/EIPS/eip-2255)
     *
     * @param args - {@link RequestPermissionsParameters}
     * @returns The wallet permissions. {@link RequestPermissionsReturnType}
     *
     * @example
     * import { createWalletClient, custom } from 'viem'
     * import { mainnet } from 'viem/chains'
     *
     * const client = createWalletClient({
     *   chain: mainnet,
     *   transport: custom(window.ethereum),
     * })
     * const permissions = await client.requestPermissions({
     *   eth_accounts: {}
     * })
     */
    requestPermissions: (args: RequestPermissionsParameters) => Promise<RequestPermissionsReturnType>;
    /**
     * Sends a **signed** transaction to the network
     *
     * - Docs: https://viem.sh/docs/actions/wallet/sendRawTransaction
     * - JSON-RPC Method: [`eth_sendRawTransaction`](https://ethereum.github.io/execution-apis/api-documentation/)
     *
     * @param client - Client to use
     * @param parameters - {@link SendRawTransactionParameters}
     * @returns The transaction hash. {@link SendRawTransactionReturnType}
     *
     * @example
     * import { createWalletClient, custom } from 'viem'
     * import { mainnet } from 'viem/chains'
     * import { sendRawTransaction } from 'viem/wallet'
     *
     * const client = createWalletClient({
     *   chain: mainnet,
     *   transport: custom(window.ethereum),
     * })
     *
     * const hash = await client.sendRawTransaction({
     *   serializedTransaction: '0x02f850018203118080825208808080c080a04012522854168b27e5dc3d5839bab5e6b39e1a0ffd343901ce1622e3d64b48f1a04e00902ae0502c4728cbf12156290df99c3ed7de85b1dbfe20b5c36931733a33'
     * })
     */
    sendRawTransaction: (args: SendRawTransactionParameters) => Promise<SendRawTransactionReturnType>;
    /**
     * Creates, signs, and sends a new transaction to the network.
     *
     * - Docs: https://viem.sh/docs/actions/wallet/sendTransaction
     * - Examples: https://stackblitz.com/github/wevm/viem/tree/main/examples/transactions_sending-transactions
     * - JSON-RPC Methods:
     *   - JSON-RPC Accounts: [`eth_sendTransaction`](https://ethereum.org/en/developers/docs/apis/json-rpc/#eth_sendtransaction)
     *   - Local Accounts: [`eth_sendRawTransaction`](https://ethereum.org/en/developers/docs/apis/json-rpc/#eth_sendrawtransaction)
     *
     * @param args - {@link SendTransactionParameters}
     * @returns The [Transaction](https://viem.sh/docs/glossary/terms#transaction) hash. {@link SendTransactionReturnType}
     *
     * @example
     * import { createWalletClient, custom } from 'viem'
     * import { mainnet } from 'viem/chains'
     *
     * const client = createWalletClient({
     *   chain: mainnet,
     *   transport: custom(window.ethereum),
     * })
     * const hash = await client.sendTransaction({
     *   account: '******************************************',
     *   to: '******************************************',
     *   value: 1000000000000000000n,
     * })
     *
     * @example
     * // Account Hoisting
     * import { createWalletClient, http } from 'viem'
     * import { privateKeyToAccount } from 'viem/accounts'
     * import { mainnet } from 'viem/chains'
     *
     * const client = createWalletClient({
     *   account: privateKeyToAccount('0x…'),
     *   chain: mainnet,
     *   transport: http(),
     * })
     * const hash = await client.sendTransaction({
     *   to: '******************************************',
     *   value: 1000000000000000000n,
     * })
     */
    sendTransaction: <const request extends SendTransactionRequest<chain, chainOverride>, chainOverride extends Chain | undefined = undefined>(args: SendTransactionParameters<chain, account, chainOverride, request>) => Promise<SendTransactionReturnType>;
    /**
     * Calculates an Ethereum-specific signature in [EIP-191 format](https://eips.ethereum.org/EIPS/eip-191): `keccak256("\x19Ethereum Signed Message:\n" + len(message) + message))`.
     *
     * - Docs: https://viem.sh/docs/actions/wallet/signMessage
     * - JSON-RPC Methods:
     *   - JSON-RPC Accounts: [`personal_sign`](https://docs.metamask.io/guide/signing-data#personal-sign)
     *   - Local Accounts: Signs locally. No JSON-RPC request.
     *
     * With the calculated signature, you can:
     * - use [`verifyMessage`](https://viem.sh/docs/utilities/verifyMessage) to verify the signature,
     * - use [`recoverMessageAddress`](https://viem.sh/docs/utilities/recoverMessageAddress) to recover the signing address from a signature.
     *
     * @param args - {@link SignMessageParameters}
     * @returns The signed message. {@link SignMessageReturnType}
     *
     * @example
     * import { createWalletClient, custom } from 'viem'
     * import { mainnet } from 'viem/chains'
     *
     * const client = createWalletClient({
     *   chain: mainnet,
     *   transport: custom(window.ethereum),
     * })
     * const signature = await client.signMessage({
     *   account: '******************************************',
     *   message: 'hello world',
     * })
     *
     * @example
     * // Account Hoisting
     * import { createWalletClient, http } from 'viem'
     * import { privateKeyToAccount } from 'viem/accounts'
     * import { mainnet } from 'viem/chains'
     *
     * const client = createWalletClient({
     *   account: privateKeyToAccount('0x…'),
     *   chain: mainnet,
     *   transport: http(),
     * })
     * const signature = await client.signMessage({
     *   message: 'hello world',
     * })
     */
    signMessage: (args: SignMessageParameters<account>) => Promise<SignMessageReturnType>;
    /**
     * Signs a transaction.
     *
     * - Docs: https://viem.sh/docs/actions/wallet/signTransaction
     * - JSON-RPC Methods:
     *   - JSON-RPC Accounts: [`eth_signTransaction`](https://ethereum.github.io/execution-apis/api-documentation/)
     *   - Local Accounts: Signs locally. No JSON-RPC request.
     *
     * @param args - {@link SignTransactionParameters}
     * @returns The signed message. {@link SignTransactionReturnType}
     *
     * @example
     * import { createWalletClient, custom } from 'viem'
     * import { mainnet } from 'viem/chains'
     *
     * const client = createWalletClient({
     *   chain: mainnet,
     *   transport: custom(window.ethereum),
     * })
     * const request = await client.prepareTransactionRequest({
     *   account: '******************************************',
     *   to: '******************************************',
     *   value: 1n,
     * })
     * const signature = await client.signTransaction(request)
     *
     * @example
     * // Account Hoisting
     * import { createWalletClient, http } from 'viem'
     * import { privateKeyToAccount } from 'viem/accounts'
     * import { mainnet } from 'viem/chains'
     *
     * const client = createWalletClient({
     *   account: privateKeyToAccount('0x…'),
     *   chain: mainnet,
     *   transport: custom(window.ethereum),
     * })
     * const request = await client.prepareTransactionRequest({
     *   to: '******************************************',
     *   value: 1n,
     * })
     * const signature = await client.signTransaction(request)
     */
    signTransaction: <chainOverride extends Chain | undefined, const request extends SignTransactionRequest<chain, chainOverride> = SignTransactionRequest<chain, chainOverride>>(args: SignTransactionParameters<chain, account, chainOverride, request>) => Promise<SignTransactionReturnType<request>>;
    /**
     * Signs typed data and calculates an Ethereum-specific signature in [EIP-191 format](https://eips.ethereum.org/EIPS/eip-191): `keccak256("\x19Ethereum Signed Message:\n" + len(message) + message))`.
     *
     * - Docs: https://viem.sh/docs/actions/wallet/signTypedData
     * - JSON-RPC Methods:
     *   - JSON-RPC Accounts: [`eth_signTypedData_v4`](https://docs.metamask.io/guide/signing-data#signtypeddata-v4)
     *   - Local Accounts: Signs locally. No JSON-RPC request.
     *
     * @param client - Client to use
     * @param args - {@link SignTypedDataParameters}
     * @returns The signed data. {@link SignTypedDataReturnType}
     *
     * @example
     * import { createWalletClient, custom } from 'viem'
     * import { mainnet } from 'viem/chains'
     *
     * const client = createWalletClient({
     *   chain: mainnet,
     *   transport: custom(window.ethereum),
     * })
     * const signature = await client.signTypedData({
     *   account: '******************************************',
     *   domain: {
     *     name: 'Ether Mail',
     *     version: '1',
     *     chainId: 1,
     *     verifyingContract: '******************************************',
     *   },
     *   types: {
     *     Person: [
     *       { name: 'name', type: 'string' },
     *       { name: 'wallet', type: 'address' },
     *     ],
     *     Mail: [
     *       { name: 'from', type: 'Person' },
     *       { name: 'to', type: 'Person' },
     *       { name: 'contents', type: 'string' },
     *     ],
     *   },
     *   primaryType: 'Mail',
     *   message: {
     *     from: {
     *       name: 'Cow',
     *       wallet: '******************************************',
     *     },
     *     to: {
     *       name: 'Bob',
     *       wallet: '******************************************',
     *     },
     *     contents: 'Hello, Bob!',
     *   },
     * })
     *
     * @example
     * // Account Hoisting
     * import { createWalletClient, http } from 'viem'
     * import { privateKeyToAccount } from 'viem/accounts'
     * import { mainnet } from 'viem/chains'
     *
     * const client = createWalletClient({
     *   account: privateKeyToAccount('0x…'),
     *   chain: mainnet,
     *   transport: http(),
     * })
     * const signature = await client.signTypedData({
     *   domain: {
     *     name: 'Ether Mail',
     *     version: '1',
     *     chainId: 1,
     *     verifyingContract: '******************************************',
     *   },
     *   types: {
     *     Person: [
     *       { name: 'name', type: 'string' },
     *       { name: 'wallet', type: 'address' },
     *     ],
     *     Mail: [
     *       { name: 'from', type: 'Person' },
     *       { name: 'to', type: 'Person' },
     *       { name: 'contents', type: 'string' },
     *     ],
     *   },
     *   primaryType: 'Mail',
     *   message: {
     *     from: {
     *       name: 'Cow',
     *       wallet: '******************************************',
     *     },
     *     to: {
     *       name: 'Bob',
     *       wallet: '******************************************',
     *     },
     *     contents: 'Hello, Bob!',
     *   },
     * })
     */
    signTypedData: <const typedData extends TypedData | {
        [key: string]: unknown;
    }, primaryType extends string>(args: SignTypedDataParameters<typedData, primaryType, account>) => Promise<SignTypedDataReturnType>;
    /**
     * Switch the target chain in a wallet.
     *
     * - Docs: https://viem.sh/docs/actions/wallet/switchChain
     * - JSON-RPC Methods: [`eth_switchEthereumChain`](https://eips.ethereum.org/EIPS/eip-3326)
     *
     * @param args - {@link SwitchChainParameters}
     *
     * @example
     * import { createWalletClient, custom } from 'viem'
     * import { mainnet, optimism } from 'viem/chains'
     *
     * const client = createWalletClient({
     *   chain: mainnet,
     *   transport: custom(window.ethereum),
     * })
     * await client.switchChain({ id: optimism.id })
     */
    switchChain: (args: SwitchChainParameters) => Promise<void>;
    /**
     * Adds an EVM chain to the wallet.
     *
     * - Docs: https://viem.sh/docs/actions/wallet/watchAsset
     * - JSON-RPC Methods: [`eth_switchEthereumChain`](https://eips.ethereum.org/EIPS/eip-747)
     *
     * @param args - {@link WatchAssetParameters}
     * @returns Boolean indicating if the token was successfully added. {@link WatchAssetReturnType}
     *
     * @example
     * import { createWalletClient, custom } from 'viem'
     * import { mainnet } from 'viem/chains'
     *
     * const client = createWalletClient({
     *   chain: mainnet,
     *   transport: custom(window.ethereum),
     * })
     * const success = await client.watchAsset({
     *   type: 'ERC20',
     *   options: {
     *     address: '******************************************',
     *     decimals: 18,
     *     symbol: 'WETH',
     *   },
     * })
     */
    watchAsset: (args: WatchAssetParameters) => Promise<WatchAssetReturnType>;
    /**
     * Executes a write function on a contract.
     *
     * - Docs: https://viem.sh/docs/contract/writeContract
     * - Examples: https://stackblitz.com/github/wevm/viem/tree/main/examples/contracts_writing-to-contracts
     *
     * A "write" function on a Solidity contract modifies the state of the blockchain. These types of functions require gas to be executed, and hence a [Transaction](https://viem.sh/docs/glossary/terms) is needed to be broadcast in order to change the state.
     *
     * Internally, uses a [Wallet Client](https://viem.sh/docs/clients/wallet) to call the [`sendTransaction` action](https://viem.sh/docs/actions/wallet/sendTransaction) with [ABI-encoded `data`](https://viem.sh/docs/contract/encodeFunctionData).
     *
     * __Warning: The `write` internally sends a transaction – it does not validate if the contract write will succeed (the contract may throw an error). It is highly recommended to [simulate the contract write with `contract.simulate`](https://viem.sh/docs/contract/writeContract#usage) before you execute it.__
     *
     * @param args - {@link WriteContractParameters}
     * @returns A [Transaction Hash](https://viem.sh/docs/glossary/terms#hash). {@link WriteContractReturnType}
     *
     * @example
     * import { createWalletClient, custom, parseAbi } from 'viem'
     * import { mainnet } from 'viem/chains'
     *
     * const client = createWalletClient({
     *   chain: mainnet,
     *   transport: custom(window.ethereum),
     * })
     * const hash = await client.writeContract({
     *   address: '******************************************',
     *   abi: parseAbi(['function mint(uint32 tokenId) nonpayable']),
     *   functionName: 'mint',
     *   args: [69420],
     * })
     *
     * @example
     * // With Validation
     * import { createWalletClient, custom, parseAbi } from 'viem'
     * import { mainnet } from 'viem/chains'
     *
     * const client = createWalletClient({
     *   chain: mainnet,
     *   transport: custom(window.ethereum),
     * })
     * const { request } = await client.simulateContract({
     *   address: '******************************************',
     *   abi: parseAbi(['function mint(uint32 tokenId) nonpayable']),
     *   functionName: 'mint',
     *   args: [69420],
     * }
     * const hash = await client.writeContract(request)
     */
    writeContract: <const abi extends Abi | readonly unknown[], functionName extends ContractFunctionName<abi, 'payable' | 'nonpayable'>, args extends ContractFunctionArgs<abi, 'payable' | 'nonpayable', functionName>, chainOverride extends Chain | undefined = undefined>(args: WriteContractParameters<abi, functionName, args, chain, account, chainOverride>) => Promise<WriteContractReturnType>;
};
export declare function walletActions<transport extends Transport, chain extends Chain | undefined = Chain | undefined, account extends Account | undefined = Account | undefined>(client: Client<transport, chain, account>): WalletActions<chain, account>;
//# sourceMappingURL=wallet.d.ts.map