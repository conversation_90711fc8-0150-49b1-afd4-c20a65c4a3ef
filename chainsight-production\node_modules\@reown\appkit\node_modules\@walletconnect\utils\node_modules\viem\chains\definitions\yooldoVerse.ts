import { define<PERSON><PERSON><PERSON> } from '../../utils/chain/defineChain.js'

export const yooldoVerse = /*#__PURE__*/ define<PERSON>hain({
  id: 50_005,
  name: '<PERSON><PERSON><PERSON> Verse',
  nativeCurrency: { name: '<PERSON><PERSON>', symbol: 'OAS', decimals: 18 },
  rpcUrls: {
    default: {
      http: ['https://rpc.yooldo-verse.xyz'],
    },
  },
  blockExplorers: {
    default: {
      name: '<PERSON><PERSON><PERSON> Verse Explorer',
      url: 'https://explorer.yooldo-verse.xyz',
    },
  },
})
