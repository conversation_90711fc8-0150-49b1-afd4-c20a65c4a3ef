{"name": "@chainsight/crypto-engine", "version": "1.0.0", "description": "Cryptocurrency and blockchain utilities for Chainsight platform", "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"build": "tsc", "dev": "tsc --watch", "test": "jest", "lint": "eslint src/**/*.ts", "clean": "rm -rf dist"}, "dependencies": {"web3": "^4.2.0", "ethers": "^6.7.1", "@solana/web3.js": "^1.78.4", "axios": "^1.5.0", "bignumber.js": "^9.1.1", "crypto-js": "^4.1.1"}, "devDependencies": {"@types/node": "^20.5.0", "typescript": "^5.1.6", "jest": "^29.6.2", "@types/jest": "^29.5.4", "eslint": "^8.47.0", "@typescript-eslint/eslint-plugin": "^6.4.0", "@typescript-eslint/parser": "^6.4.0"}, "keywords": ["crypto", "blockchain", "ethereum", "web3", "defi", "chainsight"], "author": "Connectouch", "license": "MIT"}