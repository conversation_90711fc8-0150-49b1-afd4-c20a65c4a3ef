// Chatbot Engine Plugin - Main exports
export * from './bot/ChatBot';
export * from './bot/ConversationManager';
export * from './nlp/IntentClassifier';
export * from './nlp/EntityExtractor';
export * from './responses/ResponseGenerator';
export * from './knowledge/KnowledgeBase';
export * from './analytics/ConversationAnalytics';
export * from './types/ChatbotTypes';
export * from './utils/ChatbotUtils';

// Plugin configuration
export interface ChatbotEngineConfig {
  botName?: string;
  personality?: 'professional' | 'friendly' | 'luxury' | 'technical';
  language?: string;
  enableAnalytics?: boolean;
  enableLearning?: boolean;
  confidenceThreshold?: number;
  maxConversationLength?: number;
  responseDelay?: number;
  enableTypingIndicator?: boolean;
}

// Default configuration
export const defaultChatbotConfig: ChatbotEngineConfig = {
  botName: 'Chainsight Assistant',
  personality: 'luxury',
  language: 'en',
  enableAnalytics: true,
  enableLearning: true,
  confidenceThreshold: 0.7,
  maxConversationLength: 50,
  responseDelay: 1000,
  enableTypingIndicator: true
};

// Chatbot Engine Plugin Class
export class ChatbotEngine {
  private config: ChatbotEngineConfig;
  private knowledgeBase: Map<string, any> = new Map();
  private conversations: Map<string, any[]> = new Map();

  constructor(config: Partial<ChatbotEngineConfig> = {}) {
    this.config = { ...defaultChatbotConfig, ...config };
    this.initializeKnowledgeBase();
  }

  getConfig(): ChatbotEngineConfig {
    return this.config;
  }

  updateConfig(newConfig: Partial<ChatbotEngineConfig>): void {
    this.config = { ...this.config, ...newConfig };
  }

  private initializeKnowledgeBase(): void {
    // Initialize with Chainsight-specific knowledge
    this.knowledgeBase.set('crypto', {
      intents: ['price_inquiry', 'token_creation', 'market_analysis', 'trading_help'],
      responses: {
        price_inquiry: 'I can provide real-time cryptocurrency prices and market analysis. Which token would you like to know about?',
        token_creation: 'I can help you create and deploy ERC20 tokens with custom features. What type of token are you looking to create?',
        market_analysis: 'I offer comprehensive market analysis using AI algorithms. Let me analyze the current market conditions for you.',
        trading_help: 'I can assist with trading strategies and market insights. What trading assistance do you need?'
      }
    });

    this.knowledgeBase.set('finance', {
      intents: ['stock_analysis', 'portfolio_review', 'investment_advice', 'risk_assessment'],
      responses: {
        stock_analysis: 'I can analyze stocks using technical and fundamental analysis. Which stock would you like me to analyze?',
        portfolio_review: 'I can review your portfolio for optimization opportunities. Share your holdings for a detailed analysis.',
        investment_advice: 'I provide AI-powered investment recommendations based on market data and risk analysis.',
        risk_assessment: 'I can assess investment risks using advanced algorithms. What investment would you like me to evaluate?'
      }
    });

    this.knowledgeBase.set('design', {
      intents: ['animation_creation', 'ui_design', 'logo_design', 'branding_help'],
      responses: {
        animation_creation: 'I can create stunning animations including Lottie files, SVG animations, and CSS effects. What type of animation do you need?',
        ui_design: 'I can help design luxury UI components with our signature black and gold theme. What UI elements do you need?',
        logo_design: 'I can create professional logos and branding materials. Tell me about your brand vision.',
        branding_help: 'I can assist with complete branding solutions including color schemes, typography, and visual identity.'
      }
    });

    this.knowledgeBase.set('hr_legal', {
      intents: ['resume_analysis', 'contract_review', 'compliance_check', 'document_analysis'],
      responses: {
        resume_analysis: 'I can analyze resumes for skills, experience, and improvement recommendations. Upload a resume for detailed analysis.',
        contract_review: 'I can review contracts for key terms, risks, and compliance issues. What type of contract needs review?',
        compliance_check: 'I can check documents for regulatory compliance and identify potential issues.',
        document_analysis: 'I can analyze various documents including PDFs, Word files, and text documents. What document needs analysis?'
      }
    });
  }

  // Core chatbot functionality
  async processMessage(message: string, userId: string, sessionId?: string): Promise<any> {
    const session = sessionId || this.generateSessionId();
    
    // Get or create conversation history
    if (!this.conversations.has(session)) {
      this.conversations.set(session, []);
    }
    
    const conversation = this.conversations.get(session)!;
    
    // Add user message to conversation
    conversation.push({
      role: 'user',
      content: message,
      timestamp: new Date(),
      userId
    });

    // Classify intent and extract entities
    const intent = await this.classifyIntent(message);
    const entities = await this.extractEntities(message);
    
    // Generate response
    const response = await this.generateResponse(message, intent, entities, conversation);
    
    // Add bot response to conversation
    conversation.push({
      role: 'assistant',
      content: response.content,
      timestamp: new Date(),
      intent,
      entities,
      confidence: response.confidence
    });

    // Trim conversation if too long
    if (conversation.length > (this.config.maxConversationLength || 50)) {
      conversation.splice(0, conversation.length - (this.config.maxConversationLength || 50));
    }

    return {
      response: response.content,
      intent,
      entities,
      confidence: response.confidence,
      sessionId: session,
      timestamp: new Date()
    };
  }

  private async classifyIntent(message: string): Promise<string> {
    const messageLower = message.toLowerCase();
    
    // Simple intent classification based on keywords
    if (this.containsKeywords(messageLower, ['price', 'cost', 'value', 'worth'])) {
      return 'price_inquiry';
    }
    if (this.containsKeywords(messageLower, ['create', 'make', 'build', 'deploy'])) {
      return 'creation_request';
    }
    if (this.containsKeywords(messageLower, ['analyze', 'analysis', 'review', 'check'])) {
      return 'analysis_request';
    }
    if (this.containsKeywords(messageLower, ['help', 'assist', 'support', 'guide'])) {
      return 'help_request';
    }
    if (this.containsKeywords(messageLower, ['hello', 'hi', 'hey', 'greetings'])) {
      return 'greeting';
    }
    
    return 'general_inquiry';
  }

  private async extractEntities(message: string): Promise<any[]> {
    const entities = [];
    const messageLower = message.toLowerCase();
    
    // Extract cryptocurrency symbols
    const cryptoSymbols = ['btc', 'eth', 'usdt', 'bnb', 'ada', 'sol', 'dot', 'link'];
    for (const symbol of cryptoSymbols) {
      if (messageLower.includes(symbol)) {
        entities.push({ type: 'cryptocurrency', value: symbol.toUpperCase() });
      }
    }
    
    // Extract stock symbols (basic pattern)
    const stockPattern = /\b[A-Z]{1,5}\b/g;
    const stockMatches = message.match(stockPattern);
    if (stockMatches) {
      stockMatches.forEach(match => {
        entities.push({ type: 'stock_symbol', value: match });
      });
    }
    
    // Extract numbers (prices, amounts, etc.)
    const numberPattern = /\$?[\d,]+\.?\d*/g;
    const numberMatches = message.match(numberPattern);
    if (numberMatches) {
      numberMatches.forEach(match => {
        entities.push({ type: 'number', value: match });
      });
    }
    
    return entities;
  }

  private async generateResponse(
    message: string, 
    intent: string, 
    entities: any[], 
    conversation: any[]
  ): Promise<{ content: string; confidence: number }> {
    
    // Get domain-specific response
    const domainResponse = this.getDomainResponse(message, intent);
    if (domainResponse) {
      return { content: domainResponse, confidence: 0.9 };
    }

    // Generate contextual response based on conversation history
    const contextualResponse = this.getContextualResponse(message, intent, conversation);
    if (contextualResponse) {
      return { content: contextualResponse, confidence: 0.8 };
    }

    // Default responses
    const defaultResponses = {
      greeting: `Hello! I'm ${this.config.botName}, your luxury AI assistant. I can help you with cryptocurrency analysis, financial planning, design creation, document analysis, and much more. How can I assist you today?`,
      help_request: 'I\'m here to help! I can assist with crypto trading, market analysis, smart contract deployment, portfolio optimization, animation creation, document analysis, and more. What specific area interests you?',
      general_inquiry: 'I understand you\'re looking for assistance. As your Chainsight AI, I specialize in blockchain operations, financial analysis, design services, and document processing. Could you provide more details about what you need?'
    };

    return {
      content: defaultResponses[intent as keyof typeof defaultResponses] || defaultResponses.general_inquiry,
      confidence: 0.7
    };
  }

  private getDomainResponse(message: string, intent: string): string | null {
    const messageLower = message.toLowerCase();
    
    // Check each domain for relevant keywords
    for (const [domain, data] of this.knowledgeBase.entries()) {
      const domainKeywords = this.getDomainKeywords(domain);
      if (this.containsKeywords(messageLower, domainKeywords)) {
        // Find matching intent response
        const responses = data.responses;
        if (responses[intent]) {
          return responses[intent];
        }
        // Return first available response for the domain
        return Object.values(responses)[0] as string;
      }
    }
    
    return null;
  }

  private getDomainKeywords(domain: string): string[] {
    const keywords = {
      crypto: ['crypto', 'bitcoin', 'ethereum', 'token', 'blockchain', 'defi', 'nft'],
      finance: ['stock', 'investment', 'portfolio', 'trading', 'market', 'finance'],
      design: ['design', 'animation', 'logo', 'ui', 'ux', 'branding', 'visual'],
      hr_legal: ['resume', 'contract', 'legal', 'hr', 'document', 'compliance']
    };
    
    return keywords[domain as keyof typeof keywords] || [];
  }

  private getContextualResponse(message: string, intent: string, conversation: any[]): string | null {
    // Analyze recent conversation for context
    const recentMessages = conversation.slice(-5);
    const topics = recentMessages.map(msg => msg.intent).filter(Boolean);
    
    if (topics.length > 0) {
      const dominantTopic = this.getMostFrequent(topics);
      return this.getContinuationResponse(dominantTopic, intent);
    }
    
    return null;
  }

  private getContinuationResponse(previousTopic: string, currentIntent: string): string | null {
    const continuations = {
      price_inquiry: 'Would you like me to analyze the price trends or set up price alerts for you?',
      analysis_request: 'I can provide a more detailed analysis if you\'d like. What specific aspects interest you most?',
      creation_request: 'Great! I can help you create that. Would you like to see some templates or start from scratch?'
    };
    
    return continuations[previousTopic as keyof typeof continuations] || null;
  }

  // Utility methods
  private containsKeywords(text: string, keywords: string[]): boolean {
    return keywords.some(keyword => text.includes(keyword));
  }

  private getMostFrequent(array: string[]): string {
    const frequency = array.reduce((acc, item) => {
      acc[item] = (acc[item] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);
    
    return Object.keys(frequency).reduce((a, b) => 
      frequency[a] > frequency[b] ? a : b
    );
  }

  private generateSessionId(): string {
    return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  // Public utility methods
  getConversationHistory(sessionId: string): any[] {
    return this.conversations.get(sessionId) || [];
  }

  clearConversation(sessionId: string): void {
    this.conversations.delete(sessionId);
  }

  getActiveConversations(): string[] {
    return Array.from(this.conversations.keys());
  }

  addKnowledge(domain: string, knowledge: any): void {
    this.knowledgeBase.set(domain, knowledge);
  }

  getKnowledgeDomains(): string[] {
    return Array.from(this.knowledgeBase.keys());
  }
}

// Export singleton instance
export const chatbotEngine = new ChatbotEngine();

// Plugin metadata
export const PLUGIN_INFO = {
  name: 'Chatbot Engine',
  version: '1.0.0',
  description: 'Intelligent conversational AI for customer support',
  author: 'Connectouch',
  capabilities: [
    'Natural language processing',
    'Intent classification',
    'Entity extraction',
    'Contextual responses',
    'Multi-domain knowledge',
    'Conversation analytics',
    'Learning capabilities'
  ]
};
