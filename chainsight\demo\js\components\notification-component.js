/* ==================== NOTIFICATION COMPONENT ====================
   Toast notification system with glass effect
   ========================================================== */

class NotificationComponent {
    constructor() {
        this.container = null;
        this.notifications = new Map();
        this.defaultOptions = {
            type: 'info', // success, warning, error, info
            title: '',
            message: '',
            duration: 5000,
            closable: true,
            position: 'top-right', // top-left, top-right, bottom-left, bottom-right, top-center, bottom-center
            animation: 'slide', // slide, fade, bounce
            onClick: null,
            onClose: null,
            persistent: false,
            showProgress: true
        };
        
        this.init();
    }

    /**
     * Initialize notification system
     */
    init() {
        this.createContainer();
    }

    /**
     * Create notification container
     */
    createContainer() {
        this.container = DOMUtils.createElement('div', {
            className: 'notification-container',
            id: 'notification-container'
        });

        document.body.appendChild(this.container);

        // Add container styles
        this.addContainerStyles();
    }

    /**
     * Add container styles
     */
    addContainerStyles() {
        const styles = `
            .notification-container {
                position: fixed;
                z-index: var(--z-toast);
                pointer-events: none;
                max-width: 400px;
            }
            
            .notification-container.top-right {
                top: var(--space-lg);
                right: var(--space-lg);
            }
            
            .notification-container.top-left {
                top: var(--space-lg);
                left: var(--space-lg);
            }
            
            .notification-container.bottom-right {
                bottom: var(--space-lg);
                right: var(--space-lg);
            }
            
            .notification-container.bottom-left {
                bottom: var(--space-lg);
                left: var(--space-lg);
            }
            
            .notification-container.top-center {
                top: var(--space-lg);
                left: 50%;
                transform: translateX(-50%);
            }
            
            .notification-container.bottom-center {
                bottom: var(--space-lg);
                left: 50%;
                transform: translateX(-50%);
            }
        `;

        if (!document.getElementById('notification-styles')) {
            const styleSheet = DOMUtils.createElement('style', {
                id: 'notification-styles'
            }, styles);
            document.head.appendChild(styleSheet);
        }
    }

    /**
     * Show notification
     * @param {Object} options - Notification options
     * @returns {string} - Notification ID
     */
    show(options = {}) {
        const config = { ...this.defaultOptions, ...options };
        const id = `notification-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
        
        const notification = this.createNotification(id, config);
        this.notifications.set(id, { element: notification, config });

        // Update container position
        this.updateContainerPosition(config.position);

        // Add to container
        this.container.appendChild(notification);

        // Trigger animation
        requestAnimationFrame(() => {
            notification.classList.add('show');
        });

        // Auto-hide if not persistent
        if (!config.persistent && config.duration > 0) {
            setTimeout(() => {
                this.hide(id);
            }, config.duration);
        }

        return id;
    }

    /**
     * Create notification element
     * @param {string} id - Notification ID
     * @param {Object} config - Notification configuration
     * @returns {Element} - Notification element
     */
    createNotification(id, config) {
        const notification = DOMUtils.createElement('div', {
            className: `notification notification-${config.type} notification-${config.animation}`,
            'data-id': id
        });

        // Create notification content
        const content = DOMUtils.createElement('div', {
            className: 'notification-content glass-effect'
        });

        // Add icon
        const icon = this.createIcon(config.type);
        content.appendChild(icon);

        // Add text content
        const textContent = DOMUtils.createElement('div', {
            className: 'notification-text'
        });

        if (config.title) {
            const title = DOMUtils.createElement('div', {
                className: 'notification-title font-semibold'
            }, config.title);
            textContent.appendChild(title);
        }

        if (config.message) {
            const message = DOMUtils.createElement('div', {
                className: 'notification-message'
            }, config.message);
            textContent.appendChild(message);
        }

        content.appendChild(textContent);

        // Add close button
        if (config.closable) {
            const closeBtn = DOMUtils.createElement('button', {
                className: 'notification-close',
                onclick: () => this.hide(id)
            }, '×');
            content.appendChild(closeBtn);
        }

        notification.appendChild(content);

        // Add progress bar
        if (config.showProgress && !config.persistent && config.duration > 0) {
            const progress = this.createProgressBar(config.duration);
            notification.appendChild(progress);
        }

        // Add click handler
        if (config.onClick) {
            notification.style.cursor = 'pointer';
            notification.addEventListener('click', (e) => {
                if (!e.target.classList.contains('notification-close')) {
                    config.onClick(id, config);
                }
            });
        }

        // Make pointer events work
        notification.style.pointerEvents = 'auto';

        return notification;
    }

    /**
     * Create notification icon
     * @param {string} type - Notification type
     * @returns {Element} - Icon element
     */
    createIcon(type) {
        const iconMap = {
            success: '✓',
            warning: '⚠',
            error: '✕',
            info: 'ℹ'
        };

        return DOMUtils.createElement('div', {
            className: `notification-icon notification-icon-${type}`
        }, iconMap[type] || iconMap.info);
    }

    /**
     * Create progress bar
     * @param {number} duration - Duration in milliseconds
     * @returns {Element} - Progress bar element
     */
    createProgressBar(duration) {
        const progressContainer = DOMUtils.createElement('div', {
            className: 'notification-progress'
        });

        const progressBar = DOMUtils.createElement('div', {
            className: 'notification-progress-bar'
        });

        progressContainer.appendChild(progressBar);

        // Animate progress bar
        progressBar.style.animationDuration = `${duration}ms`;

        return progressContainer;
    }

    /**
     * Hide notification
     * @param {string} id - Notification ID
     */
    hide(id) {
        const notification = this.notifications.get(id);
        if (!notification) return;

        const element = notification.element;
        const config = notification.config;

        // Add hide animation
        element.classList.add('hide');

        // Remove after animation
        setTimeout(() => {
            if (element.parentNode) {
                element.parentNode.removeChild(element);
            }
            this.notifications.delete(id);

            // Callback
            if (config.onClose) {
                config.onClose(id, config);
            }
        }, 300);
    }

    /**
     * Hide all notifications
     */
    hideAll() {
        this.notifications.forEach((_, id) => {
            this.hide(id);
        });
    }

    /**
     * Update container position
     * @param {string} position - Position string
     */
    updateContainerPosition(position) {
        // Remove all position classes
        this.container.className = 'notification-container';
        // Add new position class
        this.container.classList.add(position);
    }

    /**
     * Success notification
     * @param {string} message - Message text
     * @param {Object} options - Additional options
     * @returns {string} - Notification ID
     */
    success(message, options = {}) {
        return this.show({
            type: 'success',
            message,
            ...options
        });
    }

    /**
     * Warning notification
     * @param {string} message - Message text
     * @param {Object} options - Additional options
     * @returns {string} - Notification ID
     */
    warning(message, options = {}) {
        return this.show({
            type: 'warning',
            message,
            ...options
        });
    }

    /**
     * Error notification
     * @param {string} message - Message text
     * @param {Object} options - Additional options
     * @returns {string} - Notification ID
     */
    error(message, options = {}) {
        return this.show({
            type: 'error',
            message,
            duration: 0, // Don't auto-hide errors
            ...options
        });
    }

    /**
     * Info notification
     * @param {string} message - Message text
     * @param {Object} options - Additional options
     * @returns {string} - Notification ID
     */
    info(message, options = {}) {
        return this.show({
            type: 'info',
            message,
            ...options
        });
    }
}

// Add notification styles
const notificationStyles = `
    .notification {
        margin-bottom: var(--space-sm);
        opacity: 0;
        transform: translateX(100%);
        transition: all var(--transition-normal);
        max-width: 100%;
        position: relative;
        overflow: hidden;
    }
    
    .notification.show {
        opacity: 1;
        transform: translateX(0);
    }
    
    .notification.hide {
        opacity: 0;
        transform: translateX(100%);
    }
    
    .notification-content {
        display: flex;
        align-items: flex-start;
        gap: var(--space-sm);
        padding: var(--space-md);
        border-radius: var(--radius-lg);
        min-height: 60px;
    }
    
    .notification-icon {
        width: 24px;
        height: 24px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: bold;
        font-size: var(--text-sm);
        flex-shrink: 0;
    }
    
    .notification-icon-success {
        background: var(--color-success);
        color: white;
    }
    
    .notification-icon-warning {
        background: var(--color-warning);
        color: white;
    }
    
    .notification-icon-error {
        background: var(--color-error);
        color: white;
    }
    
    .notification-icon-info {
        background: var(--color-info);
        color: white;
    }
    
    .notification-text {
        flex: 1;
        min-width: 0;
    }
    
    .notification-title {
        font-size: var(--text-sm);
        margin-bottom: var(--space-xs);
    }
    
    .notification-message {
        font-size: var(--text-sm);
        opacity: 0.9;
        line-height: 1.4;
    }
    
    .notification-close {
        background: none;
        border: none;
        color: var(--text-secondary);
        cursor: pointer;
        font-size: var(--text-lg);
        padding: 0;
        width: 20px;
        height: 20px;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 50%;
        transition: all var(--transition-fast);
        flex-shrink: 0;
    }
    
    .notification-close:hover {
        background: rgba(255, 255, 255, 0.1);
        color: var(--text-primary);
    }
    
    .notification-progress {
        position: absolute;
        bottom: 0;
        left: 0;
        right: 0;
        height: 3px;
        background: rgba(255, 255, 255, 0.1);
        overflow: hidden;
    }
    
    .notification-progress-bar {
        height: 100%;
        background: var(--neon-blue);
        width: 100%;
        animation: notificationProgress linear forwards;
    }
    
    @keyframes notificationProgress {
        from { width: 100%; }
        to { width: 0%; }
    }
    
    .notification-fade {
        transform: translateY(-20px);
    }
    
    .notification-fade.show {
        transform: translateY(0);
    }
    
    .notification-bounce.show {
        animation: notificationBounce 0.5s ease-out;
    }
    
    @keyframes notificationBounce {
        0% { transform: translateX(100%) scale(0.8); }
        50% { transform: translateX(-10px) scale(1.05); }
        100% { transform: translateX(0) scale(1); }
    }
`;

// Inject styles
if (typeof document !== 'undefined' && !document.getElementById('notification-component-styles')) {
    const styleSheet = DOMUtils.createElement('style', {
        id: 'notification-component-styles'
    }, notificationStyles);
    document.head.appendChild(styleSheet);
}

// Create global instance
const notifications = new NotificationComponent();

// Export for module systems
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { NotificationComponent, notifications };
}

// Global assignment for browser
if (typeof window !== 'undefined') {
    window.NotificationComponent = NotificationComponent;
    window.notifications = notifications;
}
