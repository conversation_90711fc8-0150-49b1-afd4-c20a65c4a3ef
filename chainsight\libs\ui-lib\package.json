{"name": "@chainsight/ui-lib", "version": "1.0.0", "description": "Luxury UI component library for Chainsight platform", "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"build": "tsc && tailwindcss -i ./src/styles/globals.css -o ./dist/styles.css", "dev": "tsc --watch", "test": "jest", "lint": "eslint src/**/*.{ts,tsx}", "clean": "rm -rf dist", "storybook": "storybook dev -p 6006", "build-storybook": "storybook build"}, "dependencies": {"react": "^18.2.0", "react-dom": "^18.2.0", "framer-motion": "^10.16.4", "lucide-react": "^0.279.0", "class-variance-authority": "^0.7.0", "clsx": "^2.0.0", "tailwind-merge": "^1.14.0"}, "devDependencies": {"@types/react": "^18.2.21", "@types/react-dom": "^18.2.7", "@types/node": "^20.5.0", "typescript": "^5.1.6", "tailwindcss": "^3.3.3", "autoprefixer": "^10.4.15", "postcss": "^8.4.29", "jest": "^29.6.2", "@types/jest": "^29.5.4", "eslint": "^8.47.0", "@typescript-eslint/eslint-plugin": "^6.4.0", "@typescript-eslint/parser": "^6.4.0", "@storybook/react": "^7.4.0", "@storybook/react-vite": "^7.4.0"}, "peerDependencies": {"react": ">=18.0.0", "react-dom": ">=18.0.0"}, "keywords": ["react", "ui", "components", "luxury", "design-system", "chainsight"], "author": "Connectouch", "license": "MIT"}