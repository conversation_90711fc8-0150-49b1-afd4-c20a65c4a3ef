"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_rainbow-me_rainbowkit_dist_de_DE-P43L3PR7_js"],{

/***/ "(app-pages-browser)/./node_modules/@rainbow-me/rainbowkit/dist/de_DE-P43L3PR7.js":
/*!********************************************************************!*\
  !*** ./node_modules/@rainbow-me/rainbowkit/dist/de_DE-P43L3PR7.js ***!
  \********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ de_DE_default)\n/* harmony export */ });\n/* __next_internal_client_entry_do_not_use__ default auto */ // src/locales/de_DE.json\nvar de_DE_default = '{\\n  \"connect_wallet\": {\\n    \"label\": \"Wallet verbinden\",\\n    \"wrong_network\": {\\n      \"label\": \"Falsches Netzwerk\"\\n    }\\n  },\\n  \"intro\": {\\n    \"title\": \"Was ist ein Wallet?\",\\n    \"description\": \"Ein Wallet wird verwendet, um digitale Assets zu senden, empfangen, speichern und anzeigen. Es ist auch eine neue M\\xF6glichkeit, sich anzumelden, ohne auf jeder Website neue Konten und Passw\\xF6rter erstellen zu m\\xFCssen.\",\\n    \"digital_asset\": {\\n      \"title\": \"Ein Zuhause f\\xFCr Ihre digitalen Verm\\xF6genswerte\",\\n      \"description\": \"Wallets werden verwendet, um digitale Assets wie Ethereum und NFTs zu senden, empfangen, speichern und anzeigen.\"\\n    },\\n    \"login\": {\\n      \"title\": \"Eine neue M\\xF6glichkeit, sich anzumelden\",\\n      \"description\": \"Anstatt auf jeder Website neue Konten und Passw\\xF6rter zu erstellen, verbinden Sie einfach Ihr Wallet.\"\\n    },\\n    \"get\": {\\n      \"label\": \"Ein Wallet holen\"\\n    },\\n    \"learn_more\": {\\n      \"label\": \"Mehr erfahren\"\\n    }\\n  },\\n  \"sign_in\": {\\n    \"label\": \"Verifizieren Sie Ihr Konto\",\\n    \"description\": \"Um die Verbindung abzuschlie\\xDFen, m\\xFCssen Sie eine Nachricht in Ihrem Wallet signieren, um zu verifizieren, dass Sie der Inhaber dieses Kontos sind.\",\\n    \"message\": {\\n      \"send\": \"Nachricht signieren\",\\n      \"preparing\": \"Nachricht wird vorbereitet...\",\\n      \"cancel\": \"Abbrechen\",\\n      \"preparing_error\": \"Fehler beim Vorbereiten der Nachricht, bitte erneut versuchen!\"\\n    },\\n    \"signature\": {\\n      \"waiting\": \"Warten auf Signatur...\",\\n      \"verifying\": \"Signatur wird \\xFCberpr\\xFCft...\",\\n      \"signing_error\": \"Fehler beim Signieren der Nachricht, bitte erneut versuchen!\",\\n      \"verifying_error\": \"Fehler bei der \\xDCberpr\\xFCfung der Signatur, bitte erneut versuchen!\",\\n      \"oops_error\": \"Oops, etwas ist schiefgelaufen!\"\\n    }\\n  },\\n  \"connect\": {\\n    \"label\": \"Verbinden\",\\n    \"title\": \"Ein Wallet verbinden\",\\n    \"new_to_ethereum\": {\\n      \"description\": \"Neu bei Ethereum-Wallets?\",\\n      \"learn_more\": {\\n        \"label\": \"Mehr erfahren\"\\n      }\\n    },\\n    \"learn_more\": {\\n      \"label\": \"Mehr erfahren\"\\n    },\\n    \"recent\": \"Zuletzt\",\\n    \"status\": {\\n      \"opening\": \"%{wallet} wird ge\\xF6ffnet...\",\\n      \"connecting\": \"Verbinden\",\\n      \"connect_mobile\": \"Fahren Sie in %{wallet} fort\",\\n      \"not_installed\": \"%{wallet} ist nicht installiert\",\\n      \"not_available\": \"%{wallet} ist nicht verf\\xFCgbar\",\\n      \"confirm\": \"Best\\xE4tigen Sie die Verbindung in der Erweiterung\",\\n      \"confirm_mobile\": \"Akzeptieren Sie die Verbindungsanfrage im Wallet\"\\n    },\\n    \"secondary_action\": {\\n      \"get\": {\\n        \"description\": \"Haben Sie kein %{wallet}?\",\\n        \"label\": \"HOLEN\"\\n      },\\n      \"install\": {\\n        \"label\": \"INSTALLIEREN\"\\n      },\\n      \"retry\": {\\n        \"label\": \"ERNEUT VERSUCHEN\"\\n      }\\n    },\\n    \"walletconnect\": {\\n      \"description\": {\\n        \"full\": \"Ben\\xF6tigen Sie das offizielle WalletConnect-Modul?\",\\n        \"compact\": \"Ben\\xF6tigen Sie das WalletConnect-Modul?\"\\n      },\\n      \"open\": {\\n        \"label\": \"\\xD6FFNEN\"\\n      }\\n    }\\n  },\\n  \"connect_scan\": {\\n    \"title\": \"Mit %{wallet} scannen\",\\n    \"fallback_title\": \"Mit Ihrem Telefon scannen\"\\n  },\\n  \"connector_group\": {\\n    \"installed\": \"Installiert\",\\n    \"recommended\": \"Empfohlen\",\\n    \"other\": \"Andere\",\\n    \"popular\": \"Beliebt\",\\n    \"more\": \"Mehr\",\\n    \"others\": \"Andere\"\\n  },\\n  \"get\": {\\n    \"title\": \"Ein Wallet holen\",\\n    \"action\": {\\n      \"label\": \"HOLEN\"\\n    },\\n    \"mobile\": {\\n      \"description\": \"Mobiles Wallet\"\\n    },\\n    \"extension\": {\\n      \"description\": \"Browser-Erweiterung\"\\n    },\\n    \"mobile_and_extension\": {\\n      \"description\": \"Mobiles Wallet und Erweiterung\"\\n    },\\n    \"mobile_and_desktop\": {\\n      \"description\": \"Mobile und Desktop Wallet\"\\n    },\\n    \"looking_for\": {\\n      \"title\": \"Nicht das, wonach Sie suchen?\",\\n      \"mobile\": {\\n        \"description\": \"W\\xE4hlen Sie auf dem Hauptbildschirm ein Wallet aus, um mit einem anderen Wallet-Anbieter zu beginnen.\"\\n      },\\n      \"desktop\": {\\n        \"compact_description\": \"W\\xE4hlen Sie auf dem Hauptbildschirm ein Wallet aus, um mit einem anderen Wallet-Anbieter zu beginnen.\",\\n        \"wide_description\": \"W\\xE4hlen Sie links ein Wallet aus, um mit einem anderen Wallet-Anbieter zu beginnen.\"\\n      }\\n    }\\n  },\\n  \"get_options\": {\\n    \"title\": \"Beginnen Sie mit %{wallet}\",\\n    \"short_title\": \"%{wallet} besorgen\",\\n    \"mobile\": {\\n      \"title\": \"%{wallet} f\\xFCr Mobilger\\xE4te\",\\n      \"description\": \"Verwenden Sie das mobile Wallet, um die Welt von Ethereum zu erkunden.\",\\n      \"download\": {\\n        \"label\": \"App herunterladen\"\\n      }\\n    },\\n    \"extension\": {\\n      \"title\": \"%{wallet} f\\xFCr %{browser}\",\\n      \"description\": \"Greifen Sie direkt von Ihrem bevorzugten Webbrowser auf Ihr Wallet zu.\",\\n      \"download\": {\\n        \"label\": \"Zu %{browser} hinzuf\\xFCgen\"\\n      }\\n    },\\n    \"desktop\": {\\n      \"title\": \"%{wallet} f\\xFCr %{platform}\",\\n      \"description\": \"Greifen Sie nativ von Ihrem leistungsstarken Desktop auf Ihr Wallet zu.\",\\n      \"download\": {\\n        \"label\": \"Hinzuf\\xFCgen zu %{platform}\"\\n      }\\n    }\\n  },\\n  \"get_mobile\": {\\n    \"title\": \"%{wallet} installieren\",\\n    \"description\": \"Scannen Sie mit Ihrem Telefon, um auf iOS oder Android herunterzuladen\",\\n    \"continue\": {\\n      \"label\": \"Fortfahren\"\\n    }\\n  },\\n  \"get_instructions\": {\\n    \"mobile\": {\\n      \"connect\": {\\n        \"label\": \"Verbinden\"\\n      },\\n      \"learn_more\": {\\n        \"label\": \"Mehr erfahren\"\\n      }\\n    },\\n    \"extension\": {\\n      \"refresh\": {\\n        \"label\": \"Aktualisieren\"\\n      },\\n      \"learn_more\": {\\n        \"label\": \"Mehr erfahren\"\\n      }\\n    },\\n    \"desktop\": {\\n      \"connect\": {\\n        \"label\": \"Verbinden\"\\n      },\\n      \"learn_more\": {\\n        \"label\": \"Mehr erfahren\"\\n      }\\n    }\\n  },\\n  \"chains\": {\\n    \"title\": \"Netzwerke wechseln\",\\n    \"wrong_network\": \"Falsches Netzwerk erkannt, wechseln oder trennen Sie die Verbindung, um fortzufahren.\",\\n    \"confirm\": \"Im Wallet best\\xE4tigen\",\\n    \"switching_not_supported\": \"Ihr Wallet unterst\\xFCtzt das Wechseln von Netzwerken von %{appName} aus nicht. Versuchen Sie stattdessen, innerhalb Ihres Wallets die Netzwerke zu wechseln.\",\\n    \"switching_not_supported_fallback\": \"Ihr Wallet unterst\\xFCtzt das Wechseln von Netzwerken von dieser App aus nicht. Versuchen Sie stattdessen, innerhalb Ihres Wallets die Netzwerke zu wechseln.\",\\n    \"disconnect\": \"Trennen\",\\n    \"connected\": \"Verbunden\"\\n  },\\n  \"profile\": {\\n    \"disconnect\": {\\n      \"label\": \"Trennen\"\\n    },\\n    \"copy_address\": {\\n      \"label\": \"Adresse kopieren\",\\n      \"copied\": \"Kopiert!\"\\n    },\\n    \"explorer\": {\\n      \"label\": \"Mehr im Explorer ansehen\"\\n    },\\n    \"transactions\": {\\n      \"description\": \"%{appName}-Transaktionen werden hier angezeigt...\",\\n      \"description_fallback\": \"Ihre Transaktionen werden hier angezeigt...\",\\n      \"recent\": {\\n        \"title\": \"Neueste Transaktionen\"\\n      },\\n      \"clear\": {\\n        \"label\": \"Alles l\\xF6schen\"\\n      }\\n    }\\n  },\\n  \"wallet_connectors\": {\\n    \"argent\": {\\n      \"qr_code\": {\\n        \"step1\": {\\n          \"description\": \"F\\xFCgen Sie Argent zu Ihrem Startbildschirm hinzu, um schneller auf Ihr Wallet zuzugreifen.\",\\n          \"title\": \"\\xD6ffnen Sie die Argent-App\"\\n        },\\n        \"step2\": {\\n          \"description\": \"Erstellen Sie ein Wallet und einen Benutzernamen oder importieren Sie ein bestehendes Wallet.\",\\n          \"title\": \"Erstellen oder importieren Sie ein Wallet\"\\n        },\\n        \"step3\": {\\n          \"description\": \"Nach dem Scannen erscheint eine Verbindungsmeldung, um Ihr Wallet zu verbinden.\",\\n          \"title\": \"Tippen Sie auf die Schaltfl\\xE4che QR-Scan\"\\n        }\\n      }\\n    },\\n    \"berasig\": {\\n      \"extension\": {\\n        \"step1\": {\\n          \"title\": \"Installieren Sie die BeraSig-Erweiterung\",\\n          \"description\": \"Wir empfehlen, BeraSig an die Taskleiste anzuheften, um leichter auf Ihre Brieftasche zuzugreifen.\"\\n        },\\n        \"step2\": {\\n          \"title\": \"Erstellen Sie eine Wallet\",\\n          \"description\": \"Stellen Sie sicher, dass Sie Ihr Wallet mit einer sicheren Methode sichern. Teilen Sie niemals Ihre geheime Phrase mit jemandem.\"\\n        },\\n        \"step3\": {\\n          \"title\": \"Aktualisieren Sie Ihren Browser\",\\n          \"description\": \"Sobald Sie Ihr Wallet eingerichtet haben, klicken Sie unten, um den Browser zu aktualisieren und die Erweiterung zu laden.\"\\n        }\\n      }\\n    },\\n    \"best\": {\\n      \"qr_code\": {\\n        \"step1\": {\\n          \"title\": \"\\xD6ffnen Sie die Best Wallet-App\",\\n          \"description\": \"F\\xFCgen Sie die Best Wallet zu Ihrem Startbildschirm hinzu, um schneller auf Ihre Wallet zuzugreifen.\"\\n        },\\n        \"step2\": {\\n          \"title\": \"Erstellen oder importieren Sie ein Wallet\",\\n          \"description\": \"Erstellen Sie ein neues Wallet oder importieren Sie ein bestehendes.\"\\n        },\\n        \"step3\": {\\n          \"title\": \"Tippen Sie auf das QR-Symbol und scannen Sie\",\\n          \"description\": \"Tippen Sie auf das QR-Symbol auf Ihrem Startbildschirm, scannen Sie den Code und best\\xE4tigen Sie die Eingabeaufforderung, um die Verbindung herzustellen.\"\\n        }\\n      }\\n    },\\n    \"bifrost\": {\\n      \"qr_code\": {\\n        \"step1\": {\\n          \"description\": \"Wir empfehlen, die Bifrost Wallet auf Ihren Startbildschirm zu legen, um schneller darauf zugreifen zu k\\xF6nnen.\",\\n          \"title\": \"\\xD6ffnen Sie die Bifrost Wallet-App\"\\n        },\\n        \"step2\": {\\n          \"description\": \"Erstellen oder importieren Sie ein Wallet mit Ihrer Wiederherstellungsphrase.\",\\n          \"title\": \"Erstellen oder importieren Sie ein Wallet\"\\n        },\\n        \"step3\": {\\n          \"description\": \"Nach dem Scannen erscheint eine Verbindungsmeldung, um Ihr Wallet zu verbinden.\",\\n          \"title\": \"Tippen Sie auf die Scan-Schaltfl\\xE4che\"\\n        }\\n      }\\n    },\\n    \"bitget\": {\\n      \"qr_code\": {\\n        \"step1\": {\\n          \"description\": \"Wir empfehlen, die Bitget Wallet auf Ihren Startbildschirm zu legen, um schneller darauf zugreifen zu k\\xF6nnen.\",\\n          \"title\": \"\\xD6ffnen Sie die Bitget Wallet-App\"\\n        },\\n        \"step2\": {\\n          \"description\": \"Stellen Sie sicher, dass Sie Ihr Wallet mit einer sicheren Methode sichern. Teilen Sie niemals Ihre geheime Phrase mit jemandem.\",\\n          \"title\": \"Erstellen oder importieren Sie ein Wallet\"\\n        },\\n        \"step3\": {\\n          \"description\": \"Nach dem Scannen erscheint eine Verbindungsmeldung, um Ihr Wallet zu verbinden.\",\\n          \"title\": \"Tippen Sie auf die Scan-Schaltfl\\xE4che\"\\n        }\\n      },\\n      \"extension\": {\\n        \"step1\": {\\n          \"description\": \"Wir empfehlen, die Bitget Wallet in Ihre Taskleiste zu heften, um schneller auf Ihr Wallet zugreifen zu k\\xF6nnen.\",\\n          \"title\": \"Installieren Sie die Bitget Wallet-Erweiterung\"\\n        },\\n        \"step2\": {\\n          \"description\": \"Stellen Sie sicher, dass Sie Ihr Wallet mit einer sicheren Methode sichern. Teilen Sie niemals Ihre geheime Phrase mit jemandem.\",\\n          \"title\": \"Erstellen oder importieren Sie ein Wallet\"\\n        },\\n        \"step3\": {\\n          \"description\": \"Sobald Sie Ihr Wallet eingerichtet haben, klicken Sie unten, um den Browser zu aktualisieren und die Erweiterung zu laden.\",\\n          \"title\": \"Aktualisieren Sie Ihren Browser\"\\n        }\\n      }\\n    },\\n    \"bitski\": {\\n      \"extension\": {\\n        \"step1\": {\\n          \"description\": \"Wir empfehlen, Bitski in Ihre Taskleiste zu heften, um schneller auf Ihr Wallet zugreifen zu k\\xF6nnen.\",\\n          \"title\": \"Installieren Sie die Bitski-Erweiterung\"\\n        },\\n        \"step2\": {\\n          \"description\": \"Stellen Sie sicher, dass Sie Ihr Wallet mit einer sicheren Methode sichern. Teilen Sie niemals Ihre geheime Phrase mit jemandem.\",\\n          \"title\": \"Erstellen oder importieren Sie ein Wallet\"\\n        },\\n        \"step3\": {\\n          \"description\": \"Sobald Sie Ihr Wallet eingerichtet haben, klicken Sie unten, um den Browser zu aktualisieren und die Erweiterung zu laden.\",\\n          \"title\": \"Aktualisieren Sie Ihren Browser\"\\n        }\\n      }\\n    },\\n    \"bitverse\": {\\n      \"qr_code\": {\\n        \"step1\": {\\n          \"title\": \"\\xD6ffnen Sie die Bitverse Wallet App\",\\n          \"description\": \"F\\xFCgen Sie die Bitverse Wallet Ihrem Startbildschirm hinzu, um schneller auf Ihr Wallet zuzugreifen.\"\\n        },\\n        \"step2\": {\\n          \"title\": \"Erstellen oder importieren Sie ein Wallet\",\\n          \"description\": \"Erstellen Sie ein neues Wallet oder importieren Sie ein bestehendes.\"\\n        },\\n        \"step3\": {\\n          \"title\": \"Tippen Sie auf das QR-Symbol und scannen Sie\",\\n          \"description\": \"Tippen Sie auf das QR-Symbol auf Ihrem Startbildschirm, scannen Sie den Code und best\\xE4tigen Sie die Eingabeaufforderung, um die Verbindung herzustellen.\"\\n        }\\n      }\\n    },\\n    \"bloom\": {\\n      \"desktop\": {\\n        \"step1\": {\\n          \"title\": \"\\xD6ffnen Sie die Bloom Wallet-App\",\\n          \"description\": \"Wir empfehlen, Bloom Wallet auf Ihrem Startbildschirm abzulegen, um schneller darauf zugreifen zu k\\xF6nnen.\"\\n        },\\n        \"step2\": {\\n          \"description\": \"Erstellen oder importieren Sie ein Wallet mit Ihrer Wiederherstellungsphrase.\",\\n          \"title\": \"Erstellen oder importieren Sie ein Wallet\"\\n        },\\n        \"step3\": {\\n          \"description\": \"Nachdem Sie ein Wallet haben, klicken Sie auf \\u201EVerbinden\\u201C, um \\xFCber Bloom eine Verbindung herzustellen. Eine Verbindungsmeldung in der App wird angezeigt, um die Verbindung zu best\\xE4tigen.\",\\n          \"title\": \"Klicken Sie auf Verbinden\"\\n        }\\n      }\\n    },\\n    \"bybit\": {\\n      \"qr_code\": {\\n        \"step1\": {\\n          \"description\": \"Wir empfehlen, Bybit auf Ihrem Startbildschirm abzulegen, um schneller darauf zuzugreifen.\",\\n          \"title\": \"\\xD6ffnen Sie die Bybit App\"\\n        },\\n        \"step2\": {\\n          \"description\": \"Sie k\\xF6nnen Ihr Wallet ganz einfach mit unserer Backup-Funktion auf Ihrem Telefon sichern.\",\\n          \"title\": \"Erstellen oder importieren Sie ein Wallet\"\\n        },\\n        \"step3\": {\\n          \"description\": \"Nach dem Scannen erscheint eine Verbindungsmeldung, um Ihr Wallet zu verbinden.\",\\n          \"title\": \"Tippen Sie auf die Scan-Schaltfl\\xE4che\"\\n        }\\n      },\\n      \"extension\": {\\n        \"step1\": {\\n          \"description\": \"Klicken Sie oben rechts in Ihrem Browser und heften Sie Bybit Wallet f\\xFCr einfachen Zugriff an.\",\\n          \"title\": \"Installieren Sie die Bybit Wallet Erweiterung\"\\n        },\\n        \"step2\": {\\n          \"description\": \"Erstellen Sie ein neues Wallet oder importieren Sie ein bestehendes.\",\\n          \"title\": \"Erstellen oder importieren Sie ein Wallet\"\\n        },\\n        \"step3\": {\\n          \"description\": \"Sobald Sie das Bybit Wallet eingerichtet haben, klicken Sie unten, um den Browser zu aktualisieren und die Erweiterung zu laden.\",\\n          \"title\": \"Aktualisieren Sie Ihren Browser\"\\n        }\\n      }\\n    },\\n    \"binance\": {\\n      \"qr_code\": {\\n        \"step1\": {\\n          \"description\": \"Wir empfehlen, Binance auf Ihrem Startbildschirm abzulegen, um schneller darauf zuzugreifen.\",\\n          \"title\": \"\\xD6ffnen Sie die Binance App\"\\n        },\\n        \"step2\": {\\n          \"description\": \"Sie k\\xF6nnen Ihr Wallet ganz einfach mit unserer Backup-Funktion auf Ihrem Telefon sichern.\",\\n          \"title\": \"Erstellen oder importieren Sie ein Wallet\"\\n        },\\n        \"step3\": {\\n          \"description\": \"Nach dem Scannen erscheint eine Verbindungsmeldung, um Ihr Wallet zu verbinden.\",\\n          \"title\": \"Tippen Sie auf die WalletConnect-Schaltfl\\xE4che\"\\n        }\\n      }\\n    },\\n    \"coin98\": {\\n      \"qr_code\": {\\n        \"step1\": {\\n          \"description\": \"Wir empfehlen, die Coin98 Wallet auf Ihren Startbildschirm zu legen, um schneller darauf zugreifen zu k\\xF6nnen.\",\\n          \"title\": \"\\xD6ffnen Sie die Coin98 Wallet-App\"\\n        },\\n        \"step2\": {\\n          \"description\": \"Sie k\\xF6nnen Ihr Wallet ganz einfach mit unserer Backup-Funktion auf Ihrem Telefon sichern.\",\\n          \"title\": \"Erstellen oder importieren Sie ein Wallet\"\\n        },\\n        \"step3\": {\\n          \"description\": \"Nach dem Scannen erscheint eine Verbindungsmeldung, um Ihr Wallet zu verbinden.\",\\n          \"title\": \"Tippen Sie auf die WalletConnect-Schaltfl\\xE4che\"\\n        }\\n      },\\n      \"extension\": {\\n        \"step1\": {\\n          \"description\": \"Klicken Sie oben rechts in Ihrem Browser und heften Sie die Coin98 Wallet an, um einen einfachen Zugriff zu erm\\xF6glichen.\",\\n          \"title\": \"Installieren Sie die Coin98 Wallet-Erweiterung\"\\n        },\\n        \"step2\": {\\n          \"description\": \"Erstellen Sie ein neues Wallet oder importieren Sie ein bestehendes.\",\\n          \"title\": \"Erstellen oder importieren Sie ein Wallet\"\\n        },\\n        \"step3\": {\\n          \"description\": \"Sobald Sie die Coin98 Wallet eingerichtet haben, klicken Sie unten, um den Browser zu aktualisieren und die Erweiterung zu laden.\",\\n          \"title\": \"Aktualisieren Sie Ihren Browser\"\\n        }\\n      }\\n    },\\n    \"coinbase\": {\\n      \"qr_code\": {\\n        \"step1\": {\\n          \"description\": \"Wir empfehlen, die Coinbase Wallet auf Ihren Startbildschirm zu legen, um schneller darauf zugreifen zu k\\xF6nnen.\",\\n          \"title\": \"\\xD6ffnen Sie die Coinbase Wallet-App\"\\n        },\\n        \"step2\": {\\n          \"description\": \"Sie k\\xF6nnen Ihr Wallet problemlos mit der Cloud-Backup-Funktion sichern.\",\\n          \"title\": \"Erstellen oder importieren Sie ein Wallet\"\\n        },\\n        \"step3\": {\\n          \"description\": \"Nach dem Scannen erscheint eine Verbindungsmeldung, um Ihr Wallet zu verbinden.\",\\n          \"title\": \"Tippen Sie auf die Scan-Schaltfl\\xE4che\"\\n        }\\n      },\\n      \"extension\": {\\n        \"step1\": {\\n          \"description\": \"Wir empfehlen, die Coinbase Wallet in Ihre Taskleiste zu heften, um schneller auf Ihr Wallet zugreifen zu k\\xF6nnen.\",\\n          \"title\": \"Installieren Sie die Coinbase Wallet-Erweiterung\"\\n        },\\n        \"step2\": {\\n          \"description\": \"Stellen Sie sicher, dass Sie Ihr Wallet mit einer sicheren Methode sichern. Teilen Sie niemals Ihre geheime Phrase mit jemandem.\",\\n          \"title\": \"Erstellen oder importieren Sie ein Wallet\"\\n        },\\n        \"step3\": {\\n          \"description\": \"Sobald Sie Ihr Wallet eingerichtet haben, klicken Sie unten, um den Browser zu aktualisieren und die Erweiterung zu laden.\",\\n          \"title\": \"Aktualisieren Sie Ihren Browser\"\\n        }\\n      }\\n    },\\n    \"compass\": {\\n      \"extension\": {\\n        \"step1\": {\\n          \"description\": \"Wir empfehlen, die Compass Wallet an Ihre Taskleiste zu heften, um schneller auf Ihr Wallet zuzugreifen.\",\\n          \"title\": \"Installieren Sie die Compass Wallet Erweiterung\"\\n        },\\n        \"step2\": {\\n          \"description\": \"Stellen Sie sicher, dass Sie Ihr Wallet mit einer sicheren Methode sichern. Teilen Sie niemals Ihre geheime Phrase mit jemandem.\",\\n          \"title\": \"Erstellen oder importieren Sie ein Wallet\"\\n        },\\n        \"step3\": {\\n          \"description\": \"Sobald Sie Ihr Wallet eingerichtet haben, klicken Sie unten, um den Browser zu aktualisieren und die Erweiterung zu laden.\",\\n          \"title\": \"Aktualisieren Sie Ihren Browser\"\\n        }\\n      }\\n    },\\n    \"core\": {\\n      \"qr_code\": {\\n        \"step1\": {\\n          \"description\": \"Wir empfehlen, Core auf Ihren Startbildschirm zu legen, um schneller auf Ihr Wallet zuzugreifen.\",\\n          \"title\": \"\\xD6ffnen Sie die Core-App\"\\n        },\\n        \"step2\": {\\n          \"description\": \"Sie k\\xF6nnen Ihr Wallet ganz einfach mit unserer Backup-Funktion auf Ihrem Telefon sichern.\",\\n          \"title\": \"Erstellen oder importieren Sie ein Wallet\"\\n        },\\n        \"step3\": {\\n          \"description\": \"Nach dem Scannen erscheint eine Verbindungsmeldung, um Ihr Wallet zu verbinden.\",\\n          \"title\": \"Tippen Sie auf die WalletConnect-Schaltfl\\xE4che\"\\n        }\\n      },\\n      \"extension\": {\\n        \"step1\": {\\n          \"description\": \"Wir empfehlen, Core an Ihre Taskleiste anzuheften, um schneller auf Ihr Wallet zugreifen zu k\\xF6nnen.\",\\n          \"title\": \"Installieren Sie die Core-Erweiterung\"\\n        },\\n        \"step2\": {\\n          \"description\": \"Stellen Sie sicher, dass Sie Ihr Wallet mit einer sicheren Methode sichern. Teilen Sie niemals Ihre geheime Phrase mit jemandem.\",\\n          \"title\": \"Erstellen oder importieren Sie ein Wallet\"\\n        },\\n        \"step3\": {\\n          \"description\": \"Sobald Sie Ihr Wallet eingerichtet haben, klicken Sie unten, um den Browser zu aktualisieren und die Erweiterung zu laden.\",\\n          \"title\": \"Aktualisieren Sie Ihren Browser\"\\n        }\\n      }\\n    },\\n    \"fox\": {\\n      \"qr_code\": {\\n        \"step1\": {\\n          \"description\": \"Wir empfehlen, FoxWallet auf Ihrem Startbildschirm zu platzieren, um schnelleren Zugriff zu erhalten.\",\\n          \"title\": \"\\xD6ffnen Sie die FoxWallet-App\"\\n        },\\n        \"step2\": {\\n          \"description\": \"Stellen Sie sicher, dass Sie Ihr Wallet mit einer sicheren Methode sichern. Teilen Sie niemals Ihre geheime Phrase mit jemandem.\",\\n          \"title\": \"Erstellen oder importieren Sie ein Wallet\"\\n        },\\n        \"step3\": {\\n          \"description\": \"Nach dem Scannen erscheint eine Verbindungsmeldung, um Ihr Wallet zu verbinden.\",\\n          \"title\": \"Tippen Sie auf die Scan-Schaltfl\\xE4che\"\\n        }\\n      }\\n    },\\n    \"frontier\": {\\n      \"qr_code\": {\\n        \"step1\": {\\n          \"description\": \"Wir empfehlen, Frontier Wallet auf Ihrem Startbildschirm zu platzieren, um schnelleren Zugriff zu erhalten.\",\\n          \"title\": \"\\xD6ffnen Sie die Frontier Wallet-App\"\\n        },\\n        \"step2\": {\\n          \"description\": \"Stellen Sie sicher, dass Sie Ihr Wallet mit einer sicheren Methode sichern. Teilen Sie niemals Ihre geheime Phrase mit jemandem.\",\\n          \"title\": \"Erstellen oder importieren Sie ein Wallet\"\\n        },\\n        \"step3\": {\\n          \"description\": \"Nach dem Scannen erscheint eine Verbindungsmeldung, um Ihr Wallet zu verbinden.\",\\n          \"title\": \"Tippen Sie auf die Scan-Schaltfl\\xE4che\"\\n        }\\n      },\\n      \"extension\": {\\n        \"step1\": {\\n          \"description\": \"Wir empfehlen, Frontier Wallet an Ihre Taskleiste anzuheften, um schneller auf Ihr Wallet zugreifen zu k\\xF6nnen.\",\\n          \"title\": \"Installieren Sie die Frontier Wallet-Erweiterung\"\\n        },\\n        \"step2\": {\\n          \"description\": \"Stellen Sie sicher, dass Sie Ihr Wallet mit einer sicheren Methode sichern. Teilen Sie niemals Ihre geheime Phrase mit jemandem.\",\\n          \"title\": \"Erstellen oder importieren Sie ein Wallet\"\\n        },\\n        \"step3\": {\\n          \"description\": \"Sobald Sie Ihr Wallet eingerichtet haben, klicken Sie unten, um den Browser zu aktualisieren und die Erweiterung zu laden.\",\\n          \"title\": \"Aktualisieren Sie Ihren Browser\"\\n        }\\n      }\\n    },\\n    \"im_token\": {\\n      \"qr_code\": {\\n        \"step1\": {\\n          \"title\": \"\\xD6ffnen Sie die imToken-App\",\\n          \"description\": \"Platzieren Sie die imToken-App auf Ihrem Startbildschirm f\\xFCr schnelleren Zugriff auf Ihr Wallet.\"\\n        },\\n        \"step2\": {\\n          \"title\": \"Erstellen oder importieren Sie ein Wallet\",\\n          \"description\": \"Erstellen Sie ein neues Wallet oder importieren Sie ein bestehendes.\"\\n        },\\n        \"step3\": {\\n          \"title\": \"Tippen Sie auf das Scanner-Symbol in der oberen rechten Ecke\",\\n          \"description\": \"W\\xE4hlen Sie Neue Verbindung, dann scannen Sie den QR-Code und best\\xE4tigen Sie die Eingabeaufforderung, um die Verbindung herzustellen.\"\\n        }\\n      }\\n    },\\n    \"iopay\": {\\n      \"qr_code\": {\\n        \"step1\": {\\n          \"description\": \"Wir empfehlen, ioPay auf Ihrem Startbildschirm abzulegen, um schneller darauf zuzugreifen.\",\\n          \"title\": \"\\xD6ffnen Sie die ioPay App\"\\n        },\\n        \"step2\": {\\n          \"description\": \"Sie k\\xF6nnen Ihr Wallet ganz einfach mit unserer Backup-Funktion auf Ihrem Telefon sichern.\",\\n          \"title\": \"Erstellen oder importieren Sie ein Wallet\"\\n        },\\n        \"step3\": {\\n          \"description\": \"Nach dem Scannen erscheint eine Verbindungsmeldung, um Ihr Wallet zu verbinden.\",\\n          \"title\": \"Tippen Sie auf die WalletConnect-Schaltfl\\xE4che\"\\n        }\\n      }\\n    },\\n    \"kaikas\": {\\n      \"extension\": {\\n        \"step1\": {\\n          \"description\": \"Wir empfehlen, Kaikas an Ihre Taskleiste zu heften, um schneller auf Ihr Wallet zuzugreifen.\",\\n          \"title\": \"Installieren Sie die Kaikas Erweiterung\"\\n        },\\n        \"step2\": {\\n          \"description\": \"Stellen Sie sicher, dass Sie Ihr Wallet mit einer sicheren Methode sichern. Teilen Sie niemals Ihre geheime Phrase mit jemandem.\",\\n          \"title\": \"Erstellen oder importieren Sie ein Wallet\"\\n        },\\n        \"step3\": {\\n          \"description\": \"Sobald Sie Ihr Wallet eingerichtet haben, klicken Sie unten, um den Browser zu aktualisieren und die Erweiterung zu laden.\",\\n          \"title\": \"Aktualisieren Sie Ihren Browser\"\\n        }\\n      },\\n      \"qr_code\": {\\n        \"step1\": {\\n          \"title\": \"\\xD6ffnen Sie die Kaikas App\",\\n          \"description\": \"Legen Sie die Kaikas App auf Ihrem Startbildschirm ab, um schneller auf Ihr Wallet zuzugreifen.\"\\n        },\\n        \"step2\": {\\n          \"title\": \"Erstellen oder importieren Sie ein Wallet\",\\n          \"description\": \"Erstellen Sie ein neues Wallet oder importieren Sie ein bestehendes.\"\\n        },\\n        \"step3\": {\\n          \"title\": \"Tippen Sie auf das Scanner-Symbol in der oberen rechten Ecke\",\\n          \"description\": \"W\\xE4hlen Sie Neue Verbindung, dann scannen Sie den QR-Code und best\\xE4tigen Sie die Eingabeaufforderung, um die Verbindung herzustellen.\"\\n        }\\n      }\\n    },\\n    \"kaia\": {\\n      \"extension\": {\\n        \"step1\": {\\n          \"description\": \"Wir empfehlen, Kaia an Ihre Taskleiste zu heften, um schneller auf Ihr Wallet zuzugreifen.\",\\n          \"title\": \"Installieren Sie die Kaia Erweiterung\"\\n        },\\n        \"step2\": {\\n          \"description\": \"Stellen Sie sicher, dass Sie Ihr Wallet mit einer sicheren Methode sichern. Teilen Sie niemals Ihre geheime Phrase mit jemandem.\",\\n          \"title\": \"Erstellen oder importieren Sie ein Wallet\"\\n        },\\n        \"step3\": {\\n          \"description\": \"Sobald Sie Ihr Wallet eingerichtet haben, klicken Sie unten, um den Browser zu aktualisieren und die Erweiterung zu laden.\",\\n          \"title\": \"Aktualisieren Sie Ihren Browser\"\\n        }\\n      },\\n      \"qr_code\": {\\n        \"step1\": {\\n          \"title\": \"\\xD6ffnen Sie die Kaia-App\",\\n          \"description\": \"Legen Sie die Kaia-App auf Ihren Startbildschirm f\\xFCr schnelleren Zugriff auf Ihre Wallet.\"\\n        },\\n        \"step2\": {\\n          \"title\": \"Erstellen oder importieren Sie ein Wallet\",\\n          \"description\": \"Erstellen Sie ein neues Wallet oder importieren Sie ein bestehendes.\"\\n        },\\n        \"step3\": {\\n          \"title\": \"Tippen Sie auf das Scanner-Symbol in der oberen rechten Ecke\",\\n          \"description\": \"W\\xE4hlen Sie Neue Verbindung, dann scannen Sie den QR-Code und best\\xE4tigen Sie die Eingabeaufforderung, um die Verbindung herzustellen.\"\\n        }\\n      }\\n    },\\n    \"kraken\": {\\n      \"qr_code\": {\\n        \"step1\": {\\n          \"title\": \"\\xD6ffnen Sie die Kraken Wallet App\",\\n          \"description\": \"F\\xFCgen Sie die Kraken Wallet Ihrem Startbildschirm hinzu, um schneller auf Ihr Wallet zuzugreifen.\"\\n        },\\n        \"step2\": {\\n          \"title\": \"Erstellen oder importieren Sie ein Wallet\",\\n          \"description\": \"Erstellen Sie ein neues Wallet oder importieren Sie ein bestehendes.\"\\n        },\\n        \"step3\": {\\n          \"title\": \"Tippen Sie auf das QR-Symbol und scannen Sie\",\\n          \"description\": \"Tippen Sie auf das QR-Symbol auf Ihrem Startbildschirm, scannen Sie den Code und best\\xE4tigen Sie die Eingabeaufforderung, um die Verbindung herzustellen.\"\\n        }\\n      }\\n    },\\n    \"kresus\": {\\n      \"qr_code\": {\\n        \"step1\": {\\n          \"title\": \"\\xD6ffnen Sie die Kresus Wallet App\",\\n          \"description\": \"F\\xFCgen Sie die Kresus Wallet Ihrem Startbildschirm hinzu, um schneller auf Ihr Wallet zuzugreifen.\"\\n        },\\n        \"step2\": {\\n          \"title\": \"Erstellen oder importieren Sie ein Wallet\",\\n          \"description\": \"Erstellen Sie ein neues Wallet oder importieren Sie ein bestehendes.\"\\n        },\\n        \"step3\": {\\n          \"title\": \"Tippen Sie auf das QR-Symbol und scannen Sie\",\\n          \"description\": \"Tippen Sie auf das QR-Symbol auf Ihrem Startbildschirm, scannen Sie den Code und best\\xE4tigen Sie die Eingabeaufforderung, um die Verbindung herzustellen.\"\\n        }\\n      }\\n    },\\n    \"magicEden\": {\\n      \"extension\": {\\n        \"step1\": {\\n          \"title\": \"Installieren Sie die Magic Eden Erweiterung\",\\n          \"description\": \"Wir empfehlen, Magic Eden an Ihre Taskleiste zu heften, um einfacher auf Ihr Wallet zuzugreifen.\"\\n        },\\n        \"step2\": {\\n          \"title\": \"Erstellen oder importieren Sie ein Wallet\",\\n          \"description\": \"Stellen Sie sicher, dass Sie Ihre Wallet auf sichere Weise sichern. Teilen Sie niemals Ihren geheimen Wiederherstellungssatz mit anderen.\"\\n        },\\n        \"step3\": {\\n          \"title\": \"Aktualisieren Sie Ihren Browser\",\\n          \"description\": \"Sobald Sie Ihr Wallet eingerichtet haben, klicken Sie unten, um den Browser zu aktualisieren und die Erweiterung zu laden.\"\\n        }\\n      }\\n    },\\n    \"metamask\": {\\n      \"qr_code\": {\\n        \"step1\": {\\n          \"title\": \"\\xD6ffnen Sie die MetaMask-App\",\\n          \"description\": \"Wir empfehlen, MetaMask auf Ihrem Startbildschirm zu platzieren, um schnelleren Zugriff zu erhalten.\"\\n        },\\n        \"step2\": {\\n          \"title\": \"Erstellen oder importieren Sie ein Wallet\",\\n          \"description\": \"Stellen Sie sicher, dass Sie Ihr Wallet mit einer sicheren Methode sichern. Teilen Sie niemals Ihre geheime Phrase mit jemandem.\"\\n        },\\n        \"step3\": {\\n          \"title\": \"Tippen Sie auf die Scan-Schaltfl\\xE4che\",\\n          \"description\": \"Nach dem Scannen erscheint eine Verbindungsmeldung, um Ihr Wallet zu verbinden.\"\\n        }\\n      },\\n      \"extension\": {\\n        \"step1\": {\\n          \"title\": \"Installieren Sie die MetaMask-Erweiterung\",\\n          \"description\": \"Wir empfehlen, MetaMask an Ihre Taskleiste anzuheften, um schneller auf Ihr Wallet zugreifen zu k\\xF6nnen.\"\\n        },\\n        \"step2\": {\\n          \"title\": \"Erstellen oder importieren Sie ein Wallet\",\\n          \"description\": \"Stellen Sie sicher, dass Sie Ihr Wallet mit einer sicheren Methode sichern. Teilen Sie niemals Ihre geheime Phrase mit jemandem.\"\\n        },\\n        \"step3\": {\\n          \"title\": \"Aktualisieren Sie Ihren Browser\",\\n          \"description\": \"Sobald Sie Ihr Wallet eingerichtet haben, klicken Sie unten, um den Browser zu aktualisieren und die Erweiterung zu laden.\"\\n        }\\n      }\\n    },\\n    \"nestwallet\": {\\n      \"extension\": {\\n        \"step1\": {\\n          \"title\": \"Installieren Sie die NestWallet Erweiterung\",\\n          \"description\": \"Wir empfehlen, die NestWallet an Ihre Taskleiste zu heften, um schneller auf Ihr Wallet zuzugreifen.\"\\n        },\\n        \"step2\": {\\n          \"title\": \"Erstellen oder importieren Sie ein Wallet\",\\n          \"description\": \"Stellen Sie sicher, dass Sie Ihr Wallet mit einer sicheren Methode sichern. Teilen Sie niemals Ihre geheime Phrase mit jemandem.\"\\n        },\\n        \"step3\": {\\n          \"title\": \"Aktualisieren Sie Ihren Browser\",\\n          \"description\": \"Sobald Sie Ihr Wallet eingerichtet haben, klicken Sie unten, um den Browser zu aktualisieren und die Erweiterung zu laden.\"\\n        }\\n      }\\n    },\\n    \"okx\": {\\n      \"qr_code\": {\\n        \"step1\": {\\n          \"title\": \"\\xD6ffnen Sie die OKX Wallet-App\",\\n          \"description\": \"Wir empfehlen, OKX Wallet auf Ihrem Startbildschirm zu platzieren, um schnelleren Zugriff zu erhalten.\"\\n        },\\n        \"step2\": {\\n          \"title\": \"Erstellen oder importieren Sie ein Wallet\",\\n          \"description\": \"Stellen Sie sicher, dass Sie Ihr Wallet mit einer sicheren Methode sichern. Teilen Sie niemals Ihre geheime Phrase mit jemandem.\"\\n        },\\n        \"step3\": {\\n          \"title\": \"Tippen Sie auf die Scan-Schaltfl\\xE4che\",\\n          \"description\": \"Nach dem Scannen erscheint eine Verbindungsmeldung, um Ihr Wallet zu verbinden.\"\\n        }\\n      },\\n      \"extension\": {\\n        \"step1\": {\\n          \"title\": \"Installieren Sie die OKX Wallet-Erweiterung\",\\n          \"description\": \"Wir empfehlen, OKX Wallet an Ihre Taskleiste anzuheften, um schneller auf Ihr Wallet zugreifen zu k\\xF6nnen.\"\\n        },\\n        \"step2\": {\\n          \"title\": \"Erstellen oder importieren Sie ein Wallet\",\\n          \"description\": \"Stellen Sie sicher, dass Sie Ihr Wallet mit einer sicheren Methode sichern. Teilen Sie niemals Ihre geheime Phrase mit jemandem.\"\\n        },\\n        \"step3\": {\\n          \"title\": \"Aktualisieren Sie Ihren Browser\",\\n          \"description\": \"Sobald Sie Ihr Wallet eingerichtet haben, klicken Sie unten, um den Browser zu aktualisieren und die Erweiterung zu laden.\"\\n        }\\n      }\\n    },\\n    \"omni\": {\\n      \"qr_code\": {\\n        \"step1\": {\\n          \"title\": \"\\xD6ffnen Sie die Omni-App\",\\n          \"description\": \"F\\xFCgen Sie Omni Ihrem Startbildschirm hinzu, um schneller auf Ihr Wallet zuzugreifen.\"\\n        },\\n        \"step2\": {\\n          \"title\": \"Erstellen oder importieren Sie ein Wallet\",\\n          \"description\": \"Erstellen Sie ein neues Wallet oder importieren Sie ein bestehendes.\"\\n        },\\n        \"step3\": {\\n          \"title\": \"Tippen Sie auf das QR-Symbol und scannen Sie\",\\n          \"description\": \"Tippen Sie auf das QR-Symbol auf Ihrem Startbildschirm, scannen Sie den Code und best\\xE4tigen Sie die Eingabeaufforderung, um die Verbindung herzustellen.\"\\n        }\\n      }\\n    },\\n    \"1inch\": {\\n      \"qr_code\": {\\n        \"step1\": {\\n          \"description\": \"Legen Sie die 1inch Wallet auf Ihrem Startbildschirm ab, um schneller auf Ihr Wallet zuzugreifen.\",\\n          \"title\": \"\\xD6ffnen Sie die 1inch Wallet App\"\\n        },\\n        \"step2\": {\\n          \"description\": \"Erstellen Sie ein Wallet und einen Benutzernamen oder importieren Sie ein bestehendes Wallet.\",\\n          \"title\": \"Erstellen oder importieren Sie ein Wallet\"\\n        },\\n        \"step3\": {\\n          \"description\": \"Nach dem Scannen erscheint eine Verbindungsmeldung, um Ihr Wallet zu verbinden.\",\\n          \"title\": \"Tippen Sie auf die Schaltfl\\xE4che QR-Scan\"\\n        }\\n      }\\n    },\\n    \"token_pocket\": {\\n      \"qr_code\": {\\n        \"step1\": {\\n          \"title\": \"\\xD6ffnen Sie die TokenPocket-App\",\\n          \"description\": \"Wir empfehlen, TokenPocket auf Ihrem Startbildschirm zu platzieren, um schnelleren Zugriff zu erhalten.\"\\n        },\\n        \"step2\": {\\n          \"title\": \"Erstellen oder importieren Sie ein Wallet\",\\n          \"description\": \"Stellen Sie sicher, dass Sie Ihr Wallet mit einer sicheren Methode sichern. Teilen Sie niemals Ihre geheime Phrase mit jemandem.\"\\n        },\\n        \"step3\": {\\n          \"title\": \"Tippen Sie auf die Scan-Schaltfl\\xE4che\",\\n          \"description\": \"Nach dem Scannen erscheint eine Verbindungsmeldung, um Ihr Wallet zu verbinden.\"\\n        }\\n      },\\n      \"extension\": {\\n        \"step1\": {\\n          \"title\": \"Installieren Sie die TokenPocket-Erweiterung\",\\n          \"description\": \"Wir empfehlen, TokenPocket an Ihre Taskleiste anzuheften, um schneller auf Ihr Wallet zugreifen zu k\\xF6nnen.\"\\n        },\\n        \"step2\": {\\n          \"title\": \"Erstellen oder importieren Sie ein Wallet\",\\n          \"description\": \"Stellen Sie sicher, dass Sie Ihr Wallet mit einer sicheren Methode sichern. Teilen Sie niemals Ihre geheime Phrase mit jemandem.\"\\n        },\\n        \"step3\": {\\n          \"title\": \"Aktualisieren Sie Ihren Browser\",\\n          \"description\": \"Sobald Sie Ihr Wallet eingerichtet haben, klicken Sie unten, um den Browser zu aktualisieren und die Erweiterung zu laden.\"\\n        }\\n      }\\n    },\\n    \"trust\": {\\n      \"qr_code\": {\\n        \"step1\": {\\n          \"title\": \"\\xD6ffnen Sie die Trust Wallet-App\",\\n          \"description\": \"Platzieren Sie Trust Wallet auf Ihrem Startbildschirm f\\xFCr schnelleren Zugriff auf Ihr Wallet.\"\\n        },\\n        \"step2\": {\\n          \"title\": \"Erstellen oder importieren Sie ein Wallet\",\\n          \"description\": \"Erstellen Sie ein neues Wallet oder importieren Sie ein bestehendes.\"\\n        },\\n        \"step3\": {\\n          \"title\": \"Tippen Sie auf WalletConnect in den Einstellungen\",\\n          \"description\": \"W\\xE4hlen Sie Neue Verbindung, dann scannen Sie den QR-Code und best\\xE4tigen Sie die Eingabeaufforderung, um die Verbindung herzustellen.\"\\n        }\\n      },\\n      \"extension\": {\\n        \"step1\": {\\n          \"title\": \"Installieren Sie die Trust Wallet-Erweiterung\",\\n          \"description\": \"Klicken Sie oben rechts in Ihrem Browser und pinnen Sie Trust Wallet f\\xFCr einfachen Zugriff.\"\\n        },\\n        \"step2\": {\\n          \"title\": \"Erstellen oder importieren Sie ein Wallet\",\\n          \"description\": \"Erstellen Sie ein neues Wallet oder importieren Sie ein bestehendes.\"\\n        },\\n        \"step3\": {\\n          \"title\": \"Aktualisieren Sie Ihren Browser\",\\n          \"description\": \"Sobald Sie Trust Wallet eingerichtet haben, klicken Sie unten, um den Browser zu aktualisieren und die Erweiterung zu laden.\"\\n        }\\n      }\\n    },\\n    \"uniswap\": {\\n      \"qr_code\": {\\n        \"step1\": {\\n          \"title\": \"\\xD6ffnen Sie die Uniswap-App\",\\n          \"description\": \"F\\xFCgen Sie Uniswap Wallet Ihrem Startbildschirm hinzu, um schneller auf Ihr Wallet zuzugreifen.\"\\n        },\\n        \"step2\": {\\n          \"title\": \"Erstellen oder importieren Sie ein Wallet\",\\n          \"description\": \"Erstellen Sie ein neues Wallet oder importieren Sie ein bestehendes.\"\\n        },\\n        \"step3\": {\\n          \"title\": \"Tippen Sie auf das QR-Symbol und scannen Sie\",\\n          \"description\": \"Tippen Sie auf das QR-Symbol auf Ihrem Startbildschirm, scannen Sie den Code und best\\xE4tigen Sie die Eingabeaufforderung, um die Verbindung herzustellen.\"\\n        }\\n      }\\n    },\\n    \"zerion\": {\\n      \"qr_code\": {\\n        \"step1\": {\\n          \"title\": \"\\xD6ffnen Sie die Zerion-App\",\\n          \"description\": \"Wir empfehlen, Zerion auf Ihrem Startbildschirm zu platzieren, um schnelleren Zugriff zu haben.\"\\n        },\\n        \"step2\": {\\n          \"title\": \"Erstellen oder importieren Sie ein Wallet\",\\n          \"description\": \"Stellen Sie sicher, dass Sie Ihr Wallet mit einer sicheren Methode sichern. Teilen Sie niemals Ihre geheime Phrase mit jemandem.\"\\n        },\\n        \"step3\": {\\n          \"title\": \"Tippen Sie auf die Scan-Schaltfl\\xE4che\",\\n          \"description\": \"Nach dem Scannen erscheint eine Verbindungsmeldung, um Ihr Wallet zu verbinden.\"\\n        }\\n      },\\n      \"extension\": {\\n        \"step1\": {\\n          \"title\": \"Installieren Sie die Zerion-Erweiterung\",\\n          \"description\": \"Wir empfehlen, Zerion an Ihre Taskleiste anzuheften, um schnelleren Zugriff auf Ihre Wallet zu haben.\"\\n        },\\n        \"step2\": {\\n          \"title\": \"Erstellen oder importieren Sie ein Wallet\",\\n          \"description\": \"Stellen Sie sicher, dass Sie Ihr Wallet mit einer sicheren Methode sichern. Teilen Sie niemals Ihre geheime Phrase mit jemandem.\"\\n        },\\n        \"step3\": {\\n          \"title\": \"Aktualisieren Sie Ihren Browser\",\\n          \"description\": \"Sobald Sie Ihr Wallet eingerichtet haben, klicken Sie unten, um den Browser zu aktualisieren und die Erweiterung zu laden.\"\\n        }\\n      }\\n    },\\n    \"rainbow\": {\\n      \"qr_code\": {\\n        \"step1\": {\\n          \"title\": \"\\xD6ffnen Sie die Rainbow-App\",\\n          \"description\": \"Wir empfehlen, Rainbow auf Ihrem Startbildschirm zu platzieren, um schnelleren Zugriff auf Ihre Wallet zu haben.\"\\n        },\\n        \"step2\": {\\n          \"title\": \"Erstellen oder importieren Sie ein Wallet\",\\n          \"description\": \"Sie k\\xF6nnen Ihr Wallet ganz einfach mit unserer Backup-Funktion auf Ihrem Telefon sichern.\"\\n        },\\n        \"step3\": {\\n          \"title\": \"Tippen Sie auf die Scan-Schaltfl\\xE4che\",\\n          \"description\": \"Nach dem Scannen erscheint eine Verbindungsmeldung, um Ihr Wallet zu verbinden.\"\\n        }\\n      }\\n    },\\n    \"enkrypt\": {\\n      \"extension\": {\\n        \"step1\": {\\n          \"description\": \"Wir empfehlen, die Enkrypt Wallet an Ihre Taskleiste anzuheften, um schnelleren Zugriff auf Ihre Wallet zu haben.\",\\n          \"title\": \"Installieren Sie die Enkrypt Wallet-Erweiterung\"\\n        },\\n        \"step2\": {\\n          \"description\": \"Stellen Sie sicher, dass Sie Ihr Wallet mit einer sicheren Methode sichern. Teilen Sie niemals Ihre geheime Phrase mit jemandem.\",\\n          \"title\": \"Erstellen oder importieren Sie ein Wallet\"\\n        },\\n        \"step3\": {\\n          \"description\": \"Sobald Sie Ihr Wallet eingerichtet haben, klicken Sie unten, um den Browser zu aktualisieren und die Erweiterung zu laden.\",\\n          \"title\": \"Aktualisieren Sie Ihren Browser\"\\n        }\\n      }\\n    },\\n    \"frame\": {\\n      \"extension\": {\\n        \"step1\": {\\n          \"description\": \"Wir empfehlen, Frame an Ihre Taskleiste anzuheften, um schnelleren Zugriff auf Ihre Wallet zu haben.\",\\n          \"title\": \"Installieren Sie Frame und die zugeh\\xF6rige Erweiterung\"\\n        },\\n        \"step2\": {\\n          \"description\": \"Stellen Sie sicher, dass Sie Ihr Wallet mit einer sicheren Methode sichern. Teilen Sie niemals Ihre geheime Phrase mit jemandem.\",\\n          \"title\": \"Erstellen oder importieren Sie ein Wallet\"\\n        },\\n        \"step3\": {\\n          \"description\": \"Sobald Sie Ihr Wallet eingerichtet haben, klicken Sie unten, um den Browser zu aktualisieren und die Erweiterung zu laden.\",\\n          \"title\": \"Aktualisieren Sie Ihren Browser\"\\n        }\\n      }\\n    },\\n    \"one_key\": {\\n      \"extension\": {\\n        \"step1\": {\\n          \"title\": \"Installieren Sie die OneKey Wallet-Erweiterung\",\\n          \"description\": \"Wir empfehlen, die OneKey Wallet an Ihre Taskleiste anzuheften, um schnelleren Zugriff auf Ihre Wallet zu haben.\"\\n        },\\n        \"step2\": {\\n          \"title\": \"Erstellen oder importieren Sie ein Wallet\",\\n          \"description\": \"Stellen Sie sicher, dass Sie Ihr Wallet mit einer sicheren Methode sichern. Teilen Sie niemals Ihre geheime Phrase mit jemandem.\"\\n        },\\n        \"step3\": {\\n          \"title\": \"Aktualisieren Sie Ihren Browser\",\\n          \"description\": \"Sobald Sie Ihr Wallet eingerichtet haben, klicken Sie unten, um den Browser zu aktualisieren und die Erweiterung zu laden.\"\\n        }\\n      }\\n    },\\n    \"paraswap\": {\\n      \"qr_code\": {\\n        \"step1\": {\\n          \"title\": \"\\xD6ffnen Sie die ParaSwap-App\",\\n          \"description\": \"F\\xFCgen Sie die ParaSwap Wallet zu Ihrem Startbildschirm hinzu, um schneller auf Ihre Wallet zuzugreifen.\"\\n        },\\n        \"step2\": {\\n          \"title\": \"Erstellen oder importieren Sie ein Wallet\",\\n          \"description\": \"Erstellen Sie ein neues Wallet oder importieren Sie ein bestehendes.\"\\n        },\\n        \"step3\": {\\n          \"title\": \"Tippen Sie auf das QR-Symbol und scannen Sie\",\\n          \"description\": \"Tippen Sie auf das QR-Symbol auf Ihrem Startbildschirm, scannen Sie den Code und best\\xE4tigen Sie die Eingabeaufforderung, um die Verbindung herzustellen.\"\\n        }\\n      }\\n    },\\n    \"phantom\": {\\n      \"extension\": {\\n        \"step1\": {\\n          \"title\": \"Installieren Sie die Phantom-Erweiterung\",\\n          \"description\": \"Wir empfehlen, Phantom an Ihre Taskleiste anzuheften, um leichteren Zugriff auf Ihre Wallet zu haben.\"\\n        },\\n        \"step2\": {\\n          \"title\": \"Erstellen oder importieren Sie ein Wallet\",\\n          \"description\": \"Stellen Sie sicher, dass Sie Ihre Wallet auf sichere Weise sichern. Teilen Sie niemals Ihren geheimen Wiederherstellungssatz mit anderen.\"\\n        },\\n        \"step3\": {\\n          \"title\": \"Aktualisieren Sie Ihren Browser\",\\n          \"description\": \"Sobald Sie Ihr Wallet eingerichtet haben, klicken Sie unten, um den Browser zu aktualisieren und die Erweiterung zu laden.\"\\n        }\\n      }\\n    },\\n    \"rabby\": {\\n      \"extension\": {\\n        \"step1\": {\\n          \"title\": \"Installieren Sie die Rabby-Erweiterung\",\\n          \"description\": \"Wir empfehlen, Rabby an Ihre Taskleiste anzuheften, um schnelleren Zugriff auf Ihre Wallet zu haben.\"\\n        },\\n        \"step2\": {\\n          \"title\": \"Erstellen oder importieren Sie ein Wallet\",\\n          \"description\": \"Stellen Sie sicher, dass Sie Ihr Wallet mit einer sicheren Methode sichern. Teilen Sie niemals Ihre geheime Phrase mit jemandem.\"\\n        },\\n        \"step3\": {\\n          \"title\": \"Aktualisieren Sie Ihren Browser\",\\n          \"description\": \"Sobald Sie Ihr Wallet eingerichtet haben, klicken Sie unten, um den Browser zu aktualisieren und die Erweiterung zu laden.\"\\n        }\\n      }\\n    },\\n    \"ronin\": {\\n      \"qr_code\": {\\n        \"step1\": {\\n          \"description\": \"Wir empfehlen, die Ronin Wallet Ihrem Startbildschirm hinzuzuf\\xFCgen, um schneller darauf zuzugreifen.\",\\n          \"title\": \"\\xD6ffnen Sie die Ronin Wallet App\"\\n        },\\n        \"step2\": {\\n          \"description\": \"Stellen Sie sicher, dass Sie Ihr Wallet mit einer sicheren Methode sichern. Teilen Sie niemals Ihre geheime Phrase mit jemandem.\",\\n          \"title\": \"Erstellen oder importieren Sie ein Wallet\"\\n        },\\n        \"step3\": {\\n          \"description\": \"Nach dem Scannen erscheint eine Verbindungsmeldung, um Ihr Wallet zu verbinden.\",\\n          \"title\": \"Tippen Sie auf die Scan-Schaltfl\\xE4che\"\\n        }\\n      },\\n      \"extension\": {\\n        \"step1\": {\\n          \"description\": \"Wir empfehlen, die Ronin Wallet an Ihre Taskleiste zu heften, um schneller darauf zuzugreifen.\",\\n          \"title\": \"Installieren Sie die Ronin Wallet Erweiterung\"\\n        },\\n        \"step2\": {\\n          \"description\": \"Stellen Sie sicher, dass Sie Ihr Wallet mit einer sicheren Methode sichern. Teilen Sie niemals Ihre geheime Phrase mit jemandem.\",\\n          \"title\": \"Erstellen oder importieren Sie ein Wallet\"\\n        },\\n        \"step3\": {\\n          \"description\": \"Sobald Sie Ihr Wallet eingerichtet haben, klicken Sie unten, um den Browser zu aktualisieren und die Erweiterung zu laden.\",\\n          \"title\": \"Aktualisieren Sie Ihren Browser\"\\n        }\\n      }\\n    },\\n    \"ramper\": {\\n      \"extension\": {\\n        \"step1\": {\\n          \"title\": \"Installieren Sie die Ramper Erweiterung\",\\n          \"description\": \"Wir empfehlen, Ramper an Ihre Taskleiste zu heften, um einfacher darauf zugreifen zu k\\xF6nnen.\"\\n        },\\n        \"step2\": {\\n          \"title\": \"Erstellen Sie eine Wallet\",\\n          \"description\": \"Stellen Sie sicher, dass Sie Ihr Wallet mit einer sicheren Methode sichern. Teilen Sie niemals Ihre geheime Phrase mit jemandem.\"\\n        },\\n        \"step3\": {\\n          \"title\": \"Aktualisieren Sie Ihren Browser\",\\n          \"description\": \"Sobald Sie Ihr Wallet eingerichtet haben, klicken Sie unten, um den Browser zu aktualisieren und die Erweiterung zu laden.\"\\n        }\\n      }\\n    },\\n    \"safeheron\": {\\n      \"extension\": {\\n        \"step1\": {\\n          \"title\": \"Installieren Sie die Core-Erweiterung\",\\n          \"description\": \"Wir empfehlen, Safeheron an Ihre Taskleiste anzuheften, um schnelleren Zugriff auf Ihre Wallet zu haben.\"\\n        },\\n        \"step2\": {\\n          \"title\": \"Erstellen oder importieren Sie ein Wallet\",\\n          \"description\": \"Stellen Sie sicher, dass Sie Ihr Wallet mit einer sicheren Methode sichern. Teilen Sie niemals Ihre geheime Phrase mit jemandem.\"\\n        },\\n        \"step3\": {\\n          \"title\": \"Aktualisieren Sie Ihren Browser\",\\n          \"description\": \"Sobald Sie Ihr Wallet eingerichtet haben, klicken Sie unten, um den Browser zu aktualisieren und die Erweiterung zu laden.\"\\n        }\\n      }\\n    },\\n    \"taho\": {\\n      \"extension\": {\\n        \"step1\": {\\n          \"title\": \"Installieren Sie die Taho-Erweiterung\",\\n          \"description\": \"Wir empfehlen, Taho an Ihre Taskleiste anzuheften, um schnelleren Zugriff auf Ihre Wallet zu haben.\"\\n        },\\n        \"step2\": {\\n          \"title\": \"Erstellen oder importieren Sie ein Wallet\",\\n          \"description\": \"Stellen Sie sicher, dass Sie Ihr Wallet mit einer sicheren Methode sichern. Teilen Sie niemals Ihre geheime Phrase mit jemandem.\"\\n        },\\n        \"step3\": {\\n          \"title\": \"Aktualisieren Sie Ihren Browser\",\\n          \"description\": \"Sobald Sie Ihr Wallet eingerichtet haben, klicken Sie unten, um den Browser zu aktualisieren und die Erweiterung zu laden.\"\\n        }\\n      }\\n    },\\n    \"wigwam\": {\\n      \"extension\": {\\n        \"step1\": {\\n          \"title\": \"Installieren Sie die Wigwam-Erweiterung\",\\n          \"description\": \"Wir empfehlen, Wigwam an Ihre Taskleiste anzuheften, um einen schnelleren Zugriff auf Ihr Wallet zu erm\\xF6glichen.\"\\n        },\\n        \"step2\": {\\n          \"title\": \"Erstellen oder importieren Sie ein Wallet\",\\n          \"description\": \"Stellen Sie sicher, dass Sie Ihr Wallet mit einer sicheren Methode sichern. Teilen Sie niemals Ihre geheime Phrase mit jemandem.\"\\n        },\\n        \"step3\": {\\n          \"title\": \"Aktualisieren Sie Ihren Browser\",\\n          \"description\": \"Sobald Sie Ihr Wallet eingerichtet haben, klicken Sie unten, um den Browser zu aktualisieren und die Erweiterung zu laden.\"\\n        }\\n      }\\n    },\\n    \"talisman\": {\\n      \"extension\": {\\n        \"step1\": {\\n          \"title\": \"Installieren Sie die Talisman-Erweiterung\",\\n          \"description\": \"Wir empfehlen, Talisman an Ihre Taskleiste anzuheften, um schnelleren Zugriff auf Ihre Wallet zu haben.\"\\n        },\\n        \"step2\": {\\n          \"title\": \"Erstellen oder Importieren Sie eine Ethereum-Wallet\",\\n          \"description\": \"Stellen Sie sicher, dass Sie Ihre Wallet auf sichere Weise sichern. Teilen Sie niemals Ihre Wiederherstellungsphrase mit anderen.\"\\n        },\\n        \"step3\": {\\n          \"title\": \"Aktualisieren Sie Ihren Browser\",\\n          \"description\": \"Sobald Sie Ihr Wallet eingerichtet haben, klicken Sie unten, um den Browser zu aktualisieren und die Erweiterung zu laden.\"\\n        }\\n      }\\n    },\\n    \"xdefi\": {\\n      \"extension\": {\\n        \"step1\": {\\n          \"title\": \"Installieren Sie die XDEFI Wallet-Erweiterung\",\\n          \"description\": \"Wir empfehlen, die XDEFI Wallet an Ihre Taskleiste anzuheften, um schnelleren Zugriff auf Ihre Wallet zu haben.\"\\n        },\\n        \"step2\": {\\n          \"title\": \"Erstellen oder importieren Sie ein Wallet\",\\n          \"description\": \"Stellen Sie sicher, dass Sie Ihr Wallet mit einer sicheren Methode sichern. Teilen Sie niemals Ihre geheime Phrase mit jemandem.\"\\n        },\\n        \"step3\": {\\n          \"title\": \"Aktualisieren Sie Ihren Browser\",\\n          \"description\": \"Sobald Sie Ihr Wallet eingerichtet haben, klicken Sie unten, um den Browser zu aktualisieren und die Erweiterung zu laden.\"\\n        }\\n      }\\n    },\\n    \"zeal\": {\\n      \"qr_code\": {\\n        \"step1\": {\\n          \"title\": \"\\xD6ffnen Sie die Zeal App\",\\n          \"description\": \"F\\xFCgen Sie die Zeal Wallet Ihrem Startbildschirm hinzu, um schneller auf Ihr Wallet zuzugreifen.\"\\n        },\\n        \"step2\": {\\n          \"title\": \"Erstellen oder importieren Sie ein Wallet\",\\n          \"description\": \"Erstellen Sie ein neues Wallet oder importieren Sie ein bestehendes.\"\\n        },\\n        \"step3\": {\\n          \"title\": \"Tippen Sie auf das QR-Symbol und scannen Sie\",\\n          \"description\": \"Tippen Sie auf das QR-Symbol auf Ihrem Startbildschirm, scannen Sie den Code und best\\xE4tigen Sie die Eingabeaufforderung, um die Verbindung herzustellen.\"\\n        }\\n      },\\n      \"extension\": {\\n        \"step1\": {\\n          \"title\": \"Installieren Sie die Zeal-Erweiterung\",\\n          \"description\": \"Wir empfehlen, Zeal an Ihre Taskleiste anzuheften, um schnelleren Zugriff auf Ihre Wallet zu haben.\"\\n        },\\n        \"step2\": {\\n          \"title\": \"Erstellen oder importieren Sie ein Wallet\",\\n          \"description\": \"Stellen Sie sicher, dass Sie Ihr Wallet mit einer sicheren Methode sichern. Teilen Sie niemals Ihre geheime Phrase mit jemandem.\"\\n        },\\n        \"step3\": {\\n          \"title\": \"Aktualisieren Sie Ihren Browser\",\\n          \"description\": \"Sobald Sie Ihr Wallet eingerichtet haben, klicken Sie unten, um den Browser zu aktualisieren und die Erweiterung zu laden.\"\\n        }\\n      }\\n    },\\n    \"safepal\": {\\n      \"extension\": {\\n        \"step1\": {\\n          \"title\": \"Installieren Sie die SafePal Wallet-Erweiterung\",\\n          \"description\": \"Klicken Sie oben rechts in Ihrem Browser und heften Sie SafePal Wallet f\\xFCr einfachen Zugriff an.\"\\n        },\\n        \"step2\": {\\n          \"title\": \"Erstellen oder importieren Sie ein Wallet\",\\n          \"description\": \"Erstellen Sie ein neues Wallet oder importieren Sie ein bestehendes.\"\\n        },\\n        \"step3\": {\\n          \"title\": \"Aktualisieren Sie Ihren Browser\",\\n          \"description\": \"Sobald Sie die SafePal Wallet eingerichtet haben, klicken Sie unten, um den Browser zu aktualisieren und die Erweiterung zu laden.\"\\n        }\\n      },\\n      \"qr_code\": {\\n        \"step1\": {\\n          \"title\": \"\\xD6ffnen Sie die SafePal Wallet-App\",\\n          \"description\": \"Platzieren Sie SafePal Wallet auf Ihrem Startbildschirm, um schnelleren Zugriff auf Ihre Wallet zu haben.\"\\n        },\\n        \"step2\": {\\n          \"title\": \"Erstellen oder importieren Sie ein Wallet\",\\n          \"description\": \"Erstellen Sie ein neues Wallet oder importieren Sie ein bestehendes.\"\\n        },\\n        \"step3\": {\\n          \"title\": \"Tippen Sie auf WalletConnect in den Einstellungen\",\\n          \"description\": \"W\\xE4hlen Sie Neue Verbindung, dann scannen Sie den QR-Code und best\\xE4tigen Sie die Eingabeaufforderung, um die Verbindung herzustellen.\"\\n        }\\n      }\\n    },\\n    \"desig\": {\\n      \"extension\": {\\n        \"step1\": {\\n          \"title\": \"Installieren Sie die Desig-Erweiterung\",\\n          \"description\": \"Wir empfehlen, Desig an Ihre Taskleiste anzuheften, um leichteren Zugriff auf Ihre Wallet zu haben.\"\\n        },\\n        \"step2\": {\\n          \"title\": \"Erstellen Sie eine Wallet\",\\n          \"description\": \"Stellen Sie sicher, dass Sie Ihr Wallet mit einer sicheren Methode sichern. Teilen Sie niemals Ihre geheime Phrase mit jemandem.\"\\n        },\\n        \"step3\": {\\n          \"title\": \"Aktualisieren Sie Ihren Browser\",\\n          \"description\": \"Sobald Sie Ihr Wallet eingerichtet haben, klicken Sie unten, um den Browser zu aktualisieren und die Erweiterung zu laden.\"\\n        }\\n      }\\n    },\\n    \"subwallet\": {\\n      \"extension\": {\\n        \"step1\": {\\n          \"title\": \"Installieren Sie die SubWallet-Erweiterung\",\\n          \"description\": \"Wir empfehlen, SubWallet an Ihre Taskleiste zu heften, um schneller auf Ihr Wallet zugreifen zu k\\xF6nnen.\"\\n        },\\n        \"step2\": {\\n          \"title\": \"Erstellen oder importieren Sie ein Wallet\",\\n          \"description\": \"Stellen Sie sicher, dass Sie Ihre Wallet auf sichere Weise sichern. Teilen Sie niemals Ihre Wiederherstellungsphrase mit anderen.\"\\n        },\\n        \"step3\": {\\n          \"title\": \"Aktualisieren Sie Ihren Browser\",\\n          \"description\": \"Sobald Sie Ihr Wallet eingerichtet haben, klicken Sie unten, um den Browser zu aktualisieren und die Erweiterung zu laden.\"\\n        }\\n      },\\n      \"qr_code\": {\\n        \"step1\": {\\n          \"title\": \"\\xD6ffnen Sie die SubWallet-App\",\\n          \"description\": \"Wir empfehlen, SubWallet auf Ihrem Startbildschirm abzulegen, um schneller darauf zugreifen zu k\\xF6nnen.\"\\n        },\\n        \"step2\": {\\n          \"title\": \"Erstellen oder importieren Sie ein Wallet\",\\n          \"description\": \"Stellen Sie sicher, dass Sie Ihr Wallet mit einer sicheren Methode sichern. Teilen Sie niemals Ihre geheime Phrase mit jemandem.\"\\n        },\\n        \"step3\": {\\n          \"title\": \"Tippen Sie auf die Scan-Schaltfl\\xE4che\",\\n          \"description\": \"Nach dem Scannen erscheint eine Verbindungsmeldung, um Ihr Wallet zu verbinden.\"\\n        }\\n      }\\n    },\\n    \"clv\": {\\n      \"extension\": {\\n        \"step1\": {\\n          \"title\": \"Installieren Sie die CLV Wallet-Erweiterung\",\\n          \"description\": \"Wir empfehlen, CLV Wallet an Ihre Taskleiste zu heften, um schneller auf Ihr Wallet zugreifen zu k\\xF6nnen.\"\\n        },\\n        \"step2\": {\\n          \"title\": \"Erstellen oder importieren Sie ein Wallet\",\\n          \"description\": \"Stellen Sie sicher, dass Sie Ihr Wallet mit einer sicheren Methode sichern. Teilen Sie niemals Ihre geheime Phrase mit jemandem.\"\\n        },\\n        \"step3\": {\\n          \"title\": \"Aktualisieren Sie Ihren Browser\",\\n          \"description\": \"Sobald Sie Ihr Wallet eingerichtet haben, klicken Sie unten, um den Browser zu aktualisieren und die Erweiterung zu laden.\"\\n        }\\n      },\\n      \"qr_code\": {\\n        \"step1\": {\\n          \"title\": \"\\xD6ffnen Sie die CLV Wallet-App\",\\n          \"description\": \"Wir empfehlen, CLV Wallet auf Ihrem Startbildschirm abzulegen, um schneller darauf zugreifen zu k\\xF6nnen.\"\\n        },\\n        \"step2\": {\\n          \"title\": \"Erstellen oder importieren Sie ein Wallet\",\\n          \"description\": \"Stellen Sie sicher, dass Sie Ihr Wallet mit einer sicheren Methode sichern. Teilen Sie niemals Ihre geheime Phrase mit jemandem.\"\\n        },\\n        \"step3\": {\\n          \"title\": \"Tippen Sie auf die Scan-Schaltfl\\xE4che\",\\n          \"description\": \"Nach dem Scannen erscheint eine Verbindungsmeldung, um Ihr Wallet zu verbinden.\"\\n        }\\n      }\\n    },\\n    \"okto\": {\\n      \"qr_code\": {\\n        \"step1\": {\\n          \"title\": \"\\xD6ffnen Sie die Okto-App\",\\n          \"description\": \"F\\xFCgen Sie Okto Ihrem Startbildschirm hinzu, um schnellen Zugriff zu erhalten\"\\n        },\\n        \"step2\": {\\n          \"title\": \"Erstellen Sie ein MPC-Wallet\",\\n          \"description\": \"Erstellen Sie ein Konto und generieren Sie ein Wallet\"\\n        },\\n        \"step3\": {\\n          \"title\": \"Tippen Sie auf WalletConnect in den Einstellungen\",\\n          \"description\": \"Tippen Sie auf das QR-Scan-Symbol oben rechts und best\\xE4tigen Sie die Aufforderung zum Verbinden.\"\\n        }\\n      }\\n    },\\n    \"ledger\": {\\n      \"desktop\": {\\n        \"step1\": {\\n          \"title\": \"\\xD6ffnen Sie die Ledger Live-App\",\\n          \"description\": \"Wir empfehlen, Ledger Live auf Ihrem Startbildschirm abzulegen, um schneller darauf zugreifen zu k\\xF6nnen.\"\\n        },\\n        \"step2\": {\\n          \"title\": \"Richten Sie Ihr Ledger ein\",\\n          \"description\": \"Richten Sie ein neues Ledger ein oder verbinden Sie sich mit einem bestehenden.\"\\n        },\\n        \"step3\": {\\n          \"title\": \"Verbinden\",\\n          \"description\": \"Eine Verbindungsmeldung erscheint, um Ihr Wallet zu verbinden.\"\\n        }\\n      },\\n      \"qr_code\": {\\n        \"step1\": {\\n          \"title\": \"\\xD6ffnen Sie die Ledger Live-App\",\\n          \"description\": \"Wir empfehlen, Ledger Live auf Ihrem Startbildschirm abzulegen, um schneller darauf zugreifen zu k\\xF6nnen.\"\\n        },\\n        \"step2\": {\\n          \"title\": \"Richten Sie Ihr Ledger ein\",\\n          \"description\": \"Sie k\\xF6nnen entweder mit der Desktop-App synchronisieren oder Ihr Ledger verbinden.\"\\n        },\\n        \"step3\": {\\n          \"title\": \"Code scannen\",\\n          \"description\": \"Tippen Sie auf WalletConnect und wechseln Sie dann zum Scanner. Nach dem Scannen erscheint eine Verbindungsmeldung, um Ihr Wallet zu verbinden.\"\\n        }\\n      }\\n    },\\n    \"valora\": {\\n      \"qr_code\": {\\n        \"step1\": {\\n          \"title\": \"\\xD6ffnen Sie die Valora-App\",\\n          \"description\": \"Wir empfehlen, Valora f\\xFCr einen schnelleren Zugriff auf Ihrem Startbildschirm zu platzieren.\"\\n        },\\n        \"step2\": {\\n          \"title\": \"Erstellen oder importieren Sie eine Wallet\",\\n          \"description\": \"Erstellen Sie ein neues Wallet oder importieren Sie ein bestehendes.\"\\n        },\\n        \"step3\": {\\n          \"title\": \"Tippen Sie auf die Scan-Schaltfl\\xE4che\",\\n          \"description\": \"Nach dem Scannen erscheint eine Verbindungsmeldung, um Ihr Wallet zu verbinden.\"\\n        }\\n      }\\n    },\\n    \"gate\": {\\n      \"qr_code\": {\\n        \"step1\": {\\n          \"title\": \"\\xD6ffnen Sie die Gate-App\",\\n          \"description\": \"Wir empfehlen, Gate auf Ihrem Startbildschirm abzulegen, um schnellen Zugriff zu erhalten.\"\\n        },\\n        \"step2\": {\\n          \"title\": \"Erstellen oder importieren Sie ein Wallet\",\\n          \"description\": \"Erstellen Sie ein neues Wallet oder importieren Sie ein bestehendes.\"\\n        },\\n        \"step3\": {\\n          \"title\": \"Tippen Sie auf die Scan-Schaltfl\\xE4che\",\\n          \"description\": \"Nach dem Scannen erscheint eine Verbindungsmeldung, um Ihr Wallet zu verbinden.\"\\n        }\\n      },\\n      \"extension\": {\\n        \"step1\": {\\n          \"title\": \"Installieren Sie die Gate-Erweiterung\",\\n          \"description\": \"Wir empfehlen, Gate in Ihre Taskleiste zu heften, um leichteren Zugriff auf Ihr Wallet zu haben.\"\\n        },\\n        \"step2\": {\\n          \"title\": \"Erstellen oder importieren Sie ein Wallet\",\\n          \"description\": \"Stellen Sie sicher, dass Sie Ihre Wallet auf sichere Weise sichern. Teilen Sie niemals Ihren geheimen Wiederherstellungssatz mit anderen.\"\\n        },\\n        \"step3\": {\\n          \"title\": \"Aktualisieren Sie Ihren Browser\",\\n          \"description\": \"Sobald Sie Ihr Wallet eingerichtet haben, klicken Sie unten, um den Browser zu aktualisieren und die Erweiterung zu laden.\"\\n        }\\n      }\\n    },\\n    \"xportal\": {\\n      \"qr_code\": {\\n        \"step1\": {\\n          \"description\": \"Legen Sie xPortal auf Ihrem Startbildschirm ab, um schneller auf Ihr Wallet zuzugreifen.\",\\n          \"title\": \"\\xD6ffnen Sie die xPortal-App\"\\n        },\\n        \"step2\": {\\n          \"description\": \"Erstellen Sie ein neues Wallet oder importieren Sie ein bestehendes.\",\\n          \"title\": \"Erstellen oder importieren Sie ein Wallet\"\\n        },\\n        \"step3\": {\\n          \"description\": \"Nach dem Scannen erscheint eine Verbindungsmeldung, um Ihr Wallet zu verbinden.\",\\n          \"title\": \"Tippen Sie auf die Schaltfl\\xE4che QR-Scan\"\\n        }\\n      }\\n    },\\n    \"mew\": {\\n      \"qr_code\": {\\n        \"step1\": {\\n          \"description\": \"Wir empfehlen, MEW Wallet auf Ihrem Startbildschirm abzulegen, um schneller darauf zugreifen zu k\\xF6nnen.\",\\n          \"title\": \"\\xD6ffnen Sie die MEW Wallet-App\"\\n        },\\n        \"step2\": {\\n          \"description\": \"Sie k\\xF6nnen Ihr Wallet problemlos mit der Cloud-Backup-Funktion sichern.\",\\n          \"title\": \"Erstellen oder importieren Sie ein Wallet\"\\n        },\\n        \"step3\": {\\n          \"description\": \"Nach dem Scannen erscheint eine Verbindungsmeldung, um Ihr Wallet zu verbinden.\",\\n          \"title\": \"Tippen Sie auf die Scan-Schaltfl\\xE4che\"\\n        }\\n      }\\n    }\\n  },\\n  \"zilpay\": {\\n    \"qr_code\": {\\n      \"step1\": {\\n        \"title\": \"\\xD6ffne die ZilPay-App\",\\n        \"description\": \"F\\xFCge ZilPay zu deinem Startbildschirm hinzu, um schneller auf dein Wallet zuzugreifen.\"\\n      },\\n      \"step2\": {\\n        \"title\": \"Erstellen oder importieren Sie ein Wallet\",\\n        \"description\": \"Erstellen Sie ein neues Wallet oder importieren Sie ein bestehendes.\"\\n      },\\n      \"step3\": {\\n        \"title\": \"Tippen Sie auf die Scan-Schaltfl\\xE4che\",\\n        \"description\": \"Nach dem Scannen erscheint eine Verbindungsmeldung, um Ihr Wallet zu verbinden.\"\\n      }\\n    }\\n  }\\n}\\n';\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@rainbow-me/rainbowkit/dist/de_DE-P43L3PR7.js\n"));

/***/ })

}]);