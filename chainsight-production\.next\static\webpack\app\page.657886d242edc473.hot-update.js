"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ HomePage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _components_Navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/Navigation */ \"(app-pages-browser)/./src/components/Navigation.tsx\");\n/* harmony import */ var _components_ModularDashboard__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ModularDashboard */ \"(app-pages-browser)/./src/components/ModularDashboard.tsx\");\n/* harmony import */ var _components_AIModuleSelector__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/AIModuleSelector */ \"(app-pages-browser)/./src/components/AIModuleSelector.tsx\");\n/* harmony import */ var _components_RealTimeNotifications__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/RealTimeNotifications */ \"(app-pages-browser)/./src/components/RealTimeNotifications.tsx\");\n/* harmony import */ var _components_SystemStatus__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/SystemStatus */ \"(app-pages-browser)/./src/components/SystemStatus.tsx\");\n/* harmony import */ var _components_ClientOnly__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ClientOnly */ \"(app-pages-browser)/./src/components/ClientOnly.tsx\");\n/* harmony import */ var _components_RealTimeChat__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/RealTimeChat */ \"(app-pages-browser)/./src/components/RealTimeChat.tsx\");\n/* harmony import */ var _store_useAppStore__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/store/useAppStore */ \"(app-pages-browser)/./src/store/useAppStore.ts\");\n/* harmony import */ var _hooks_useAIModules__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/hooks/useAIModules */ \"(app-pages-browser)/./src/hooks/useAIModules.ts\");\n/* harmony import */ var _hooks_useRealTimeData__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/hooks/useRealTimeData */ \"(app-pages-browser)/./src/hooks/useRealTimeData.ts\");\n/* harmony import */ var _lib_debug__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/lib/debug */ \"(app-pages-browser)/./src/lib/debug.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\nfunction HomePage() {\n    _s();\n    const [isChatOpen, setIsChatOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const { modules, isLoading: modulesLoading } = (0,_hooks_useAIModules__WEBPACK_IMPORTED_MODULE_10__.useAIModules)();\n    const { connectionStatus: realtimeConnectionStatus, marketData } = (0,_hooks_useRealTimeData__WEBPACK_IMPORTED_MODULE_11__.useRealTimeData)();\n    // Debug tracking\n    const { logMount, logUnmount, logError, logInfo } = (0,_lib_debug__WEBPACK_IMPORTED_MODULE_12__.useDebug)('HomePage');\n    // Global state\n    const { updateSettings, updateConnectionStatus } = (0,_store_useAppStore__WEBPACK_IMPORTED_MODULE_9__.useAppActions)();\n    const settings = (0,_store_useAppStore__WEBPACK_IMPORTED_MODULE_9__.useSettings)();\n    const connectionStatus = (0,_store_useAppStore__WEBPACK_IMPORTED_MODULE_9__.useConnectionStatus)();\n    const activeModule = settings.activeModule;\n    const handleModuleSelect = (moduleId)=>{\n        updateSettings({\n            activeModule: moduleId\n        });\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"HomePage.useEffect\": ()=>{\n            try {\n                logMount();\n                // Initialize production platform\n                _lib_debug__WEBPACK_IMPORTED_MODULE_12__.chainSightDebugger.logInfo('🚀 Chainsight Production Platform Initialized');\n                logInfo('Platform initialization started');\n                // Performance tracking\n                _lib_debug__WEBPACK_IMPORTED_MODULE_12__.debugUtils.performanceMark('platform-init-start');\n                // Update connection status\n                updateConnectionStatus({\n                    api: 'connected',\n                    websocket: 'connected',\n                    blockchain: 'connected'\n                });\n                _lib_debug__WEBPACK_IMPORTED_MODULE_12__.debugUtils.performanceMark('platform-init-end');\n                _lib_debug__WEBPACK_IMPORTED_MODULE_12__.debugUtils.performanceMeasure('platform-init', 'platform-init-start', 'platform-init-end');\n                logInfo('Platform initialization completed', {\n                    modulesCount: modules.length,\n                    connectionStatus,\n                    chatOpen: isChatOpen\n                });\n            } catch (error) {\n                logError(error, 'Platform initialization failed');\n            }\n            return ({\n                \"HomePage.useEffect\": ()=>{\n                    logUnmount();\n                }\n            })[\"HomePage.useEffect\"];\n        }\n    }[\"HomePage.useEffect\"], [\n        connectionStatus,\n        updateConnectionStatus,\n        logMount,\n        logUnmount,\n        logError,\n        logInfo,\n        modules.length,\n        isChatOpen\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-br from-cosmic-dark via-cosmic-mid to-cosmic-light\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ClientOnly__WEBPACK_IMPORTED_MODULE_7__.ClientOnly, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Navigation__WEBPACK_IMPORTED_MODULE_2__.Navigation, {}, void 0, false, {\n                    fileName: \"F:\\\\AI chain\\\\chainsight-production\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 74,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"F:\\\\AI chain\\\\chainsight-production\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 73,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                className: \"pt-20\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                        className: \"relative overflow-hidden min-h-screen flex items-center justify-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute inset-0 bg-gradient-to-br from-cosmic-dark via-cosmic-mid to-cosmic-light\"\n                            }, void 0, false, {\n                                fileName: \"F:\\\\AI chain\\\\chainsight-production\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 81,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute top-[20%] left-[20%] w-96 h-96 bg-gradient-to-r from-neon-blue/20 to-neon-purple/20 rounded-full blur-3xl animate-pulse\"\n                            }, void 0, false, {\n                                fileName: \"F:\\\\AI chain\\\\chainsight-production\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 84,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute top-[80%] right-[20%] w-96 h-96 bg-gradient-to-r from-neon-pink/20 to-neon-orange/20 rounded-full blur-3xl animate-pulse\"\n                            }, void 0, false, {\n                                fileName: \"F:\\\\AI chain\\\\chainsight-production\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 85,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute top-[40%] right-[40%] w-96 h-96 bg-gradient-to-r from-neon-green/20 to-accent-cyan/20 rounded-full blur-3xl animate-pulse\"\n                            }, void 0, false, {\n                                fileName: \"F:\\\\AI chain\\\\chainsight-production\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 86,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative max-w-7xl mx-auto px-4 py-20 text-center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_13__.motion.div, {\n                                    initial: {\n                                        opacity: 0,\n                                        y: 20\n                                    },\n                                    animate: {\n                                        opacity: 1,\n                                        y: 0\n                                    },\n                                    className: \"space-y-12\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_13__.motion.h1, {\n                                            initial: {\n                                                opacity: 0,\n                                                y: 20\n                                            },\n                                            animate: {\n                                                opacity: 1,\n                                                y: 0\n                                            },\n                                            transition: {\n                                                delay: 0.4,\n                                                duration: 0.8\n                                            },\n                                            className: \"text-6xl md:text-8xl font-bold leading-tight float\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"block bg-gradient-to-r from-neon-blue via-neon-purple to-neon-pink bg-clip-text text-transparent\",\n                                                    children: \"CHAINSIGHT\"\n                                                }, void 0, false, {\n                                                    fileName: \"F:\\\\AI chain\\\\chainsight-production\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 101,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"block text-2xl md:text-4xl mt-4 neon-glow-blue\",\n                                                    children: \"by Connectouch\"\n                                                }, void 0, false, {\n                                                    fileName: \"F:\\\\AI chain\\\\chainsight-production\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 104,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"F:\\\\AI chain\\\\chainsight-production\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 95,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_13__.motion.p, {\n                                            initial: {\n                                                opacity: 0,\n                                                y: 20\n                                            },\n                                            animate: {\n                                                opacity: 1,\n                                                y: 0\n                                            },\n                                            transition: {\n                                                delay: 0.6,\n                                                duration: 0.8\n                                            },\n                                            className: \"text-xl md:text-2xl text-white/80 max-w-3xl mx-auto leading-relaxed\",\n                                            children: [\n                                                \"\\uD83D\\uDE80 Vibrant AI-Powered Platform with\",\n                                                ' ',\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"neon-glow-purple\",\n                                                    children: \"Real-Time Analytics\"\n                                                }, void 0, false, {\n                                                    fileName: \"F:\\\\AI chain\\\\chainsight-production\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 117,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \",\",\n                                                ' ',\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"neon-glow-pink\",\n                                                    children: \"Interactive Charts\"\n                                                }, void 0, false, {\n                                                    fileName: \"F:\\\\AI chain\\\\chainsight-production\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 118,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \", and\",\n                                                ' ',\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"neon-glow-blue\",\n                                                    children: \"Futuristic Design\"\n                                                }, void 0, false, {\n                                                    fileName: \"F:\\\\AI chain\\\\chainsight-production\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 119,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"F:\\\\AI chain\\\\chainsight-production\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 110,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_13__.motion.div, {\n                                            initial: {\n                                                opacity: 0,\n                                                y: 20\n                                            },\n                                            animate: {\n                                                opacity: 1,\n                                                y: 0\n                                            },\n                                            transition: {\n                                                delay: 0.8,\n                                                duration: 0.8\n                                            },\n                                            className: \"max-w-4xl mx-auto\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ClientOnly__WEBPACK_IMPORTED_MODULE_7__.ClientOnly, {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_AIModuleSelector__WEBPACK_IMPORTED_MODULE_4__.AIModuleSelector, {\n                                                    activeModule: activeModule,\n                                                    onModuleChange: handleModuleSelect,\n                                                    modules: modules,\n                                                    loading: modulesLoading\n                                                }, void 0, false, {\n                                                    fileName: \"F:\\\\AI chain\\\\chainsight-production\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 130,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"F:\\\\AI chain\\\\chainsight-production\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 129,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"F:\\\\AI chain\\\\chainsight-production\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 123,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"F:\\\\AI chain\\\\chainsight-production\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 89,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"F:\\\\AI chain\\\\chainsight-production\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 88,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"F:\\\\AI chain\\\\chainsight-production\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 79,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                        className: \"py-20\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"max-w-7xl mx-auto px-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ClientOnly__WEBPACK_IMPORTED_MODULE_7__.ClientOnly, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ModularDashboard__WEBPACK_IMPORTED_MODULE_3__.ModularDashboard, {\n                                    activeModule: activeModule,\n                                    marketData: marketData,\n                                    modules: modules\n                                }, void 0, false, {\n                                    fileName: \"F:\\\\AI chain\\\\chainsight-production\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 146,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"F:\\\\AI chain\\\\chainsight-production\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 145,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"F:\\\\AI chain\\\\chainsight-production\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 144,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"F:\\\\AI chain\\\\chainsight-production\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 143,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"fixed bottom-4 right-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ClientOnly__WEBPACK_IMPORTED_MODULE_7__.ClientOnly, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_SystemStatus__WEBPACK_IMPORTED_MODULE_6__.SystemStatus, {\n                                status: connectionStatus\n                            }, void 0, false, {\n                                fileName: \"F:\\\\AI chain\\\\chainsight-production\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 158,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"F:\\\\AI chain\\\\chainsight-production\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 157,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"F:\\\\AI chain\\\\chainsight-production\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 156,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ClientOnly__WEBPACK_IMPORTED_MODULE_7__.ClientOnly, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_RealTimeNotifications__WEBPACK_IMPORTED_MODULE_5__.RealTimeNotifications, {}, void 0, false, {\n                            fileName: \"F:\\\\AI chain\\\\chainsight-production\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 164,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"F:\\\\AI chain\\\\chainsight-production\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 163,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"F:\\\\AI chain\\\\chainsight-production\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 77,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ClientOnly__WEBPACK_IMPORTED_MODULE_7__.ClientOnly, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_RealTimeChat__WEBPACK_IMPORTED_MODULE_8__.RealTimeChat, {\n                    isOpen: isChatOpen,\n                    onToggle: ()=>setIsChatOpen(!isChatOpen)\n                }, void 0, false, {\n                    fileName: \"F:\\\\AI chain\\\\chainsight-production\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 170,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"F:\\\\AI chain\\\\chainsight-production\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 169,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"F:\\\\AI chain\\\\chainsight-production\\\\src\\\\app\\\\page.tsx\",\n        lineNumber: 72,\n        columnNumber: 5\n    }, this);\n}\n_s(HomePage, \"CVIKEsDwVjbhGixgZ3W1U8BoJXo=\", false, function() {\n    return [\n        _hooks_useAIModules__WEBPACK_IMPORTED_MODULE_10__.useAIModules,\n        _hooks_useRealTimeData__WEBPACK_IMPORTED_MODULE_11__.useRealTimeData,\n        _lib_debug__WEBPACK_IMPORTED_MODULE_12__.useDebug,\n        _store_useAppStore__WEBPACK_IMPORTED_MODULE_9__.useAppActions,\n        _store_useAppStore__WEBPACK_IMPORTED_MODULE_9__.useSettings,\n        _store_useAppStore__WEBPACK_IMPORTED_MODULE_9__.useConnectionStatus\n    ];\n});\n_c = HomePage;\nvar _c;\n$RefreshReg$(_c, \"HomePage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/page.tsx\n"));

/***/ })

});