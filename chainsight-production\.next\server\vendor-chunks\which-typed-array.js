"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/which-typed-array";
exports.ids = ["vendor-chunks/which-typed-array"];
exports.modules = {

/***/ "(ssr)/./node_modules/which-typed-array/index.js":
/*!*************************************************!*\
  !*** ./node_modules/which-typed-array/index.js ***!
  \*************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nvar forEach = __webpack_require__(/*! for-each */ \"(ssr)/./node_modules/for-each/index.js\");\nvar availableTypedArrays = __webpack_require__(/*! available-typed-arrays */ \"(ssr)/./node_modules/available-typed-arrays/index.js\");\nvar callBind = __webpack_require__(/*! call-bind */ \"(ssr)/./node_modules/call-bind/index.js\");\nvar callBound = __webpack_require__(/*! call-bound */ \"(ssr)/./node_modules/call-bound/index.js\");\nvar gOPD = __webpack_require__(/*! gopd */ \"(ssr)/./node_modules/gopd/index.js\");\nvar getProto = __webpack_require__(/*! get-proto */ \"(ssr)/./node_modules/get-proto/index.js\");\n\nvar $toString = callBound('Object.prototype.toString');\nvar hasToStringTag = __webpack_require__(/*! has-tostringtag/shams */ \"(ssr)/./node_modules/has-tostringtag/shams.js\")();\n\nvar g = typeof globalThis === 'undefined' ? global : globalThis;\nvar typedArrays = availableTypedArrays();\n\nvar $slice = callBound('String.prototype.slice');\n\n/** @type {<T = unknown>(array: readonly T[], value: unknown) => number} */\nvar $indexOf = callBound('Array.prototype.indexOf', true) || function indexOf(array, value) {\n\tfor (var i = 0; i < array.length; i += 1) {\n\t\tif (array[i] === value) {\n\t\t\treturn i;\n\t\t}\n\t}\n\treturn -1;\n};\n\n/** @typedef {import('./types').Getter} Getter */\n/** @type {import('./types').Cache} */\nvar cache = { __proto__: null };\nif (hasToStringTag && gOPD && getProto) {\n\tforEach(typedArrays, function (typedArray) {\n\t\tvar arr = new g[typedArray]();\n\t\tif (Symbol.toStringTag in arr && getProto) {\n\t\t\tvar proto = getProto(arr);\n\t\t\t// @ts-expect-error TS won't narrow inside a closure\n\t\t\tvar descriptor = gOPD(proto, Symbol.toStringTag);\n\t\t\tif (!descriptor && proto) {\n\t\t\t\tvar superProto = getProto(proto);\n\t\t\t\t// @ts-expect-error TS won't narrow inside a closure\n\t\t\t\tdescriptor = gOPD(superProto, Symbol.toStringTag);\n\t\t\t}\n\t\t\t// @ts-expect-error TODO: fix\n\t\t\tcache['$' + typedArray] = callBind(descriptor.get);\n\t\t}\n\t});\n} else {\n\tforEach(typedArrays, function (typedArray) {\n\t\tvar arr = new g[typedArray]();\n\t\tvar fn = arr.slice || arr.set;\n\t\tif (fn) {\n\t\t\tcache[\n\t\t\t\t/** @type {`$${import('.').TypedArrayName}`} */ ('$' + typedArray)\n\t\t\t] = /** @type {import('./types').BoundSlice | import('./types').BoundSet} */ (\n\t\t\t\t// @ts-expect-error TODO FIXME\n\t\t\t\tcallBind(fn)\n\t\t\t);\n\t\t}\n\t});\n}\n\n/** @type {(value: object) => false | import('.').TypedArrayName} */\nvar tryTypedArrays = function tryAllTypedArrays(value) {\n\t/** @type {ReturnType<typeof tryAllTypedArrays>} */ var found = false;\n\tforEach(\n\t\t/** @type {Record<`\\$${import('.').TypedArrayName}`, Getter>} */ (cache),\n\t\t/** @type {(getter: Getter, name: `\\$${import('.').TypedArrayName}`) => void} */\n\t\tfunction (getter, typedArray) {\n\t\t\tif (!found) {\n\t\t\t\ttry {\n\t\t\t\t\t// @ts-expect-error a throw is fine here\n\t\t\t\t\tif ('$' + getter(value) === typedArray) {\n\t\t\t\t\t\tfound = /** @type {import('.').TypedArrayName} */ ($slice(typedArray, 1));\n\t\t\t\t\t}\n\t\t\t\t} catch (e) { /**/ }\n\t\t\t}\n\t\t}\n\t);\n\treturn found;\n};\n\n/** @type {(value: object) => false | import('.').TypedArrayName} */\nvar trySlices = function tryAllSlices(value) {\n\t/** @type {ReturnType<typeof tryAllSlices>} */ var found = false;\n\tforEach(\n\t\t/** @type {Record<`\\$${import('.').TypedArrayName}`, Getter>} */(cache),\n\t\t/** @type {(getter: Getter, name: `\\$${import('.').TypedArrayName}`) => void} */ function (getter, name) {\n\t\t\tif (!found) {\n\t\t\t\ttry {\n\t\t\t\t\t// @ts-expect-error a throw is fine here\n\t\t\t\t\tgetter(value);\n\t\t\t\t\tfound = /** @type {import('.').TypedArrayName} */ ($slice(name, 1));\n\t\t\t\t} catch (e) { /**/ }\n\t\t\t}\n\t\t}\n\t);\n\treturn found;\n};\n\n/** @type {import('.')} */\nmodule.exports = function whichTypedArray(value) {\n\tif (!value || typeof value !== 'object') { return false; }\n\tif (!hasToStringTag) {\n\t\t/** @type {string} */\n\t\tvar tag = $slice($toString(value), 8, -1);\n\t\tif ($indexOf(typedArrays, tag) > -1) {\n\t\t\treturn tag;\n\t\t}\n\t\tif (tag !== 'Object') {\n\t\t\treturn false;\n\t\t}\n\t\t// node < 0.6 hits here on real Typed Arrays\n\t\treturn trySlices(value);\n\t}\n\tif (!gOPD) { return null; } // unknown engine\n\treturn tryTypedArrays(value);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvd2hpY2gtdHlwZWQtYXJyYXkvaW5kZXguanMiLCJtYXBwaW5ncyI6IkFBQWE7O0FBRWIsY0FBYyxtQkFBTyxDQUFDLHdEQUFVO0FBQ2hDLDJCQUEyQixtQkFBTyxDQUFDLG9GQUF3QjtBQUMzRCxlQUFlLG1CQUFPLENBQUMsMERBQVc7QUFDbEMsZ0JBQWdCLG1CQUFPLENBQUMsNERBQVk7QUFDcEMsV0FBVyxtQkFBTyxDQUFDLGdEQUFNO0FBQ3pCLGVBQWUsbUJBQU8sQ0FBQywwREFBVzs7QUFFbEM7QUFDQSxxQkFBcUIsbUJBQU8sQ0FBQyw0RUFBdUI7O0FBRXBEO0FBQ0E7O0FBRUE7O0FBRUEsV0FBVyw4REFBOEQ7QUFDekU7QUFDQSxpQkFBaUIsa0JBQWtCO0FBQ25DO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQSxjQUFjLDBCQUEwQjtBQUN4QyxXQUFXLHlCQUF5QjtBQUNwQyxjQUFjO0FBQ2Q7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsRUFBRTtBQUNGLEVBQUU7QUFDRjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsZUFBZSxJQUFJLDJCQUEyQixHQUFHO0FBQ2pELGtCQUFrQiwyREFBMkQ7QUFDN0U7QUFDQTtBQUNBO0FBQ0E7QUFDQSxFQUFFO0FBQ0Y7O0FBRUEsV0FBVyx1REFBdUQ7QUFDbEU7QUFDQSxZQUFZLHNDQUFzQztBQUNsRDtBQUNBLGFBQWEsWUFBWSwyQkFBMkIsWUFBWTtBQUNoRSxhQUFhLDRCQUE0QiwyQkFBMkIsWUFBWTtBQUNoRjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EseUJBQXlCLDRCQUE0QjtBQUNyRDtBQUNBLE1BQU0sWUFBWTtBQUNsQjtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBLFdBQVcsdURBQXVEO0FBQ2xFO0FBQ0EsWUFBWSxpQ0FBaUM7QUFDN0M7QUFDQSxhQUFhLFlBQVksMkJBQTJCLFlBQVk7QUFDaEUsYUFBYSw0QkFBNEIsMkJBQTJCLFlBQVk7QUFDaEY7QUFDQTtBQUNBO0FBQ0E7QUFDQSx3QkFBd0IsNEJBQTRCO0FBQ3BELE1BQU0sWUFBWTtBQUNsQjtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBLFdBQVcsYUFBYTtBQUN4QjtBQUNBLDRDQUE0QztBQUM1QztBQUNBLGFBQWEsUUFBUTtBQUNyQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGNBQWMsZUFBZTtBQUM3QjtBQUNBIiwic291cmNlcyI6WyJGOlxcQUkgY2hhaW5cXGNoYWluc2lnaHQtcHJvZHVjdGlvblxcbm9kZV9tb2R1bGVzXFx3aGljaC10eXBlZC1hcnJheVxcaW5kZXguanMiXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBzdHJpY3QnO1xuXG52YXIgZm9yRWFjaCA9IHJlcXVpcmUoJ2Zvci1lYWNoJyk7XG52YXIgYXZhaWxhYmxlVHlwZWRBcnJheXMgPSByZXF1aXJlKCdhdmFpbGFibGUtdHlwZWQtYXJyYXlzJyk7XG52YXIgY2FsbEJpbmQgPSByZXF1aXJlKCdjYWxsLWJpbmQnKTtcbnZhciBjYWxsQm91bmQgPSByZXF1aXJlKCdjYWxsLWJvdW5kJyk7XG52YXIgZ09QRCA9IHJlcXVpcmUoJ2dvcGQnKTtcbnZhciBnZXRQcm90byA9IHJlcXVpcmUoJ2dldC1wcm90bycpO1xuXG52YXIgJHRvU3RyaW5nID0gY2FsbEJvdW5kKCdPYmplY3QucHJvdG90eXBlLnRvU3RyaW5nJyk7XG52YXIgaGFzVG9TdHJpbmdUYWcgPSByZXF1aXJlKCdoYXMtdG9zdHJpbmd0YWcvc2hhbXMnKSgpO1xuXG52YXIgZyA9IHR5cGVvZiBnbG9iYWxUaGlzID09PSAndW5kZWZpbmVkJyA/IGdsb2JhbCA6IGdsb2JhbFRoaXM7XG52YXIgdHlwZWRBcnJheXMgPSBhdmFpbGFibGVUeXBlZEFycmF5cygpO1xuXG52YXIgJHNsaWNlID0gY2FsbEJvdW5kKCdTdHJpbmcucHJvdG90eXBlLnNsaWNlJyk7XG5cbi8qKiBAdHlwZSB7PFQgPSB1bmtub3duPihhcnJheTogcmVhZG9ubHkgVFtdLCB2YWx1ZTogdW5rbm93bikgPT4gbnVtYmVyfSAqL1xudmFyICRpbmRleE9mID0gY2FsbEJvdW5kKCdBcnJheS5wcm90b3R5cGUuaW5kZXhPZicsIHRydWUpIHx8IGZ1bmN0aW9uIGluZGV4T2YoYXJyYXksIHZhbHVlKSB7XG5cdGZvciAodmFyIGkgPSAwOyBpIDwgYXJyYXkubGVuZ3RoOyBpICs9IDEpIHtcblx0XHRpZiAoYXJyYXlbaV0gPT09IHZhbHVlKSB7XG5cdFx0XHRyZXR1cm4gaTtcblx0XHR9XG5cdH1cblx0cmV0dXJuIC0xO1xufTtcblxuLyoqIEB0eXBlZGVmIHtpbXBvcnQoJy4vdHlwZXMnKS5HZXR0ZXJ9IEdldHRlciAqL1xuLyoqIEB0eXBlIHtpbXBvcnQoJy4vdHlwZXMnKS5DYWNoZX0gKi9cbnZhciBjYWNoZSA9IHsgX19wcm90b19fOiBudWxsIH07XG5pZiAoaGFzVG9TdHJpbmdUYWcgJiYgZ09QRCAmJiBnZXRQcm90bykge1xuXHRmb3JFYWNoKHR5cGVkQXJyYXlzLCBmdW5jdGlvbiAodHlwZWRBcnJheSkge1xuXHRcdHZhciBhcnIgPSBuZXcgZ1t0eXBlZEFycmF5XSgpO1xuXHRcdGlmIChTeW1ib2wudG9TdHJpbmdUYWcgaW4gYXJyICYmIGdldFByb3RvKSB7XG5cdFx0XHR2YXIgcHJvdG8gPSBnZXRQcm90byhhcnIpO1xuXHRcdFx0Ly8gQHRzLWV4cGVjdC1lcnJvciBUUyB3b24ndCBuYXJyb3cgaW5zaWRlIGEgY2xvc3VyZVxuXHRcdFx0dmFyIGRlc2NyaXB0b3IgPSBnT1BEKHByb3RvLCBTeW1ib2wudG9TdHJpbmdUYWcpO1xuXHRcdFx0aWYgKCFkZXNjcmlwdG9yICYmIHByb3RvKSB7XG5cdFx0XHRcdHZhciBzdXBlclByb3RvID0gZ2V0UHJvdG8ocHJvdG8pO1xuXHRcdFx0XHQvLyBAdHMtZXhwZWN0LWVycm9yIFRTIHdvbid0IG5hcnJvdyBpbnNpZGUgYSBjbG9zdXJlXG5cdFx0XHRcdGRlc2NyaXB0b3IgPSBnT1BEKHN1cGVyUHJvdG8sIFN5bWJvbC50b1N0cmluZ1RhZyk7XG5cdFx0XHR9XG5cdFx0XHQvLyBAdHMtZXhwZWN0LWVycm9yIFRPRE86IGZpeFxuXHRcdFx0Y2FjaGVbJyQnICsgdHlwZWRBcnJheV0gPSBjYWxsQmluZChkZXNjcmlwdG9yLmdldCk7XG5cdFx0fVxuXHR9KTtcbn0gZWxzZSB7XG5cdGZvckVhY2godHlwZWRBcnJheXMsIGZ1bmN0aW9uICh0eXBlZEFycmF5KSB7XG5cdFx0dmFyIGFyciA9IG5ldyBnW3R5cGVkQXJyYXldKCk7XG5cdFx0dmFyIGZuID0gYXJyLnNsaWNlIHx8IGFyci5zZXQ7XG5cdFx0aWYgKGZuKSB7XG5cdFx0XHRjYWNoZVtcblx0XHRcdFx0LyoqIEB0eXBlIHtgJCR7aW1wb3J0KCcuJykuVHlwZWRBcnJheU5hbWV9YH0gKi8gKCckJyArIHR5cGVkQXJyYXkpXG5cdFx0XHRdID0gLyoqIEB0eXBlIHtpbXBvcnQoJy4vdHlwZXMnKS5Cb3VuZFNsaWNlIHwgaW1wb3J0KCcuL3R5cGVzJykuQm91bmRTZXR9ICovIChcblx0XHRcdFx0Ly8gQHRzLWV4cGVjdC1lcnJvciBUT0RPIEZJWE1FXG5cdFx0XHRcdGNhbGxCaW5kKGZuKVxuXHRcdFx0KTtcblx0XHR9XG5cdH0pO1xufVxuXG4vKiogQHR5cGUgeyh2YWx1ZTogb2JqZWN0KSA9PiBmYWxzZSB8IGltcG9ydCgnLicpLlR5cGVkQXJyYXlOYW1lfSAqL1xudmFyIHRyeVR5cGVkQXJyYXlzID0gZnVuY3Rpb24gdHJ5QWxsVHlwZWRBcnJheXModmFsdWUpIHtcblx0LyoqIEB0eXBlIHtSZXR1cm5UeXBlPHR5cGVvZiB0cnlBbGxUeXBlZEFycmF5cz59ICovIHZhciBmb3VuZCA9IGZhbHNlO1xuXHRmb3JFYWNoKFxuXHRcdC8qKiBAdHlwZSB7UmVjb3JkPGBcXCQke2ltcG9ydCgnLicpLlR5cGVkQXJyYXlOYW1lfWAsIEdldHRlcj59ICovIChjYWNoZSksXG5cdFx0LyoqIEB0eXBlIHsoZ2V0dGVyOiBHZXR0ZXIsIG5hbWU6IGBcXCQke2ltcG9ydCgnLicpLlR5cGVkQXJyYXlOYW1lfWApID0+IHZvaWR9ICovXG5cdFx0ZnVuY3Rpb24gKGdldHRlciwgdHlwZWRBcnJheSkge1xuXHRcdFx0aWYgKCFmb3VuZCkge1xuXHRcdFx0XHR0cnkge1xuXHRcdFx0XHRcdC8vIEB0cy1leHBlY3QtZXJyb3IgYSB0aHJvdyBpcyBmaW5lIGhlcmVcblx0XHRcdFx0XHRpZiAoJyQnICsgZ2V0dGVyKHZhbHVlKSA9PT0gdHlwZWRBcnJheSkge1xuXHRcdFx0XHRcdFx0Zm91bmQgPSAvKiogQHR5cGUge2ltcG9ydCgnLicpLlR5cGVkQXJyYXlOYW1lfSAqLyAoJHNsaWNlKHR5cGVkQXJyYXksIDEpKTtcblx0XHRcdFx0XHR9XG5cdFx0XHRcdH0gY2F0Y2ggKGUpIHsgLyoqLyB9XG5cdFx0XHR9XG5cdFx0fVxuXHQpO1xuXHRyZXR1cm4gZm91bmQ7XG59O1xuXG4vKiogQHR5cGUgeyh2YWx1ZTogb2JqZWN0KSA9PiBmYWxzZSB8IGltcG9ydCgnLicpLlR5cGVkQXJyYXlOYW1lfSAqL1xudmFyIHRyeVNsaWNlcyA9IGZ1bmN0aW9uIHRyeUFsbFNsaWNlcyh2YWx1ZSkge1xuXHQvKiogQHR5cGUge1JldHVyblR5cGU8dHlwZW9mIHRyeUFsbFNsaWNlcz59ICovIHZhciBmb3VuZCA9IGZhbHNlO1xuXHRmb3JFYWNoKFxuXHRcdC8qKiBAdHlwZSB7UmVjb3JkPGBcXCQke2ltcG9ydCgnLicpLlR5cGVkQXJyYXlOYW1lfWAsIEdldHRlcj59ICovKGNhY2hlKSxcblx0XHQvKiogQHR5cGUgeyhnZXR0ZXI6IEdldHRlciwgbmFtZTogYFxcJCR7aW1wb3J0KCcuJykuVHlwZWRBcnJheU5hbWV9YCkgPT4gdm9pZH0gKi8gZnVuY3Rpb24gKGdldHRlciwgbmFtZSkge1xuXHRcdFx0aWYgKCFmb3VuZCkge1xuXHRcdFx0XHR0cnkge1xuXHRcdFx0XHRcdC8vIEB0cy1leHBlY3QtZXJyb3IgYSB0aHJvdyBpcyBmaW5lIGhlcmVcblx0XHRcdFx0XHRnZXR0ZXIodmFsdWUpO1xuXHRcdFx0XHRcdGZvdW5kID0gLyoqIEB0eXBlIHtpbXBvcnQoJy4nKS5UeXBlZEFycmF5TmFtZX0gKi8gKCRzbGljZShuYW1lLCAxKSk7XG5cdFx0XHRcdH0gY2F0Y2ggKGUpIHsgLyoqLyB9XG5cdFx0XHR9XG5cdFx0fVxuXHQpO1xuXHRyZXR1cm4gZm91bmQ7XG59O1xuXG4vKiogQHR5cGUge2ltcG9ydCgnLicpfSAqL1xubW9kdWxlLmV4cG9ydHMgPSBmdW5jdGlvbiB3aGljaFR5cGVkQXJyYXkodmFsdWUpIHtcblx0aWYgKCF2YWx1ZSB8fCB0eXBlb2YgdmFsdWUgIT09ICdvYmplY3QnKSB7IHJldHVybiBmYWxzZTsgfVxuXHRpZiAoIWhhc1RvU3RyaW5nVGFnKSB7XG5cdFx0LyoqIEB0eXBlIHtzdHJpbmd9ICovXG5cdFx0dmFyIHRhZyA9ICRzbGljZSgkdG9TdHJpbmcodmFsdWUpLCA4LCAtMSk7XG5cdFx0aWYgKCRpbmRleE9mKHR5cGVkQXJyYXlzLCB0YWcpID4gLTEpIHtcblx0XHRcdHJldHVybiB0YWc7XG5cdFx0fVxuXHRcdGlmICh0YWcgIT09ICdPYmplY3QnKSB7XG5cdFx0XHRyZXR1cm4gZmFsc2U7XG5cdFx0fVxuXHRcdC8vIG5vZGUgPCAwLjYgaGl0cyBoZXJlIG9uIHJlYWwgVHlwZWQgQXJyYXlzXG5cdFx0cmV0dXJuIHRyeVNsaWNlcyh2YWx1ZSk7XG5cdH1cblx0aWYgKCFnT1BEKSB7IHJldHVybiBudWxsOyB9IC8vIHVua25vd24gZW5naW5lXG5cdHJldHVybiB0cnlUeXBlZEFycmF5cyh2YWx1ZSk7XG59O1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/which-typed-array/index.js\n");

/***/ })

};
;