from fastapi import APIRouter, Depends, HTTPException, UploadFile, File
from sqlalchemy.orm import Session
from pydantic import BaseModel
from typing import List, Dict, Any
import PyPDF2
import io
import re
from datetime import datetime

from db import get_db, User
from routers.auth import get_current_user

router = APIRouter()

# Pydantic models
class ResumeAnalysis(BaseModel):
    name: str
    email: str
    phone: str
    skills: List[str]
    experience_years: int
    education: List[Dict[str, str]]
    work_experience: List[Dict[str, str]]
    score: float
    recommendations: List[str]

class ContractAnalysis(BaseModel):
    contract_type: str
    key_terms: List[str]
    risks: List[str]
    recommendations: List[str]
    compliance_score: float
    summary: str

@router.post("/analyze-resume", response_model=ResumeAnalysis)
async def analyze_resume(
    file: UploadFile = File(...),
    current_user: User = Depends(get_current_user)
):
    """Analyze a resume/CV file"""
    try:
        # Read file content
        content = await file.read()
        
        if file.content_type == "application/pdf":
            text = extract_text_from_pdf(content)
        elif file.content_type == "text/plain":
            text = content.decode('utf-8')
        else:
            raise HTTPException(status_code=400, detail="Unsupported file type")
        
        # Analyze resume
        analysis = analyze_resume_text(text)
        
        return analysis
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Resume analysis failed: {str(e)}")

def extract_text_from_pdf(content: bytes) -> str:
    """Extract text from PDF content"""
    try:
        pdf_reader = PyPDF2.PdfReader(io.BytesIO(content))
        text = ""
        for page in pdf_reader.pages:
            text += page.extract_text()
        return text
    except Exception as e:
        raise HTTPException(status_code=400, detail="Failed to extract text from PDF")

def analyze_resume_text(text: str) -> ResumeAnalysis:
    """Analyze resume text and extract key information"""
    
    # Extract name (first line or after "Name:")
    name_match = re.search(r'^([A-Z][a-z]+ [A-Z][a-z]+)', text, re.MULTILINE)
    name = name_match.group(1) if name_match else "Not found"
    
    # Extract email
    email_match = re.search(r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b', text)
    email = email_match.group(0) if email_match else "Not found"
    
    # Extract phone
    phone_match = re.search(r'(\+?\d{1,3}[-.\s]?)?\(?\d{3}\)?[-.\s]?\d{3}[-.\s]?\d{4}', text)
    phone = phone_match.group(0) if phone_match else "Not found"
    
    # Extract skills
    skills = extract_skills(text)
    
    # Calculate experience years
    experience_years = calculate_experience_years(text)
    
    # Extract education
    education = extract_education(text)
    
    # Extract work experience
    work_experience = extract_work_experience(text)
    
    # Calculate score
    score = calculate_resume_score(skills, experience_years, education, work_experience)
    
    # Generate recommendations
    recommendations = generate_resume_recommendations(skills, experience_years, education)
    
    return ResumeAnalysis(
        name=name,
        email=email,
        phone=phone,
        skills=skills,
        experience_years=experience_years,
        education=education,
        work_experience=work_experience,
        score=score,
        recommendations=recommendations
    )

def extract_skills(text: str) -> List[str]:
    """Extract skills from resume text"""
    # Common technical skills
    tech_skills = [
        'Python', 'JavaScript', 'Java', 'C++', 'React', 'Node.js', 'SQL', 'MongoDB',
        'AWS', 'Docker', 'Kubernetes', 'Git', 'Machine Learning', 'AI', 'Blockchain',
        'HTML', 'CSS', 'TypeScript', 'Vue.js', 'Angular', 'Django', 'Flask', 'FastAPI'
    ]
    
    found_skills = []
    text_upper = text.upper()
    
    for skill in tech_skills:
        if skill.upper() in text_upper:
            found_skills.append(skill)
    
    # Extract from skills section
    skills_section = re.search(r'SKILLS?:?\s*(.+?)(?:\n\n|\n[A-Z])', text, re.IGNORECASE | re.DOTALL)
    if skills_section:
        skills_text = skills_section.group(1)
        additional_skills = re.findall(r'\b[A-Za-z][A-Za-z\s.+#-]+\b', skills_text)
        found_skills.extend([s.strip() for s in additional_skills if len(s.strip()) > 2])
    
    return list(set(found_skills))[:15]  # Limit to 15 skills

def calculate_experience_years(text: str) -> int:
    """Calculate years of experience from resume"""
    # Look for year patterns
    years = re.findall(r'\b(19|20)\d{2}\b', text)
    
    if len(years) >= 2:
        years = [int(y) for y in years]
        return max(0, max(years) - min(years))
    
    # Look for explicit experience mentions
    exp_match = re.search(r'(\d+)\+?\s*years?\s*(?:of\s*)?experience', text, re.IGNORECASE)
    if exp_match:
        return int(exp_match.group(1))
    
    return 0

def extract_education(text: str) -> List[Dict[str, str]]:
    """Extract education information"""
    education = []
    
    # Common degree patterns
    degree_patterns = [
        r'(Bachelor|Master|PhD|MBA|B\.S\.|M\.S\.|B\.A\.|M\.A\.)\s+(?:of\s+|in\s+)?([A-Za-z\s]+)',
        r'(Associate|Diploma)\s+(?:of\s+|in\s+)?([A-Za-z\s]+)'
    ]
    
    for pattern in degree_patterns:
        matches = re.findall(pattern, text, re.IGNORECASE)
        for match in matches:
            education.append({
                "degree": match[0],
                "field": match[1].strip(),
                "year": "Not specified"
            })
    
    return education[:5]  # Limit to 5 entries

def extract_work_experience(text: str) -> List[Dict[str, str]]:
    """Extract work experience"""
    experience = []
    
    # Look for job titles and companies
    job_patterns = [
        r'(Software Engineer|Developer|Manager|Analyst|Consultant|Director|Senior|Junior)\s+(?:at\s+)?([A-Za-z\s&.]+)',
        r'([A-Za-z\s]+)\s+at\s+([A-Za-z\s&.]+)'
    ]
    
    for pattern in job_patterns:
        matches = re.findall(pattern, text, re.IGNORECASE)
        for match in matches:
            if len(match[1].strip()) > 2:  # Valid company name
                experience.append({
                    "title": match[0].strip(),
                    "company": match[1].strip(),
                    "duration": "Not specified"
                })
    
    return experience[:10]  # Limit to 10 entries

def calculate_resume_score(skills: List[str], experience_years: int, education: List[Dict], work_experience: List[Dict]) -> float:
    """Calculate overall resume score"""
    score = 0.0
    
    # Skills score (0-30 points)
    score += min(30, len(skills) * 2)
    
    # Experience score (0-40 points)
    score += min(40, experience_years * 4)
    
    # Education score (0-20 points)
    score += min(20, len(education) * 10)
    
    # Work experience score (0-10 points)
    score += min(10, len(work_experience) * 2)
    
    return round(score, 1)

def generate_resume_recommendations(skills: List[str], experience_years: int, education: List[Dict]) -> List[str]:
    """Generate recommendations for resume improvement"""
    recommendations = []
    
    if len(skills) < 5:
        recommendations.append("Add more technical skills to strengthen your profile")
    
    if experience_years < 2:
        recommendations.append("Consider highlighting internships, projects, or volunteer work")
    
    if len(education) == 0:
        recommendations.append("Include your educational background")
    
    if 'Python' not in skills and 'JavaScript' not in skills:
        recommendations.append("Consider learning popular programming languages like Python or JavaScript")
    
    recommendations.append("Quantify your achievements with specific metrics and numbers")
    recommendations.append("Tailor your resume to match the job description")
    
    return recommendations

@router.post("/analyze-contract", response_model=ContractAnalysis)
async def analyze_contract(
    file: UploadFile = File(...),
    current_user: User = Depends(get_current_user)
):
    """Analyze a legal contract"""
    try:
        content = await file.read()
        
        if file.content_type == "application/pdf":
            text = extract_text_from_pdf(content)
        elif file.content_type == "text/plain":
            text = content.decode('utf-8')
        else:
            raise HTTPException(status_code=400, detail="Unsupported file type")
        
        analysis = analyze_contract_text(text)
        return analysis
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Contract analysis failed: {str(e)}")

def analyze_contract_text(text: str) -> ContractAnalysis:
    """Analyze contract text for key terms and risks"""
    
    # Determine contract type
    contract_type = determine_contract_type(text)
    
    # Extract key terms
    key_terms = extract_key_terms(text)
    
    # Identify risks
    risks = identify_contract_risks(text)
    
    # Generate recommendations
    recommendations = generate_contract_recommendations(text, risks)
    
    # Calculate compliance score
    compliance_score = calculate_compliance_score(text, risks)
    
    # Generate summary
    summary = generate_contract_summary(text, contract_type)
    
    return ContractAnalysis(
        contract_type=contract_type,
        key_terms=key_terms,
        risks=risks,
        recommendations=recommendations,
        compliance_score=compliance_score,
        summary=summary
    )

def determine_contract_type(text: str) -> str:
    """Determine the type of contract"""
    text_lower = text.lower()
    
    if 'employment' in text_lower or 'employee' in text_lower:
        return "Employment Agreement"
    elif 'service' in text_lower and 'agreement' in text_lower:
        return "Service Agreement"
    elif 'nda' in text_lower or 'non-disclosure' in text_lower:
        return "Non-Disclosure Agreement"
    elif 'lease' in text_lower or 'rental' in text_lower:
        return "Lease Agreement"
    elif 'purchase' in text_lower or 'sale' in text_lower:
        return "Purchase Agreement"
    else:
        return "General Contract"

def extract_key_terms(text: str) -> List[str]:
    """Extract key contractual terms"""
    key_terms = []
    
    # Payment terms
    payment_matches = re.findall(r'\$[\d,]+(?:\.\d{2})?', text)
    if payment_matches:
        key_terms.extend([f"Payment amount: {amount}" for amount in payment_matches[:3]])
    
    # Duration/term
    duration_matches = re.findall(r'(\d+)\s*(year|month|day)s?', text, re.IGNORECASE)
    if duration_matches:
        key_terms.extend([f"Duration: {num} {period}s" for num, period in duration_matches[:2]])
    
    # Termination clauses
    if 'termination' in text.lower():
        key_terms.append("Contains termination clauses")
    
    # Confidentiality
    if 'confidential' in text.lower():
        key_terms.append("Contains confidentiality provisions")
    
    return key_terms

def identify_contract_risks(text: str) -> List[str]:
    """Identify potential risks in the contract"""
    risks = []
    text_lower = text.lower()
    
    if 'penalty' in text_lower or 'liquidated damages' in text_lower:
        risks.append("Contains penalty clauses")
    
    if 'indemnify' in text_lower or 'indemnification' in text_lower:
        risks.append("Contains indemnification clauses")
    
    if 'exclusive' in text_lower and 'jurisdiction' in text_lower:
        risks.append("Exclusive jurisdiction clause may limit legal options")
    
    if 'automatic renewal' in text_lower:
        risks.append("Automatic renewal clause present")
    
    if 'non-compete' in text_lower:
        risks.append("Non-compete restrictions apply")
    
    return risks

def generate_contract_recommendations(text: str, risks: List[str]) -> List[str]:
    """Generate recommendations for contract review"""
    recommendations = []
    
    if risks:
        recommendations.append("Review identified risk clauses carefully with legal counsel")
    
    recommendations.append("Ensure all terms are clearly defined and unambiguous")
    recommendations.append("Verify that termination procedures are fair and reasonable")
    recommendations.append("Check that payment terms and schedules are acceptable")
    
    return recommendations

def calculate_compliance_score(text: str, risks: List[str]) -> float:
    """Calculate a compliance/safety score for the contract"""
    base_score = 85.0
    
    # Deduct points for each risk
    risk_penalty = len(risks) * 5
    
    # Deduct points for missing standard clauses
    if 'force majeure' not in text.lower():
        risk_penalty += 3
    
    if 'governing law' not in text.lower():
        risk_penalty += 3
    
    return max(0.0, base_score - risk_penalty)

def generate_contract_summary(text: str, contract_type: str) -> str:
    """Generate a summary of the contract"""
    word_count = len(text.split())
    
    return f"This {contract_type} contains approximately {word_count} words. " \
           f"The document appears to be {'comprehensive' if word_count > 1000 else 'concise'} " \
           f"and covers standard contractual provisions. Key areas include terms of service, " \
           f"payment obligations, and termination procedures."
