/* ==================== BASE STYLES ====================
   Global base styles and resets
   ================================================== */

/* CSS Reset */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

*::before,
*::after {
    box-sizing: border-box;
}

/* HTML & Body */
html {
    font-size: 16px;
    line-height: 1.5;
    -webkit-text-size-adjust: 100%;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

body {
    background: var(--bg-primary);
    color: var(--text-primary);
    font-family: var(--font-primary);
    font-weight: var(--font-normal);
    overflow-x: hidden;
    position: relative;
    min-height: 100vh;
}

/* Animated Background */
body::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: 
        radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.3) 0%, transparent 50%),
        radial-gradient(circle at 40% 40%, rgba(120, 219, 255, 0.2) 0%, transparent 50%);
    z-index: -1;
    animation: backgroundShift 20s ease-in-out infinite;
}

@keyframes backgroundShift {
    0%, 100% { 
        transform: scale(1) rotate(0deg);
        opacity: 0.3;
    }
    50% { 
        transform: scale(1.1) rotate(180deg);
        opacity: 0.5;
    }
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
    font-family: var(--font-secondary);
    font-weight: var(--font-bold);
    line-height: 1.2;
    margin-bottom: var(--space-md);
}

h1 { font-size: var(--text-5xl); }
h2 { font-size: var(--text-4xl); }
h3 { font-size: var(--text-3xl); }
h4 { font-size: var(--text-2xl); }
h5 { font-size: var(--text-xl); }
h6 { font-size: var(--text-lg); }

p {
    margin-bottom: var(--space-md);
    line-height: 1.6;
}

/* Links */
a {
    color: var(--text-accent);
    text-decoration: none;
    transition: color var(--transition-fast);
}

a:hover {
    color: var(--neon-cyan);
    text-shadow: 0 0 10px currentColor;
}

/* Lists */
ul, ol {
    margin-bottom: var(--space-md);
    padding-left: var(--space-lg);
}

li {
    margin-bottom: var(--space-xs);
}

/* Images */
img {
    max-width: 100%;
    height: auto;
    display: block;
}

/* Form Elements */
input, textarea, select, button {
    font-family: inherit;
    font-size: inherit;
    line-height: inherit;
}

/* Focus Styles */
:focus {
    outline: 2px solid var(--neon-blue);
    outline-offset: 2px;
}

:focus:not(:focus-visible) {
    outline: none;
}

/* Selection */
::selection {
    background: var(--neon-blue);
    color: var(--luxury-black);
}

::-moz-selection {
    background: var(--neon-blue);
    color: var(--luxury-black);
}

/* Scrollbar Styling */
::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

::-webkit-scrollbar-track {
    background: var(--bg-secondary);
    border-radius: var(--radius-md);
}

::-webkit-scrollbar-thumb {
    background: var(--glass-bg);
    border-radius: var(--radius-md);
    border: 1px solid var(--glass-border);
}

::-webkit-scrollbar-thumb:hover {
    background: var(--neon-blue);
    box-shadow: var(--shadow-neon);
}

/* Firefox Scrollbar */
* {
    scrollbar-width: thin;
    scrollbar-color: var(--glass-bg) var(--bg-secondary);
}

/* Utility Classes */
.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}

.not-sr-only {
    position: static;
    width: auto;
    height: auto;
    padding: 0;
    margin: 0;
    overflow: visible;
    clip: auto;
    white-space: normal;
}

/* Print Styles */
@media print {
    body {
        background: white;
        color: black;
    }
    
    body::before {
        display: none;
    }
    
    .no-print {
        display: none !important;
    }
}

/* Reduced Motion */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
    
    body::before {
        animation: none;
    }
}

/* High Contrast Mode */
@media (prefers-contrast: high) {
    :root {
        --glass-bg: rgba(255, 255, 255, 0.9);
        --glass-border: rgba(255, 255, 255, 1);
    }
}
