@import "tailwindcss";

/* Dark Theme Variables */
:root {
  --primary-50: #fef7ee;
  --primary-100: #fdedd3;
  --primary-200: #fbd7a5;
  --primary-300: #f8bb6d;
  --primary-400: #f59e0b;
  --primary-500: #d97706;
  --primary-600: #c2410c;
  --primary-700: #9a3412;
  --primary-800: #7c2d12;
  --primary-900: #451a03;

  --dark-50: #f8fafc;
  --dark-100: #f1f5f9;
  --dark-200: #e2e8f0;
  --dark-300: #cbd5e1;
  --dark-400: #94a3b8;
  --dark-500: #64748b;
  --dark-600: #475569;
  --dark-700: #334155;
  --dark-800: #1e293b;
  --dark-900: #0f172a;
}

body {
  background: #0f172a;
  color: #f1f5f9;
  font-family: 'Inter', -apple-system, BlinkMacSystem<PERSON><PERSON>, sans-serif;
  overflow-x: hidden;
}

/* Glass morphism effect */
.glass {
  background: rgba(15, 23, 42, 0.8);
  backdrop-filter: blur(16px);
  border: 1px solid rgba(217, 119, 6, 0.2);
}

/* Gold gradient */
.gold-gradient {
  background: linear-gradient(135deg, #f59e0b, #d97706, #b45309);
}

/* Scrollbar styling */
::-webkit-scrollbar {
  width: 6px;
}

::-webkit-scrollbar-track {
  background: rgba(15, 23, 42, 0.5);
}

::-webkit-scrollbar-thumb {
  background: rgba(217, 119, 6, 0.5);
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: rgba(217, 119, 6, 0.7);
}
