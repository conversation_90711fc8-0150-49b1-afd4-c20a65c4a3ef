{"version": 3, "sources": [], "sections": [{"offset": {"line": 14, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[turbopack]/shared/runtime-utils.ts"], "sourcesContent": ["/**\r\n * This file contains runtime types and functions that are shared between all\r\n * TurboPack ECMAScript runtimes.\r\n *\r\n * It will be prepended to the runtime code of each runtime.\r\n */\r\n\r\n/* eslint-disable @typescript-eslint/no-unused-vars */\r\n\r\n/// <reference path=\"./runtime-types.d.ts\" />\r\n\r\ntype EsmNamespaceObject = Record<string, any>;\r\n\r\n// @ts-ignore Defined in `dev-base.ts`\r\ndeclare function getOrInstantiateModuleFromParent<M>(\r\n  id: ModuleId,\r\n  sourceModule: M\r\n): M;\r\n\r\nconst REEXPORTED_OBJECTS = Symbol(\"reexported objects\");\r\n\r\ntype ModuleContextMap = Record<ModuleId, ModuleContextEntry>;\r\n\r\ninterface ModuleContextEntry {\r\n  id: () => ModuleId;\r\n  module: () => any;\r\n}\r\n\r\ninterface ModuleContext {\r\n  // require call\r\n  (moduleId: ModuleId): Exports | EsmNamespaceObject;\r\n\r\n  // async import call\r\n  import(moduleId: ModuleId): Promise<Exports | EsmNamespaceObject>;\r\n\r\n  keys(): ModuleId[];\r\n\r\n  resolve(moduleId: ModuleId): ModuleId;\r\n}\r\n\r\ntype GetOrInstantiateModuleFromParent<M> = (\r\n  moduleId: ModuleId,\r\n  parentModule: M\r\n) => M;\r\n\r\ndeclare function getOrInstantiateRuntimeModule(moduleId: ModuleId, chunkPath: ChunkPath): Module;\r\n\r\nconst hasOwnProperty = Object.prototype.hasOwnProperty;\r\nconst toStringTag = typeof Symbol !== \"undefined\" && Symbol.toStringTag;\r\n\r\nfunction defineProp(\r\n  obj: any,\r\n  name: PropertyKey,\r\n  options: PropertyDescriptor & ThisType<any>\r\n) {\r\n  if (!hasOwnProperty.call(obj, name))\r\n    Object.defineProperty(obj, name, options);\r\n}\r\n\r\n/**\r\n * Adds the getters to the exports object.\r\n */\r\nfunction esm(\r\n  exports: Exports,\r\n  getters: Record<string, (() => any) | [() => any, (v: any) => void]>\r\n) {\r\n  defineProp(exports, \"__esModule\", { value: true });\r\n  if (toStringTag) defineProp(exports, toStringTag, { value: \"Module\" });\r\n  for (const key in getters) {\r\n    const item = getters[key];\r\n    if (Array.isArray(item)) {\r\n      defineProp(exports, key, {\r\n        get: item[0],\r\n        set: item[1],\r\n        enumerable: true,\r\n      });\r\n    } else {\r\n      defineProp(exports, key, { get: item, enumerable: true });\r\n    }\r\n  }\r\n  Object.seal(exports);\r\n}\r\n\r\n/**\r\n * Makes the module an ESM with exports\r\n */\r\nfunction esmExport(\r\n  module: Module,\r\n  exports: Exports,\r\n  getters: Record<string, () => any>\r\n) {\r\n  module.namespaceObject = module.exports;\r\n  esm(exports, getters);\r\n}\r\n\r\nfunction ensureDynamicExports(module: Module, exports: Exports) {\r\n  let reexportedObjects = module[REEXPORTED_OBJECTS];\r\n\r\n  if (!reexportedObjects) {\r\n    reexportedObjects = module[REEXPORTED_OBJECTS] = [];\r\n    module.exports = module.namespaceObject = new Proxy(exports, {\r\n      get(target, prop) {\r\n        if (\r\n          hasOwnProperty.call(target, prop) ||\r\n          prop === \"default\" ||\r\n          prop === \"__esModule\"\r\n        ) {\r\n          return Reflect.get(target, prop);\r\n        }\r\n        for (const obj of reexportedObjects!) {\r\n          const value = Reflect.get(obj, prop);\r\n          if (value !== undefined) return value;\r\n        }\r\n        return undefined;\r\n      },\r\n      ownKeys(target) {\r\n        const keys = Reflect.ownKeys(target);\r\n        for (const obj of reexportedObjects!) {\r\n          for (const key of Reflect.ownKeys(obj)) {\r\n            if (key !== \"default\" && !keys.includes(key)) keys.push(key);\r\n          }\r\n        }\r\n        return keys;\r\n      },\r\n    });\r\n  }\r\n}\r\n\r\n/**\r\n * Dynamically exports properties from an object\r\n */\r\nfunction dynamicExport(\r\n  module: Module,\r\n  exports: Exports,\r\n  object: Record<string, any>\r\n) {\r\n  ensureDynamicExports(module, exports);\r\n\r\n  if (typeof object === \"object\" && object !== null) {\r\n    module[REEXPORTED_OBJECTS]!.push(object);\r\n  }\r\n}\r\n\r\nfunction exportValue(module: Module, value: any) {\r\n  module.exports = value;\r\n}\r\n\r\nfunction exportNamespace(module: Module, namespace: any) {\r\n  module.exports = module.namespaceObject = namespace;\r\n}\r\n\r\nfunction createGetter(obj: Record<string | symbol, any>, key: string | symbol) {\r\n  return () => obj[key];\r\n}\r\n\r\n/**\r\n * @returns prototype of the object\r\n */\r\nconst getProto: (obj: any) => any = Object.getPrototypeOf\r\n  ? (obj) => Object.getPrototypeOf(obj)\r\n  : (obj) => obj.__proto__;\r\n\r\n/** Prototypes that are not expanded for exports */\r\nconst LEAF_PROTOTYPES = [null, getProto({}), getProto([]), getProto(getProto)];\r\n\r\n/**\r\n * @param raw\r\n * @param ns\r\n * @param allowExportDefault\r\n *   * `false`: will have the raw module as default export\r\n *   * `true`: will have the default property as default export\r\n */\r\nfunction interopEsm(\r\n  raw: Exports,\r\n  ns: EsmNamespaceObject,\r\n  allowExportDefault?: boolean\r\n) {\r\n  const getters: { [s: string]: () => any } = Object.create(null);\r\n  for (\r\n    let current = raw;\r\n    (typeof current === \"object\" || typeof current === \"function\") &&\r\n    !LEAF_PROTOTYPES.includes(current);\r\n    current = getProto(current)\r\n  ) {\r\n    for (const key of Object.getOwnPropertyNames(current)) {\r\n      getters[key] = createGetter(raw, key);\r\n    }\r\n  }\r\n\r\n  // this is not really correct\r\n  // we should set the `default` getter if the imported module is a `.cjs file`\r\n  if (!(allowExportDefault && \"default\" in getters)) {\r\n    getters[\"default\"] = () => raw;\r\n  }\r\n\r\n  esm(ns, getters);\r\n  return ns;\r\n}\r\n\r\nfunction createNS(raw: Module[\"exports\"]): EsmNamespaceObject {\r\n  if (typeof raw === \"function\") {\r\n    return function (this: any, ...args: any[]) {\r\n      return raw.apply(this, args);\r\n    };\r\n  } else {\r\n    return Object.create(null);\r\n  }\r\n}\r\n\r\nfunction esmImport(\r\n  sourceModule: Module,\r\n  id: ModuleId\r\n): Exclude<Module[\"namespaceObject\"], undefined> {\r\n  const module = getOrInstantiateModuleFromParent(id, sourceModule);\r\n  if (module.error) throw module.error;\r\n\r\n  // any ES module has to have `module.namespaceObject` defined.\r\n  if (module.namespaceObject) return module.namespaceObject;\r\n\r\n  // only ESM can be an async module, so we don't need to worry about exports being a promise here.\r\n  const raw = module.exports;\r\n  return (module.namespaceObject = interopEsm(\r\n    raw,\r\n    createNS(raw),\r\n    raw && (raw as any).__esModule\r\n  ));\r\n}\r\n\r\n// Add a simple runtime require so that environments without one can still pass\r\n// `typeof require` CommonJS checks so that exports are correctly registered.\r\nconst runtimeRequire =\r\n  // @ts-ignore\r\n  typeof require === \"function\"\r\n    // @ts-ignore\r\n    ? require\r\n    : function require() {\r\n        throw new Error(\"Unexpected use of runtime require\");\r\n      };\r\n\r\nfunction commonJsRequire(sourceModule: Module, id: ModuleId): Exports {\r\n  const module = getOrInstantiateModuleFromParent(id, sourceModule);\r\n  if (module.error) throw module.error;\r\n  return module.exports;\r\n}\r\n\r\n/**\r\n * `require.context` and require/import expression runtime.\r\n */\r\nfunction moduleContext(map: ModuleContextMap): ModuleContext {\r\n  function moduleContext(id: ModuleId): Exports {\r\n    if (hasOwnProperty.call(map, id)) {\r\n      return map[id].module();\r\n    }\r\n\r\n    const e = new Error(`Cannot find module '${id}'`);\r\n    (e as any).code = \"MODULE_NOT_FOUND\";\r\n    throw e;\r\n  }\r\n\r\n  moduleContext.keys = (): ModuleId[] => {\r\n    return Object.keys(map);\r\n  };\r\n\r\n  moduleContext.resolve = (id: ModuleId): ModuleId => {\r\n    if (hasOwnProperty.call(map, id)) {\r\n      return map[id].id();\r\n    }\r\n\r\n    const e = new Error(`Cannot find module '${id}'`);\r\n    (e as any).code = \"MODULE_NOT_FOUND\";\r\n    throw e;\r\n  };\r\n\r\n  moduleContext.import = async (id: ModuleId) => {\r\n    return await (moduleContext(id) as Promise<Exports>);\r\n  };\r\n\r\n  return moduleContext;\r\n}\r\n\r\n/**\r\n * Returns the path of a chunk defined by its data.\r\n */\r\nfunction getChunkPath(chunkData: ChunkData): ChunkPath {\r\n  return typeof chunkData === \"string\" ? chunkData : chunkData.path;\r\n}\r\n\r\nfunction isPromise<T = any>(maybePromise: any): maybePromise is Promise<T> {\r\n  return (\r\n    maybePromise != null &&\r\n    typeof maybePromise === \"object\" &&\r\n    \"then\" in maybePromise &&\r\n    typeof maybePromise.then === \"function\"\r\n  );\r\n}\r\n\r\nfunction isAsyncModuleExt<T extends {}>(obj: T): obj is AsyncModuleExt & T {\r\n  return turbopackQueues in obj;\r\n}\r\n\r\nfunction createPromise<T>() {\r\n  let resolve: (value: T | PromiseLike<T>) => void;\r\n  let reject: (reason?: any) => void;\r\n\r\n  const promise = new Promise<T>((res, rej) => {\r\n    reject = rej;\r\n    resolve = res;\r\n  });\r\n\r\n  return {\r\n    promise,\r\n    resolve: resolve!,\r\n    reject: reject!,\r\n  };\r\n}\r\n\r\n// everything below is adapted from webpack\r\n// https://github.com/webpack/webpack/blob/6be4065ade1e252c1d8dcba4af0f43e32af1bdc1/lib/runtime/AsyncModuleRuntimeModule.js#L13\r\n\r\nconst turbopackQueues = Symbol(\"turbopack queues\");\r\nconst turbopackExports = Symbol(\"turbopack exports\");\r\nconst turbopackError = Symbol(\"turbopack error\");\r\n\r\nconst enum QueueStatus {\r\n  Unknown = -1,\r\n  Unresolved = 0,\r\n  Resolved = 1,\r\n}\r\n\r\ntype AsyncQueueFn = (() => void) & { queueCount: number };\r\ntype AsyncQueue = AsyncQueueFn[] & {\r\n  status: QueueStatus;\r\n};\r\n\r\nfunction resolveQueue(queue?: AsyncQueue) {\r\n  if (queue && queue.status !== QueueStatus.Resolved) {\r\n    queue.status = QueueStatus.Resolved;\r\n    queue.forEach((fn) => fn.queueCount--);\r\n    queue.forEach((fn) => (fn.queueCount-- ? fn.queueCount++ : fn()));\r\n  }\r\n}\r\n\r\ntype Dep = Exports | AsyncModulePromise | Promise<Exports>;\r\n\r\ntype AsyncModuleExt = {\r\n  [turbopackQueues]: (fn: (queue: AsyncQueue) => void) => void;\r\n  [turbopackExports]: Exports;\r\n  [turbopackError]?: any;\r\n};\r\n\r\ntype AsyncModulePromise<T = Exports> = Promise<T> & AsyncModuleExt;\r\n\r\nfunction wrapDeps(deps: Dep[]): AsyncModuleExt[] {\r\n  return deps.map((dep): AsyncModuleExt => {\r\n    if (dep !== null && typeof dep === \"object\") {\r\n      if (isAsyncModuleExt(dep)) return dep;\r\n      if (isPromise(dep)) {\r\n        const queue: AsyncQueue = Object.assign([], {\r\n          status: QueueStatus.Unresolved,\r\n        });\r\n\r\n        const obj: AsyncModuleExt = {\r\n          [turbopackExports]: {},\r\n          [turbopackQueues]: (fn: (queue: AsyncQueue) => void) => fn(queue),\r\n        };\r\n\r\n        dep.then(\r\n          (res) => {\r\n            obj[turbopackExports] = res;\r\n            resolveQueue(queue);\r\n          },\r\n          (err) => {\r\n            obj[turbopackError] = err;\r\n            resolveQueue(queue);\r\n          }\r\n        );\r\n\r\n        return obj;\r\n      }\r\n    }\r\n\r\n    return {\r\n      [turbopackExports]: dep,\r\n      [turbopackQueues]: () => {},\r\n    };\r\n  });\r\n}\r\n\r\nfunction asyncModule(\r\n  module: Module,\r\n  body: (\r\n    handleAsyncDependencies: (\r\n      deps: Dep[]\r\n    ) => Exports[] | Promise<() => Exports[]>,\r\n    asyncResult: (err?: any) => void\r\n  ) => void,\r\n  hasAwait: boolean\r\n) {\r\n  const queue: AsyncQueue | undefined = hasAwait\r\n    ? Object.assign([], { status: QueueStatus.Unknown })\r\n    : undefined;\r\n\r\n  const depQueues: Set<AsyncQueue> = new Set();\r\n\r\n  const { resolve, reject, promise: rawPromise } = createPromise<Exports>();\r\n\r\n  const promise: AsyncModulePromise = Object.assign(rawPromise, {\r\n    [turbopackExports]: module.exports,\r\n    [turbopackQueues]: (fn) => {\r\n      queue && fn(queue);\r\n      depQueues.forEach(fn);\r\n      promise[\"catch\"](() => {});\r\n    },\r\n  } satisfies AsyncModuleExt);\r\n\r\n  const attributes: PropertyDescriptor = {\r\n    get(): any {\r\n      return promise;\r\n    },\r\n    set(v: any) {\r\n      // Calling `esmExport` leads to this.\r\n      if (v !== promise) {\r\n        promise[turbopackExports] = v;\r\n      }\r\n    },\r\n  };\r\n\r\n  Object.defineProperty(module, \"exports\", attributes);\r\n  Object.defineProperty(module, \"namespaceObject\", attributes);\r\n\r\n  function handleAsyncDependencies(deps: Dep[]) {\r\n    const currentDeps = wrapDeps(deps);\r\n\r\n    const getResult = () =>\r\n      currentDeps.map((d) => {\r\n        if (d[turbopackError]) throw d[turbopackError];\r\n        return d[turbopackExports];\r\n      });\r\n\r\n    const { promise, resolve } = createPromise<() => Exports[]>();\r\n\r\n    const fn: AsyncQueueFn = Object.assign(() => resolve(getResult), {\r\n      queueCount: 0,\r\n    });\r\n\r\n    function fnQueue(q: AsyncQueue) {\r\n      if (q !== queue && !depQueues.has(q)) {\r\n        depQueues.add(q);\r\n        if (q && q.status === QueueStatus.Unresolved) {\r\n          fn.queueCount++;\r\n          q.push(fn);\r\n        }\r\n      }\r\n    }\r\n\r\n    currentDeps.map((dep) => dep[turbopackQueues](fnQueue));\r\n\r\n    return fn.queueCount ? promise : getResult();\r\n  }\r\n\r\n  function asyncResult(err?: any) {\r\n    if (err) {\r\n      reject((promise[turbopackError] = err));\r\n    } else {\r\n      resolve(promise[turbopackExports]);\r\n    }\r\n\r\n    resolveQueue(queue);\r\n  }\r\n\r\n  body(handleAsyncDependencies, asyncResult);\r\n\r\n  if (queue && queue.status === QueueStatus.Unknown) {\r\n    queue.status = QueueStatus.Unresolved;\r\n  }\r\n}\r\n\r\n/**\r\n * A pseudo \"fake\" URL object to resolve to its relative path.\r\n *\r\n * When UrlRewriteBehavior is set to relative, calls to the `new URL()` will construct url without base using this\r\n * runtime function to generate context-agnostic urls between different rendering context, i.e ssr / client to avoid\r\n * hydration mismatch.\r\n *\r\n * This is based on webpack's existing implementation:\r\n * https://github.com/webpack/webpack/blob/87660921808566ef3b8796f8df61bd79fc026108/lib/runtime/RelativeUrlRuntimeModule.js\r\n */\r\nconst relativeURL = function relativeURL(this: any, inputUrl: string) {\r\n  const realUrl = new URL(inputUrl, \"x:/\");\r\n  const values: Record<string, any> = {};\r\n  for (const key in realUrl) values[key] = (realUrl as any)[key];\r\n  values.href = inputUrl;\r\n  values.pathname = inputUrl.replace(/[?#].*/, \"\");\r\n  values.origin = values.protocol = \"\";\r\n  values.toString = values.toJSON = (..._args: Array<any>) => inputUrl;\r\n  for (const key in values)\r\n    Object.defineProperty(this, key, {\r\n      enumerable: true,\r\n      configurable: true,\r\n      value: values[key],\r\n    });\r\n};\r\n\r\nrelativeURL.prototype = URL.prototype;\r\n\r\n/**\r\n * Utility function to ensure all variants of an enum are handled.\r\n */\r\nfunction invariant(never: never, computeMessage: (arg: any) => string): never {\r\n  throw new Error(`Invariant: ${computeMessage(never)}`);\r\n}\r\n\r\n/**\r\n * A stub function to make `require` available but non-functional in ESM.\r\n */\r\nfunction requireStub(_moduleId: ModuleId): never {\r\n  throw new Error(\"dynamic usage of require is not supported\");\r\n}\r\n"], "names": [], "mappings": "AAAA;;;;;CAKC,GAED,oDAAoD,GAEpD,6CAA6C;AAU7C,MAAM,qBAAqB,OAAO;AA4BlC,MAAM,iBAAiB,OAAO,SAAS,CAAC,cAAc;AACtD,MAAM,cAAc,OAAO,WAAW,eAAe,OAAO,WAAW;AAEvE,SAAS,WACP,GAAQ,EACR,IAAiB,EACjB,OAA2C;IAE3C,IAAI,CAAC,eAAe,IAAI,CAAC,KAAK,OAC5B,OAAO,cAAc,CAAC,KAAK,MAAM;AACrC;AAEA;;CAEC,GACD,SAAS,IACP,OAAgB,EAChB,OAAoE;IAEpE,WAAW,SAAS,cAAc;QAAE,OAAO;IAAK;IAChD,IAAI,aAAa,WAAW,SAAS,aAAa;QAAE,OAAO;IAAS;IACpE,IAAK,MAAM,OAAO,QAAS;QACzB,MAAM,OAAO,OAAO,CAAC,IAAI;QACzB,IAAI,MAAM,OAAO,CAAC,OAAO;YACvB,WAAW,SAAS,KAAK;gBACvB,KAAK,IAAI,CAAC,EAAE;gBACZ,KAAK,IAAI,CAAC,EAAE;gBACZ,YAAY;YACd;QACF,OAAO;YACL,WAAW,SAAS,KAAK;gBAAE,KAAK;gBAAM,YAAY;YAAK;QACzD;IACF;IACA,OAAO,IAAI,CAAC;AACd;AAEA;;CAEC,GACD,SAAS,UACP,MAAc,EACd,OAAgB,EAChB,OAAkC;IAElC,OAAO,eAAe,GAAG,OAAO,OAAO;IACvC,IAAI,SAAS;AACf;AAEA,SAAS,qBAAqB,MAAc,EAAE,OAAgB;IAC5D,IAAI,oBAAoB,MAAM,CAAC,mBAAmB;IAElD,IAAI,CAAC,mBAAmB;QACtB,oBAAoB,MAAM,CAAC,mBAAmB,GAAG,EAAE;QACnD,OAAO,OAAO,GAAG,OAAO,eAAe,GAAG,IAAI,MAAM,SAAS;YAC3D,KAAI,MAAM,EAAE,IAAI;gBACd,IACE,eAAe,IAAI,CAAC,QAAQ,SAC5B,SAAS,aACT,SAAS,cACT;oBACA,OAAO,QAAQ,GAAG,CAAC,QAAQ;gBAC7B;gBACA,KAAK,MAAM,OAAO,kBAAoB;oBACpC,MAAM,QAAQ,QAAQ,GAAG,CAAC,KAAK;oBAC/B,IAAI,UAAU,WAAW,OAAO;gBAClC;gBACA,OAAO;YACT;YACA,SAAQ,MAAM;gBACZ,MAAM,OAAO,QAAQ,OAAO,CAAC;gBAC7B,KAAK,MAAM,OAAO,kBAAoB;oBACpC,KAAK,MAAM,OAAO,QAAQ,OAAO,CAAC,KAAM;wBACtC,IAAI,QAAQ,aAAa,CAAC,KAAK,QAAQ,CAAC,MAAM,KAAK,IAAI,CAAC;oBAC1D;gBACF;gBACA,OAAO;YACT;QACF;IACF;AACF;AAEA;;CAEC,GACD,SAAS,cACP,MAAc,EACd,OAAgB,EAChB,MAA2B;IAE3B,qBAAqB,QAAQ;IAE7B,IAAI,OAAO,WAAW,YAAY,WAAW,MAAM;QACjD,MAAM,CAAC,mBAAmB,CAAE,IAAI,CAAC;IACnC;AACF;AAEA,SAAS,YAAY,MAAc,EAAE,KAAU;IAC7C,OAAO,OAAO,GAAG;AACnB;AAEA,SAAS,gBAAgB,MAAc,EAAE,SAAc;IACrD,OAAO,OAAO,GAAG,OAAO,eAAe,GAAG;AAC5C;AAEA,SAAS,aAAa,GAAiC,EAAE,GAAoB;IAC3E,OAAO,IAAM,GAAG,CAAC,IAAI;AACvB;AAEA;;CAEC,GACD,MAAM,WAA8B,OAAO,cAAc,GACrD,CAAC,MAAQ,OAAO,cAAc,CAAC,OAC/B,CAAC,MAAQ,IAAI,SAAS;AAE1B,iDAAiD,GACjD,MAAM,kBAAkB;IAAC;IAAM,SAAS,CAAC;IAAI,SAAS,EAAE;IAAG,SAAS;CAAU;AAE9E;;;;;;CAMC,GACD,SAAS,WACP,GAAY,EACZ,EAAsB,EACtB,kBAA4B;IAE5B,MAAM,UAAsC,OAAO,MAAM,CAAC;IAC1D,IACE,IAAI,UAAU,KACd,CAAC,OAAO,YAAY,YAAY,OAAO,YAAY,UAAU,KAC7D,CAAC,gBAAgB,QAAQ,CAAC,UAC1B,UAAU,SAAS,SACnB;QACA,KAAK,MAAM,OAAO,OAAO,mBAAmB,CAAC,SAAU;YACrD,OAAO,CAAC,IAAI,GAAG,aAAa,KAAK;QACnC;IACF;IAEA,6BAA6B;IAC7B,6EAA6E;IAC7E,IAAI,CAAC,CAAC,sBAAsB,aAAa,OAAO,GAAG;QACjD,OAAO,CAAC,UAAU,GAAG,IAAM;IAC7B;IAEA,IAAI,IAAI;IACR,OAAO;AACT;AAEA,SAAS,SAAS,GAAsB;IACtC,IAAI,OAAO,QAAQ,YAAY;QAC7B,OAAO,SAAqB,GAAG,IAAW;YACxC,OAAO,IAAI,KAAK,CAAC,IAAI,EAAE;QACzB;IACF,OAAO;QACL,OAAO,OAAO,MAAM,CAAC;IACvB;AACF;AAEA,SAAS,UACP,YAAoB,EACpB,EAAY;IAEZ,MAAM,SAAS,iCAAiC,IAAI;IACpD,IAAI,OAAO,KAAK,EAAE,MAAM,OAAO,KAAK;IAEpC,8DAA8D;IAC9D,IAAI,OAAO,eAAe,EAAE,OAAO,OAAO,eAAe;IAEzD,iGAAiG;IACjG,MAAM,MAAM,OAAO,OAAO;IAC1B,OAAQ,OAAO,eAAe,GAAG,WAC/B,KACA,SAAS,MACT,OAAO,AAAC,IAAY,UAAU;AAElC;AAEA,+EAA+E;AAC/E,6EAA6E;AAC7E,MAAM,iBACJ,aAAa;AACb,OAAO,YAAY,aAEf,UACA,SAAS;IACP,MAAM,IAAI,MAAM;AAClB;AAEN,SAAS,gBAAgB,YAAoB,EAAE,EAAY;IACzD,MAAM,SAAS,iCAAiC,IAAI;IACpD,IAAI,OAAO,KAAK,EAAE,MAAM,OAAO,KAAK;IACpC,OAAO,OAAO,OAAO;AACvB;AAEA;;CAEC,GACD,SAAS,cAAc,GAAqB;IAC1C,SAAS,cAAc,EAAY;QACjC,IAAI,eAAe,IAAI,CAAC,KAAK,KAAK;YAChC,OAAO,GAAG,CAAC,GAAG,CAAC,MAAM;QACvB;QAEA,MAAM,IAAI,IAAI,MAAM,CAAC,oBAAoB,EAAE,GAAG,CAAC,CAAC;QAC/C,EAAU,IAAI,GAAG;QAClB,MAAM;IACR;IAEA,cAAc,IAAI,GAAG;QACnB,OAAO,OAAO,IAAI,CAAC;IACrB;IAEA,cAAc,OAAO,GAAG,CAAC;QACvB,IAAI,eAAe,IAAI,CAAC,KAAK,KAAK;YAChC,OAAO,GAAG,CAAC,GAAG,CAAC,EAAE;QACnB;QAEA,MAAM,IAAI,IAAI,MAAM,CAAC,oBAAoB,EAAE,GAAG,CAAC,CAAC;QAC/C,EAAU,IAAI,GAAG;QAClB,MAAM;IACR;IAEA,cAAc,MAAM,GAAG,OAAO;QAC5B,OAAO,MAAO,cAAc;IAC9B;IAEA,OAAO;AACT;AAEA;;CAEC,GACD,SAAS,aAAa,SAAoB;IACxC,OAAO,OAAO,cAAc,WAAW,YAAY,UAAU,IAAI;AACnE;AAEA,SAAS,UAAmB,YAAiB;IAC3C,OACE,gBAAgB,QAChB,OAAO,iBAAiB,YACxB,UAAU,gBACV,OAAO,aAAa,IAAI,KAAK;AAEjC;AAEA,SAAS,iBAA+B,GAAM;IAC5C,OAAO,mBAAmB;AAC5B;AAEA,SAAS;IACP,IAAI;IACJ,IAAI;IAEJ,MAAM,UAAU,IAAI,QAAW,CAAC,KAAK;QACnC,SAAS;QACT,UAAU;IACZ;IAEA,OAAO;QACL;QACA,SAAS;QACT,QAAQ;IACV;AACF;AAEA,2CAA2C;AAC3C,+HAA+H;AAE/H,MAAM,kBAAkB,OAAO;AAC/B,MAAM,mBAAmB,OAAO;AAChC,MAAM,iBAAiB,OAAO;AAa9B,SAAS,aAAa,KAAkB;IACtC,IAAI,SAAS,MAAM,MAAM,QAA2B;QAClD,MAAM,MAAM;QACZ,MAAM,OAAO,CAAC,CAAC,KAAO,GAAG,UAAU;QACnC,MAAM,OAAO,CAAC,CAAC,KAAQ,GAAG,UAAU,KAAK,GAAG,UAAU,KAAK;IAC7D;AACF;AAYA,SAAS,SAAS,IAAW;IAC3B,OAAO,KAAK,GAAG,CAAC,CAAC;QACf,IAAI,QAAQ,QAAQ,OAAO,QAAQ,UAAU;YAC3C,IAAI,iBAAiB,MAAM,OAAO;YAClC,IAAI,UAAU,MAAM;gBAClB,MAAM,QAAoB,OAAO,MAAM,CAAC,EAAE,EAAE;oBAC1C,MAAM;gBACR;gBAEA,MAAM,MAAsB;oBAC1B,CAAC,iBAAiB,EAAE,CAAC;oBACrB,CAAC,gBAAgB,EAAE,CAAC,KAAoC,GAAG;gBAC7D;gBAEA,IAAI,IAAI,CACN,CAAC;oBACC,GAAG,CAAC,iBAAiB,GAAG;oBACxB,aAAa;gBACf,GACA,CAAC;oBACC,GAAG,CAAC,eAAe,GAAG;oBACtB,aAAa;gBACf;gBAGF,OAAO;YACT;QACF;QAEA,OAAO;YACL,CAAC,iBAAiB,EAAE;YACpB,CAAC,gBAAgB,EAAE,KAAO;QAC5B;IACF;AACF;AAEA,SAAS,YACP,MAAc,EACd,IAKS,EACT,QAAiB;IAEjB,MAAM,QAAgC,WAClC,OAAO,MAAM,CAAC,EAAE,EAAE;QAAE,MAAM;IAAsB,KAChD;IAEJ,MAAM,YAA6B,IAAI;IAEvC,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,SAAS,UAAU,EAAE,GAAG;IAEjD,MAAM,UAA8B,OAAO,MAAM,CAAC,YAAY;QAC5D,CAAC,iBAAiB,EAAE,OAAO,OAAO;QAClC,CAAC,gBAAgB,EAAE,CAAC;YAClB,SAAS,GAAG;YACZ,UAAU,OAAO,CAAC;YAClB,OAAO,CAAC,QAAQ,CAAC,KAAO;QAC1B;IACF;IAEA,MAAM,aAAiC;QACrC;YACE,OAAO;QACT;QACA,KAAI,CAAM;YACR,qCAAqC;YACrC,IAAI,MAAM,SAAS;gBACjB,OAAO,CAAC,iBAAiB,GAAG;YAC9B;QACF;IACF;IAEA,OAAO,cAAc,CAAC,QAAQ,WAAW;IACzC,OAAO,cAAc,CAAC,QAAQ,mBAAmB;IAEjD,SAAS,wBAAwB,IAAW;QAC1C,MAAM,cAAc,SAAS;QAE7B,MAAM,YAAY,IAChB,YAAY,GAAG,CAAC,CAAC;gBACf,IAAI,CAAC,CAAC,eAAe,EAAE,MAAM,CAAC,CAAC,eAAe;gBAC9C,OAAO,CAAC,CAAC,iBAAiB;YAC5B;QAEF,MAAM,EAAE,OAAO,EAAE,OAAO,EAAE,GAAG;QAE7B,MAAM,KAAmB,OAAO,MAAM,CAAC,IAAM,QAAQ,YAAY;YAC/D,YAAY;QACd;QAEA,SAAS,QAAQ,CAAa;YAC5B,IAAI,MAAM,SAAS,CAAC,UAAU,GAAG,CAAC,IAAI;gBACpC,UAAU,GAAG,CAAC;gBACd,IAAI,KAAK,EAAE,MAAM,QAA6B;oBAC5C,GAAG,UAAU;oBACb,EAAE,IAAI,CAAC;gBACT;YACF;QACF;QAEA,YAAY,GAAG,CAAC,CAAC,MAAQ,GAAG,CAAC,gBAAgB,CAAC;QAE9C,OAAO,GAAG,UAAU,GAAG,UAAU;IACnC;IAEA,SAAS,YAAY,GAAS;QAC5B,IAAI,KAAK;YACP,OAAQ,OAAO,CAAC,eAAe,GAAG;QACpC,OAAO;YACL,QAAQ,OAAO,CAAC,iBAAiB;QACnC;QAEA,aAAa;IACf;IAEA,KAAK,yBAAyB;IAE9B,IAAI,SAAS,MAAM,MAAM,SAA0B;QACjD,MAAM,MAAM;IACd;AACF;AAEA;;;;;;;;;CASC,GACD,MAAM,cAAc,SAAS,YAAuB,QAAgB;IAClE,MAAM,UAAU,IAAI,IAAI,UAAU;IAClC,MAAM,SAA8B,CAAC;IACrC,IAAK,MAAM,OAAO,QAAS,MAAM,CAAC,IAAI,GAAG,AAAC,OAAe,CAAC,IAAI;IAC9D,OAAO,IAAI,GAAG;IACd,OAAO,QAAQ,GAAG,SAAS,OAAO,CAAC,UAAU;IAC7C,OAAO,MAAM,GAAG,OAAO,QAAQ,GAAG;IAClC,OAAO,QAAQ,GAAG,OAAO,MAAM,GAAG,CAAC,GAAG,QAAsB;IAC5D,IAAK,MAAM,OAAO,OAChB,OAAO,cAAc,CAAC,IAAI,EAAE,KAAK;QAC/B,YAAY;QACZ,cAAc;QACd,OAAO,MAAM,CAAC,IAAI;IACpB;AACJ;AAEA,YAAY,SAAS,GAAG,IAAI,SAAS;AAErC;;CAEC,GACD,SAAS,UAAU,KAAY,EAAE,cAAoC;IACnE,MAAM,IAAI,MAAM,CAAC,WAAW,EAAE,eAAe,QAAQ;AACvD;AAEA;;CAEC,GACD,SAAS,YAAY,SAAmB;IACtC,MAAM,IAAI,MAAM;AAClB", "ignoreList": [0]}}, {"offset": {"line": 348, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[turbopack]/browser/runtime/base/runtime-base.ts"], "sourcesContent": ["/**\r\n * This file contains runtime types and functions that are shared between all\r\n * Turbopack *development* ECMAScript runtimes.\r\n *\r\n * It will be appended to the runtime code of each runtime right after the\r\n * shared runtime utils.\r\n */\r\n\r\n/* eslint-disable @typescript-eslint/no-unused-vars */\r\n\r\n/// <reference path=\"../base/globals.d.ts\" />\r\n/// <reference path=\"../../../shared/runtime-utils.ts\" />\r\n\r\n// Used in WebWorkers to tell the runtime about the chunk base path\r\ndeclare var TURBOPACK_WORKER_LOCATION: string;\r\n// Used in WebWorkers to tell the runtime about the current chunk url since it can't be detected via document.currentScript\r\n// Note it's stored in reversed order to use push and pop\r\ndeclare var TURBOPACK_NEXT_CHUNK_URLS: ChunkUrl[] | undefined;\r\n\r\n// Injected by rust code\r\ndeclare var CHUNK_BASE_PATH: string;\r\ndeclare var CHUNK_SUFFIX_PATH: string;\r\n\r\n// Provided by build or dev base\r\ndeclare function instantiateModule(id: ModuleId, source: SourceInfo): Module;\r\n\r\ntype RuntimeParams = {\r\n  otherChunks: ChunkData[];\r\n  runtimeModuleIds: ModuleId[];\r\n};\r\n\r\ntype ChunkRegistration = [\r\n  chunkPath: ChunkScript,\r\n  chunkModules: ModuleFactories,\r\n  params: RuntimeParams | undefined\r\n];\r\n\r\ntype ChunkList = {\r\n  script: ChunkListScript;\r\n  chunks: ChunkData[];\r\n  source: \"entry\" | \"dynamic\";\r\n};\r\n\r\nenum SourceType {\r\n  /**\r\n   * The module was instantiated because it was included in an evaluated chunk's\r\n   * runtime.\r\n   */\r\n  Runtime = 0,\r\n  /**\r\n   * The module was instantiated because a parent module imported it.\r\n   */\r\n  Parent = 1,\r\n  /**\r\n   * The module was instantiated because it was included in a chunk's hot module\r\n   * update.\r\n   */\r\n  Update = 2,\r\n}\r\n\r\ntype SourceInfo =\r\n  | {\r\n      type: SourceType.Runtime;\r\n      chunkPath: ChunkPath;\r\n    }\r\n  | {\r\n      type: SourceType.Parent;\r\n      parentId: ModuleId;\r\n    }\r\n  | {\r\n      type: SourceType.Update;\r\n      parents?: ModuleId[];\r\n    };\r\n\r\ninterface RuntimeBackend {\r\n  registerChunk: (chunkPath: ChunkPath, params?: RuntimeParams) => void;\r\n  loadChunk: (chunkUrl: ChunkUrl, source: SourceInfo) => Promise<void>;\r\n}\r\n\r\ninterface DevRuntimeBackend {\r\n  reloadChunk?: (chunkUrl: ChunkUrl) => Promise<void>;\r\n  unloadChunk?: (chunkUrl: ChunkUrl) => void;\r\n  restart: () => void;\r\n}\r\n\r\nconst moduleFactories: ModuleFactories = Object.create(null);\r\n/**\r\n * Module IDs that are instantiated as part of the runtime of a chunk.\r\n */\r\nconst runtimeModules: Set<ModuleId> = new Set();\r\n/**\r\n * Map from module ID to the chunks that contain this module.\r\n *\r\n * In HMR, we need to keep track of which modules are contained in which so\r\n * chunks. This is so we don't eagerly dispose of a module when it is removed\r\n * from chunk A, but still exists in chunk B.\r\n */\r\nconst moduleChunksMap: Map<ModuleId, Set<ChunkPath>> = new Map();\r\n/**\r\n * Map from a chunk path to all modules it contains.\r\n */\r\nconst chunkModulesMap: Map<ChunkPath, Set<ModuleId>> = new Map();\r\n/**\r\n * Chunk lists that contain a runtime. When these chunk lists receive an update\r\n * that can't be reconciled with the current state of the page, we need to\r\n * reload the runtime entirely.\r\n */\r\nconst runtimeChunkLists: Set<ChunkListPath> = new Set();\r\n/**\r\n * Map from a chunk list to the chunk paths it contains.\r\n */\r\nconst chunkListChunksMap: Map<ChunkListPath, Set<ChunkPath>> = new Map();\r\n/**\r\n * Map from a chunk path to the chunk lists it belongs to.\r\n */\r\nconst chunkChunkListsMap: Map<ChunkPath, Set<ChunkListPath>> = new Map();\r\n\r\nconst availableModules: Map<ModuleId, Promise<any> | true> = new Map();\r\n\r\nconst availableModuleChunks: Map<ChunkPath, Promise<any> | true> = new Map();\r\n\r\nasync function loadChunk(\r\n  source: SourceInfo,\r\n  chunkData: ChunkData\r\n): Promise<any> {\r\n  if (typeof chunkData === \"string\") {\r\n    return loadChunkPath(source, chunkData);\r\n  }\r\n\r\n  const includedList = chunkData.included || [];\r\n  const modulesPromises = includedList.map((included) => {\r\n    if (moduleFactories[included]) return true;\r\n    return availableModules.get(included);\r\n  });\r\n  if (modulesPromises.length > 0 && modulesPromises.every((p) => p)) {\r\n    // When all included items are already loaded or loading, we can skip loading ourselves\r\n    return Promise.all(modulesPromises);\r\n  }\r\n\r\n  const includedModuleChunksList = chunkData.moduleChunks || [];\r\n  const moduleChunksPromises = includedModuleChunksList\r\n    .map((included) => {\r\n      // TODO(alexkirsz) Do we need this check?\r\n      // if (moduleFactories[included]) return true;\r\n      return availableModuleChunks.get(included);\r\n    })\r\n    .filter((p) => p);\r\n\r\n  let promise;\r\n  if (moduleChunksPromises.length > 0) {\r\n    // Some module chunks are already loaded or loading.\r\n\r\n    if (moduleChunksPromises.length === includedModuleChunksList.length) {\r\n      // When all included module chunks are already loaded or loading, we can skip loading ourselves\r\n      return Promise.all(moduleChunksPromises);\r\n    }\r\n\r\n    const moduleChunksToLoad: Set<ChunkPath> = new Set();\r\n    for (const moduleChunk of includedModuleChunksList) {\r\n      if (!availableModuleChunks.has(moduleChunk)) {\r\n        moduleChunksToLoad.add(moduleChunk);\r\n      }\r\n    }\r\n\r\n    for (const moduleChunkToLoad of moduleChunksToLoad) {\r\n      const promise = loadChunkPath(source, moduleChunkToLoad);\r\n\r\n      availableModuleChunks.set(moduleChunkToLoad, promise);\r\n\r\n      moduleChunksPromises.push(promise);\r\n    }\r\n\r\n    promise = Promise.all(moduleChunksPromises);\r\n  } else {\r\n    promise = loadChunkPath(source, chunkData.path);\r\n\r\n    // Mark all included module chunks as loading if they are not already loaded or loading.\r\n    for (const includedModuleChunk of includedModuleChunksList) {\r\n      if (!availableModuleChunks.has(includedModuleChunk)) {\r\n        availableModuleChunks.set(includedModuleChunk, promise);\r\n      }\r\n    }\r\n  }\r\n\r\n  for (const included of includedList) {\r\n    if (!availableModules.has(included)) {\r\n      // It might be better to race old and new promises, but it's rare that the new promise will be faster than a request started earlier.\r\n      // In production it's even more rare, because the chunk optimization tries to deduplicate modules anyway.\r\n      availableModules.set(included, promise);\r\n    }\r\n  }\r\n\r\n  return promise;\r\n}\r\n\r\nasync function loadChunkByUrl(source: SourceInfo, chunkUrl: ChunkUrl) {\r\n  try {\r\n    await BACKEND.loadChunk(chunkUrl, source);\r\n  } catch (error) {\r\n    let loadReason;\r\n    switch (source.type) {\r\n      case SourceType.Runtime:\r\n        loadReason = `as a runtime dependency of chunk ${source.chunkPath}`;\r\n        break;\r\n      case SourceType.Parent:\r\n        loadReason = `from module ${source.parentId}`;\r\n        break;\r\n      case SourceType.Update:\r\n        loadReason = \"from an HMR update\";\r\n        break;\r\n      default:\r\n        invariant(source, (source) => `Unknown source type: ${source?.type}`);\r\n    }\r\n    throw new Error(\r\n      `Failed to load chunk ${chunkUrl} ${loadReason}${\r\n        error ? `: ${error}` : \"\"\r\n      }`,\r\n      error\r\n        ? {\r\n            cause: error,\r\n          }\r\n        : undefined\r\n    );\r\n  }\r\n}\r\n\r\nasync function loadChunkPath(\r\n  source: SourceInfo,\r\n  chunkPath: ChunkPath\r\n): Promise<any> {\r\n  const url = getChunkRelativeUrl(chunkPath);\r\n  return loadChunkByUrl(source, url);\r\n}\r\n\r\n/**\r\n * Returns an absolute url to an asset.\r\n */\r\nfunction createResolvePathFromModule(\r\n  resolver: (moduleId: string) => Exports\r\n): (moduleId: string) => string {\r\n  return function resolvePathFromModule(moduleId: string): string {\r\n    const exported = resolver(moduleId);\r\n    return exported?.default ?? exported;\r\n  };\r\n}\r\n\r\n/**\r\n * no-op for browser\r\n * @param modulePath\r\n */\r\nfunction resolveAbsolutePath(modulePath?: string): string {\r\n  return `/ROOT/${modulePath ?? \"\"}`;\r\n}\r\n\r\nfunction getWorkerBlobURL(chunks: ChunkPath[]): string {\r\n  let bootstrap = `self.TURBOPACK_WORKER_LOCATION = ${JSON.stringify(location.origin)};\r\nself.TURBOPACK_NEXT_CHUNK_URLS = ${JSON.stringify(chunks.reverse().map(getChunkRelativeUrl), null, 2)};\r\nimportScripts(...self.TURBOPACK_NEXT_CHUNK_URLS.map(c => self.TURBOPACK_WORKER_LOCATION + c).reverse());`;\r\n  let blob = new Blob([bootstrap], { type: \"text/javascript\" });\r\n  return URL.createObjectURL(blob);\r\n}\r\n\r\n/**\r\n * Adds a module to a chunk.\r\n */\r\nfunction addModuleToChunk(moduleId: ModuleId, chunkPath: ChunkPath) {\r\n  let moduleChunks = moduleChunksMap.get(moduleId);\r\n  if (!moduleChunks) {\r\n    moduleChunks = new Set([chunkPath]);\r\n    moduleChunksMap.set(moduleId, moduleChunks);\r\n  } else {\r\n    moduleChunks.add(chunkPath);\r\n  }\r\n\r\n  let chunkModules = chunkModulesMap.get(chunkPath);\r\n  if (!chunkModules) {\r\n    chunkModules = new Set([moduleId]);\r\n    chunkModulesMap.set(chunkPath, chunkModules);\r\n  } else {\r\n    chunkModules.add(moduleId);\r\n  }\r\n}\r\n\r\n/**\r\n * Returns the first chunk that included a module.\r\n * This is used by the Node.js backend, hence why it's marked as unused in this\r\n * file.\r\n */\r\nfunction getFirstModuleChunk(moduleId: ModuleId) {\r\n  const moduleChunkPaths = moduleChunksMap.get(moduleId);\r\n  if (moduleChunkPaths == null) {\r\n    return null;\r\n  }\r\n\r\n  return moduleChunkPaths.values().next().value;\r\n}\r\n\r\n/**\r\n * Instantiates a runtime module.\r\n */\r\nfunction instantiateRuntimeModule(\r\n  moduleId: ModuleId,\r\n  chunkPath: ChunkPath\r\n): Module {\r\n  return instantiateModule(moduleId, { type: SourceType.Runtime, chunkPath });\r\n}\r\n/**\r\n * Returns the URL relative to the origin where a chunk can be fetched from.\r\n */\r\nfunction getChunkRelativeUrl(chunkPath: ChunkPath | ChunkListPath): ChunkUrl {\r\n  return `${CHUNK_BASE_PATH}${chunkPath\r\n    .split(\"/\")\r\n    .map((p) => encodeURIComponent(p))\r\n    .join(\"/\")}${CHUNK_SUFFIX_PATH}` as ChunkUrl;\r\n}\r\n\r\n/**\r\n * Return the ChunkPath from a ChunkScript.\r\n */\r\nfunction getPathFromScript(chunkScript: ChunkPath | ChunkScript): ChunkPath;\r\nfunction getPathFromScript(chunkScript: ChunkListPath | ChunkListScript): ChunkListPath;\r\nfunction getPathFromScript(chunkScript: ChunkPath | ChunkListPath | ChunkScript | ChunkListScript): ChunkPath | ChunkListPath {\r\n  if (typeof chunkScript === \"string\") {\r\n    return chunkScript as ChunkPath | ChunkListPath;\r\n  }\r\n  const chunkUrl = typeof TURBOPACK_NEXT_CHUNK_URLS !== \"undefined\"\r\n    ? TURBOPACK_NEXT_CHUNK_URLS.pop()!\r\n    : chunkScript.getAttribute(\"src\")!;\r\n  const src = decodeURIComponent(chunkUrl.replace(/[?#].*$/, \"\"));\r\n  const path = src.startsWith(CHUNK_BASE_PATH) ? src.slice(CHUNK_BASE_PATH.length) : src;\r\n  return path as ChunkPath | ChunkListPath;\r\n}\r\n\r\n/**\r\n * Marks a chunk list as a runtime chunk list. There can be more than one\r\n * runtime chunk list. For instance, integration tests can have multiple chunk\r\n * groups loaded at runtime, each with its own chunk list.\r\n */\r\nfunction markChunkListAsRuntime(chunkListPath: ChunkListPath) {\r\n  runtimeChunkLists.add(chunkListPath);\r\n}\r\n\r\nfunction registerChunk([\r\n  chunkScript,\r\n  chunkModules,\r\n  runtimeParams,\r\n]: ChunkRegistration) {\r\n  const chunkPath = getPathFromScript(chunkScript);\r\n  for (const [moduleId, moduleFactory] of Object.entries(chunkModules)) {\r\n    if (!moduleFactories[moduleId]) {\r\n      moduleFactories[moduleId] = moduleFactory;\r\n    }\r\n    addModuleToChunk(moduleId, chunkPath);\r\n  }\r\n\r\n  return BACKEND.registerChunk(chunkPath, runtimeParams);\r\n}\r\n\r\nconst regexJsUrl = /\\.js(?:\\?[^#]*)?(?:#.*)?$/;\r\n/**\r\n * Checks if a given path/URL ends with .js, optionally followed by ?query or #fragment.\r\n */\r\nfunction isJs(chunkUrlOrPath: ChunkUrl | ChunkPath): boolean {\r\n  return regexJsUrl.test(chunkUrlOrPath);\r\n}\r\n\r\nconst regexCssUrl = /\\.css(?:\\?[^#]*)?(?:#.*)?$/;\r\n/**\r\n * Checks if a given path/URL ends with .css, optionally followed by ?query or #fragment.\r\n */\r\nfunction isCss(chunkUrl: ChunkUrl): boolean {\r\n  return regexCssUrl.test(chunkUrl);\r\n}\r\n"], "names": [], "mappings": "AAAA;;;;;;CAMC,GAED,oDAAoD,GAEpD,6CAA6C;AAC7C,yDAAyD;AAEzD,mEAAmE;AA8BnE,IAAA,AAAK,oCAAA;IACH;;;GAGC;IAED;;GAEC;IAED;;;GAGC;WAbE;EAAA;AA0CL,MAAM,kBAAmC,OAAO,MAAM,CAAC;AACvD;;CAEC,GACD,MAAM,iBAAgC,IAAI;AAC1C;;;;;;CAMC,GACD,MAAM,kBAAiD,IAAI;AAC3D;;CAEC,GACD,MAAM,kBAAiD,IAAI;AAC3D;;;;CAIC,GACD,MAAM,oBAAwC,IAAI;AAClD;;CAEC,GACD,MAAM,qBAAyD,IAAI;AACnE;;CAEC,GACD,MAAM,qBAAyD,IAAI;AAEnE,MAAM,mBAAuD,IAAI;AAEjE,MAAM,wBAA6D,IAAI;AAEvE,eAAe,UACb,MAAkB,EAClB,SAAoB;IAEpB,IAAI,OAAO,cAAc,UAAU;QACjC,OAAO,cAAc,QAAQ;IAC/B;IAEA,MAAM,eAAe,UAAU,QAAQ,IAAI,EAAE;IAC7C,MAAM,kBAAkB,aAAa,GAAG,CAAC,CAAC;QACxC,IAAI,eAAe,CAAC,SAAS,EAAE,OAAO;QACtC,OAAO,iBAAiB,GAAG,CAAC;IAC9B;IACA,IAAI,gBAAgB,MAAM,GAAG,KAAK,gBAAgB,KAAK,CAAC,CAAC,IAAM,IAAI;QACjE,uFAAuF;QACvF,OAAO,QAAQ,GAAG,CAAC;IACrB;IAEA,MAAM,2BAA2B,UAAU,YAAY,IAAI,EAAE;IAC7D,MAAM,uBAAuB,yBAC1B,GAAG,CAAC,CAAC;QACJ,yCAAyC;QACzC,8CAA8C;QAC9C,OAAO,sBAAsB,GAAG,CAAC;IACnC,GACC,MAAM,CAAC,CAAC,IAAM;IAEjB,IAAI;IACJ,IAAI,qBAAqB,MAAM,GAAG,GAAG;QACnC,oDAAoD;QAEpD,IAAI,qBAAqB,MAAM,KAAK,yBAAyB,MAAM,EAAE;YACnE,+FAA+F;YAC/F,OAAO,QAAQ,GAAG,CAAC;QACrB;QAEA,MAAM,qBAAqC,IAAI;QAC/C,KAAK,MAAM,eAAe,yBAA0B;YAClD,IAAI,CAAC,sBAAsB,GAAG,CAAC,cAAc;gBAC3C,mBAAmB,GAAG,CAAC;YACzB;QACF;QAEA,KAAK,MAAM,qBAAqB,mBAAoB;YAClD,MAAM,UAAU,cAAc,QAAQ;YAEtC,sBAAsB,GAAG,CAAC,mBAAmB;YAE7C,qBAAqB,IAAI,CAAC;QAC5B;QAEA,UAAU,QAAQ,GAAG,CAAC;IACxB,OAAO;QACL,UAAU,cAAc,QAAQ,UAAU,IAAI;QAE9C,wFAAwF;QACxF,KAAK,MAAM,uBAAuB,yBAA0B;YAC1D,IAAI,CAAC,sBAAsB,GAAG,CAAC,sBAAsB;gBACnD,sBAAsB,GAAG,CAAC,qBAAqB;YACjD;QACF;IACF;IAEA,KAAK,MAAM,YAAY,aAAc;QACnC,IAAI,CAAC,iBAAiB,GAAG,CAAC,WAAW;YACnC,qIAAqI;YACrI,yGAAyG;YACzG,iBAAiB,GAAG,CAAC,UAAU;QACjC;IACF;IAEA,OAAO;AACT;AAEA,eAAe,eAAe,MAAkB,EAAE,QAAkB;IAClE,IAAI;QACF,MAAM,QAAQ,SAAS,CAAC,UAAU;IACpC,EAAE,OAAO,OAAO;QACd,IAAI;QACJ,OAAQ,OAAO,IAAI;YACjB;gBACE,aAAa,CAAC,iCAAiC,EAAE,OAAO,SAAS,EAAE;gBACnE;YACF;gBACE,aAAa,CAAC,YAAY,EAAE,OAAO,QAAQ,EAAE;gBAC7C;YACF;gBACE,aAAa;gBACb;YACF;gBACE,UAAU,QAAQ,CAAC,SAAW,CAAC,qBAAqB,EAAE,QAAQ,MAAM;QACxE;QACA,MAAM,IAAI,MACR,CAAC,qBAAqB,EAAE,SAAS,CAAC,EAAE,aAClC,QAAQ,CAAC,EAAE,EAAE,OAAO,GAAG,IACvB,EACF,QACI;YACE,OAAO;QACT,IACA;IAER;AACF;AAEA,eAAe,cACb,MAAkB,EAClB,SAAoB;IAEpB,MAAM,MAAM,oBAAoB;IAChC,OAAO,eAAe,QAAQ;AAChC;AAEA;;CAEC,GACD,SAAS,4BACP,QAAuC;IAEvC,OAAO,SAAS,sBAAsB,QAAgB;QACpD,MAAM,WAAW,SAAS;QAC1B,OAAO,UAAU,WAAW;IAC9B;AACF;AAEA;;;CAGC,GACD,SAAS,oBAAoB,UAAmB;IAC9C,OAAO,CAAC,MAAM,EAAE,cAAc,IAAI;AACpC;AAEA,SAAS,iBAAiB,MAAmB;IAC3C,IAAI,YAAY,CAAC,iCAAiC,EAAE,KAAK,SAAS,CAAC,SAAS,MAAM,EAAE;iCACrD,EAAE,KAAK,SAAS,CAAC,OAAO,OAAO,GAAG,GAAG,CAAC,sBAAsB,MAAM,GAAG;wGACE,CAAC;IACvG,IAAI,OAAO,IAAI,KAAK;QAAC;KAAU,EAAE;QAAE,MAAM;IAAkB;IAC3D,OAAO,IAAI,eAAe,CAAC;AAC7B;AAEA;;CAEC,GACD,SAAS,iBAAiB,QAAkB,EAAE,SAAoB;IAChE,IAAI,eAAe,gBAAgB,GAAG,CAAC;IACvC,IAAI,CAAC,cAAc;QACjB,eAAe,IAAI,IAAI;YAAC;SAAU;QAClC,gBAAgB,GAAG,CAAC,UAAU;IAChC,OAAO;QACL,aAAa,GAAG,CAAC;IACnB;IAEA,IAAI,eAAe,gBAAgB,GAAG,CAAC;IACvC,IAAI,CAAC,cAAc;QACjB,eAAe,IAAI,IAAI;YAAC;SAAS;QACjC,gBAAgB,GAAG,CAAC,WAAW;IACjC,OAAO;QACL,aAAa,GAAG,CAAC;IACnB;AACF;AAEA;;;;CAIC,GACD,SAAS,oBAAoB,QAAkB;IAC7C,MAAM,mBAAmB,gBAAgB,GAAG,CAAC;IAC7C,IAAI,oBAAoB,MAAM;QAC5B,OAAO;IACT;IAEA,OAAO,iBAAiB,MAAM,GAAG,IAAI,GAAG,KAAK;AAC/C;AAEA;;CAEC,GACD,SAAS,yBACP,QAAkB,EAClB,SAAoB;IAEpB,OAAO,kBAAkB,UAAU;QAAE,IAAI;QAAsB;IAAU;AAC3E;AACA;;CAEC,GACD,SAAS,oBAAoB,SAAoC;IAC/D,OAAO,GAAG,kBAAkB,UACzB,KAAK,CAAC,KACN,GAAG,CAAC,CAAC,IAAM,mBAAmB,IAC9B,IAAI,CAAC,OAAO,mBAAmB;AACpC;AAOA,SAAS,kBAAkB,WAAsE;IAC/F,IAAI,OAAO,gBAAgB,UAAU;QACnC,OAAO;IACT;IACA,MAAM,WAAW,OAAO,8BAA8B,cAClD,0BAA0B,GAAG,KAC7B,YAAY,YAAY,CAAC;IAC7B,MAAM,MAAM,mBAAmB,SAAS,OAAO,CAAC,WAAW;IAC3D,MAAM,OAAO,IAAI,UAAU,CAAC,mBAAmB,IAAI,KAAK,CAAC,gBAAgB,MAAM,IAAI;IACnF,OAAO;AACT;AAEA;;;;CAIC,GACD,SAAS,uBAAuB,aAA4B;IAC1D,kBAAkB,GAAG,CAAC;AACxB;AAEA,SAAS,cAAc,CACrB,aACA,cACA,cACkB;IAClB,MAAM,YAAY,kBAAkB;IACpC,KAAK,MAAM,CAAC,UAAU,cAAc,IAAI,OAAO,OAAO,CAAC,cAAe;QACpE,IAAI,CAAC,eAAe,CAAC,SAAS,EAAE;YAC9B,eAAe,CAAC,SAAS,GAAG;QAC9B;QACA,iBAAiB,UAAU;IAC7B;IAEA,OAAO,QAAQ,aAAa,CAAC,WAAW;AAC1C;AAEA,MAAM,aAAa;AACnB;;CAEC,GACD,SAAS,KAAK,cAAoC;IAChD,OAAO,WAAW,IAAI,CAAC;AACzB;AAEA,MAAM,cAAc;AACpB;;CAEC,GACD,SAAS,MAAM,QAAkB;IAC/B,OAAO,YAAY,IAAI,CAAC;AAC1B", "ignoreList": [0]}}, {"offset": {"line": 590, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[turbopack]/browser/runtime/base/dev-base.ts"], "sourcesContent": ["/// <reference path=\"./dev-globals.d.ts\" />\r\n/// <reference path=\"./dev-protocol.d.ts\" />\r\n/// <reference path=\"./dev-extensions.ts\" />\r\n\r\n/**\r\n * This file contains runtime types and functions that are shared between all\r\n * Turbopack *development* ECMAScript runtimes.\r\n *\r\n * It will be appended to the runtime code of each runtime right after the\r\n * shared runtime utils.\r\n */\r\n\r\n/* eslint-disable @typescript-eslint/no-unused-vars */\r\n\r\nconst devModuleCache: ModuleCache<HotModule> = Object.create(null);\r\n\r\n// This file must not use `import` and `export` statements. Otherwise, it\r\n// becomes impossible to augment interfaces declared in `<reference>`d files\r\n// (e.g. `Module`). Hence, the need for `import()` here.\r\ntype RefreshRuntimeGlobals =\r\n  import(\"@next/react-refresh-utils/dist/runtime\").RefreshRuntimeGlobals;\r\n\r\ndeclare var $RefreshHelpers$: RefreshRuntimeGlobals[\"$RefreshHelpers$\"];\r\ndeclare var $RefreshReg$: RefreshRuntimeGlobals[\"$RefreshReg$\"];\r\ndeclare var $RefreshSig$: RefreshRuntimeGlobals[\"$RefreshSig$\"];\r\ndeclare var $RefreshInterceptModuleExecution$:\r\n  | RefreshRuntimeGlobals[\"$RefreshInterceptModuleExecution$\"];\r\n\r\ntype RefreshContext = {\r\n  register: RefreshRuntimeGlobals[\"$RefreshReg$\"];\r\n  signature: RefreshRuntimeGlobals[\"$RefreshSig$\"];\r\n  registerExports: typeof registerExportsAndSetupBoundaryForReactRefresh;\r\n};\r\n\r\ntype RefreshHelpers = RefreshRuntimeGlobals[\"$RefreshHelpers$\"];\r\n\r\ninterface TurbopackDevBaseContext extends TurbopackBaseContext<Module> {\r\n  k: RefreshContext;\r\n  R: ResolvePathFromModule;\r\n}\r\n\r\ninterface TurbopackDevContext extends TurbopackDevBaseContext {}\r\n\r\ntype ModuleFactory = (\r\n  this: Module[\"exports\"],\r\n  context: TurbopackDevBaseContext\r\n) => undefined\r\n\r\ninterface DevRuntimeBackend {\r\n  reloadChunk?: (chunkUrl: ChunkUrl) => Promise<void>;\r\n  unloadChunk?: (chunkUrl: ChunkUrl) => void;\r\n  restart: () => void;\r\n}\r\n\r\nclass UpdateApplyError extends Error {\r\n  name = \"UpdateApplyError\";\r\n\r\n  dependencyChain: string[];\r\n\r\n  constructor(message: string, dependencyChain: string[]) {\r\n    super(message);\r\n    this.dependencyChain = dependencyChain;\r\n  }\r\n}\r\n\r\n/**\r\n * Maps module IDs to persisted data between executions of their hot module\r\n * implementation (`hot.data`).\r\n */\r\nconst moduleHotData: Map<ModuleId, HotData> = new Map();\r\n/**\r\n * Maps module instances to their hot module state.\r\n */\r\nconst moduleHotState: Map<Module, HotState> = new Map();\r\n/**\r\n * Modules that call `module.hot.invalidate()` (while being updated).\r\n */\r\nconst queuedInvalidatedModules: Set<ModuleId> = new Set();\r\n\r\n/**\r\n * Gets or instantiates a runtime module.\r\n */\r\n// @ts-ignore\r\nfunction getOrInstantiateRuntimeModule(\r\n  moduleId: ModuleId,\r\n  chunkPath: ChunkPath,\r\n): Module {\r\n  const module = devModuleCache[moduleId];\r\n  if (module) {\r\n    if (module.error) {\r\n      throw module.error;\r\n    }\r\n    return module;\r\n  }\r\n\r\n  // @ts-ignore\r\n  return instantiateModule(moduleId, { type: SourceType.Runtime, chunkPath });\r\n}\r\n\r\n/**\r\n * Retrieves a module from the cache, or instantiate it if it is not cached.\r\n */\r\n// @ts-ignore Defined in `runtime-utils.ts`\r\nconst getOrInstantiateModuleFromParent: GetOrInstantiateModuleFromParent<HotModule> = (\r\n  id,\r\n  sourceModule,\r\n) => {\r\n  if (!sourceModule.hot.active) {\r\n    console.warn(\r\n      `Unexpected import of module ${id} from module ${sourceModule.id}, which was deleted by an HMR update`\r\n    );\r\n  }\r\n\r\n  const module = devModuleCache[id];\r\n\r\n  if (sourceModule.children.indexOf(id) === -1) {\r\n    sourceModule.children.push(id);\r\n  }\r\n\r\n  if (module) {\r\n    if (module.parents.indexOf(sourceModule.id) === -1) {\r\n      module.parents.push(sourceModule.id);\r\n    }\r\n\r\n    return module;\r\n  }\r\n\r\n  return instantiateModule(id, {\r\n    type: SourceType.Parent,\r\n    parentId: sourceModule.id,\r\n  });\r\n};\r\n\r\n// @ts-ignore Defined in `runtime-base.ts`\r\nfunction instantiateModule(id: ModuleId, source: SourceInfo): Module {\r\n  const moduleFactory = moduleFactories[id];\r\n  if (typeof moduleFactory !== \"function\") {\r\n    // This can happen if modules incorrectly handle HMR disposes/updates,\r\n    // e.g. when they keep a `setTimeout` around which still executes old code\r\n    // and contains e.g. a `require(\"something\")` call.\r\n    let instantiationReason;\r\n    switch (source.type) {\r\n      case SourceType.Runtime:\r\n        instantiationReason = `as a runtime entry of chunk ${source.chunkPath}`;\r\n        break;\r\n      case SourceType.Parent:\r\n        instantiationReason = `because it was required from module ${source.parentId}`;\r\n        break;\r\n      case SourceType.Update:\r\n        instantiationReason = \"because of an HMR update\";\r\n        break;\r\n      default:\r\n        invariant(source, (source) => `Unknown source type: ${source?.type}`);\r\n    }\r\n    throw new Error(\r\n      `Module ${id} was instantiated ${instantiationReason}, but the module factory is not available. It might have been deleted in an HMR update.`\r\n    );\r\n  }\r\n\r\n  const hotData = moduleHotData.get(id)!;\r\n  const { hot, hotState } = createModuleHot(id, hotData);\r\n\r\n  let parents: ModuleId[];\r\n  switch (source.type) {\r\n    case SourceType.Runtime:\r\n      runtimeModules.add(id);\r\n      parents = [];\r\n      break;\r\n    case SourceType.Parent:\r\n      // No need to add this module as a child of the parent module here, this\r\n      // has already been taken care of in `getOrInstantiateModuleFromParent`.\r\n      parents = [source.parentId];\r\n      break;\r\n    case SourceType.Update:\r\n      parents = source.parents || [];\r\n      break;\r\n    default:\r\n      invariant(source, (source) => `Unknown source type: ${source?.type}`);\r\n  }\r\n\r\n  const module: HotModule = {\r\n    exports: {},\r\n    error: undefined,\r\n    loaded: false,\r\n    id,\r\n    parents,\r\n    children: [],\r\n    namespaceObject: undefined,\r\n    hot,\r\n  };\r\n\r\n  devModuleCache[id] = module;\r\n  moduleHotState.set(module, hotState);\r\n\r\n  // NOTE(alexkirsz) This can fail when the module encounters a runtime error.\r\n  try {\r\n    const sourceInfo: SourceInfo = { type: SourceType.Parent, parentId: id };\r\n\r\n    runModuleExecutionHooks(module, (refresh) => {\r\n      const r = commonJsRequire.bind(null, module);\r\n      moduleFactory.call(\r\n        module.exports,\r\n        augmentContext({\r\n          a: asyncModule.bind(null, module),\r\n          e: module.exports,\r\n          r: commonJsRequire.bind(null, module),\r\n          t: runtimeRequire,\r\n          f: moduleContext,\r\n          i: esmImport.bind(null, module),\r\n          s: esmExport.bind(null, module, module.exports),\r\n          j: dynamicExport.bind(null, module, module.exports),\r\n          v: exportValue.bind(null, module),\r\n          n: exportNamespace.bind(null, module),\r\n          m: module,\r\n          c: devModuleCache,\r\n          M: moduleFactories,\r\n          l: loadChunk.bind(null, sourceInfo),\r\n          L: loadChunkByUrl.bind(null, sourceInfo),\r\n          w: loadWebAssembly.bind(null, sourceInfo),\r\n          u: loadWebAssemblyModule.bind(null, sourceInfo),\r\n          g: globalThis,\r\n          P: resolveAbsolutePath,\r\n          U: relativeURL,\r\n          k: refresh,\r\n          R: createResolvePathFromModule(r),\r\n          b: getWorkerBlobURL,\r\n          z: requireStub,\r\n          d: typeof module.id === \"string\" ? module.id.replace(/(^|\\/)\\/+$/, \"\") : module.id\r\n        })\r\n      );\r\n    });\r\n  } catch (error) {\r\n    module.error = error as any;\r\n    throw error;\r\n  }\r\n\r\n  module.loaded = true;\r\n  if (module.namespaceObject && module.exports !== module.namespaceObject) {\r\n    // in case of a circular dependency: cjs1 -> esm2 -> cjs1\r\n    interopEsm(module.exports, module.namespaceObject);\r\n  }\r\n\r\n  return module;\r\n}\r\n\r\n/**\r\n * NOTE(alexkirsz) Webpack has a \"module execution\" interception hook that\r\n * Next.js' React Refresh runtime hooks into to add module context to the\r\n * refresh registry.\r\n */\r\nfunction runModuleExecutionHooks(\r\n  module: Module,\r\n  executeModule: (ctx: RefreshContext) => void\r\n) {\r\n  const cleanupReactRefreshIntercept =\r\n    typeof globalThis.$RefreshInterceptModuleExecution$ === \"function\"\r\n      ? globalThis.$RefreshInterceptModuleExecution$(module.id)\r\n      : () => {};\r\n\r\n  try {\r\n    executeModule({\r\n      register: globalThis.$RefreshReg$,\r\n      signature: globalThis.$RefreshSig$,\r\n      registerExports: registerExportsAndSetupBoundaryForReactRefresh,\r\n    });\r\n  } catch (e) {\r\n    throw e;\r\n  } finally {\r\n    // Always cleanup the intercept, even if module execution failed.\r\n    cleanupReactRefreshIntercept();\r\n  }\r\n}\r\n\r\n/**\r\n * This is adapted from https://github.com/vercel/next.js/blob/3466862d9dc9c8bb3131712134d38757b918d1c0/packages/react-refresh-utils/internal/ReactRefreshModule.runtime.ts\r\n */\r\nfunction registerExportsAndSetupBoundaryForReactRefresh(\r\n  module: HotModule,\r\n  helpers: RefreshHelpers\r\n) {\r\n  const currentExports = module.exports;\r\n  const prevExports = module.hot.data.prevExports ?? null;\r\n\r\n  helpers.registerExportsForReactRefresh(currentExports, module.id);\r\n\r\n  // A module can be accepted automatically based on its exports, e.g. when\r\n  // it is a Refresh Boundary.\r\n  if (helpers.isReactRefreshBoundary(currentExports)) {\r\n    // Save the previous exports on update, so we can compare the boundary\r\n    // signatures.\r\n    module.hot.dispose((data) => {\r\n      data.prevExports = currentExports;\r\n    });\r\n    // Unconditionally accept an update to this module, we'll check if it's\r\n    // still a Refresh Boundary later.\r\n    module.hot.accept();\r\n\r\n    // This field is set when the previous version of this module was a\r\n    // Refresh Boundary, letting us know we need to check for invalidation or\r\n    // enqueue an update.\r\n    if (prevExports !== null) {\r\n      // A boundary can become ineligible if its exports are incompatible\r\n      // with the previous exports.\r\n      //\r\n      // For example, if you add/remove/change exports, we'll want to\r\n      // re-execute the importing modules, and force those components to\r\n      // re-render. Similarly, if you convert a class component to a\r\n      // function, we want to invalidate the boundary.\r\n      if (\r\n        helpers.shouldInvalidateReactRefreshBoundary(\r\n          helpers.getRefreshBoundarySignature(prevExports),\r\n          helpers.getRefreshBoundarySignature(currentExports)\r\n        )\r\n      ) {\r\n        module.hot.invalidate();\r\n      } else {\r\n        helpers.scheduleUpdate();\r\n      }\r\n    }\r\n  } else {\r\n    // Since we just executed the code for the module, it's possible that the\r\n    // new exports made it ineligible for being a boundary.\r\n    // We only care about the case when we were _previously_ a boundary,\r\n    // because we already accepted this update (accidental side effect).\r\n    const isNoLongerABoundary = prevExports !== null;\r\n    if (isNoLongerABoundary) {\r\n      module.hot.invalidate();\r\n    }\r\n  }\r\n}\r\n\r\nfunction formatDependencyChain(dependencyChain: ModuleId[]): string {\r\n  return `Dependency chain: ${dependencyChain.join(\" -> \")}`;\r\n}\r\n\r\nfunction computeOutdatedModules(\r\n  added: Map<ModuleId, EcmascriptModuleEntry | undefined>,\r\n  modified: Map<ModuleId, EcmascriptModuleEntry>\r\n): {\r\n  outdatedModules: Set<ModuleId>;\r\n  newModuleFactories: Map<ModuleId, ModuleFactory>;\r\n} {\r\n  const newModuleFactories = new Map<ModuleId, ModuleFactory>();\r\n\r\n  for (const [moduleId, entry] of added) {\r\n    if (entry != null) {\r\n      newModuleFactories.set(moduleId, _eval(entry));\r\n    }\r\n  }\r\n\r\n  const outdatedModules = computedInvalidatedModules(modified.keys());\r\n\r\n  for (const [moduleId, entry] of modified) {\r\n    newModuleFactories.set(moduleId, _eval(entry));\r\n  }\r\n\r\n  return { outdatedModules, newModuleFactories };\r\n}\r\n\r\nfunction computedInvalidatedModules(\r\n  invalidated: Iterable<ModuleId>\r\n): Set<ModuleId> {\r\n  const outdatedModules = new Set<ModuleId>();\r\n\r\n  for (const moduleId of invalidated) {\r\n    const effect = getAffectedModuleEffects(moduleId);\r\n\r\n    switch (effect.type) {\r\n      case \"unaccepted\":\r\n        throw new UpdateApplyError(\r\n          `cannot apply update: unaccepted module. ${formatDependencyChain(\r\n            effect.dependencyChain\r\n          )}.`,\r\n          effect.dependencyChain\r\n        );\r\n      case \"self-declined\":\r\n        throw new UpdateApplyError(\r\n          `cannot apply update: self-declined module. ${formatDependencyChain(\r\n            effect.dependencyChain\r\n          )}.`,\r\n          effect.dependencyChain\r\n        );\r\n      case \"accepted\":\r\n        for (const outdatedModuleId of effect.outdatedModules) {\r\n          outdatedModules.add(outdatedModuleId);\r\n        }\r\n        break;\r\n      // TODO(alexkirsz) Dependencies: handle dependencies effects.\r\n      default:\r\n        invariant(effect, (effect) => `Unknown effect type: ${effect?.type}`);\r\n    }\r\n  }\r\n\r\n  return outdatedModules;\r\n}\r\n\r\nfunction computeOutdatedSelfAcceptedModules(\r\n  outdatedModules: Iterable<ModuleId>\r\n): { moduleId: ModuleId; errorHandler: true | Function }[] {\r\n  const outdatedSelfAcceptedModules: { moduleId: ModuleId; errorHandler: true | Function }[] = [];\r\n  for (const moduleId of outdatedModules) {\r\n    const module = devModuleCache[moduleId];\r\n    const hotState = moduleHotState.get(module)!;\r\n    if (module && hotState.selfAccepted && !hotState.selfInvalidated) {\r\n      outdatedSelfAcceptedModules.push({\r\n        moduleId,\r\n        errorHandler: hotState.selfAccepted,\r\n      });\r\n    }\r\n  }\r\n  return outdatedSelfAcceptedModules;\r\n}\r\n\r\n/**\r\n * Adds, deletes, and moves modules between chunks. This must happen before the\r\n * dispose phase as it needs to know which modules were removed from all chunks,\r\n * which we can only compute *after* taking care of added and moved modules.\r\n */\r\nfunction updateChunksPhase(\r\n  chunksAddedModules: Map<ChunkPath, Set<ModuleId>>,\r\n  chunksDeletedModules: Map<ChunkPath, Set<ModuleId>>\r\n): { disposedModules: Set<ModuleId> } {\r\n  for (const [chunkPath, addedModuleIds] of chunksAddedModules) {\r\n    for (const moduleId of addedModuleIds) {\r\n      addModuleToChunk(moduleId, chunkPath);\r\n    }\r\n  }\r\n\r\n  const disposedModules: Set<ModuleId> = new Set();\r\n  for (const [chunkPath, addedModuleIds] of chunksDeletedModules) {\r\n    for (const moduleId of addedModuleIds) {\r\n      if (removeModuleFromChunk(moduleId, chunkPath)) {\r\n        disposedModules.add(moduleId);\r\n      }\r\n    }\r\n  }\r\n\r\n  return { disposedModules };\r\n}\r\n\r\nfunction disposePhase(\r\n  outdatedModules: Iterable<ModuleId>,\r\n  disposedModules: Iterable<ModuleId>\r\n): { outdatedModuleParents: Map<ModuleId, Array<ModuleId>> } {\r\n  for (const moduleId of outdatedModules) {\r\n    disposeModule(moduleId, \"replace\");\r\n  }\r\n\r\n  for (const moduleId of disposedModules) {\r\n    disposeModule(moduleId, \"clear\");\r\n  }\r\n\r\n  // Removing modules from the module cache is a separate step.\r\n  // We also want to keep track of previous parents of the outdated modules.\r\n  const outdatedModuleParents = new Map();\r\n  for (const moduleId of outdatedModules) {\r\n    const oldModule = devModuleCache[moduleId];\r\n    outdatedModuleParents.set(moduleId, oldModule?.parents);\r\n    delete devModuleCache[moduleId];\r\n  }\r\n\r\n  // TODO(alexkirsz) Dependencies: remove outdated dependency from module\r\n  // children.\r\n\r\n  return { outdatedModuleParents };\r\n}\r\n\r\n/**\r\n * Disposes of an instance of a module.\r\n *\r\n * Returns the persistent hot data that should be kept for the next module\r\n * instance.\r\n *\r\n * NOTE: mode = \"replace\" will not remove modules from the devModuleCache\r\n * This must be done in a separate step afterwards.\r\n * This is important because all modules need to be disposed to update the\r\n * parent/child relationships before they are actually removed from the devModuleCache.\r\n * If this was done in this method, the following disposeModule calls won't find\r\n * the module from the module id in the cache.\r\n */\r\nfunction disposeModule(moduleId: ModuleId, mode: \"clear\" | \"replace\") {\r\n  const module = devModuleCache[moduleId];\r\n  if (!module) {\r\n    return;\r\n  }\r\n\r\n  const hotState = moduleHotState.get(module)!;\r\n  const data = {};\r\n\r\n  // Run the `hot.dispose` handler, if any, passing in the persistent\r\n  // `hot.data` object.\r\n  for (const disposeHandler of hotState.disposeHandlers) {\r\n    disposeHandler(data);\r\n  }\r\n\r\n  // This used to warn in `getOrInstantiateModuleFromParent` when a disposed\r\n  // module is still importing other modules.\r\n  module.hot.active = false;\r\n\r\n  moduleHotState.delete(module);\r\n\r\n  // TODO(alexkirsz) Dependencies: delete the module from outdated deps.\r\n\r\n  // Remove the disposed module from its children's parent list.\r\n  // It will be added back once the module re-instantiates and imports its\r\n  // children again.\r\n  for (const childId of module.children) {\r\n    const child = devModuleCache[childId];\r\n    if (!child) {\r\n      continue;\r\n    }\r\n\r\n    const idx = child.parents.indexOf(module.id);\r\n    if (idx >= 0) {\r\n      child.parents.splice(idx, 1);\r\n    }\r\n  }\r\n\r\n  switch (mode) {\r\n    case \"clear\":\r\n      delete devModuleCache[module.id];\r\n      moduleHotData.delete(module.id);\r\n      break;\r\n    case \"replace\":\r\n      moduleHotData.set(module.id, data);\r\n      break;\r\n    default:\r\n      invariant(mode, (mode) => `invalid mode: ${mode}`);\r\n  }\r\n}\r\n\r\nfunction applyPhase(\r\n  outdatedSelfAcceptedModules: {\r\n    moduleId: ModuleId;\r\n    errorHandler: true | Function;\r\n  }[],\r\n  newModuleFactories: Map<ModuleId, ModuleFactory>,\r\n  outdatedModuleParents: Map<ModuleId, Array<ModuleId>>,\r\n  reportError: (err: any) => void\r\n) {\r\n  // Update module factories.\r\n  for (const [moduleId, factory] of newModuleFactories.entries()) {\r\n    moduleFactories[moduleId] = factory;\r\n  }\r\n\r\n  // TODO(alexkirsz) Run new runtime entries here.\r\n\r\n  // TODO(alexkirsz) Dependencies: call accept handlers for outdated deps.\r\n\r\n  // Re-instantiate all outdated self-accepted modules.\r\n  for (const { moduleId, errorHandler } of outdatedSelfAcceptedModules) {\r\n    try {\r\n      instantiateModule(moduleId, {\r\n        type: SourceType.Update,\r\n        parents: outdatedModuleParents.get(moduleId),\r\n      });\r\n    } catch (err) {\r\n      if (typeof errorHandler === \"function\") {\r\n        try {\r\n          errorHandler(err, { moduleId, module: devModuleCache[moduleId] });\r\n        } catch (err2) {\r\n          reportError(err2);\r\n          reportError(err);\r\n        }\r\n      } else {\r\n        reportError(err);\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\nfunction applyUpdate(update: PartialUpdate) {\r\n  switch (update.type) {\r\n    case \"ChunkListUpdate\":\r\n      applyChunkListUpdate(update);\r\n      break;\r\n    default:\r\n      invariant(update, (update) => `Unknown update type: ${update.type}`);\r\n  }\r\n}\r\n\r\nfunction applyChunkListUpdate(update: ChunkListUpdate) {\r\n  if (update.merged != null) {\r\n    for (const merged of update.merged) {\r\n      switch (merged.type) {\r\n        case \"EcmascriptMergedUpdate\":\r\n          applyEcmascriptMergedUpdate(merged);\r\n          break;\r\n        default:\r\n          invariant(merged, (merged) => `Unknown merged type: ${merged.type}`);\r\n      }\r\n    }\r\n  }\r\n\r\n  if (update.chunks != null) {\r\n    for (const [chunkPath, chunkUpdate] of Object.entries(update.chunks) as Array<[ChunkPath, ChunkUpdate]>) {\r\n      const chunkUrl = getChunkRelativeUrl(chunkPath);\r\n\r\n      switch (chunkUpdate.type) {\r\n        case \"added\":\r\n          BACKEND.loadChunk(chunkUrl, { type: SourceType.Update });\r\n          break;\r\n        case \"total\":\r\n          DEV_BACKEND.reloadChunk?.(chunkUrl);\r\n          break;\r\n        case \"deleted\":\r\n          DEV_BACKEND.unloadChunk?.(chunkUrl);\r\n          break;\r\n        case \"partial\":\r\n          invariant(\r\n            chunkUpdate.instruction,\r\n            (instruction) =>\r\n              `Unknown partial instruction: ${JSON.stringify(instruction)}.`\r\n          );\r\n          break;\r\n        default:\r\n          invariant(\r\n            chunkUpdate,\r\n            (chunkUpdate) => `Unknown chunk update type: ${chunkUpdate.type}`\r\n          );\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\nfunction applyEcmascriptMergedUpdate(update: EcmascriptMergedUpdate) {\r\n  const { entries = {}, chunks = {} } = update;\r\n  const { added, modified, chunksAdded, chunksDeleted } = computeChangedModules(\r\n    entries,\r\n    chunks\r\n  );\r\n  const { outdatedModules, newModuleFactories } = computeOutdatedModules(\r\n    added,\r\n    modified\r\n  );\r\n  const { disposedModules } = updateChunksPhase(chunksAdded, chunksDeleted);\r\n\r\n  applyInternal(outdatedModules, disposedModules, newModuleFactories);\r\n}\r\n\r\nfunction applyInvalidatedModules(outdatedModules: Set<ModuleId>) {\r\n  if (queuedInvalidatedModules.size > 0) {\r\n    computedInvalidatedModules(queuedInvalidatedModules).forEach((moduleId) => {\r\n      outdatedModules.add(moduleId);\r\n    });\r\n\r\n    queuedInvalidatedModules.clear();\r\n  }\r\n\r\n  return outdatedModules;\r\n}\r\n\r\nfunction applyInternal(\r\n  outdatedModules: Set<ModuleId>,\r\n  disposedModules: Iterable<ModuleId>,\r\n  newModuleFactories: Map<ModuleId, ModuleFactory>\r\n) {\r\n  outdatedModules = applyInvalidatedModules(outdatedModules);\r\n\r\n  const outdatedSelfAcceptedModules =\r\n    computeOutdatedSelfAcceptedModules(outdatedModules);\r\n\r\n  const { outdatedModuleParents } = disposePhase(\r\n    outdatedModules,\r\n    disposedModules\r\n  );\r\n\r\n  // we want to continue on error and only throw the error after we tried applying all updates\r\n  let error: any;\r\n\r\n  function reportError(err: any) {\r\n    if (!error) error = err;\r\n  }\r\n\r\n  applyPhase(\r\n    outdatedSelfAcceptedModules,\r\n    newModuleFactories,\r\n    outdatedModuleParents,\r\n    reportError\r\n  );\r\n\r\n  if (error) {\r\n    throw error;\r\n  }\r\n\r\n  if (queuedInvalidatedModules.size > 0) {\r\n    applyInternal(new Set(), [], new Map());\r\n  }\r\n}\r\n\r\nfunction computeChangedModules(\r\n  entries: Record<ModuleId, EcmascriptModuleEntry>,\r\n  updates: Record<ChunkPath, EcmascriptMergedChunkUpdate>\r\n): {\r\n  added: Map<ModuleId, EcmascriptModuleEntry | undefined>;\r\n  modified: Map<ModuleId, EcmascriptModuleEntry>;\r\n  deleted: Set<ModuleId>;\r\n  chunksAdded: Map<ChunkPath, Set<ModuleId>>;\r\n  chunksDeleted: Map<ChunkPath, Set<ModuleId>>;\r\n} {\r\n  const chunksAdded = new Map();\r\n  const chunksDeleted = new Map();\r\n  const added: Map<ModuleId, EcmascriptModuleEntry> = new Map();\r\n  const modified = new Map();\r\n  const deleted: Set<ModuleId> = new Set();\r\n\r\n  for (const [chunkPath, mergedChunkUpdate] of Object.entries(updates) as Array<[ChunkPath, EcmascriptMergedChunkUpdate]>) {\r\n    switch (mergedChunkUpdate.type) {\r\n      case \"added\": {\r\n        const updateAdded = new Set(mergedChunkUpdate.modules);\r\n        for (const moduleId of updateAdded) {\r\n          added.set(moduleId, entries[moduleId]);\r\n        }\r\n        chunksAdded.set(chunkPath, updateAdded);\r\n        break;\r\n      }\r\n      case \"deleted\": {\r\n        // We could also use `mergedChunkUpdate.modules` here.\r\n        const updateDeleted = new Set(chunkModulesMap.get(chunkPath));\r\n        for (const moduleId of updateDeleted) {\r\n          deleted.add(moduleId);\r\n        }\r\n        chunksDeleted.set(chunkPath, updateDeleted);\r\n        break;\r\n      }\r\n      case \"partial\": {\r\n        const updateAdded = new Set(mergedChunkUpdate.added);\r\n        const updateDeleted = new Set(mergedChunkUpdate.deleted);\r\n        for (const moduleId of updateAdded) {\r\n          added.set(moduleId, entries[moduleId]);\r\n        }\r\n        for (const moduleId of updateDeleted) {\r\n          deleted.add(moduleId);\r\n        }\r\n        chunksAdded.set(chunkPath, updateAdded);\r\n        chunksDeleted.set(chunkPath, updateDeleted);\r\n        break;\r\n      }\r\n      default:\r\n        invariant(\r\n          mergedChunkUpdate,\r\n          (mergedChunkUpdate) =>\r\n            `Unknown merged chunk update type: ${mergedChunkUpdate.type}`\r\n        );\r\n    }\r\n  }\r\n\r\n  // If a module was added from one chunk and deleted from another in the same update,\r\n  // consider it to be modified, as it means the module was moved from one chunk to another\r\n  // AND has new code in a single update.\r\n  for (const moduleId of added.keys()) {\r\n    if (deleted.has(moduleId)) {\r\n      added.delete(moduleId);\r\n      deleted.delete(moduleId);\r\n    }\r\n  }\r\n\r\n  for (const [moduleId, entry] of Object.entries(entries)) {\r\n    // Modules that haven't been added to any chunk but have new code are considered\r\n    // to be modified.\r\n    // This needs to be under the previous loop, as we need it to get rid of modules\r\n    // that were added and deleted in the same update.\r\n    if (!added.has(moduleId)) {\r\n      modified.set(moduleId, entry);\r\n    }\r\n  }\r\n\r\n  return { added, deleted, modified, chunksAdded, chunksDeleted };\r\n}\r\n\r\ntype ModuleEffect =\r\n  | {\r\n      type: \"unaccepted\";\r\n      dependencyChain: ModuleId[];\r\n    }\r\n  | {\r\n      type: \"self-declined\";\r\n      dependencyChain: ModuleId[];\r\n      moduleId: ModuleId;\r\n    }\r\n  | {\r\n      type: \"accepted\";\r\n      moduleId: ModuleId;\r\n      outdatedModules: Set<ModuleId>;\r\n    };\r\n\r\nfunction getAffectedModuleEffects(moduleId: ModuleId): ModuleEffect {\r\n  const outdatedModules: Set<ModuleId> = new Set();\r\n\r\n  type QueueItem = { moduleId?: ModuleId; dependencyChain: ModuleId[] };\r\n\r\n  const queue: QueueItem[] = [\r\n    {\r\n      moduleId,\r\n      dependencyChain: [],\r\n    },\r\n  ];\r\n\r\n  let nextItem;\r\n  while ((nextItem = queue.shift())) {\r\n    const { moduleId, dependencyChain } = nextItem;\r\n\r\n    if (moduleId != null) {\r\n      if (outdatedModules.has(moduleId)) {\r\n        // Avoid infinite loops caused by cycles between modules in the dependency chain.\r\n        continue;\r\n      }\r\n\r\n      outdatedModules.add(moduleId);\r\n    }\r\n\r\n    // We've arrived at the runtime of the chunk, which means that nothing\r\n    // else above can accept this update.\r\n    if (moduleId === undefined) {\r\n      return {\r\n        type: \"unaccepted\",\r\n        dependencyChain,\r\n      };\r\n    }\r\n\r\n    const module = devModuleCache[moduleId];\r\n    const hotState = moduleHotState.get(module)!;\r\n\r\n    if (\r\n      // The module is not in the cache. Since this is a \"modified\" update,\r\n      // it means that the module was never instantiated before.\r\n      !module || // The module accepted itself without invalidating globalThis.\r\n      // TODO is that right?\r\n      (hotState.selfAccepted && !hotState.selfInvalidated)\r\n    ) {\r\n      continue;\r\n    }\r\n\r\n    if (hotState.selfDeclined) {\r\n      return {\r\n        type: \"self-declined\",\r\n        dependencyChain,\r\n        moduleId,\r\n      };\r\n    }\r\n\r\n    if (runtimeModules.has(moduleId)) {\r\n      queue.push({\r\n        moduleId: undefined,\r\n        dependencyChain: [...dependencyChain, moduleId],\r\n      });\r\n      continue;\r\n    }\r\n\r\n    for (const parentId of module.parents) {\r\n      const parent = devModuleCache[parentId];\r\n\r\n      if (!parent) {\r\n        // TODO(alexkirsz) Is this even possible?\r\n        continue;\r\n      }\r\n\r\n      // TODO(alexkirsz) Dependencies: check accepted and declined\r\n      // dependencies here.\r\n\r\n      queue.push({\r\n        moduleId: parentId,\r\n        dependencyChain: [...dependencyChain, moduleId],\r\n      });\r\n    }\r\n  }\r\n\r\n  return {\r\n    type: \"accepted\",\r\n    moduleId,\r\n    outdatedModules,\r\n  };\r\n}\r\n\r\nfunction handleApply(chunkListPath: ChunkListPath, update: ServerMessage) {\r\n  switch (update.type) {\r\n    case \"partial\": {\r\n      // This indicates that the update is can be applied to the current state of the application.\r\n      applyUpdate(update.instruction);\r\n      break;\r\n    }\r\n    case \"restart\": {\r\n      // This indicates that there is no way to apply the update to the\r\n      // current state of the application, and that the application must be\r\n      // restarted.\r\n      DEV_BACKEND.restart();\r\n      break;\r\n    }\r\n    case \"notFound\": {\r\n      // This indicates that the chunk list no longer exists: either the dynamic import which created it was removed,\r\n      // or the page itself was deleted.\r\n      // If it is a dynamic import, we simply discard all modules that the chunk has exclusive access to.\r\n      // If it is a runtime chunk list, we restart the application.\r\n      if (runtimeChunkLists.has(chunkListPath)) {\r\n        DEV_BACKEND.restart();\r\n      } else {\r\n        disposeChunkList(chunkListPath);\r\n      }\r\n      break;\r\n    }\r\n    default:\r\n      throw new Error(`Unknown update type: ${update.type}`);\r\n  }\r\n}\r\n\r\nfunction createModuleHot(\r\n  moduleId: ModuleId,\r\n  hotData: HotData\r\n): { hot: Hot; hotState: HotState } {\r\n  const hotState: HotState = {\r\n    selfAccepted: false,\r\n    selfDeclined: false,\r\n    selfInvalidated: false,\r\n    disposeHandlers: [],\r\n  };\r\n\r\n  const hot: Hot = {\r\n    // TODO(alexkirsz) This is not defined in the HMR API. It was used to\r\n    // decide whether to warn whenever an HMR-disposed module required other\r\n    // modules. We might want to remove it.\r\n    active: true,\r\n\r\n    data: hotData ?? {},\r\n\r\n    // TODO(alexkirsz) Support full (dep, callback, errorHandler) form.\r\n    accept: (\r\n      modules?: string | string[] | AcceptErrorHandler,\r\n      _callback?: AcceptCallback,\r\n      _errorHandler?: AcceptErrorHandler\r\n    ) => {\r\n      if (modules === undefined) {\r\n        hotState.selfAccepted = true;\r\n      } else if (typeof modules === \"function\") {\r\n        hotState.selfAccepted = modules;\r\n      } else {\r\n        throw new Error(\"unsupported `accept` signature\");\r\n      }\r\n    },\r\n\r\n    decline: (dep) => {\r\n      if (dep === undefined) {\r\n        hotState.selfDeclined = true;\r\n      } else {\r\n        throw new Error(\"unsupported `decline` signature\");\r\n      }\r\n    },\r\n\r\n    dispose: (callback) => {\r\n      hotState.disposeHandlers.push(callback);\r\n    },\r\n\r\n    addDisposeHandler: (callback) => {\r\n      hotState.disposeHandlers.push(callback);\r\n    },\r\n\r\n    removeDisposeHandler: (callback) => {\r\n      const idx = hotState.disposeHandlers.indexOf(callback);\r\n      if (idx >= 0) {\r\n        hotState.disposeHandlers.splice(idx, 1);\r\n      }\r\n    },\r\n\r\n    invalidate: () => {\r\n      hotState.selfInvalidated = true;\r\n      queuedInvalidatedModules.add(moduleId);\r\n    },\r\n\r\n    // NOTE(alexkirsz) This is part of the management API, which we don't\r\n    // implement, but the Next.js React Refresh runtime uses this to decide\r\n    // whether to schedule an update.\r\n    status: () => \"idle\",\r\n\r\n    // NOTE(alexkirsz) Since we always return \"idle\" for now, these are no-ops.\r\n    addStatusHandler: (_handler) => {},\r\n    removeStatusHandler: (_handler) => {},\r\n\r\n    // NOTE(jridgewell) Check returns the list of updated modules, but we don't\r\n    // want the webpack code paths to ever update (the turbopack paths handle\r\n    // this already).\r\n    check: () => Promise.resolve(null),\r\n  };\r\n\r\n  return { hot, hotState };\r\n}\r\n\r\n/**\r\n * Removes a module from a chunk.\r\n * Returns `true` if there are no remaining chunks including this module.\r\n */\r\nfunction removeModuleFromChunk(\r\n  moduleId: ModuleId,\r\n  chunkPath: ChunkPath\r\n): boolean {\r\n  const moduleChunks = moduleChunksMap.get(moduleId)!;\r\n  moduleChunks.delete(chunkPath);\r\n\r\n  const chunkModules = chunkModulesMap.get(chunkPath)!;\r\n  chunkModules.delete(moduleId);\r\n\r\n  const noRemainingModules = chunkModules.size === 0;\r\n  if (noRemainingModules) {\r\n    chunkModulesMap.delete(chunkPath);\r\n  }\r\n\r\n  const noRemainingChunks = moduleChunks.size === 0;\r\n  if (noRemainingChunks) {\r\n    moduleChunksMap.delete(moduleId);\r\n  }\r\n\r\n  return noRemainingChunks;\r\n}\r\n\r\n/**\r\n * Disposes of a chunk list and its corresponding exclusive chunks.\r\n */\r\nfunction disposeChunkList(chunkListPath: ChunkListPath): boolean {\r\n  const chunkPaths = chunkListChunksMap.get(chunkListPath);\r\n  if (chunkPaths == null) {\r\n    return false;\r\n  }\r\n  chunkListChunksMap.delete(chunkListPath);\r\n\r\n  for (const chunkPath of chunkPaths) {\r\n    const chunkChunkLists = chunkChunkListsMap.get(chunkPath)!;\r\n    chunkChunkLists.delete(chunkListPath);\r\n\r\n    if (chunkChunkLists.size === 0) {\r\n      chunkChunkListsMap.delete(chunkPath);\r\n      disposeChunk(chunkPath);\r\n    }\r\n  }\r\n\r\n  // We must also dispose of the chunk list's chunk itself to ensure it may\r\n  // be reloaded properly in the future.\r\n  const chunkListUrl = getChunkRelativeUrl(chunkListPath)\r\n\r\n  DEV_BACKEND.unloadChunk?.(chunkListUrl);\r\n\r\n  return true;\r\n}\r\n\r\n/**\r\n * Disposes of a chunk and its corresponding exclusive modules.\r\n *\r\n * @returns Whether the chunk was disposed of.\r\n */\r\nfunction disposeChunk(chunkPath: ChunkPath): boolean {\r\n  const chunkUrl = getChunkRelativeUrl(chunkPath)\r\n  // This should happen whether the chunk has any modules in it or not.\r\n  // For instance, CSS chunks have no modules in them, but they still need to be unloaded.\r\n  DEV_BACKEND.unloadChunk?.(chunkUrl);\r\n\r\n  const chunkModules = chunkModulesMap.get(chunkPath);\r\n  if (chunkModules == null) {\r\n    return false;\r\n  }\r\n  chunkModules.delete(chunkPath);\r\n\r\n  for (const moduleId of chunkModules) {\r\n    const moduleChunks = moduleChunksMap.get(moduleId)!;\r\n    moduleChunks.delete(chunkPath);\r\n\r\n    const noRemainingChunks = moduleChunks.size === 0;\r\n    if (noRemainingChunks) {\r\n      moduleChunksMap.delete(moduleId);\r\n      disposeModule(moduleId, \"clear\");\r\n      availableModules.delete(moduleId);\r\n    }\r\n  }\r\n\r\n  return true;\r\n}\r\n\r\n\r\n/**\r\n * Subscribes to chunk list updates from the update server and applies them.\r\n */\r\nfunction registerChunkList(\r\n  chunkList: ChunkList\r\n) {\r\n  const chunkListScript = chunkList.script;\r\n  const chunkListPath = getPathFromScript(chunkListScript);\r\n  // The \"chunk\" is also registered to finish the loading in the backend\r\n  BACKEND.registerChunk(chunkListPath as string as ChunkPath);\r\n  globalThis.TURBOPACK_CHUNK_UPDATE_LISTENERS!.push([\r\n    chunkListPath,\r\n    handleApply.bind(null, chunkListPath),\r\n  ]);\r\n\r\n  // Adding chunks to chunk lists and vice versa.\r\n  const chunkPaths = new Set(chunkList.chunks.map(getChunkPath));\r\n  chunkListChunksMap.set(chunkListPath, chunkPaths);\r\n  for (const chunkPath of chunkPaths) {\r\n    let chunkChunkLists = chunkChunkListsMap.get(chunkPath);\r\n    if (!chunkChunkLists) {\r\n      chunkChunkLists = new Set([chunkListPath]);\r\n      chunkChunkListsMap.set(chunkPath, chunkChunkLists);\r\n    } else {\r\n      chunkChunkLists.add(chunkListPath);\r\n    }\r\n  }\r\n\r\n  if (chunkList.source === \"entry\") {\r\n    markChunkListAsRuntime(chunkListPath);\r\n  }\r\n}\r\n\r\nglobalThis.TURBOPACK_CHUNK_UPDATE_LISTENERS ??= [];\r\n"], "names": [], "mappings": "AAAA,2CAA2C;AAC3C,4CAA4C;AAC5C,4CAA4C;AAE5C;;;;;;CAMC,GAED,oDAAoD,GAEpD,MAAM,iBAAyC,OAAO,MAAM,CAAC;AAwC7D,MAAM,yBAAyB;IAC7B,OAAO,mBAAmB;IAE1B,gBAA0B;IAE1B,YAAY,OAAe,EAAE,eAAyB,CAAE;QACtD,KAAK,CAAC;QACN,IAAI,CAAC,eAAe,GAAG;IACzB;AACF;AAEA;;;CAGC,GACD,MAAM,gBAAwC,IAAI;AAClD;;CAEC,GACD,MAAM,iBAAwC,IAAI;AAClD;;CAEC,GACD,MAAM,2BAA0C,IAAI;AAEpD;;CAEC,GACD,aAAa;AACb,SAAS,8BACP,QAAkB,EAClB,SAAoB;IAEpB,MAAM,SAAS,cAAc,CAAC,SAAS;IACvC,IAAI,QAAQ;QACV,IAAI,OAAO,KAAK,EAAE;YAChB,MAAM,OAAO,KAAK;QACpB;QACA,OAAO;IACT;IAEA,aAAa;IACb,OAAO,kBAAkB,UAAU;QAAE,MAAM,WAAW,OAAO;QAAE;IAAU;AAC3E;AAEA;;CAEC,GACD,2CAA2C;AAC3C,MAAM,mCAAgF,CACpF,IACA;IAEA,IAAI,CAAC,aAAa,GAAG,CAAC,MAAM,EAAE;QAC5B,QAAQ,IAAI,CACV,CAAC,4BAA4B,EAAE,GAAG,aAAa,EAAE,aAAa,EAAE,CAAC,oCAAoC,CAAC;IAE1G;IAEA,MAAM,SAAS,cAAc,CAAC,GAAG;IAEjC,IAAI,aAAa,QAAQ,CAAC,OAAO,CAAC,QAAQ,CAAC,GAAG;QAC5C,aAAa,QAAQ,CAAC,IAAI,CAAC;IAC7B;IAEA,IAAI,QAAQ;QACV,IAAI,OAAO,OAAO,CAAC,OAAO,CAAC,aAAa,EAAE,MAAM,CAAC,GAAG;YAClD,OAAO,OAAO,CAAC,IAAI,CAAC,aAAa,EAAE;QACrC;QAEA,OAAO;IACT;IAEA,OAAO,kBAAkB,IAAI;QAC3B,MAAM,WAAW,MAAM;QACvB,UAAU,aAAa,EAAE;IAC3B;AACF;AAEA,0CAA0C;AAC1C,SAAS,kBAAkB,EAAY,EAAE,MAAkB;IACzD,MAAM,gBAAgB,eAAe,CAAC,GAAG;IACzC,IAAI,OAAO,kBAAkB,YAAY;QACvC,sEAAsE;QACtE,0EAA0E;QAC1E,mDAAmD;QACnD,IAAI;QACJ,OAAQ,OAAO,IAAI;YACjB,KAAK,WAAW,OAAO;gBACrB,sBAAsB,CAAC,4BAA4B,EAAE,OAAO,SAAS,EAAE;gBACvE;YACF,KAAK,WAAW,MAAM;gBACpB,sBAAsB,CAAC,oCAAoC,EAAE,OAAO,QAAQ,EAAE;gBAC9E;YACF,KAAK,WAAW,MAAM;gBACpB,sBAAsB;gBACtB;YACF;gBACE,UAAU,QAAQ,CAAC,SAAW,CAAC,qBAAqB,EAAE,QAAQ,MAAM;QACxE;QACA,MAAM,IAAI,MACR,CAAC,OAAO,EAAE,GAAG,kBAAkB,EAAE,oBAAoB,uFAAuF,CAAC;IAEjJ;IAEA,MAAM,UAAU,cAAc,GAAG,CAAC;IAClC,MAAM,EAAE,GAAG,EAAE,QAAQ,EAAE,GAAG,gBAAgB,IAAI;IAE9C,IAAI;IACJ,OAAQ,OAAO,IAAI;QACjB,KAAK,WAAW,OAAO;YACrB,eAAe,GAAG,CAAC;YACnB,UAAU,EAAE;YACZ;QACF,KAAK,WAAW,MAAM;YACpB,wEAAwE;YACxE,wEAAwE;YACxE,UAAU;gBAAC,OAAO,QAAQ;aAAC;YAC3B;QACF,KAAK,WAAW,MAAM;YACpB,UAAU,OAAO,OAAO,IAAI,EAAE;YAC9B;QACF;YACE,UAAU,QAAQ,CAAC,SAAW,CAAC,qBAAqB,EAAE,QAAQ,MAAM;IACxE;IAEA,MAAM,SAAoB;QACxB,SAAS,CAAC;QACV,OAAO;QACP,QAAQ;QACR;QACA;QACA,UAAU,EAAE;QACZ,iBAAiB;QACjB;IACF;IAEA,cAAc,CAAC,GAAG,GAAG;IACrB,eAAe,GAAG,CAAC,QAAQ;IAE3B,4EAA4E;IAC5E,IAAI;QACF,MAAM,aAAyB;YAAE,MAAM,WAAW,MAAM;YAAE,UAAU;QAAG;QAEvE,wBAAwB,QAAQ,CAAC;YAC/B,MAAM,IAAI,gBAAgB,IAAI,CAAC,MAAM;YACrC,cAAc,IAAI,CAChB,OAAO,OAAO,EACd,eAAe;gBACb,GAAG,YAAY,IAAI,CAAC,MAAM;gBAC1B,GAAG,OAAO,OAAO;gBACjB,GAAG,gBAAgB,IAAI,CAAC,MAAM;gBAC9B,GAAG;gBACH,GAAG;gBACH,GAAG,UAAU,IAAI,CAAC,MAAM;gBACxB,GAAG,UAAU,IAAI,CAAC,MAAM,QAAQ,OAAO,OAAO;gBAC9C,GAAG,cAAc,IAAI,CAAC,MAAM,QAAQ,OAAO,OAAO;gBAClD,GAAG,YAAY,IAAI,CAAC,MAAM;gBAC1B,GAAG,gBAAgB,IAAI,CAAC,MAAM;gBAC9B,GAAG;gBACH,GAAG;gBACH,GAAG;gBACH,GAAG,UAAU,IAAI,CAAC,MAAM;gBACxB,GAAG,eAAe,IAAI,CAAC,MAAM;gBAC7B,GAAG,gBAAgB,IAAI,CAAC,MAAM;gBAC9B,GAAG,sBAAsB,IAAI,CAAC,MAAM;gBACpC,GAAG;gBACH,GAAG;gBACH,GAAG;gBACH,GAAG;gBACH,GAAG,4BAA4B;gBAC/B,GAAG;gBACH,GAAG;gBACH,GAAG,OAAO,OAAO,EAAE,KAAK,WAAW,OAAO,EAAE,CAAC,OAAO,CAAC,cAAc,MAAM,OAAO,EAAE;YACpF;QAEJ;IACF,EAAE,OAAO,OAAO;QACd,OAAO,KAAK,GAAG;QACf,MAAM;IACR;IAEA,OAAO,MAAM,GAAG;IAChB,IAAI,OAAO,eAAe,IAAI,OAAO,OAAO,KAAK,OAAO,eAAe,EAAE;QACvE,yDAAyD;QACzD,WAAW,OAAO,OAAO,EAAE,OAAO,eAAe;IACnD;IAEA,OAAO;AACT;AAEA;;;;CAIC,GACD,SAAS,wBACP,MAAc,EACd,aAA4C;IAE5C,MAAM,+BACJ,OAAO,WAAW,iCAAiC,KAAK,aACpD,WAAW,iCAAiC,CAAC,OAAO,EAAE,IACtD,KAAO;IAEb,IAAI;QACF,cAAc;YACZ,UAAU,WAAW,YAAY;YACjC,WAAW,WAAW,YAAY;YAClC,iBAAiB;QACnB;IACF,EAAE,OAAO,GAAG;QACV,MAAM;IACR,SAAU;QACR,iEAAiE;QACjE;IACF;AACF;AAEA;;CAEC,GACD,SAAS,+CACP,MAAiB,EACjB,OAAuB;IAEvB,MAAM,iBAAiB,OAAO,OAAO;IACrC,MAAM,cAAc,OAAO,GAAG,CAAC,IAAI,CAAC,WAAW,IAAI;IAEnD,QAAQ,8BAA8B,CAAC,gBAAgB,OAAO,EAAE;IAEhE,yEAAyE;IACzE,4BAA4B;IAC5B,IAAI,QAAQ,sBAAsB,CAAC,iBAAiB;QAClD,sEAAsE;QACtE,cAAc;QACd,OAAO,GAAG,CAAC,OAAO,CAAC,CAAC;YAClB,KAAK,WAAW,GAAG;QACrB;QACA,uEAAuE;QACvE,kCAAkC;QAClC,OAAO,GAAG,CAAC,MAAM;QAEjB,mEAAmE;QACnE,yEAAyE;QACzE,qBAAqB;QACrB,IAAI,gBAAgB,MAAM;YACxB,mEAAmE;YACnE,6BAA6B;YAC7B,EAAE;YACF,+DAA+D;YAC/D,kEAAkE;YAClE,8DAA8D;YAC9D,gDAAgD;YAChD,IACE,QAAQ,oCAAoC,CAC1C,QAAQ,2BAA2B,CAAC,cACpC,QAAQ,2BAA2B,CAAC,kBAEtC;gBACA,OAAO,GAAG,CAAC,UAAU;YACvB,OAAO;gBACL,QAAQ,cAAc;YACxB;QACF;IACF,OAAO;QACL,yEAAyE;QACzE,uDAAuD;QACvD,oEAAoE;QACpE,oEAAoE;QACpE,MAAM,sBAAsB,gBAAgB;QAC5C,IAAI,qBAAqB;YACvB,OAAO,GAAG,CAAC,UAAU;QACvB;IACF;AACF;AAEA,SAAS,sBAAsB,eAA2B;IACxD,OAAO,CAAC,kBAAkB,EAAE,gBAAgB,IAAI,CAAC,SAAS;AAC5D;AAEA,SAAS,uBACP,KAAuD,EACvD,QAA8C;IAK9C,MAAM,qBAAqB,IAAI;IAE/B,KAAK,MAAM,CAAC,UAAU,MAAM,IAAI,MAAO;QACrC,IAAI,SAAS,MAAM;YACjB,mBAAmB,GAAG,CAAC,UAAU,MAAM;QACzC;IACF;IAEA,MAAM,kBAAkB,2BAA2B,SAAS,IAAI;IAEhE,KAAK,MAAM,CAAC,UAAU,MAAM,IAAI,SAAU;QACxC,mBAAmB,GAAG,CAAC,UAAU,MAAM;IACzC;IAEA,OAAO;QAAE;QAAiB;IAAmB;AAC/C;AAEA,SAAS,2BACP,WAA+B;IAE/B,MAAM,kBAAkB,IAAI;IAE5B,KAAK,MAAM,YAAY,YAAa;QAClC,MAAM,SAAS,yBAAyB;QAExC,OAAQ,OAAO,IAAI;YACjB,KAAK;gBACH,MAAM,IAAI,iBACR,CAAC,wCAAwC,EAAE,sBACzC,OAAO,eAAe,EACtB,CAAC,CAAC,EACJ,OAAO,eAAe;YAE1B,KAAK;gBACH,MAAM,IAAI,iBACR,CAAC,2CAA2C,EAAE,sBAC5C,OAAO,eAAe,EACtB,CAAC,CAAC,EACJ,OAAO,eAAe;YAE1B,KAAK;gBACH,KAAK,MAAM,oBAAoB,OAAO,eAAe,CAAE;oBACrD,gBAAgB,GAAG,CAAC;gBACtB;gBACA;YACF,6DAA6D;YAC7D;gBACE,UAAU,QAAQ,CAAC,SAAW,CAAC,qBAAqB,EAAE,QAAQ,MAAM;QACxE;IACF;IAEA,OAAO;AACT;AAEA,SAAS,mCACP,eAAmC;IAEnC,MAAM,8BAAuF,EAAE;IAC/F,KAAK,MAAM,YAAY,gBAAiB;QACtC,MAAM,SAAS,cAAc,CAAC,SAAS;QACvC,MAAM,WAAW,eAAe,GAAG,CAAC;QACpC,IAAI,UAAU,SAAS,YAAY,IAAI,CAAC,SAAS,eAAe,EAAE;YAChE,4BAA4B,IAAI,CAAC;gBAC/B;gBACA,cAAc,SAAS,YAAY;YACrC;QACF;IACF;IACA,OAAO;AACT;AAEA;;;;CAIC,GACD,SAAS,kBACP,kBAAiD,EACjD,oBAAmD;IAEnD,KAAK,MAAM,CAAC,WAAW,eAAe,IAAI,mBAAoB;QAC5D,KAAK,MAAM,YAAY,eAAgB;YACrC,iBAAiB,UAAU;QAC7B;IACF;IAEA,MAAM,kBAAiC,IAAI;IAC3C,KAAK,MAAM,CAAC,WAAW,eAAe,IAAI,qBAAsB;QAC9D,KAAK,MAAM,YAAY,eAAgB;YACrC,IAAI,sBAAsB,UAAU,YAAY;gBAC9C,gBAAgB,GAAG,CAAC;YACtB;QACF;IACF;IAEA,OAAO;QAAE;IAAgB;AAC3B;AAEA,SAAS,aACP,eAAmC,EACnC,eAAmC;IAEnC,KAAK,MAAM,YAAY,gBAAiB;QACtC,cAAc,UAAU;IAC1B;IAEA,KAAK,MAAM,YAAY,gBAAiB;QACtC,cAAc,UAAU;IAC1B;IAEA,6DAA6D;IAC7D,0EAA0E;IAC1E,MAAM,wBAAwB,IAAI;IAClC,KAAK,MAAM,YAAY,gBAAiB;QACtC,MAAM,YAAY,cAAc,CAAC,SAAS;QAC1C,sBAAsB,GAAG,CAAC,UAAU,WAAW;QAC/C,OAAO,cAAc,CAAC,SAAS;IACjC;IAEA,uEAAuE;IACvE,YAAY;IAEZ,OAAO;QAAE;IAAsB;AACjC;AAEA;;;;;;;;;;;;CAYC,GACD,SAAS,cAAc,QAAkB,EAAE,IAAyB;IAClE,MAAM,SAAS,cAAc,CAAC,SAAS;IACvC,IAAI,CAAC,QAAQ;QACX;IACF;IAEA,MAAM,WAAW,eAAe,GAAG,CAAC;IACpC,MAAM,OAAO,CAAC;IAEd,mEAAmE;IACnE,qBAAqB;IACrB,KAAK,MAAM,kBAAkB,SAAS,eAAe,CAAE;QACrD,eAAe;IACjB;IAEA,0EAA0E;IAC1E,2CAA2C;IAC3C,OAAO,GAAG,CAAC,MAAM,GAAG;IAEpB,eAAe,MAAM,CAAC;IAEtB,sEAAsE;IAEtE,8DAA8D;IAC9D,wEAAwE;IACxE,kBAAkB;IAClB,KAAK,MAAM,WAAW,OAAO,QAAQ,CAAE;QACrC,MAAM,QAAQ,cAAc,CAAC,QAAQ;QACrC,IAAI,CAAC,OAAO;YACV;QACF;QAEA,MAAM,MAAM,MAAM,OAAO,CAAC,OAAO,CAAC,OAAO,EAAE;QAC3C,IAAI,OAAO,GAAG;YACZ,MAAM,OAAO,CAAC,MAAM,CAAC,KAAK;QAC5B;IACF;IAEA,OAAQ;QACN,KAAK;YACH,OAAO,cAAc,CAAC,OAAO,EAAE,CAAC;YAChC,cAAc,MAAM,CAAC,OAAO,EAAE;YAC9B;QACF,KAAK;YACH,cAAc,GAAG,CAAC,OAAO,EAAE,EAAE;YAC7B;QACF;YACE,UAAU,MAAM,CAAC,OAAS,CAAC,cAAc,EAAE,MAAM;IACrD;AACF;AAEA,SAAS,WACP,2BAGG,EACH,kBAAgD,EAChD,qBAAqD,EACrD,WAA+B;IAE/B,2BAA2B;IAC3B,KAAK,MAAM,CAAC,UAAU,QAAQ,IAAI,mBAAmB,OAAO,GAAI;QAC9D,eAAe,CAAC,SAAS,GAAG;IAC9B;IAEA,gDAAgD;IAEhD,wEAAwE;IAExE,qDAAqD;IACrD,KAAK,MAAM,EAAE,QAAQ,EAAE,YAAY,EAAE,IAAI,4BAA6B;QACpE,IAAI;YACF,kBAAkB,UAAU;gBAC1B,MAAM,WAAW,MAAM;gBACvB,SAAS,sBAAsB,GAAG,CAAC;YACrC;QACF,EAAE,OAAO,KAAK;YACZ,IAAI,OAAO,iBAAiB,YAAY;gBACtC,IAAI;oBACF,aAAa,KAAK;wBAAE;wBAAU,QAAQ,cAAc,CAAC,SAAS;oBAAC;gBACjE,EAAE,OAAO,MAAM;oBACb,YAAY;oBACZ,YAAY;gBACd;YACF,OAAO;gBACL,YAAY;YACd;QACF;IACF;AACF;AAEA,SAAS,YAAY,MAAqB;IACxC,OAAQ,OAAO,IAAI;QACjB,KAAK;YACH,qBAAqB;YACrB;QACF;YACE,UAAU,QAAQ,CAAC,SAAW,CAAC,qBAAqB,EAAE,OAAO,IAAI,EAAE;IACvE;AACF;AAEA,SAAS,qBAAqB,MAAuB;IACnD,IAAI,OAAO,MAAM,IAAI,MAAM;QACzB,KAAK,MAAM,UAAU,OAAO,MAAM,CAAE;YAClC,OAAQ,OAAO,IAAI;gBACjB,KAAK;oBACH,4BAA4B;oBAC5B;gBACF;oBACE,UAAU,QAAQ,CAAC,SAAW,CAAC,qBAAqB,EAAE,OAAO,IAAI,EAAE;YACvE;QACF;IACF;IAEA,IAAI,OAAO,MAAM,IAAI,MAAM;QACzB,KAAK,MAAM,CAAC,WAAW,YAAY,IAAI,OAAO,OAAO,CAAC,OAAO,MAAM,EAAsC;YACvG,MAAM,WAAW,oBAAoB;YAErC,OAAQ,YAAY,IAAI;gBACtB,KAAK;oBACH,QAAQ,SAAS,CAAC,UAAU;wBAAE,MAAM,WAAW,MAAM;oBAAC;oBACtD;gBACF,KAAK;oBACH,YAAY,WAAW,GAAG;oBAC1B;gBACF,KAAK;oBACH,YAAY,WAAW,GAAG;oBAC1B;gBACF,KAAK;oBACH,UACE,YAAY,WAAW,EACvB,CAAC,cACC,CAAC,6BAA6B,EAAE,KAAK,SAAS,CAAC,aAAa,CAAC,CAAC;oBAElE;gBACF;oBACE,UACE,aACA,CAAC,cAAgB,CAAC,2BAA2B,EAAE,YAAY,IAAI,EAAE;YAEvE;QACF;IACF;AACF;AAEA,SAAS,4BAA4B,MAA8B;IACjE,MAAM,EAAE,UAAU,CAAC,CAAC,EAAE,SAAS,CAAC,CAAC,EAAE,GAAG;IACtC,MAAM,EAAE,KAAK,EAAE,QAAQ,EAAE,WAAW,EAAE,aAAa,EAAE,GAAG,sBACtD,SACA;IAEF,MAAM,EAAE,eAAe,EAAE,kBAAkB,EAAE,GAAG,uBAC9C,OACA;IAEF,MAAM,EAAE,eAAe,EAAE,GAAG,kBAAkB,aAAa;IAE3D,cAAc,iBAAiB,iBAAiB;AAClD;AAEA,SAAS,wBAAwB,eAA8B;IAC7D,IAAI,yBAAyB,IAAI,GAAG,GAAG;QACrC,2BAA2B,0BAA0B,OAAO,CAAC,CAAC;YAC5D,gBAAgB,GAAG,CAAC;QACtB;QAEA,yBAAyB,KAAK;IAChC;IAEA,OAAO;AACT;AAEA,SAAS,cACP,eAA8B,EAC9B,eAAmC,EACnC,kBAAgD;IAEhD,kBAAkB,wBAAwB;IAE1C,MAAM,8BACJ,mCAAmC;IAErC,MAAM,EAAE,qBAAqB,EAAE,GAAG,aAChC,iBACA;IAGF,4FAA4F;IAC5F,IAAI;IAEJ,SAAS,YAAY,GAAQ;QAC3B,IAAI,CAAC,OAAO,QAAQ;IACtB;IAEA,WACE,6BACA,oBACA,uBACA;IAGF,IAAI,OAAO;QACT,MAAM;IACR;IAEA,IAAI,yBAAyB,IAAI,GAAG,GAAG;QACrC,cAAc,IAAI,OAAO,EAAE,EAAE,IAAI;IACnC;AACF;AAEA,SAAS,sBACP,OAAgD,EAChD,OAAuD;IAQvD,MAAM,cAAc,IAAI;IACxB,MAAM,gBAAgB,IAAI;IAC1B,MAAM,QAA8C,IAAI;IACxD,MAAM,WAAW,IAAI;IACrB,MAAM,UAAyB,IAAI;IAEnC,KAAK,MAAM,CAAC,WAAW,kBAAkB,IAAI,OAAO,OAAO,CAAC,SAA6D;QACvH,OAAQ,kBAAkB,IAAI;YAC5B,KAAK;gBAAS;oBACZ,MAAM,cAAc,IAAI,IAAI,kBAAkB,OAAO;oBACrD,KAAK,MAAM,YAAY,YAAa;wBAClC,MAAM,GAAG,CAAC,UAAU,OAAO,CAAC,SAAS;oBACvC;oBACA,YAAY,GAAG,CAAC,WAAW;oBAC3B;gBACF;YACA,KAAK;gBAAW;oBACd,sDAAsD;oBACtD,MAAM,gBAAgB,IAAI,IAAI,gBAAgB,GAAG,CAAC;oBAClD,KAAK,MAAM,YAAY,cAAe;wBACpC,QAAQ,GAAG,CAAC;oBACd;oBACA,cAAc,GAAG,CAAC,WAAW;oBAC7B;gBACF;YACA,KAAK;gBAAW;oBACd,MAAM,cAAc,IAAI,IAAI,kBAAkB,KAAK;oBACnD,MAAM,gBAAgB,IAAI,IAAI,kBAAkB,OAAO;oBACvD,KAAK,MAAM,YAAY,YAAa;wBAClC,MAAM,GAAG,CAAC,UAAU,OAAO,CAAC,SAAS;oBACvC;oBACA,KAAK,MAAM,YAAY,cAAe;wBACpC,QAAQ,GAAG,CAAC;oBACd;oBACA,YAAY,GAAG,CAAC,WAAW;oBAC3B,cAAc,GAAG,CAAC,WAAW;oBAC7B;gBACF;YACA;gBACE,UACE,mBACA,CAAC,oBACC,CAAC,kCAAkC,EAAE,kBAAkB,IAAI,EAAE;QAErE;IACF;IAEA,oFAAoF;IACpF,yFAAyF;IACzF,uCAAuC;IACvC,KAAK,MAAM,YAAY,MAAM,IAAI,GAAI;QACnC,IAAI,QAAQ,GAAG,CAAC,WAAW;YACzB,MAAM,MAAM,CAAC;YACb,QAAQ,MAAM,CAAC;QACjB;IACF;IAEA,KAAK,MAAM,CAAC,UAAU,MAAM,IAAI,OAAO,OAAO,CAAC,SAAU;QACvD,gFAAgF;QAChF,kBAAkB;QAClB,gFAAgF;QAChF,kDAAkD;QAClD,IAAI,CAAC,MAAM,GAAG,CAAC,WAAW;YACxB,SAAS,GAAG,CAAC,UAAU;QACzB;IACF;IAEA,OAAO;QAAE;QAAO;QAAS;QAAU;QAAa;IAAc;AAChE;AAkBA,SAAS,yBAAyB,QAAkB;IAClD,MAAM,kBAAiC,IAAI;IAI3C,MAAM,QAAqB;QACzB;YACE;YACA,iBAAiB,EAAE;QACrB;KACD;IAED,IAAI;IACJ,MAAQ,WAAW,MAAM,KAAK,GAAK;QACjC,MAAM,EAAE,QAAQ,EAAE,eAAe,EAAE,GAAG;QAEtC,IAAI,YAAY,MAAM;YACpB,IAAI,gBAAgB,GAAG,CAAC,WAAW;gBAEjC;YACF;YAEA,gBAAgB,GAAG,CAAC;QACtB;QAEA,sEAAsE;QACtE,qCAAqC;QACrC,IAAI,aAAa,WAAW;YAC1B,OAAO;gBACL,MAAM;gBACN;YACF;QACF;QAEA,MAAM,SAAS,cAAc,CAAC,SAAS;QACvC,MAAM,WAAW,eAAe,GAAG,CAAC;QAEpC,IACE,qEAAqE;QACrE,0DAA0D;QAC1D,CAAC,UAEA,SAAS,YAAY,IAAI,CAAC,SAAS,eAAe,EACnD;YACA;QACF;QAEA,IAAI,SAAS,YAAY,EAAE;YACzB,OAAO;gBACL,MAAM;gBACN;gBACA;YACF;QACF;QAEA,IAAI,eAAe,GAAG,CAAC,WAAW;YAChC,MAAM,IAAI,CAAC;gBACT,UAAU;gBACV,iBAAiB;uBAAI;oBAAiB;iBAAS;YACjD;YACA;QACF;QAEA,KAAK,MAAM,YAAY,OAAO,OAAO,CAAE;YACrC,MAAM,SAAS,cAAc,CAAC,SAAS;YAEvC,IAAI,CAAC,QAAQ;gBAEX;YACF;YAEA,4DAA4D;YAC5D,qBAAqB;YAErB,MAAM,IAAI,CAAC;gBACT,UAAU;gBACV,iBAAiB;uBAAI;oBAAiB;iBAAS;YACjD;QACF;IACF;IAEA,OAAO;QACL,MAAM;QACN;QACA;IACF;AACF;AAEA,SAAS,YAAY,aAA4B,EAAE,MAAqB;IACtE,OAAQ,OAAO,IAAI;QACjB,KAAK;YAAW;gBACd,4FAA4F;gBAC5F,YAAY,OAAO,WAAW;gBAC9B;YACF;QACA,KAAK;YAAW;gBACd,iEAAiE;gBACjE,qEAAqE;gBACrE,aAAa;gBACb,YAAY,OAAO;gBACnB;YACF;QACA,KAAK;YAAY;gBACf,+GAA+G;gBAC/G,kCAAkC;gBAClC,mGAAmG;gBACnG,6DAA6D;gBAC7D,IAAI,kBAAkB,GAAG,CAAC,gBAAgB;oBACxC,YAAY,OAAO;gBACrB,OAAO;oBACL,iBAAiB;gBACnB;gBACA;YACF;QACA;YACE,MAAM,IAAI,MAAM,CAAC,qBAAqB,EAAE,OAAO,IAAI,EAAE;IACzD;AACF;AAEA,SAAS,gBACP,QAAkB,EAClB,OAAgB;IAEhB,MAAM,WAAqB;QACzB,cAAc;QACd,cAAc;QACd,iBAAiB;QACjB,iBAAiB,EAAE;IACrB;IAEA,MAAM,MAAW;QACf,qEAAqE;QACrE,wEAAwE;QACxE,uCAAuC;QACvC,QAAQ;QAER,MAAM,WAAW,CAAC;QAElB,mEAAmE;QACnE,QAAQ,CACN,SACA,WACA;YAEA,IAAI,YAAY,WAAW;gBACzB,SAAS,YAAY,GAAG;YAC1B,OAAO,IAAI,OAAO,YAAY,YAAY;gBACxC,SAAS,YAAY,GAAG;YAC1B,OAAO;gBACL,MAAM,IAAI,MAAM;YAClB;QACF;QAEA,SAAS,CAAC;YACR,IAAI,QAAQ,WAAW;gBACrB,SAAS,YAAY,GAAG;YAC1B,OAAO;gBACL,MAAM,IAAI,MAAM;YAClB;QACF;QAEA,SAAS,CAAC;YACR,SAAS,eAAe,CAAC,IAAI,CAAC;QAChC;QAEA,mBAAmB,CAAC;YAClB,SAAS,eAAe,CAAC,IAAI,CAAC;QAChC;QAEA,sBAAsB,CAAC;YACrB,MAAM,MAAM,SAAS,eAAe,CAAC,OAAO,CAAC;YAC7C,IAAI,OAAO,GAAG;gBACZ,SAAS,eAAe,CAAC,MAAM,CAAC,KAAK;YACvC;QACF;QAEA,YAAY;YACV,SAAS,eAAe,GAAG;YAC3B,yBAAyB,GAAG,CAAC;QAC/B;QAEA,qEAAqE;QACrE,uEAAuE;QACvE,iCAAiC;QACjC,QAAQ,IAAM;QAEd,2EAA2E;QAC3E,kBAAkB,CAAC,YAAc;QACjC,qBAAqB,CAAC,YAAc;QAEpC,2EAA2E;QAC3E,yEAAyE;QACzE,iBAAiB;QACjB,OAAO,IAAM,QAAQ,OAAO,CAAC;IAC/B;IAEA,OAAO;QAAE;QAAK;IAAS;AACzB;AAEA;;;CAGC,GACD,SAAS,sBACP,QAAkB,EAClB,SAAoB;IAEpB,MAAM,eAAe,gBAAgB,GAAG,CAAC;IACzC,aAAa,MAAM,CAAC;IAEpB,MAAM,eAAe,gBAAgB,GAAG,CAAC;IACzC,aAAa,MAAM,CAAC;IAEpB,MAAM,qBAAqB,aAAa,IAAI,KAAK;IACjD,IAAI,oBAAoB;QACtB,gBAAgB,MAAM,CAAC;IACzB;IAEA,MAAM,oBAAoB,aAAa,IAAI,KAAK;IAChD,IAAI,mBAAmB;QACrB,gBAAgB,MAAM,CAAC;IACzB;IAEA,OAAO;AACT;AAEA;;CAEC,GACD,SAAS,iBAAiB,aAA4B;IACpD,MAAM,aAAa,mBAAmB,GAAG,CAAC;IAC1C,IAAI,cAAc,MAAM;QACtB,OAAO;IACT;IACA,mBAAmB,MAAM,CAAC;IAE1B,KAAK,MAAM,aAAa,WAAY;QAClC,MAAM,kBAAkB,mBAAmB,GAAG,CAAC;QAC/C,gBAAgB,MAAM,CAAC;QAEvB,IAAI,gBAAgB,IAAI,KAAK,GAAG;YAC9B,mBAAmB,MAAM,CAAC;YAC1B,aAAa;QACf;IACF;IAEA,yEAAyE;IACzE,sCAAsC;IACtC,MAAM,eAAe,oBAAoB;IAEzC,YAAY,WAAW,GAAG;IAE1B,OAAO;AACT;AAEA;;;;CAIC,GACD,SAAS,aAAa,SAAoB;IACxC,MAAM,WAAW,oBAAoB;IACrC,qEAAqE;IACrE,wFAAwF;IACxF,YAAY,WAAW,GAAG;IAE1B,MAAM,eAAe,gBAAgB,GAAG,CAAC;IACzC,IAAI,gBAAgB,MAAM;QACxB,OAAO;IACT;IACA,aAAa,MAAM,CAAC;IAEpB,KAAK,MAAM,YAAY,aAAc;QACnC,MAAM,eAAe,gBAAgB,GAAG,CAAC;QACzC,aAAa,MAAM,CAAC;QAEpB,MAAM,oBAAoB,aAAa,IAAI,KAAK;QAChD,IAAI,mBAAmB;YACrB,gBAAgB,MAAM,CAAC;YACvB,cAAc,UAAU;YACxB,iBAAiB,MAAM,CAAC;QAC1B;IACF;IAEA,OAAO;AACT;AAGA;;CAEC,GACD,SAAS,kBACP,SAAoB;IAEpB,MAAM,kBAAkB,UAAU,MAAM;IACxC,MAAM,gBAAgB,kBAAkB;IACxC,sEAAsE;IACtE,QAAQ,aAAa,CAAC;IACtB,WAAW,gCAAgC,CAAE,IAAI,CAAC;QAChD;QACA,YAAY,IAAI,CAAC,MAAM;KACxB;IAED,+CAA+C;IAC/C,MAAM,aAAa,IAAI,IAAI,UAAU,MAAM,CAAC,GAAG,CAAC;IAChD,mBAAmB,GAAG,CAAC,eAAe;IACtC,KAAK,MAAM,aAAa,WAAY;QAClC,IAAI,kBAAkB,mBAAmB,GAAG,CAAC;QAC7C,IAAI,CAAC,iBAAiB;YACpB,kBAAkB,IAAI,IAAI;gBAAC;aAAc;YACzC,mBAAmB,GAAG,CAAC,WAAW;QACpC,OAAO;YACL,gBAAgB,GAAG,CAAC;QACtB;IACF;IAEA,IAAI,UAAU,MAAM,KAAK,SAAS;QAChC,uBAAuB;IACzB;AACF;AAEA,WAAW,gCAAgC,KAAK,EAAE", "ignoreList": [0]}}, {"offset": {"line": 1418, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[turbopack]/browser/runtime/dom/runtime-backend-dom.ts"], "sourcesContent": ["/**\r\n * This file contains the runtime code specific to the Turbopack development\r\n * ECMAScript DOM runtime.\r\n *\r\n * It will be appended to the base development runtime code.\r\n */\r\n\r\n/* eslint-disable @typescript-eslint/no-unused-vars */\r\n\r\n/// <reference path=\"../../../browser/runtime/base/runtime-base.ts\" />\r\n/// <reference path=\"../../../shared/runtime-types.d.ts\" />\r\n\r\ntype ChunkResolver = {\r\n  resolved: boolean;\r\n  resolve: () => void;\r\n  reject: (error?: Error) => void;\r\n  promise: Promise<void>;\r\n};\r\n\r\nlet BACKEND: RuntimeBackend;\r\n\r\nfunction augmentContext(context: unknown): unknown {\r\n  return context;\r\n}\r\n\r\nfunction fetchWebAssembly(wasmChunkPath: ChunkPath) {\r\n  return fetch(getChunkRelativeUrl(wasmChunkPath));\r\n}\r\n\r\nasync function loadWebAssembly(\r\n  _source: unknown,\r\n  wasmChunkPath: ChunkPath,\r\n  importsObj: WebAssembly.Imports\r\n): Promise<Exports> {\r\n  const req = fetchWebAssembly(wasmChunkPath);\r\n\r\n  const { instance } = await WebAssembly.instantiateStreaming(req, importsObj);\r\n\r\n  return instance.exports;\r\n}\r\n\r\nasync function loadWebAssemblyModule(\r\n  _source: unknown,\r\n  wasmChunkPath: ChunkPath\r\n): Promise<WebAssembly.Module> {\r\n  const req = fetchWebAssembly(wasmChunkPath);\r\n\r\n  return await WebAssembly.compileStreaming(req);\r\n}\r\n\r\n/**\r\n * Maps chunk paths to the corresponding resolver.\r\n */\r\nconst chunkResolvers: Map<ChunkUrl, ChunkResolver> = new Map();\r\n\r\n(() => {\r\n  BACKEND = {\r\n    async registerChunk(chunkPath, params) {\r\n      const chunkUrl = getChunkRelativeUrl(chunkPath);\r\n\r\n      const resolver = getOrCreateResolver(chunkUrl);\r\n      resolver.resolve();\r\n\r\n      if (params == null) {\r\n        return;\r\n      }\r\n\r\n      for (const otherChunkData of params.otherChunks) {\r\n        const otherChunkPath = getChunkPath(otherChunkData)\r\n        const otherChunkUrl = getChunkRelativeUrl(otherChunkPath);\r\n\r\n        // Chunk might have started loading, so we want to avoid triggering another load.\r\n        getOrCreateResolver(otherChunkUrl);\r\n      }\r\n\r\n      // This waits for chunks to be loaded, but also marks included items as available.\r\n      await Promise.all(\r\n        params.otherChunks.map((otherChunkData) =>\r\n          loadChunk({ type: SourceType.Runtime, chunkPath }, otherChunkData)\r\n        )\r\n      );\r\n\r\n      if (params.runtimeModuleIds.length > 0) {\r\n        for (const moduleId of params.runtimeModuleIds) {\r\n          getOrInstantiateRuntimeModule(moduleId, chunkPath);\r\n        }\r\n      }\r\n    },\r\n\r\n    /**\r\n     * Loads the given chunk, and returns a promise that resolves once the chunk\r\n     * has been loaded.\r\n    */\r\n    loadChunk(chunkUrl, source) {\r\n      return doLoadChunk(chunkUrl, source);\r\n    },\r\n  };\r\n\r\n  function getOrCreateResolver(chunkUrl: ChunkUrl): ChunkResolver {\r\n    let resolver = chunkResolvers.get(chunkUrl);\r\n    if (!resolver) {\r\n      let resolve: () => void;\r\n      let reject: (error?: Error) => void;\r\n      const promise = new Promise<void>((innerResolve, innerReject) => {\r\n        resolve = innerResolve;\r\n        reject = innerReject;\r\n      });\r\n      resolver = {\r\n        resolved: false,\r\n        promise,\r\n        resolve: () => {\r\n          resolver!.resolved = true;\r\n          resolve();\r\n        },\r\n        reject: reject!,\r\n      };\r\n      chunkResolvers.set(chunkUrl, resolver);\r\n    }\r\n    return resolver;\r\n  }\r\n\r\n   /**\r\n    * Loads the given chunk, and returns a promise that resolves once the chunk\r\n    * has been loaded.\r\n    */\r\n  function doLoadChunk(chunkUrl: ChunkUrl, source: SourceInfo) {\r\n    const resolver = getOrCreateResolver(chunkUrl);\r\n    if (resolver.resolved) {\r\n      return resolver.promise;\r\n    }\r\n\r\n    if (source.type === SourceType.Runtime) {\r\n      // We don't need to load chunks references from runtime code, as they're already\r\n      // present in the DOM.\r\n\r\n      if (isCss(chunkUrl)) {\r\n        // CSS chunks do not register themselves, and as such must be marked as\r\n        // loaded instantly.\r\n        resolver.resolve();\r\n      }\r\n\r\n      // We need to wait for JS chunks to register themselves within `registerChunk`\r\n      // before we can start instantiating runtime modules, hence the absence of\r\n      // `resolver.resolve()` in this branch.\r\n\r\n      return resolver.promise;\r\n    }\r\n\r\n    if (typeof importScripts === \"function\") {\r\n      // We're in a web worker\r\n      if (isCss(chunkUrl)) {\r\n        // ignore\r\n      } else if (isJs(chunkUrl)) {\r\n        self.TURBOPACK_NEXT_CHUNK_URLS!.push(chunkUrl);\r\n        importScripts(TURBOPACK_WORKER_LOCATION + chunkUrl);\r\n      } else {\r\n        throw new Error(`can't infer type of chunk from URL ${chunkUrl} in worker`);\r\n      }\r\n    } else {\r\n      // TODO(PACK-2140): remove this once all filenames are guaranteed to be escaped.\r\n      const decodedChunkUrl = decodeURI(chunkUrl);\r\n\r\n      if (isCss(chunkUrl)) {\r\n        const previousLinks = document.querySelectorAll(\r\n          `link[rel=stylesheet][href=\"${chunkUrl}\"],link[rel=stylesheet][href^=\"${chunkUrl}?\"],link[rel=stylesheet][href=\"${decodedChunkUrl}\"],link[rel=stylesheet][href^=\"${decodedChunkUrl}?\"]`\r\n        );\r\n        if (previousLinks.length > 0) {\r\n          // CSS chunks do not register themselves, and as such must be marked as\r\n          // loaded instantly.\r\n          resolver.resolve();\r\n        } else {\r\n          const link = document.createElement(\"link\");\r\n          link.rel = \"stylesheet\";\r\n          link.href = chunkUrl;\r\n          link.onerror = () => {\r\n            resolver.reject();\r\n          };\r\n          link.onload = () => {\r\n            // CSS chunks do not register themselves, and as such must be marked as\r\n            // loaded instantly.\r\n            resolver.resolve();\r\n          };\r\n          document.body.appendChild(link);\r\n        }\r\n      } else if (isJs(chunkUrl)) {\r\n        const previousScripts = document.querySelectorAll(\r\n          `script[src=\"${chunkUrl}\"],script[src^=\"${chunkUrl}?\"],script[src=\"${decodedChunkUrl}\"],script[src^=\"${decodedChunkUrl}?\"]`\r\n        );\r\n        if (previousScripts.length > 0) {\r\n          // There is this edge where the script already failed loading, but we\r\n          // can't detect that. The Promise will never resolve in this case.\r\n          for (const script of Array.from(previousScripts)) {\r\n            script.addEventListener(\"error\", () => {\r\n              resolver.reject();\r\n            });\r\n          }\r\n        } else {\r\n          const script = document.createElement(\"script\");\r\n          script.src = chunkUrl;\r\n          // We'll only mark the chunk as loaded once the script has been executed,\r\n          // which happens in `registerChunk`. Hence the absence of `resolve()` in\r\n          // this branch.\r\n          script.onerror = () => {\r\n            resolver.reject();\r\n          };\r\n          document.body.appendChild(script);\r\n        }\r\n      } else {\r\n        throw new Error(`can't infer type of chunk from URL ${chunkUrl}`);\r\n      }\r\n    }\r\n\r\n    return resolver.promise;\r\n  }\r\n\r\n})();\r\n"], "names": [], "mappings": "AAAA;;;;;CAKC,GAED,oDAAoD,GAEpD,sEAAsE;AACtE,2DAA2D;AAS3D,IAAI;AAEJ,SAAS,eAAe,OAAgB;IACtC,OAAO;AACT;AAEA,SAAS,iBAAiB,aAAwB;IAChD,OAAO,MAAM,oBAAoB;AACnC;AAEA,eAAe,gBACb,OAAgB,EAChB,aAAwB,EACxB,UAA+B;IAE/B,MAAM,MAAM,iBAAiB;IAE7B,MAAM,EAAE,QAAQ,EAAE,GAAG,MAAM,YAAY,oBAAoB,CAAC,KAAK;IAEjE,OAAO,SAAS,OAAO;AACzB;AAEA,eAAe,sBACb,OAAgB,EAChB,aAAwB;IAExB,MAAM,MAAM,iBAAiB;IAE7B,OAAO,MAAM,YAAY,gBAAgB,CAAC;AAC5C;AAEA;;CAEC,GACD,MAAM,iBAA+C,IAAI;AAEzD,CAAC;IACC,UAAU;QACR,MAAM,eAAc,SAAS,EAAE,MAAM;YACnC,MAAM,WAAW,oBAAoB;YAErC,MAAM,WAAW,oBAAoB;YACrC,SAAS,OAAO;YAEhB,IAAI,UAAU,MAAM;gBAClB;YACF;YAEA,KAAK,MAAM,kBAAkB,OAAO,WAAW,CAAE;gBAC/C,MAAM,iBAAiB,aAAa;gBACpC,MAAM,gBAAgB,oBAAoB;gBAE1C,iFAAiF;gBACjF,oBAAoB;YACtB;YAEA,kFAAkF;YAClF,MAAM,QAAQ,GAAG,CACf,OAAO,WAAW,CAAC,GAAG,CAAC,CAAC,iBACtB,UAAU;oBAAE,MAAM,WAAW,OAAO;oBAAE;gBAAU,GAAG;YAIvD,IAAI,OAAO,gBAAgB,CAAC,MAAM,GAAG,GAAG;gBACtC,KAAK,MAAM,YAAY,OAAO,gBAAgB,CAAE;oBAC9C,8BAA8B,UAAU;gBAC1C;YACF;QACF;QAEA;;;IAGA,GACA,WAAU,QAAQ,EAAE,MAAM;YACxB,OAAO,YAAY,UAAU;QAC/B;IACF;IAEA,SAAS,oBAAoB,QAAkB;QAC7C,IAAI,WAAW,eAAe,GAAG,CAAC;QAClC,IAAI,CAAC,UAAU;YACb,IAAI;YACJ,IAAI;YACJ,MAAM,UAAU,IAAI,QAAc,CAAC,cAAc;gBAC/C,UAAU;gBACV,SAAS;YACX;YACA,WAAW;gBACT,UAAU;gBACV;gBACA,SAAS;oBACP,SAAU,QAAQ,GAAG;oBACrB;gBACF;gBACA,QAAQ;YACV;YACA,eAAe,GAAG,CAAC,UAAU;QAC/B;QACA,OAAO;IACT;IAEC;;;IAGC,GACF,SAAS,YAAY,QAAkB,EAAE,MAAkB;QACzD,MAAM,WAAW,oBAAoB;QACrC,IAAI,SAAS,QAAQ,EAAE;YACrB,OAAO,SAAS,OAAO;QACzB;QAEA,IAAI,OAAO,IAAI,KAAK,WAAW,OAAO,EAAE;YACtC,gFAAgF;YAChF,sBAAsB;YAEtB,IAAI,MAAM,WAAW;gBACnB,uEAAuE;gBACvE,oBAAoB;gBACpB,SAAS,OAAO;YAClB;YAEA,8EAA8E;YAC9E,0EAA0E;YAC1E,uCAAuC;YAEvC,OAAO,SAAS,OAAO;QACzB;QAEA,IAAI,OAAO,kBAAkB,YAAY;YACvC,wBAAwB;YACxB,IAAI,MAAM,WAAW;YACnB,SAAS;YACX,OAAO,IAAI,KAAK,WAAW;gBACzB,KAAK,yBAAyB,CAAE,IAAI,CAAC;gBACrC,cAAc,4BAA4B;YAC5C,OAAO;gBACL,MAAM,IAAI,MAAM,CAAC,mCAAmC,EAAE,SAAS,UAAU,CAAC;YAC5E;QACF,OAAO;YACL,gFAAgF;YAChF,MAAM,kBAAkB,UAAU;YAElC,IAAI,MAAM,WAAW;gBACnB,MAAM,gBAAgB,SAAS,gBAAgB,CAC7C,CAAC,2BAA2B,EAAE,SAAS,+BAA+B,EAAE,SAAS,+BAA+B,EAAE,gBAAgB,+BAA+B,EAAE,gBAAgB,GAAG,CAAC;gBAEzL,IAAI,cAAc,MAAM,GAAG,GAAG;oBAC5B,uEAAuE;oBACvE,oBAAoB;oBACpB,SAAS,OAAO;gBAClB,OAAO;oBACL,MAAM,OAAO,SAAS,aAAa,CAAC;oBACpC,KAAK,GAAG,GAAG;oBACX,KAAK,IAAI,GAAG;oBACZ,KAAK,OAAO,GAAG;wBACb,SAAS,MAAM;oBACjB;oBACA,KAAK,MAAM,GAAG;wBACZ,uEAAuE;wBACvE,oBAAoB;wBACpB,SAAS,OAAO;oBAClB;oBACA,SAAS,IAAI,CAAC,WAAW,CAAC;gBAC5B;YACF,OAAO,IAAI,KAAK,WAAW;gBACzB,MAAM,kBAAkB,SAAS,gBAAgB,CAC/C,CAAC,YAAY,EAAE,SAAS,gBAAgB,EAAE,SAAS,gBAAgB,EAAE,gBAAgB,gBAAgB,EAAE,gBAAgB,GAAG,CAAC;gBAE7H,IAAI,gBAAgB,MAAM,GAAG,GAAG;oBAC9B,qEAAqE;oBACrE,kEAAkE;oBAClE,KAAK,MAAM,UAAU,MAAM,IAAI,CAAC,iBAAkB;wBAChD,OAAO,gBAAgB,CAAC,SAAS;4BAC/B,SAAS,MAAM;wBACjB;oBACF;gBACF,OAAO;oBACL,MAAM,SAAS,SAAS,aAAa,CAAC;oBACtC,OAAO,GAAG,GAAG;oBACb,yEAAyE;oBACzE,wEAAwE;oBACxE,eAAe;oBACf,OAAO,OAAO,GAAG;wBACf,SAAS,MAAM;oBACjB;oBACA,SAAS,IAAI,CAAC,WAAW,CAAC;gBAC5B;YACF,OAAO;gBACL,MAAM,IAAI,MAAM,CAAC,mCAAmC,EAAE,UAAU;YAClE;QACF;QAEA,OAAO,SAAS,OAAO;IACzB;AAEF,CAAC", "ignoreList": [0]}}, {"offset": {"line": 1581, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[turbopack]/browser/runtime/dom/dev-backend-dom.ts"], "sourcesContent": ["/**\r\n * This file contains the runtime code specific to the Turbopack development\r\n * ECMAScript DOM runtime.\r\n *\r\n * It will be appended to the base development runtime code.\r\n */\r\n\r\n/* eslint-disable @typescript-eslint/no-unused-vars */\r\n\r\n/// <reference path=\"../base/runtime-base.ts\" />\r\n/// <reference path=\"../base/dev-base.ts\" />\r\n/// <reference path=\"./runtime-backend-dom.ts\" />\r\n/// <reference path=\"../../../shared/require-type.d.ts\" />\r\n\r\nlet DEV_BACKEND: DevRuntimeBackend;\r\n\r\n(() => {\r\n  DEV_BACKEND = {\r\n    unloadChunk(chunkUrl) {\r\n      deleteResolver(chunkUrl);\r\n\r\n      // TODO(PACK-2140): remove this once all filenames are guaranteed to be escaped.\r\n      const decodedChunkUrl = decodeURI(chunkUrl);\r\n\r\n      if (isCss(chunkUrl)) {\r\n        const links = document.querySelectorAll(\r\n          `link[href=\"${chunkUrl}\"],link[href^=\"${chunkUrl}?\"],link[href=\"${decodedChunkUrl}\"],link[href^=\"${decodedChunkUrl}?\"]`\r\n        );\r\n        for (const link of Array.from(links)) {\r\n          link.remove();\r\n        }\r\n      } else if (isJs(chunkUrl)) {\r\n        // Unloading a JS chunk would have no effect, as it lives in the JS\r\n        // runtime once evaluated.\r\n        // However, we still want to remove the script tag from the DOM to keep\r\n        // the HTML somewhat consistent from the user's perspective.\r\n        const scripts = document.querySelectorAll(\r\n          `script[src=\"${chunkUrl}\"],script[src^=\"${chunkUrl}?\"],script[src=\"${decodedChunkUrl}\"],script[src^=\"${decodedChunkUrl}?\"]`\r\n        );\r\n        for (const script of Array.from(scripts)) {\r\n          script.remove();\r\n        }\r\n      } else {\r\n        throw new Error(`can't infer type of chunk from URL ${chunkUrl}`);\r\n      }\r\n    },\r\n\r\n    reloadChunk(chunkUrl) {\r\n      return new Promise<void>((resolve, reject) => {\r\n        if (!isCss(chunkUrl)) {\r\n          reject(new Error(\"The DOM backend can only reload CSS chunks\"));\r\n          return;\r\n        }\r\n\r\n        const decodedChunkUrl = decodeURI(chunkUrl);\r\n        const previousLinks = document.querySelectorAll(\r\n          `link[rel=stylesheet][href=\"${chunkUrl}\"],link[rel=stylesheet][href^=\"${chunkUrl}?\"],link[rel=stylesheet][href=\"${decodedChunkUrl}\"],link[rel=stylesheet][href^=\"${decodedChunkUrl}?\"]`\r\n        );\r\n\r\n        if (previousLinks.length === 0) {\r\n          reject(new Error(`No link element found for chunk ${chunkUrl}`));\r\n          return;\r\n        }\r\n\r\n        const link = document.createElement(\"link\");\r\n        link.rel = \"stylesheet\";\r\n\r\n        if (navigator.userAgent.includes(\"Firefox\")) {\r\n          // Firefox won't reload CSS files that were previously loaded on the current page,\r\n          // we need to add a query param to make sure CSS is actually reloaded from the server.\r\n          //\r\n          // I believe this is this issue: https://bugzilla.mozilla.org/show_bug.cgi?id=1037506\r\n          //\r\n          // Safari has a similar issue, but only if you have a `<link rel=preload ... />` tag\r\n          // pointing to the same URL as the stylesheet: https://bugs.webkit.org/show_bug.cgi?id=187726\r\n          link.href = `${chunkUrl}?ts=${Date.now()}`;\r\n        } else {\r\n          link.href = chunkUrl;\r\n        }\r\n\r\n        link.onerror = () => {\r\n          reject();\r\n        };\r\n        link.onload = () => {\r\n          // First load the new CSS, then remove the old ones. This prevents visible\r\n          // flickering that would happen in-between removing the previous CSS and\r\n          // loading the new one.\r\n          for (const previousLink of Array.from(previousLinks))\r\n            previousLink.remove();\r\n\r\n          // CSS chunks do not register themselves, and as such must be marked as\r\n          // loaded instantly.\r\n          resolve();\r\n        };\r\n\r\n        // Make sure to insert the new CSS right after the previous one, so that\r\n        // its precedence is higher.\r\n        previousLinks[0].parentElement!.insertBefore(\r\n          link,\r\n          previousLinks[0].nextSibling\r\n        );\r\n      });\r\n    },\r\n\r\n    restart: () => self.location.reload(),\r\n  };\r\n\r\n  function deleteResolver(chunkUrl: ChunkUrl) {\r\n    chunkResolvers.delete(chunkUrl);\r\n  }\r\n})();\r\n\r\nfunction _eval({ code, url, map }: EcmascriptModuleEntry): ModuleFactory {\r\n  code += `\\n\\n//# sourceURL=${encodeURI(\r\n    location.origin + CHUNK_BASE_PATH + url + CHUNK_SUFFIX_PATH\r\n  )}`;\r\n  if (map) {\r\n    code += `\\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,${btoa(\r\n      // btoa doesn't handle nonlatin characters, so escape them as \\x sequences\r\n      // See https://stackoverflow.com/a/26603875\r\n      unescape(encodeURIComponent(map))\r\n    )}`;\r\n  }\r\n\r\n  // eslint-disable-next-line no-eval\r\n  return eval(code);\r\n}\r\n"], "names": [], "mappings": "AAAA;;;;;CAKC,GAED,oDAAoD,GAEpD,gDAAgD;AAChD,4CAA4C;AAC5C,iDAAiD;AACjD,0DAA0D;AAE1D,IAAI;AAEJ,CAAC;IACC,cAAc;QACZ,aAAY,QAAQ;YAClB,eAAe;YAEf,gFAAgF;YAChF,MAAM,kBAAkB,UAAU;YAElC,IAAI,MAAM,WAAW;gBACnB,MAAM,QAAQ,SAAS,gBAAgB,CACrC,CAAC,WAAW,EAAE,SAAS,eAAe,EAAE,SAAS,eAAe,EAAE,gBAAgB,eAAe,EAAE,gBAAgB,GAAG,CAAC;gBAEzH,KAAK,MAAM,QAAQ,MAAM,IAAI,CAAC,OAAQ;oBACpC,KAAK,MAAM;gBACb;YACF,OAAO,IAAI,KAAK,WAAW;gBACzB,mEAAmE;gBACnE,0BAA0B;gBAC1B,uEAAuE;gBACvE,4DAA4D;gBAC5D,MAAM,UAAU,SAAS,gBAAgB,CACvC,CAAC,YAAY,EAAE,SAAS,gBAAgB,EAAE,SAAS,gBAAgB,EAAE,gBAAgB,gBAAgB,EAAE,gBAAgB,GAAG,CAAC;gBAE7H,KAAK,MAAM,UAAU,MAAM,IAAI,CAAC,SAAU;oBACxC,OAAO,MAAM;gBACf;YACF,OAAO;gBACL,MAAM,IAAI,MAAM,CAAC,mCAAmC,EAAE,UAAU;YAClE;QACF;QAEA,aAAY,QAAQ;YAClB,OAAO,IAAI,QAAc,CAAC,SAAS;gBACjC,IAAI,CAAC,MAAM,WAAW;oBACpB,OAAO,IAAI,MAAM;oBACjB;gBACF;gBAEA,MAAM,kBAAkB,UAAU;gBAClC,MAAM,gBAAgB,SAAS,gBAAgB,CAC7C,CAAC,2BAA2B,EAAE,SAAS,+BAA+B,EAAE,SAAS,+BAA+B,EAAE,gBAAgB,+BAA+B,EAAE,gBAAgB,GAAG,CAAC;gBAGzL,IAAI,cAAc,MAAM,KAAK,GAAG;oBAC9B,OAAO,IAAI,MAAM,CAAC,gCAAgC,EAAE,UAAU;oBAC9D;gBACF;gBAEA,MAAM,OAAO,SAAS,aAAa,CAAC;gBACpC,KAAK,GAAG,GAAG;gBAEX,IAAI,UAAU,SAAS,CAAC,QAAQ,CAAC,YAAY;oBAC3C,kFAAkF;oBAClF,sFAAsF;oBACtF,EAAE;oBACF,qFAAqF;oBACrF,EAAE;oBACF,oFAAoF;oBACpF,6FAA6F;oBAC7F,KAAK,IAAI,GAAG,GAAG,SAAS,IAAI,EAAE,KAAK,GAAG,IAAI;gBAC5C,OAAO;oBACL,KAAK,IAAI,GAAG;gBACd;gBAEA,KAAK,OAAO,GAAG;oBACb;gBACF;gBACA,KAAK,MAAM,GAAG;oBACZ,0EAA0E;oBAC1E,wEAAwE;oBACxE,uBAAuB;oBACvB,KAAK,MAAM,gBAAgB,MAAM,IAAI,CAAC,eACpC,aAAa,MAAM;oBAErB,uEAAuE;oBACvE,oBAAoB;oBACpB;gBACF;gBAEA,wEAAwE;gBACxE,4BAA4B;gBAC5B,aAAa,CAAC,EAAE,CAAC,aAAa,CAAE,YAAY,CAC1C,MACA,aAAa,CAAC,EAAE,CAAC,WAAW;YAEhC;QACF;QAEA,SAAS,IAAM,KAAK,QAAQ,CAAC,MAAM;IACrC;IAEA,SAAS,eAAe,QAAkB;QACxC,eAAe,MAAM,CAAC;IACxB;AACF,CAAC;AAED,SAAS,MAAM,EAAE,IAAI,EAAE,GAAG,EAAE,GAAG,EAAyB;IACtD,QAAQ,CAAC,kBAAkB,EAAE,UAC3B,SAAS,MAAM,GAAG,kBAAkB,MAAM,oBACzC;IACH,IAAI,KAAK;QACP,QAAQ,CAAC,kEAAkE,EAAE,KAC3E,0EAA0E;QAC1E,2CAA2C;QAC3C,SAAS,mBAAmB,QAC3B;IACL;IAEA,mCAAmC;IACnC,OAAO,KAAK;AACd", "ignoreList": [0]}}]}