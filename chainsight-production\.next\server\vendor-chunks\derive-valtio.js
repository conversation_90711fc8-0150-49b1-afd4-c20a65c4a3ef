"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/derive-valtio";
exports.ids = ["vendor-chunks/derive-valtio"];
exports.modules = {

/***/ "(ssr)/./node_modules/derive-valtio/dist/index.modern.js":
/*!*********************************************************!*\
  !*** ./node_modules/derive-valtio/dist/index.modern.js ***!
  \*********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   derive: () => (/* binding */ f),\n/* harmony export */   underive: () => (/* binding */ u),\n/* harmony export */   unstable_deriveSubscriptions: () => (/* binding */ i)\n/* harmony export */ });\n/* harmony import */ var valtio_vanilla__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! valtio/vanilla */ \"(ssr)/./node_modules/valtio/esm/vanilla.mjs\");\nconst o=new WeakMap,r=new WeakMap,s=(e,t)=>{const n=o.get(e);n&&(n[0].forEach(t=>{const{d:n}=t;e!==n&&s(n)}),++n[2],t&&n[3].add(t))},l=e=>{const t=o.get(e);t&&(--t[2],t[2]||(t[3].forEach(e=>e()),t[3].clear()),t[0].forEach(t=>{const{d:n}=t;e!==n&&l(n)}))},c=e=>{const{s:n,d:c}=e;let a=r.get(c);a||(a=[new Set],r.set(e.d,a)),a[0].add(e);let d=o.get(n);if(!d){const e=new Set,r=(0,valtio_vanilla__WEBPACK_IMPORTED_MODULE_0__.subscribe)(n,t=>{e.forEach(e=>{const{d:o,c:r,n:c,i:a}=e;n===o&&t.every(e=>1===e[1].length&&a.includes(e[1][0]))||e.p||(s(n,r),c?l(n):e.p=Promise.resolve().then(()=>{delete e.p,l(n)}))})},!0);d=[e,r,0,new Set],o.set(n,d)}d[0].add(e)},a=e=>{const{s:t,d:n}=e,s=r.get(n);null==s||s[0].delete(e),0===(null==s?void 0:s[0].size)&&r.delete(n);const l=o.get(t);if(l){const[n,r]=l;n.delete(e),n.size||(r(),o.delete(t))}},d=e=>{const t=r.get(e);return t?Array.from(t[0]):[]},i={add:c,remove:a,list:d};function f(t,r){const s=(null==r?void 0:r.proxy)||(0,valtio_vanilla__WEBPACK_IMPORTED_MODULE_0__.proxy)({}),l=!(null==r||!r.sync),d=Object.keys(t);return d.forEach(e=>{if(Object.getOwnPropertyDescriptor(s,e))throw new Error(\"object property already defined\");const r=t[e];let i=null;const f=()=>{if(i){if(Array.from(i).map(([e])=>((e,t)=>{const n=o.get(e);return!(null==n||!n[2]||(n[3].add(t),0))})(e,f)).some(e=>e))return;if(Array.from(i).every(([e,t])=>(0,valtio_vanilla__WEBPACK_IMPORTED_MODULE_0__.getVersion)(e)===t.v))return}const t=new Map,u=r(e=>(t.set(e,{v:(0,valtio_vanilla__WEBPACK_IMPORTED_MODULE_0__.getVersion)(e)}),e)),p=()=>{var n;t.forEach((t,n)=>{var o;const r=null==(o=i)||null==(o=o.get(n))?void 0:o.s;if(r)t.s=r;else{const o={s:n,d:s,k:e,c:f,n:l,i:d};c(o),t.s=o}}),null==(n=i)||n.forEach((e,n)=>{!t.has(n)&&e.s&&a(e.s)}),i=t};u instanceof Promise?u.finally(p):p(),s[e]=u};f()}),s}function u(e,t){const n=null!=t&&t.delete?new Set:null;d(e).forEach(e=>{const{k:o}=e;null!=t&&t.keys&&!t.keys.includes(o)||(a(e),n&&n.add(o))}),n&&n.forEach(t=>{delete e[t]})}\n//# sourceMappingURL=index.modern.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/derive-valtio/dist/index.modern.js\n");

/***/ })

};
;