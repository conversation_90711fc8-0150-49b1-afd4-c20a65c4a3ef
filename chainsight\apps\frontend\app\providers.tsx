'use client';

import { QueryClient, QueryClientProvider } from 'react-query';
import { WagmiConfig, createConfig, configureChains, mainnet, polygon } from 'wagmi';
import { publicProvider } from 'wagmi/providers/public';
import { Web3Modal } from '@web3modal/react';
import { EthereumClient, w3mConnectors, w3mProvider } from '@web3modal/ethereum';
import { useState } from 'react';

const chains = [mainnet, polygon];
const projectId = process.env.NEXT_PUBLIC_WALLET_CONNECT_PROJECT_ID || 'demo';

const { publicClient } = configureChains(chains, [
  w3mProvider({ projectId }),
  publicProvider()
]);

const wagmiConfig = createConfig({
  autoConnect: true,
  connectors: w3mConnectors({ projectId, chains }),
  publicClient
});

const ethereumClient = new EthereumClient(wagmiConfig, chains);

export function Providers({ children }: { children: React.ReactNode }) {
  const [queryClient] = useState(() => new QueryClient({
    defaultOptions: {
      queries: {
        staleTime: 60 * 1000, // 1 minute
        cacheTime: 10 * 60 * 1000, // 10 minutes
      },
    },
  }));

  return (
    <QueryClientProvider client={queryClient}>
      <WagmiConfig config={wagmiConfig}>
        {children}
        <Web3Modal 
          projectId={projectId} 
          ethereumClient={ethereumClient}
          themeMode="dark"
          themeVariables={{
            '--w3m-font-family': 'Inter, sans-serif',
            '--w3m-accent-color': '#ffd700',
            '--w3m-background-color': '#0f172a',
            '--w3m-background-border-radius': '12px',
          }}
        />
      </WagmiConfig>
    </QueryClientProvider>
  );
}
