'use client';

import { motion } from 'framer-motion';
import { Wifi, WifiOff, Clock, Zap } from 'lucide-react';

interface SystemStatusProps {
  status: 'connected' | 'disconnected' | 'connecting';
}

export function SystemStatus({ status }: SystemStatusProps) {
  const getStatusIcon = () => {
    switch (status) {
      case 'connected':
        return <Wifi className="w-4 h-4 text-green-400" />;
      case 'disconnected':
        return <WifiOff className="w-4 h-4 text-red-400" />;
      case 'connecting':
        return <Zap className="w-4 h-4 text-yellow-400 animate-pulse" />;
      default:
        return <WifiOff className="w-4 h-4 text-gray-400" />;
    }
  };

  const getStatusColor = () => {
    switch (status) {
      case 'connected':
        return 'text-green-400';
      case 'disconnected':
        return 'text-red-400';
      case 'connecting':
        return 'text-yellow-400';
      default:
        return 'text-gray-400';
    }
  };

  const getStatusText = () => {
    switch (status) {
      case 'connected':
        return 'CONNECTED';
      case 'disconnected':
        return 'DISCONNECTED';
      case 'connecting':
        return 'CONNECTING...';
      default:
        return 'UNKNOWN';
    }
  };

  return (
    <div className="flex items-center space-x-4">
      {/* Connection Status */}
      <motion.div
        initial={{ opacity: 0, scale: 0.9 }}
        animate={{ opacity: 1, scale: 1 }}
        className="flex items-center space-x-2 glass rounded-full px-4 py-2"
      >
        <motion.div
          animate={status === 'connected' ? { scale: [1, 1.2, 1] } : {}}
          transition={{ duration: 2, repeat: Infinity }}
        >
          {getStatusIcon()}
        </motion.div>
        <span className={`text-sm font-medium ${getStatusColor()}`}>
          {getStatusText()}
        </span>
      </motion.div>

      {/* Last Update */}
      <motion.div
        initial={{ opacity: 0, x: 20 }}
        animate={{ opacity: 1, x: 0 }}
        transition={{ delay: 0.1 }}
        className="bg-black/20 backdrop-blur-xl border border-white/10 rounded-full px-4 py-2"
      >
        <span className="text-sm text-gray-400">
          Last: {new Date().toLocaleTimeString()}
        </span>
      </motion.div>
    </div>
  );
}
