import { create } from 'zustand';
import { persist, createJSONStorage } from 'zustand/middleware';
import { subscribeWithSelector } from 'zustand/middleware';

// Safe storage for SSR
const safeStorage = () => {
  if (typeof window === 'undefined') {
    // Return a mock storage for SSR
    return {
      getItem: () => null,
      setItem: () => {},
      removeItem: () => {},
    };
  }
  return localStorage;
};

// Types
interface User {
  id: string;
  name: string;
  email: string;
  avatar?: string;
  preferences: {
    theme: 'dark' | 'light';
    language: string;
    timezone: string;
  };
}

interface Notification {
  id: string;
  type: 'info' | 'success' | 'warning' | 'error';
  title: string;
  message: string;
  timestamp: number;
  read: boolean;
  persistent?: boolean;
}

interface AppSettings {
  sidebarCollapsed: boolean;
  activeModule: string;
  autoRefresh: boolean;
  refreshInterval: number;
  enableAnimations: boolean;
  enableSounds: boolean;
}

interface ConnectionStatus {
  api: 'connected' | 'disconnected' | 'error';
  websocket: 'connected' | 'disconnected' | 'error';
  blockchain: 'connected' | 'disconnected' | 'error';
}

interface AppState {
  // User state
  user: User | null;
  isAuthenticated: boolean;
  
  // UI state
  settings: AppSettings;
  notifications: Notification[];
  connectionStatus: ConnectionStatus;
  
  // Loading states
  isLoading: boolean;
  loadingMessage: string;
  
  // Error state
  error: string | null;
  
  // Actions
  setUser: (user: User | null) => void;
  setAuthenticated: (authenticated: boolean) => void;
  updateSettings: (settings: Partial<AppSettings>) => void;
  addNotification: (notification: Omit<Notification, 'id' | 'timestamp'>) => void;
  removeNotification: (id: string) => void;
  markNotificationRead: (id: string) => void;
  clearNotifications: () => void;
  updateConnectionStatus: (status: Partial<ConnectionStatus>) => void;
  setLoading: (loading: boolean, message?: string) => void;
  setError: (error: string | null) => void;
  reset: () => void;
}

const initialState = {
  user: null,
  isAuthenticated: false,
  settings: {
    sidebarCollapsed: false,
    activeModule: 'crypto',
    autoRefresh: true,
    refreshInterval: 30000,
    enableAnimations: true,
    enableSounds: true,
  },
  notifications: [],
  connectionStatus: {
    api: 'disconnected' as const,
    websocket: 'disconnected' as const,
    blockchain: 'disconnected' as const,
  },
  isLoading: false,
  loadingMessage: '',
  error: null,
};

export const useAppStore = create<AppState>()(
  subscribeWithSelector(
    persist(
      (set, get) => ({
        ...initialState,
        
        setUser: (user) => set({ user, isAuthenticated: !!user }),
        
        setAuthenticated: (authenticated) => set({ isAuthenticated: authenticated }),
        
        updateSettings: (newSettings) =>
          set((state) => ({
            settings: { ...state.settings, ...newSettings },
          })),
        
        addNotification: (notification) =>
          set((state) => ({
            notifications: [
              {
                ...notification,
                id: Date.now().toString() + Math.random().toString(36).substr(2, 9),
                timestamp: Date.now(),
                read: false,
              },
              ...state.notifications,
            ].slice(0, 50), // Keep only last 50 notifications
          })),
        
        removeNotification: (id) =>
          set((state) => ({
            notifications: state.notifications.filter((n) => n.id !== id),
          })),
        
        markNotificationRead: (id) =>
          set((state) => ({
            notifications: state.notifications.map((n) =>
              n.id === id ? { ...n, read: true } : n
            ),
          })),
        
        clearNotifications: () => set({ notifications: [] }),
        
        updateConnectionStatus: (status) =>
          set((state) => ({
            connectionStatus: { ...state.connectionStatus, ...status },
          })),
        
        setLoading: (loading, message = '') =>
          set({ isLoading: loading, loadingMessage: message }),
        
        setError: (error) => set({ error }),
        
        reset: () => set(initialState),
      }),
      {
        name: 'chainsight-app-store',
        storage: createJSONStorage(() => safeStorage()),
        partialize: (state) => ({
          user: state.user,
          isAuthenticated: state.isAuthenticated,
          settings: state.settings,
          notifications: state.notifications.filter(n => n.persistent),
        }),
      }
    )
  )
);

// Selectors for better performance
export const useUser = () => useAppStore((state) => state.user);
export const useIsAuthenticated = () => useAppStore((state) => state.isAuthenticated);
export const useSettings = () => useAppStore((state) => state.settings);
export const useNotifications = () => useAppStore((state) => state.notifications);
export const useUnreadNotifications = () => 
  useAppStore((state) => state.notifications.filter(n => !n.read));
export const useConnectionStatus = () => useAppStore((state) => state.connectionStatus);
export const useIsLoading = () => useAppStore((state) => state.isLoading);
export const useError = () => useAppStore((state) => state.error);

// Action selectors
export const useAppActions = () => useAppStore((state) => ({
  setUser: state.setUser,
  setAuthenticated: state.setAuthenticated,
  updateSettings: state.updateSettings,
  addNotification: state.addNotification,
  removeNotification: state.removeNotification,
  markNotificationRead: state.markNotificationRead,
  clearNotifications: state.clearNotifications,
  updateConnectionStatus: state.updateConnectionStatus,
  setLoading: state.setLoading,
  setError: state.setError,
  reset: state.reset,
}));

// Subscribe to changes for side effects
useAppStore.subscribe(
  (state) => state.connectionStatus,
  (connectionStatus) => {
    // Auto-add notifications for connection changes
    const { addNotification } = useAppStore.getState();
    
    Object.entries(connectionStatus).forEach(([service, status]) => {
      if (status === 'connected') {
        addNotification({
          type: 'success',
          title: 'Connection Established',
          message: `${service.toUpperCase()} connection is now active`,
          read: false,
        });
      } else if (status === 'error') {
        addNotification({
          type: 'error',
          title: 'Connection Error',
          message: `Failed to connect to ${service.toUpperCase()}`,
          read: false,
        });
      }
    });
  }
);

// Auto-remove old notifications
useAppStore.subscribe(
  (state) => state.notifications,
  (notifications) => {
    const now = Date.now();
    const oneHour = 60 * 60 * 1000;
    
    const expiredNotifications = notifications.filter(
      (n) => !n.persistent && now - n.timestamp > oneHour
    );
    
    if (expiredNotifications.length > 0) {
      const { removeNotification } = useAppStore.getState();
      expiredNotifications.forEach((n) => removeNotification(n.id));
    }
  }
);
