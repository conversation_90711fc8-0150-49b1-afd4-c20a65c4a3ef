"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_reown_appkit-ui_dist_esm_src_assets_svg_x_js"],{

/***/ "(app-pages-browser)/./node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/x.js":
/*!********************************************************************!*\
  !*** ./node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/x.js ***!
  \********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   xSvg: () => (/* binding */ xSvg)\n/* harmony export */ });\n/* harmony import */ var lit__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! lit */ \"(app-pages-browser)/./node_modules/lit/index.js\");\n\nconst xSvg = (0,lit__WEBPACK_IMPORTED_MODULE_0__.svg) `<svg fill=\"none\" viewBox=\"0 0 41 40\">\n  <g clip-path=\"url(#a)\">\n    <path fill=\"#000\" d=\"M.8 0h40v40H.8z\" />\n    <path\n      fill=\"#fff\"\n      d=\"m22.63 18.46 7.14-8.3h-1.69l-6.2 7.2-4.96-7.2H11.2l7.5 10.9-7.5 8.71h1.7l6.55-7.61 5.23 7.61h5.72l-7.77-11.31Zm-9.13-7.03h2.6l11.98 17.13h-2.6L13.5 11.43Z\"\n    />\n  </g>\n  <defs>\n    <clipPath id=\"a\"><path fill=\"#fff\" d=\"M.8 20a20 20 0 1 1 40 0 20 20 0 0 1-40 0Z\" /></clipPath>\n  </defs>\n</svg>`;\n//# sourceMappingURL=x.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9AcmVvd24vYXBwa2l0LXVpL2Rpc3QvZXNtL3NyYy9hc3NldHMvc3ZnL3guanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBMEI7QUFDbkIsYUFBYSx3Q0FBRztBQUN2QjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIkY6XFxBSSBjaGFpblxcY2hhaW5zaWdodC1wcm9kdWN0aW9uXFxub2RlX21vZHVsZXNcXEByZW93blxcYXBwa2l0LXVpXFxkaXN0XFxlc21cXHNyY1xcYXNzZXRzXFxzdmdcXHguanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgc3ZnIH0gZnJvbSAnbGl0JztcbmV4cG9ydCBjb25zdCB4U3ZnID0gc3ZnIGA8c3ZnIGZpbGw9XCJub25lXCIgdmlld0JveD1cIjAgMCA0MSA0MFwiPlxuICA8ZyBjbGlwLXBhdGg9XCJ1cmwoI2EpXCI+XG4gICAgPHBhdGggZmlsbD1cIiMwMDBcIiBkPVwiTS44IDBoNDB2NDBILjh6XCIgLz5cbiAgICA8cGF0aFxuICAgICAgZmlsbD1cIiNmZmZcIlxuICAgICAgZD1cIm0yMi42MyAxOC40NiA3LjE0LTguM2gtMS42OWwtNi4yIDcuMi00Ljk2LTcuMkgxMS4ybDcuNSAxMC45LTcuNSA4LjcxaDEuN2w2LjU1LTcuNjEgNS4yMyA3LjYxaDUuNzJsLTcuNzctMTEuMzFabS05LjEzLTcuMDNoMi42bDExLjk4IDE3LjEzaC0yLjZMMTMuNSAxMS40M1pcIlxuICAgIC8+XG4gIDwvZz5cbiAgPGRlZnM+XG4gICAgPGNsaXBQYXRoIGlkPVwiYVwiPjxwYXRoIGZpbGw9XCIjZmZmXCIgZD1cIk0uOCAyMGEyMCAyMCAwIDEgMSA0MCAwIDIwIDIwIDAgMCAxLTQwIDBaXCIgLz48L2NsaXBQYXRoPlxuICA8L2RlZnM+XG48L3N2Zz5gO1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9eC5qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/x.js\n"));

/***/ })

}]);