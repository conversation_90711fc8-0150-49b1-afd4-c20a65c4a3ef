"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ HomePage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _components_Navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/Navigation */ \"(app-pages-browser)/./src/components/Navigation.tsx\");\n/* harmony import */ var _components_ModularDashboard__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ModularDashboard */ \"(app-pages-browser)/./src/components/ModularDashboard.tsx\");\n/* harmony import */ var _components_AIModuleSelector__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/AIModuleSelector */ \"(app-pages-browser)/./src/components/AIModuleSelector.tsx\");\n/* harmony import */ var _components_RealTimeNotifications__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/RealTimeNotifications */ \"(app-pages-browser)/./src/components/RealTimeNotifications.tsx\");\n/* harmony import */ var _components_SystemStatus__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/SystemStatus */ \"(app-pages-browser)/./src/components/SystemStatus.tsx\");\n/* harmony import */ var _components_ClientOnly__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ClientOnly */ \"(app-pages-browser)/./src/components/ClientOnly.tsx\");\n/* harmony import */ var _components_RealTimeChat__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/RealTimeChat */ \"(app-pages-browser)/./src/components/RealTimeChat.tsx\");\n/* harmony import */ var _store_useAppStore__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/store/useAppStore */ \"(app-pages-browser)/./src/store/useAppStore.ts\");\n/* harmony import */ var _hooks_useAIModules__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/hooks/useAIModules */ \"(app-pages-browser)/./src/hooks/useAIModules.ts\");\n/* harmony import */ var _hooks_useRealTimeData__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/hooks/useRealTimeData */ \"(app-pages-browser)/./src/hooks/useRealTimeData.ts\");\n/* harmony import */ var _lib_debug__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/lib/debug */ \"(app-pages-browser)/./src/lib/debug.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\nfunction HomePage() {\n    _s();\n    const [isChatOpen, setIsChatOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const { modules, isLoading: modulesLoading } = (0,_hooks_useAIModules__WEBPACK_IMPORTED_MODULE_10__.useAIModules)();\n    const { connectionStatus, marketData } = (0,_hooks_useRealTimeData__WEBPACK_IMPORTED_MODULE_11__.useRealTimeData)();\n    // Debug tracking\n    const { logMount, logUnmount, logError, logInfo } = (0,_lib_debug__WEBPACK_IMPORTED_MODULE_12__.useDebug)('HomePage');\n    // Global state\n    const { updateSettings, updateConnectionStatus } = (0,_store_useAppStore__WEBPACK_IMPORTED_MODULE_9__.useAppActions)();\n    const settings = (0,_store_useAppStore__WEBPACK_IMPORTED_MODULE_9__.useSettings)();\n    const activeModule = settings.activeModule;\n    const handleModuleSelect = (moduleId)=>{\n        updateSettings({\n            activeModule: moduleId\n        });\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"HomePage.useEffect\": ()=>{\n            try {\n                logMount();\n                // Initialize production platform\n                _lib_debug__WEBPACK_IMPORTED_MODULE_12__.chainSightDebugger.logInfo('🚀 Chainsight Production Platform Initialized');\n                logInfo('Platform initialization started');\n                // Performance tracking\n                _lib_debug__WEBPACK_IMPORTED_MODULE_12__.debugUtils.performanceMark('platform-init-start');\n                // Update connection status\n                updateConnectionStatus({\n                    api: connectionStatus === 'connected' ? 'connected' : 'disconnected',\n                    websocket: 'connected',\n                    blockchain: 'connected'\n                });\n                _lib_debug__WEBPACK_IMPORTED_MODULE_12__.debugUtils.performanceMark('platform-init-end');\n                _lib_debug__WEBPACK_IMPORTED_MODULE_12__.debugUtils.performanceMeasure('platform-init', 'platform-init-start', 'platform-init-end');\n                logInfo('Platform initialization completed', {\n                    modulesCount: modules.length,\n                    connectionStatus,\n                    chatOpen: isChatOpen\n                });\n            } catch (error) {\n                logError(error, 'Platform initialization failed');\n            }\n            return ({\n                \"HomePage.useEffect\": ()=>{\n                    logUnmount();\n                }\n            })[\"HomePage.useEffect\"];\n        }\n    }[\"HomePage.useEffect\"], [\n        connectionStatus,\n        updateConnectionStatus,\n        logMount,\n        logUnmount,\n        logError,\n        logInfo,\n        modules.length,\n        isChatOpen\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-br from-cosmic-dark via-cosmic-mid to-cosmic-light\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ClientOnly__WEBPACK_IMPORTED_MODULE_7__.ClientOnly, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Navigation__WEBPACK_IMPORTED_MODULE_2__.Navigation, {}, void 0, false, {\n                    fileName: \"F:\\\\AI chain\\\\chainsight-production\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 73,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"F:\\\\AI chain\\\\chainsight-production\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 72,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                className: \"pt-20\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                        className: \"relative overflow-hidden min-h-screen flex items-center justify-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute inset-0 bg-gradient-to-br from-cosmic-dark via-cosmic-mid to-cosmic-light\"\n                            }, void 0, false, {\n                                fileName: \"F:\\\\AI chain\\\\chainsight-production\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 80,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute top-[20%] left-[20%] w-96 h-96 bg-gradient-to-r from-neon-blue/20 to-neon-purple/20 rounded-full blur-3xl animate-pulse\"\n                            }, void 0, false, {\n                                fileName: \"F:\\\\AI chain\\\\chainsight-production\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 83,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute top-[80%] right-[20%] w-96 h-96 bg-gradient-to-r from-neon-pink/20 to-neon-orange/20 rounded-full blur-3xl animate-pulse\"\n                            }, void 0, false, {\n                                fileName: \"F:\\\\AI chain\\\\chainsight-production\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 84,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute top-[40%] right-[40%] w-96 h-96 bg-gradient-to-r from-neon-green/20 to-accent-cyan/20 rounded-full blur-3xl animate-pulse\"\n                            }, void 0, false, {\n                                fileName: \"F:\\\\AI chain\\\\chainsight-production\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 85,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative max-w-7xl mx-auto px-4 py-20 text-center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_13__.motion.div, {\n                                    initial: {\n                                        opacity: 0,\n                                        y: 20\n                                    },\n                                    animate: {\n                                        opacity: 1,\n                                        y: 0\n                                    },\n                                    className: \"space-y-12\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_13__.motion.h1, {\n                                            initial: {\n                                                opacity: 0,\n                                                y: 20\n                                            },\n                                            animate: {\n                                                opacity: 1,\n                                                y: 0\n                                            },\n                                            transition: {\n                                                delay: 0.4,\n                                                duration: 0.8\n                                            },\n                                            className: \"text-6xl md:text-8xl font-bold leading-tight float\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"block bg-gradient-to-r from-neon-blue via-neon-purple to-neon-pink bg-clip-text text-transparent\",\n                                                    children: \"CHAINSIGHT\"\n                                                }, void 0, false, {\n                                                    fileName: \"F:\\\\AI chain\\\\chainsight-production\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 100,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"block text-2xl md:text-4xl mt-4 neon-glow-blue\",\n                                                    children: \"by Connectouch\"\n                                                }, void 0, false, {\n                                                    fileName: \"F:\\\\AI chain\\\\chainsight-production\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 103,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"F:\\\\AI chain\\\\chainsight-production\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 94,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_13__.motion.p, {\n                                            initial: {\n                                                opacity: 0,\n                                                y: 20\n                                            },\n                                            animate: {\n                                                opacity: 1,\n                                                y: 0\n                                            },\n                                            transition: {\n                                                delay: 0.6,\n                                                duration: 0.8\n                                            },\n                                            className: \"text-xl md:text-2xl text-white/80 max-w-3xl mx-auto leading-relaxed\",\n                                            children: [\n                                                \"\\uD83D\\uDE80 Vibrant AI-Powered Platform with\",\n                                                ' ',\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"neon-glow-purple\",\n                                                    children: \"Real-Time Analytics\"\n                                                }, void 0, false, {\n                                                    fileName: \"F:\\\\AI chain\\\\chainsight-production\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 116,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \",\",\n                                                ' ',\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"neon-glow-pink\",\n                                                    children: \"Interactive Charts\"\n                                                }, void 0, false, {\n                                                    fileName: \"F:\\\\AI chain\\\\chainsight-production\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 117,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \", and\",\n                                                ' ',\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"neon-glow-blue\",\n                                                    children: \"Futuristic Design\"\n                                                }, void 0, false, {\n                                                    fileName: \"F:\\\\AI chain\\\\chainsight-production\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 118,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"F:\\\\AI chain\\\\chainsight-production\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 109,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_13__.motion.div, {\n                                            initial: {\n                                                opacity: 0,\n                                                y: 20\n                                            },\n                                            animate: {\n                                                opacity: 1,\n                                                y: 0\n                                            },\n                                            transition: {\n                                                delay: 0.8,\n                                                duration: 0.8\n                                            },\n                                            className: \"max-w-4xl mx-auto\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ClientOnly__WEBPACK_IMPORTED_MODULE_7__.ClientOnly, {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_AIModuleSelector__WEBPACK_IMPORTED_MODULE_4__.AIModuleSelector, {\n                                                    activeModule: activeModule,\n                                                    onModuleChange: handleModuleSelect,\n                                                    modules: modules,\n                                                    loading: modulesLoading\n                                                }, void 0, false, {\n                                                    fileName: \"F:\\\\AI chain\\\\chainsight-production\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 129,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"F:\\\\AI chain\\\\chainsight-production\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 128,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"F:\\\\AI chain\\\\chainsight-production\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 122,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"F:\\\\AI chain\\\\chainsight-production\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 88,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"F:\\\\AI chain\\\\chainsight-production\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 87,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"F:\\\\AI chain\\\\chainsight-production\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 78,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                        className: \"py-20\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"max-w-7xl mx-auto px-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ClientOnly__WEBPACK_IMPORTED_MODULE_7__.ClientOnly, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ModularDashboard__WEBPACK_IMPORTED_MODULE_3__.ModularDashboard, {\n                                    activeModule: activeModule,\n                                    marketData: marketData,\n                                    modules: modules\n                                }, void 0, false, {\n                                    fileName: \"F:\\\\AI chain\\\\chainsight-production\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 145,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"F:\\\\AI chain\\\\chainsight-production\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 144,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"F:\\\\AI chain\\\\chainsight-production\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 143,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"F:\\\\AI chain\\\\chainsight-production\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 142,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"fixed bottom-4 right-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ClientOnly__WEBPACK_IMPORTED_MODULE_7__.ClientOnly, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_SystemStatus__WEBPACK_IMPORTED_MODULE_6__.SystemStatus, {\n                                status: connectionStatus\n                            }, void 0, false, {\n                                fileName: \"F:\\\\AI chain\\\\chainsight-production\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 157,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"F:\\\\AI chain\\\\chainsight-production\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 156,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"F:\\\\AI chain\\\\chainsight-production\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 155,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ClientOnly__WEBPACK_IMPORTED_MODULE_7__.ClientOnly, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_RealTimeNotifications__WEBPACK_IMPORTED_MODULE_5__.RealTimeNotifications, {}, void 0, false, {\n                            fileName: \"F:\\\\AI chain\\\\chainsight-production\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 163,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"F:\\\\AI chain\\\\chainsight-production\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 162,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"F:\\\\AI chain\\\\chainsight-production\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 76,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ClientOnly__WEBPACK_IMPORTED_MODULE_7__.ClientOnly, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_RealTimeChat__WEBPACK_IMPORTED_MODULE_8__.RealTimeChat, {\n                    isOpen: isChatOpen,\n                    onToggle: ()=>setIsChatOpen(!isChatOpen)\n                }, void 0, false, {\n                    fileName: \"F:\\\\AI chain\\\\chainsight-production\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 169,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"F:\\\\AI chain\\\\chainsight-production\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 168,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"F:\\\\AI chain\\\\chainsight-production\\\\src\\\\app\\\\page.tsx\",\n        lineNumber: 71,\n        columnNumber: 5\n    }, this);\n}\n_s(HomePage, \"H2FMMNnNNrVkwL6ap/tnEFWyICw=\", false, function() {\n    return [\n        _hooks_useAIModules__WEBPACK_IMPORTED_MODULE_10__.useAIModules,\n        _hooks_useRealTimeData__WEBPACK_IMPORTED_MODULE_11__.useRealTimeData,\n        _lib_debug__WEBPACK_IMPORTED_MODULE_12__.useDebug,\n        _store_useAppStore__WEBPACK_IMPORTED_MODULE_9__.useAppActions,\n        _store_useAppStore__WEBPACK_IMPORTED_MODULE_9__.useSettings\n    ];\n});\n_c = HomePage;\nvar _c;\n$RefreshReg$(_c, \"HomePage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/page.tsx\n"));

/***/ })

});