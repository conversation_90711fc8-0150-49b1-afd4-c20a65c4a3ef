# Environment Configuration Example
# Copy this file to .env and fill in your actual values

# Database Configuration
DATABASE_URL=postgresql://chainsight_user:chainsight_password@localhost:5432/chainsight

# Redis Configuration
REDIS_URL=redis://:chainsight_redis_password@localhost:6379/0

# Security
SECRET_KEY=your-super-secret-key-change-in-production-minimum-32-characters

# AI Services
OPENAI_API_KEY=your-openai-api-key-here

# Environment
ENVIRONMENT=development
NODE_ENV=development

# CORS Configuration
CORS_ORIGINS=http://localhost:3000,http://127.0.0.1:3000

# Frontend Configuration
NEXT_PUBLIC_API_URL=http://localhost:8000
NEXT_PUBLIC_WS_URL=ws://localhost:8000

# Optional: External Services
COINMARKETCAP_API_KEY=your-coinmarketcap-api-key
COINGECKO_API_KEY=your-coingecko-api-key
ETHERSCAN_API_KEY=your-etherscan-api-key

# Optional: Email Configuration
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASSWORD=your-app-password

# Optional: Cloud Storage
AWS_ACCESS_KEY_ID=your-aws-access-key
AWS_SECRET_ACCESS_KEY=your-aws-secret-key
AWS_REGION=us-east-1
S3_BUCKET_NAME=chainsight-storage

# Optional: Monitoring
SENTRY_DSN=your-sentry-dsn
DATADOG_API_KEY=your-datadog-api-key
