apiVersion: v1
kind: Namespace
metadata:
  name: chainsight
  labels:
    name: chainsight
    environment: production
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: chainsight-config
  namespace: chainsight
data:
  DATABASE_URL: "**************************************************************/chainsight"
  REDIS_URL: "redis://:chainsight_redis_password@redis:6379/0"
  ENVIRONMENT: "production"
  CORS_ORIGINS: "https://chainsight.connectouch.com"
  NEXT_PUBLIC_API_URL: "https://api.chainsight.connectouch.com"
  NEXT_PUBLIC_WS_URL: "wss://api.chainsight.connectouch.com"
---
apiVersion: v1
kind: Secret
metadata:
  name: chainsight-secrets
  namespace: chainsight
type: Opaque
stringData:
  SECRET_KEY: "your-super-secret-key-change-in-production"
  OPENAI_API_KEY: "your-openai-api-key"
  POSTGRES_PASSWORD: "chainsight_password"
  REDIS_PASSWORD: "chainsight_redis_password"
