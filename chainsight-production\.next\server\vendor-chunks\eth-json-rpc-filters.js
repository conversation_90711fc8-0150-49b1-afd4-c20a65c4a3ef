/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/eth-json-rpc-filters";
exports.ids = ["vendor-chunks/eth-json-rpc-filters"];
exports.modules = {

/***/ "(ssr)/./node_modules/eth-json-rpc-filters/base-filter-history.js":
/*!******************************************************************!*\
  !*** ./node_modules/eth-json-rpc-filters/base-filter-history.js ***!
  \******************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("const BaseFilter = __webpack_require__(/*! ./base-filter */ \"(ssr)/./node_modules/eth-json-rpc-filters/base-filter.js\")\n\n// tracks all results ever recorded\nclass BaseFilterWithHistory extends BaseFilter {\n\n  constructor () {\n    super()\n    this.allResults = []\n  }\n\n  async update () {\n    throw new Error('BaseFilterWithHistory - no update method specified')\n  }\n\n  addResults (newResults) {\n    this.allResults = this.allResults.concat(newResults)\n    super.addResults(newResults)\n  }\n\n  addInitialResults (newResults) {\n    this.allResults = this.allResults.concat(newResults)\n    super.addInitialResults(newResults)\n  }\n\n  getAllResults () {\n    return this.allResults\n  }\n\n}\n\nmodule.exports = BaseFilterWithHistory//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZXRoLWpzb24tcnBjLWZpbHRlcnMvYmFzZS1maWx0ZXItaGlzdG9yeS5qcyIsIm1hcHBpbmdzIjoiQUFBQSxtQkFBbUIsbUJBQU8sQ0FBQywrRUFBZTs7QUFFMUM7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBOztBQUVBOztBQUVBIiwic291cmNlcyI6WyJGOlxcQUkgY2hhaW5cXGNoYWluc2lnaHQtcHJvZHVjdGlvblxcbm9kZV9tb2R1bGVzXFxldGgtanNvbi1ycGMtZmlsdGVyc1xcYmFzZS1maWx0ZXItaGlzdG9yeS5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJjb25zdCBCYXNlRmlsdGVyID0gcmVxdWlyZSgnLi9iYXNlLWZpbHRlcicpXG5cbi8vIHRyYWNrcyBhbGwgcmVzdWx0cyBldmVyIHJlY29yZGVkXG5jbGFzcyBCYXNlRmlsdGVyV2l0aEhpc3RvcnkgZXh0ZW5kcyBCYXNlRmlsdGVyIHtcblxuICBjb25zdHJ1Y3RvciAoKSB7XG4gICAgc3VwZXIoKVxuICAgIHRoaXMuYWxsUmVzdWx0cyA9IFtdXG4gIH1cblxuICBhc3luYyB1cGRhdGUgKCkge1xuICAgIHRocm93IG5ldyBFcnJvcignQmFzZUZpbHRlcldpdGhIaXN0b3J5IC0gbm8gdXBkYXRlIG1ldGhvZCBzcGVjaWZpZWQnKVxuICB9XG5cbiAgYWRkUmVzdWx0cyAobmV3UmVzdWx0cykge1xuICAgIHRoaXMuYWxsUmVzdWx0cyA9IHRoaXMuYWxsUmVzdWx0cy5jb25jYXQobmV3UmVzdWx0cylcbiAgICBzdXBlci5hZGRSZXN1bHRzKG5ld1Jlc3VsdHMpXG4gIH1cblxuICBhZGRJbml0aWFsUmVzdWx0cyAobmV3UmVzdWx0cykge1xuICAgIHRoaXMuYWxsUmVzdWx0cyA9IHRoaXMuYWxsUmVzdWx0cy5jb25jYXQobmV3UmVzdWx0cylcbiAgICBzdXBlci5hZGRJbml0aWFsUmVzdWx0cyhuZXdSZXN1bHRzKVxuICB9XG5cbiAgZ2V0QWxsUmVzdWx0cyAoKSB7XG4gICAgcmV0dXJuIHRoaXMuYWxsUmVzdWx0c1xuICB9XG5cbn1cblxubW9kdWxlLmV4cG9ydHMgPSBCYXNlRmlsdGVyV2l0aEhpc3RvcnkiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/eth-json-rpc-filters/base-filter-history.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/eth-json-rpc-filters/base-filter.js":
/*!**********************************************************!*\
  !*** ./node_modules/eth-json-rpc-filters/base-filter.js ***!
  \**********************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("const SafeEventEmitter = (__webpack_require__(/*! @metamask/safe-event-emitter */ \"(ssr)/./node_modules/@metamask/safe-event-emitter/dist/cjs/index.js\")[\"default\"])\n\nclass BaseFilter extends SafeEventEmitter {\n\n  constructor () {\n    super()\n    this.updates = []\n  }\n\n  async initialize () {}\n\n  async update () {\n    throw new Error('BaseFilter - no update method specified')\n  }\n\n  addResults (newResults) {\n    this.updates = this.updates.concat(newResults)\n    newResults.forEach(result => this.emit('update', result))\n  }\n\n  addInitialResults (newResults) {}\n\n  getChangesAndClear () {\n    const updates = this.updates\n    this.updates = []\n    return updates\n  }\n  \n}\n\nmodule.exports = BaseFilter\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZXRoLWpzb24tcnBjLWZpbHRlcnMvYmFzZS1maWx0ZXIuanMiLCJtYXBwaW5ncyI6IkFBQUEseUJBQXlCLDJJQUErQzs7QUFFeEU7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7O0FBRUE7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBOztBQUVBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBIiwic291cmNlcyI6WyJGOlxcQUkgY2hhaW5cXGNoYWluc2lnaHQtcHJvZHVjdGlvblxcbm9kZV9tb2R1bGVzXFxldGgtanNvbi1ycGMtZmlsdGVyc1xcYmFzZS1maWx0ZXIuanMiXSwic291cmNlc0NvbnRlbnQiOlsiY29uc3QgU2FmZUV2ZW50RW1pdHRlciA9IHJlcXVpcmUoJ0BtZXRhbWFzay9zYWZlLWV2ZW50LWVtaXR0ZXInKS5kZWZhdWx0XG5cbmNsYXNzIEJhc2VGaWx0ZXIgZXh0ZW5kcyBTYWZlRXZlbnRFbWl0dGVyIHtcblxuICBjb25zdHJ1Y3RvciAoKSB7XG4gICAgc3VwZXIoKVxuICAgIHRoaXMudXBkYXRlcyA9IFtdXG4gIH1cblxuICBhc3luYyBpbml0aWFsaXplICgpIHt9XG5cbiAgYXN5bmMgdXBkYXRlICgpIHtcbiAgICB0aHJvdyBuZXcgRXJyb3IoJ0Jhc2VGaWx0ZXIgLSBubyB1cGRhdGUgbWV0aG9kIHNwZWNpZmllZCcpXG4gIH1cblxuICBhZGRSZXN1bHRzIChuZXdSZXN1bHRzKSB7XG4gICAgdGhpcy51cGRhdGVzID0gdGhpcy51cGRhdGVzLmNvbmNhdChuZXdSZXN1bHRzKVxuICAgIG5ld1Jlc3VsdHMuZm9yRWFjaChyZXN1bHQgPT4gdGhpcy5lbWl0KCd1cGRhdGUnLCByZXN1bHQpKVxuICB9XG5cbiAgYWRkSW5pdGlhbFJlc3VsdHMgKG5ld1Jlc3VsdHMpIHt9XG5cbiAgZ2V0Q2hhbmdlc0FuZENsZWFyICgpIHtcbiAgICBjb25zdCB1cGRhdGVzID0gdGhpcy51cGRhdGVzXG4gICAgdGhpcy51cGRhdGVzID0gW11cbiAgICByZXR1cm4gdXBkYXRlc1xuICB9XG4gIFxufVxuXG5tb2R1bGUuZXhwb3J0cyA9IEJhc2VGaWx0ZXJcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/eth-json-rpc-filters/base-filter.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/eth-json-rpc-filters/block-filter.js":
/*!***********************************************************!*\
  !*** ./node_modules/eth-json-rpc-filters/block-filter.js ***!
  \***********************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("const BaseFilter = __webpack_require__(/*! ./base-filter */ \"(ssr)/./node_modules/eth-json-rpc-filters/base-filter.js\")\nconst getBlocksForRange = __webpack_require__(/*! ./getBlocksForRange */ \"(ssr)/./node_modules/eth-json-rpc-filters/getBlocksForRange.js\")\nconst { incrementHexInt } = __webpack_require__(/*! ./hexUtils */ \"(ssr)/./node_modules/eth-json-rpc-filters/hexUtils.js\")\n\nclass BlockFilter extends BaseFilter {\n\n  constructor ({ provider, params }) {\n    super()\n    this.type = 'block'\n    this.provider = provider\n  }\n\n  async update ({ oldBlock, newBlock }) {\n    const toBlock = newBlock\n    const fromBlock = incrementHexInt(oldBlock)\n    const blockBodies = await getBlocksForRange({ provider: this.provider, fromBlock, toBlock })\n    const blockHashes = blockBodies.map((block) => block.hash)\n    this.addResults(blockHashes)\n  }\n\n}\n\nmodule.exports = BlockFilter\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZXRoLWpzb24tcnBjLWZpbHRlcnMvYmxvY2stZmlsdGVyLmpzIiwibWFwcGluZ3MiOiJBQUFBLG1CQUFtQixtQkFBTyxDQUFDLCtFQUFlO0FBQzFDLDBCQUEwQixtQkFBTyxDQUFDLDJGQUFxQjtBQUN2RCxRQUFRLGtCQUFrQixFQUFFLG1CQUFPLENBQUMseUVBQVk7O0FBRWhEOztBQUVBLGlCQUFpQixrQkFBa0I7QUFDbkM7QUFDQTtBQUNBO0FBQ0E7O0FBRUEsa0JBQWtCLG9CQUFvQjtBQUN0QztBQUNBO0FBQ0Esa0RBQWtELDZDQUE2QztBQUMvRjtBQUNBO0FBQ0E7O0FBRUE7O0FBRUEiLCJzb3VyY2VzIjpbIkY6XFxBSSBjaGFpblxcY2hhaW5zaWdodC1wcm9kdWN0aW9uXFxub2RlX21vZHVsZXNcXGV0aC1qc29uLXJwYy1maWx0ZXJzXFxibG9jay1maWx0ZXIuanMiXSwic291cmNlc0NvbnRlbnQiOlsiY29uc3QgQmFzZUZpbHRlciA9IHJlcXVpcmUoJy4vYmFzZS1maWx0ZXInKVxuY29uc3QgZ2V0QmxvY2tzRm9yUmFuZ2UgPSByZXF1aXJlKCcuL2dldEJsb2Nrc0ZvclJhbmdlJylcbmNvbnN0IHsgaW5jcmVtZW50SGV4SW50IH0gPSByZXF1aXJlKCcuL2hleFV0aWxzJylcblxuY2xhc3MgQmxvY2tGaWx0ZXIgZXh0ZW5kcyBCYXNlRmlsdGVyIHtcblxuICBjb25zdHJ1Y3RvciAoeyBwcm92aWRlciwgcGFyYW1zIH0pIHtcbiAgICBzdXBlcigpXG4gICAgdGhpcy50eXBlID0gJ2Jsb2NrJ1xuICAgIHRoaXMucHJvdmlkZXIgPSBwcm92aWRlclxuICB9XG5cbiAgYXN5bmMgdXBkYXRlICh7IG9sZEJsb2NrLCBuZXdCbG9jayB9KSB7XG4gICAgY29uc3QgdG9CbG9jayA9IG5ld0Jsb2NrXG4gICAgY29uc3QgZnJvbUJsb2NrID0gaW5jcmVtZW50SGV4SW50KG9sZEJsb2NrKVxuICAgIGNvbnN0IGJsb2NrQm9kaWVzID0gYXdhaXQgZ2V0QmxvY2tzRm9yUmFuZ2UoeyBwcm92aWRlcjogdGhpcy5wcm92aWRlciwgZnJvbUJsb2NrLCB0b0Jsb2NrIH0pXG4gICAgY29uc3QgYmxvY2tIYXNoZXMgPSBibG9ja0JvZGllcy5tYXAoKGJsb2NrKSA9PiBibG9jay5oYXNoKVxuICAgIHRoaXMuYWRkUmVzdWx0cyhibG9ja0hhc2hlcylcbiAgfVxuXG59XG5cbm1vZHVsZS5leHBvcnRzID0gQmxvY2tGaWx0ZXJcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/eth-json-rpc-filters/block-filter.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/eth-json-rpc-filters/getBlocksForRange.js":
/*!****************************************************************!*\
  !*** ./node_modules/eth-json-rpc-filters/getBlocksForRange.js ***!
  \****************************************************************/
/***/ ((module) => {

eval("module.exports = getBlocksForRange\n\nasync function getBlocksForRange({ provider, fromBlock, toBlock }) {\n  if (!fromBlock) fromBlock = toBlock\n\n  const fromBlockNumber = hexToInt(fromBlock)\n  const toBlockNumber = hexToInt(toBlock)\n  const blockCountToQuery = toBlockNumber - fromBlockNumber + 1\n  // load all blocks from old to new (inclusive)\n  const missingBlockNumbers = Array(blockCountToQuery).fill()\n                              .map((_,index) => fromBlockNumber + index)\n                              .map(intToHex)\n  let blockBodies = await Promise.all(\n    missingBlockNumbers.map(blockNum => query(provider, 'eth_getBlockByNumber', [blockNum, false]))\n  )\n  blockBodies = blockBodies.filter(block => block !== null);\n  return blockBodies\n}\n\nfunction hexToInt(hexString) {\n  if (hexString === undefined || hexString === null) return hexString\n  return Number.parseInt(hexString, 16)\n}\n\nfunction incrementHexInt(hexString){\n  if (hexString === undefined || hexString === null) return hexString\n  const value = hexToInt(hexString)\n  return intToHex(value + 1)\n}\n\nfunction intToHex(int) {\n  if (int === undefined || int === null) return int\n  const hexString = int.toString(16)\n  return '0x' + hexString\n}\n\nfunction sendAsync(provider, request) {\n  return new Promise((resolve, reject) => {\n    provider.sendAsync(request, (error, response) => {\n      if (error) {\n        reject(error);\n      } else if (response.error) {\n        reject(response.error);\n      } else if (response.result) {\n        resolve(response.result);\n      } else {\n        reject(new Error(\"Result was empty\"));\n      }\n    });\n  });\n}\n\nasync function query(provider, method, params) {\n  for (let i = 0; i < 3; i++) {\n    try {\n      return await sendAsync(provider, {\n        id: 1,\n        jsonrpc: \"2.0\",\n        method,\n        params,\n      });\n    } catch (error) {\n      console.error(\n        `provider.sendAsync failed: ${error.stack || error.message || error}`\n      );\n    }\n  }\n  return null;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/eth-json-rpc-filters/getBlocksForRange.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/eth-json-rpc-filters/hexUtils.js":
/*!*******************************************************!*\
  !*** ./node_modules/eth-json-rpc-filters/hexUtils.js ***!
  \*******************************************************/
/***/ ((module) => {

eval("\nmodule.exports = {\n  minBlockRef,\n  maxBlockRef,\n  sortBlockRefs,\n  bnToHex,\n  blockRefIsNumber,\n  hexToInt,\n  incrementHexInt,\n  intToHex,\n  unsafeRandomBytes,\n}\n\nfunction minBlockRef(...refs) {\n  const sortedRefs = sortBlockRefs(refs)\n  return sortedRefs[0]\n}\n\nfunction maxBlockRef(...refs) {\n  const sortedRefs = sortBlockRefs(refs)\n  return sortedRefs[sortedRefs.length-1]\n}\n\nfunction sortBlockRefs(refs) {\n  return refs.sort((refA, refB) => {\n    if (refA === 'latest' || refB === 'earliest') return 1\n    if (refB === 'latest' || refA === 'earliest') return -1\n    return hexToInt(refA) - hexToInt(refB)\n  })\n}\n\nfunction bnToHex(bn) {\n  return '0x' + bn.toString(16)\n}\n\nfunction blockRefIsNumber(blockRef){\n  return blockRef && !['earliest', 'latest', 'pending'].includes(blockRef)\n}\n\nfunction hexToInt(hexString) {\n  if (hexString === undefined || hexString === null) return hexString\n  return Number.parseInt(hexString, 16)\n}\n\nfunction incrementHexInt(hexString){\n  if (hexString === undefined || hexString === null) return hexString\n  const value = hexToInt(hexString)\n  return intToHex(value + 1)\n}\n\nfunction intToHex(int) {\n  if (int === undefined || int === null) return int\n  let hexString = int.toString(16)\n  const needsLeftPad = hexString.length % 2\n  if (needsLeftPad) hexString = '0' + hexString\n  return '0x' + hexString\n}\n\nfunction unsafeRandomBytes(byteCount) {\n  let result = '0x'\n  for (let i = 0; i < byteCount; i++) {\n    result += unsafeRandomNibble()\n    result += unsafeRandomNibble()\n  }\n  return result\n}\n\nfunction unsafeRandomNibble() {\n  return Math.floor(Math.random() * 16).toString(16)\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/eth-json-rpc-filters/hexUtils.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/eth-json-rpc-filters/index.js":
/*!****************************************************!*\
  !*** ./node_modules/eth-json-rpc-filters/index.js ***!
  \****************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("const Mutex = (__webpack_require__(/*! async-mutex */ \"(ssr)/./node_modules/async-mutex/lib/index.js\").Mutex)\nconst { createAsyncMiddleware, createScaffoldMiddleware } = __webpack_require__(/*! json-rpc-engine */ \"(ssr)/./node_modules/json-rpc-engine/dist/index.js\")\nconst LogFilter = __webpack_require__(/*! ./log-filter.js */ \"(ssr)/./node_modules/eth-json-rpc-filters/log-filter.js\")\nconst BlockFilter = __webpack_require__(/*! ./block-filter.js */ \"(ssr)/./node_modules/eth-json-rpc-filters/block-filter.js\")\nconst TxFilter = __webpack_require__(/*! ./tx-filter.js */ \"(ssr)/./node_modules/eth-json-rpc-filters/tx-filter.js\")\nconst { intToHex, hexToInt } = __webpack_require__(/*! ./hexUtils */ \"(ssr)/./node_modules/eth-json-rpc-filters/hexUtils.js\")\n\nmodule.exports = createEthFilterMiddleware\n\nfunction createEthFilterMiddleware({ blockTracker, provider }) {\n\n  // create filter collection\n  let filterIndex = 0\n  let filters = {}\n  // create update mutex\n  const mutex = new Mutex()\n  const waitForFree = mutexMiddlewareWrapper({ mutex })\n\n  const middleware = createScaffoldMiddleware({\n    // install filters\n    eth_newFilter:                   waitForFree(toFilterCreationMiddleware(newLogFilter)),\n    eth_newBlockFilter:              waitForFree(toFilterCreationMiddleware(newBlockFilter)),\n    eth_newPendingTransactionFilter: waitForFree(toFilterCreationMiddleware(newPendingTransactionFilter)),\n    // uninstall filters\n    eth_uninstallFilter:             waitForFree(toAsyncRpcMiddleware(uninstallFilterHandler)),\n    // checking filter changes\n    eth_getFilterChanges:            waitForFree(toAsyncRpcMiddleware(getFilterChanges)),\n    eth_getFilterLogs:               waitForFree(toAsyncRpcMiddleware(getFilterLogs)),\n  })\n\n  // setup filter updating and destroy handler\n  const filterUpdater = async ({ oldBlock, newBlock }) => {\n    if (filters.length === 0) return\n    // lock update reads\n    const releaseLock = await mutex.acquire()\n    try {\n      // process all filters in parallel\n      await Promise.all(objValues(filters).map(async (filter) => {\n        try {\n         await filter.update({ oldBlock, newBlock })\n        } catch (err) {\n          // handle each error individually so filter update errors don't affect other filters\n          console.error(err)\n        }\n      }))\n    } catch (err) {\n      // log error so we don't skip the releaseLock\n      console.error(err)\n    }\n    // unlock update reads\n    releaseLock()\n  }\n\n  // expose filter methods directly\n  middleware.newLogFilter = newLogFilter\n  middleware.newBlockFilter = newBlockFilter\n  middleware.newPendingTransactionFilter = newPendingTransactionFilter\n  middleware.uninstallFilter = uninstallFilterHandler\n  middleware.getFilterChanges = getFilterChanges\n  middleware.getFilterLogs = getFilterLogs\n\n  // expose destroy method for cleanup\n  middleware.destroy = () => {\n    uninstallAllFilters()\n  }\n\n  return middleware\n\n  //\n  // new filters\n  //\n\n  async function newLogFilter(params) {\n    const filter = new LogFilter({ provider, params })\n    const filterIndex = await installFilter(filter)\n    return filter\n  }\n\n  async function newBlockFilter() {\n    const filter = new BlockFilter({ provider })\n    const filterIndex = await installFilter(filter)\n    return filter\n  }\n\n  async function newPendingTransactionFilter() {\n    const filter = new TxFilter({ provider })\n    const filterIndex = await installFilter(filter)\n    return filter\n  }\n\n  //\n  // get filter changes\n  //\n\n  async function getFilterChanges(filterIndexHex) {\n    const filterIndex = hexToInt(filterIndexHex)\n    const filter = filters[filterIndex]\n    if (!filter) {\n      throw new Error(`No filter for index \"${filterIndex}\"`)\n    }\n    const results = filter.getChangesAndClear()\n    return results\n  }\n\n  async function getFilterLogs(filterIndexHex) {\n    const filterIndex = hexToInt(filterIndexHex)\n    const filter = filters[filterIndex]\n    if (!filter) {\n      throw new Error(`No filter for index \"${filterIndex}\"`)\n    }\n    // only return results for log filters\n    let results = []\n    if (filter.type === 'log') {\n      results = filter.getAllResults()\n    }\n    return results\n  }\n\n\n  //\n  // remove filters\n  //\n\n\n  async function uninstallFilterHandler(filterIndexHex) {\n    // check filter exists\n    const filterIndex = hexToInt(filterIndexHex)\n    const filter = filters[filterIndex]\n    const result = Boolean(filter)\n    // uninstall filter\n    if (result) {\n      await uninstallFilter(filterIndex)\n    }\n    return result\n  }\n\n  //\n  // utils\n  //\n\n  async function installFilter(filter) {\n    const prevFilterCount = objValues(filters).length\n    // install filter\n    const currentBlock = await blockTracker.getLatestBlock()\n    await filter.initialize({ currentBlock })\n    filterIndex++\n    filters[filterIndex] = filter\n    filter.id = filterIndex\n    filter.idHex = intToHex(filterIndex)\n    // update block tracker subs\n    const newFilterCount = objValues(filters).length\n    updateBlockTrackerSubs({ prevFilterCount, newFilterCount })\n    return filterIndex\n  }\n\n  async function uninstallFilter(filterIndex) {\n    const prevFilterCount = objValues(filters).length\n    delete filters[filterIndex]\n    // update block tracker subs\n    const newFilterCount = objValues(filters).length\n    updateBlockTrackerSubs({ prevFilterCount, newFilterCount })\n  }\n\n  async function uninstallAllFilters() {\n    const prevFilterCount = objValues(filters).length\n    filters = {}\n    // update block tracker subs\n    updateBlockTrackerSubs({ prevFilterCount, newFilterCount: 0 })\n  }\n\n  function updateBlockTrackerSubs({ prevFilterCount, newFilterCount }) {\n    // subscribe\n    if (prevFilterCount === 0 && newFilterCount > 0) {\n      blockTracker.on('sync', filterUpdater)\n      return\n    }\n    // unsubscribe\n    if (prevFilterCount > 0 && newFilterCount === 0) {\n      blockTracker.removeListener('sync', filterUpdater)\n      return\n    }\n  }\n\n}\n\n// helper for turning filter constructors into rpc middleware\nfunction toFilterCreationMiddleware(createFilterFn) {\n  return toAsyncRpcMiddleware(async (...args) => {\n    const filter = await createFilterFn(...args)\n    const result = intToHex(filter.id)\n    return result\n  })\n}\n\n// helper for pulling out req.params and setting res.result\nfunction toAsyncRpcMiddleware(asyncFn) {\n  return createAsyncMiddleware(async (req, res) => {\n    const result = await asyncFn.apply(null, req.params)\n    res.result = result\n  })\n}\n\nfunction mutexMiddlewareWrapper({ mutex }) {\n  return (middleware) => {\n    return async (req, res, next, end) => {\n      // wait for mutex available\n      // we can release immediately because\n      // we just need to make sure updates aren't active\n      const releaseLock = await mutex.acquire()\n      releaseLock()\n      middleware(req, res, next, end)\n    }\n  }\n}\n\nfunction objValues(obj, fn){\n  const values = []\n  for (let key in obj) {\n    values.push(obj[key])\n  }\n  return values\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/eth-json-rpc-filters/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/eth-json-rpc-filters/log-filter.js":
/*!*********************************************************!*\
  !*** ./node_modules/eth-json-rpc-filters/log-filter.js ***!
  \*********************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("const EthQuery = __webpack_require__(/*! eth-query */ \"(ssr)/./node_modules/eth-query/index.js\")\nconst pify = __webpack_require__(/*! pify */ \"(ssr)/./node_modules/eth-json-rpc-filters/node_modules/pify/index.js\")\nconst BaseFilterWithHistory = __webpack_require__(/*! ./base-filter-history */ \"(ssr)/./node_modules/eth-json-rpc-filters/base-filter-history.js\")\nconst { bnToHex, hexToInt, incrementHexInt, minBlockRef, blockRefIsNumber } = __webpack_require__(/*! ./hexUtils */ \"(ssr)/./node_modules/eth-json-rpc-filters/hexUtils.js\")\n\nclass LogFilter extends BaseFilterWithHistory {\n\n  constructor ({ provider, params }) {\n    super()\n    this.type = 'log'\n    this.ethQuery = new EthQuery(provider)\n    this.params = Object.assign({\n      fromBlock: 'latest',\n      toBlock: 'latest',\n      address: undefined,\n      topics: [],\n    }, params)\n    // normalize address parameter\n    if (this.params.address) {\n      // ensure array\n      if (!Array.isArray(this.params.address)) {\n        this.params.address = [this.params.address]\n      }\n      // ensure lowercase\n      this.params.address = this.params.address.map(address => address.toLowerCase())\n    }\n  }\n\n  async initialize({ currentBlock }) {\n    // resolve params.fromBlock\n    let fromBlock = this.params.fromBlock\n    if (['latest', 'pending'].includes(fromBlock)) fromBlock = currentBlock\n    if ('earliest' === fromBlock) fromBlock = '0x0'\n    this.params.fromBlock = fromBlock\n    // set toBlock for initial lookup\n    const toBlock = minBlockRef(this.params.toBlock, currentBlock)\n    const params = Object.assign({}, this.params, { toBlock })\n    // fetch logs and add to results\n    const newLogs = await this._fetchLogs(params)\n    this.addInitialResults(newLogs)\n  }\n\n  async update ({ oldBlock, newBlock }) {\n    // configure params for this update\n    const toBlock = newBlock\n    let fromBlock\n    // oldBlock is empty on first sync\n    if (oldBlock) {\n      fromBlock = incrementHexInt(oldBlock)\n    } else {\n      fromBlock = newBlock\n    }\n    // fetch logs\n    const params = Object.assign({}, this.params, { fromBlock, toBlock })\n    const newLogs = await this._fetchLogs(params)\n    const matchingLogs = newLogs.filter(log => this.matchLog(log))\n\n    // add to results\n    this.addResults(matchingLogs)\n  }\n\n  async _fetchLogs (params) {\n    const newLogs = await pify(cb => this.ethQuery.getLogs(params, cb))()\n    // add to results\n    return newLogs\n  }\n\n  matchLog(log) {\n    // check if block number in bounds:\n    if (hexToInt(this.params.fromBlock) >= hexToInt(log.blockNumber)) return false\n    if (blockRefIsNumber(this.params.toBlock) && hexToInt(this.params.toBlock) <= hexToInt(log.blockNumber)) return false\n\n    // address is correct:\n    const normalizedLogAddress = log.address && log.address.toLowerCase()\n    if (this.params.address && normalizedLogAddress && !this.params.address.includes(normalizedLogAddress)) return false\n\n    // topics match:\n    // topics are position-dependant\n    // topics can be nested to represent `or` [[a || b], c]\n    // topics can be null, representing a wild card for that position\n    const topicsMatch = this.params.topics.every((topicPattern, index) => {\n      // pattern is longer than actual topics\n      let logTopic = log.topics[index]\n      if (!logTopic) return false\n      logTopic = logTopic.toLowerCase()\n      // normalize subTopics\n      let subtopicsToMatch = Array.isArray(topicPattern) ? topicPattern : [topicPattern]\n      // check for wild card\n      const subtopicsIncludeWildcard = subtopicsToMatch.includes(null)\n      if (subtopicsIncludeWildcard) return true\n      subtopicsToMatch = subtopicsToMatch.map(topic => topic.toLowerCase())\n      // check each possible matching topic\n      const topicDoesMatch = subtopicsToMatch.includes(logTopic)\n      return topicDoesMatch\n    })\n\n    return topicsMatch\n  }\n\n}\n\nmodule.exports = LogFilter\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/eth-json-rpc-filters/log-filter.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/eth-json-rpc-filters/node_modules/pify/index.js":
/*!**********************************************************************!*\
  !*** ./node_modules/eth-json-rpc-filters/node_modules/pify/index.js ***!
  \**********************************************************************/
/***/ ((module) => {

"use strict";
eval("\n\nconst processFn = (fn, options, proxy, unwrapped) => function (...arguments_) {\n\tconst P = options.promiseModule;\n\n\treturn new P((resolve, reject) => {\n\t\tif (options.multiArgs) {\n\t\t\targuments_.push((...result) => {\n\t\t\t\tif (options.errorFirst) {\n\t\t\t\t\tif (result[0]) {\n\t\t\t\t\t\treject(result);\n\t\t\t\t\t} else {\n\t\t\t\t\t\tresult.shift();\n\t\t\t\t\t\tresolve(result);\n\t\t\t\t\t}\n\t\t\t\t} else {\n\t\t\t\t\tresolve(result);\n\t\t\t\t}\n\t\t\t});\n\t\t} else if (options.errorFirst) {\n\t\t\targuments_.push((error, result) => {\n\t\t\t\tif (error) {\n\t\t\t\t\treject(error);\n\t\t\t\t} else {\n\t\t\t\t\tresolve(result);\n\t\t\t\t}\n\t\t\t});\n\t\t} else {\n\t\t\targuments_.push(resolve);\n\t\t}\n\n\t\tconst self = this === proxy ? unwrapped : this;\n\t\tReflect.apply(fn, self, arguments_);\n\t});\n};\n\nconst filterCache = new WeakMap();\n\nmodule.exports = (input, options) => {\n\toptions = {\n\t\texclude: [/.+(?:Sync|Stream)$/],\n\t\terrorFirst: true,\n\t\tpromiseModule: Promise,\n\t\t...options\n\t};\n\n\tconst objectType = typeof input;\n\tif (!(input !== null && (objectType === 'object' || objectType === 'function'))) {\n\t\tthrow new TypeError(`Expected \\`input\\` to be a \\`Function\\` or \\`Object\\`, got \\`${input === null ? 'null' : objectType}\\``);\n\t}\n\n\tconst filter = (target, key) => {\n\t\tlet cached = filterCache.get(target);\n\n\t\tif (!cached) {\n\t\t\tcached = {};\n\t\t\tfilterCache.set(target, cached);\n\t\t}\n\n\t\tif (key in cached) {\n\t\t\treturn cached[key];\n\t\t}\n\n\t\tconst match = pattern => (typeof pattern === 'string' || typeof key === 'symbol') ? key === pattern : pattern.test(key);\n\t\tconst desc = Reflect.getOwnPropertyDescriptor(target, key);\n\t\tconst writableOrConfigurableOwn = (desc === undefined || desc.writable || desc.configurable);\n\t\tconst included = options.include ? options.include.some(match) : !options.exclude.some(match);\n\t\tconst shouldFilter = included && writableOrConfigurableOwn;\n\t\tcached[key] = shouldFilter;\n\t\treturn shouldFilter;\n\t};\n\n\tconst cache = new WeakMap();\n\n\tconst proxy = new Proxy(input, {\n\t\tapply(target, thisArg, args) {\n\t\t\tconst cached = cache.get(target);\n\n\t\t\tif (cached) {\n\t\t\t\treturn Reflect.apply(cached, thisArg, args);\n\t\t\t}\n\n\t\t\tconst pified = options.excludeMain ? target : processFn(target, options, proxy, target);\n\t\t\tcache.set(target, pified);\n\t\t\treturn Reflect.apply(pified, thisArg, args);\n\t\t},\n\n\t\tget(target, key) {\n\t\t\tconst property = target[key];\n\n\t\t\t// eslint-disable-next-line no-use-extend-native/no-use-extend-native\n\t\t\tif (!filter(target, key) || property === Function.prototype[key]) {\n\t\t\t\treturn property;\n\t\t\t}\n\n\t\t\tconst cached = cache.get(property);\n\n\t\t\tif (cached) {\n\t\t\t\treturn cached;\n\t\t\t}\n\n\t\t\tif (typeof property === 'function') {\n\t\t\t\tconst pified = processFn(property, options, proxy, target);\n\t\t\t\tcache.set(property, pified);\n\t\t\t\treturn pified;\n\t\t\t}\n\n\t\t\treturn property;\n\t\t}\n\t});\n\n\treturn proxy;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/eth-json-rpc-filters/node_modules/pify/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/eth-json-rpc-filters/subscriptionManager.js":
/*!******************************************************************!*\
  !*** ./node_modules/eth-json-rpc-filters/subscriptionManager.js ***!
  \******************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("const SafeEventEmitter = (__webpack_require__(/*! @metamask/safe-event-emitter */ \"(ssr)/./node_modules/@metamask/safe-event-emitter/dist/cjs/index.js\")[\"default\"])\nconst { createAsyncMiddleware, createScaffoldMiddleware } = __webpack_require__(/*! json-rpc-engine */ \"(ssr)/./node_modules/json-rpc-engine/dist/index.js\")\nconst createFilterMiddleware = __webpack_require__(/*! ./index.js */ \"(ssr)/./node_modules/eth-json-rpc-filters/index.js\")\nconst { unsafeRandomBytes, incrementHexInt } = __webpack_require__(/*! ./hexUtils.js */ \"(ssr)/./node_modules/eth-json-rpc-filters/hexUtils.js\")\nconst getBlocksForRange = __webpack_require__(/*! ./getBlocksForRange.js */ \"(ssr)/./node_modules/eth-json-rpc-filters/getBlocksForRange.js\")\n\nmodule.exports = createSubscriptionMiddleware\n\n\nfunction createSubscriptionMiddleware({ blockTracker, provider }) {\n  // state and utilities for handling subscriptions\n  const subscriptions = {}\n  const filterManager = createFilterMiddleware({ blockTracker, provider })\n\n  // internal flag\n  let isDestroyed = false\n\n  // create subscriptionManager api object\n  const events = new SafeEventEmitter()\n  const middleware = createScaffoldMiddleware({\n    eth_subscribe: createAsyncMiddleware(subscribe),\n    eth_unsubscribe: createAsyncMiddleware(unsubscribe),\n  })\n  middleware.destroy = destroy\n  return { events, middleware }\n\n  async function subscribe(req, res) {\n\n    if (isDestroyed) throw new Error(\n      'SubscriptionManager - attempting to use after destroying'\n    )\n\n    const subscriptionType = req.params[0]\n    // subId is 16 byte hex string\n    const subId = unsafeRandomBytes(16)\n\n    // create sub\n    let sub\n    switch (subscriptionType) {\n      case 'newHeads':\n        sub = createSubNewHeads({ subId })\n        break\n      case 'logs':\n        const filterParams = req.params[1]\n        const filter = await filterManager.newLogFilter(filterParams)\n        sub = createSubFromFilter({ subId, filter })\n        break\n      default:\n        throw new Error(`SubscriptionManager - unsupported subscription type \"${subscriptionType}\"`)\n\n    }\n    subscriptions[subId] = sub\n\n    res.result = subId\n    return\n\n    function createSubNewHeads({ subId }) {\n      const sub = {\n        type: subscriptionType,\n        destroy: async () => {\n          blockTracker.removeListener('sync', sub.update)\n        },\n        update: async ({ oldBlock, newBlock }) => {\n          // for newHeads\n          const toBlock = newBlock\n          const fromBlock = incrementHexInt(oldBlock)\n          const rawBlocks = await getBlocksForRange({ provider, fromBlock, toBlock })\n          const results = rawBlocks.map(normalizeBlock).filter(block => block !== null)\n          results.forEach((value) => {\n            _emitSubscriptionResult(subId, value)\n          })\n        }\n      }\n      // check for subscription updates on new block\n      blockTracker.on('sync', sub.update)\n      return sub\n    }\n\n    function createSubFromFilter({ subId, filter }) {\n      filter.on('update', result => _emitSubscriptionResult(subId, result))\n      const sub = {\n        type: subscriptionType,\n        destroy: async () => {\n          return await filterManager.uninstallFilter(filter.idHex)\n        },\n      }\n      return sub\n    }\n  }\n\n  async function unsubscribe(req, res) {\n\n    if (isDestroyed) throw new Error(\n      'SubscriptionManager - attempting to use after destroying'\n    )\n\n    const id = req.params[0]\n    const subscription = subscriptions[id]\n    // if missing, return \"false\" to indicate it was not removed\n    if (!subscription) {\n      res.result = false\n      return\n    }\n    // cleanup subscription\n    delete subscriptions[id]\n    await subscription.destroy()\n    res.result = true\n  }\n\n  function _emitSubscriptionResult(filterIdHex, value) {\n    events.emit('notification', {\n      jsonrpc: '2.0',\n      method: 'eth_subscription',\n      params: {\n        subscription: filterIdHex,\n        result: value,\n      },\n    })\n  }\n\n  function destroy() {\n    events.removeAllListeners()\n    for (const id in subscriptions) {\n      subscriptions[id].destroy()\n      delete subscriptions[id]\n    }\n    isDestroyed = true\n  }\n}\n\nfunction normalizeBlock(block) {\n  if (block === null || block === undefined) {\n    return null;\n  }\n  return {\n    hash: block.hash,\n    parentHash: block.parentHash,\n    sha3Uncles: block.sha3Uncles,\n    miner: block.miner,\n    stateRoot: block.stateRoot,\n    transactionsRoot: block.transactionsRoot,\n    receiptsRoot: block.receiptsRoot,\n    logsBloom: block.logsBloom,\n    difficulty: block.difficulty,\n    number: block.number,\n    gasLimit: block.gasLimit,\n    gasUsed: block.gasUsed,\n    nonce: block.nonce,\n    mixHash: block.mixHash,\n    timestamp: block.timestamp,\n    extraData: block.extraData,\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/eth-json-rpc-filters/subscriptionManager.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/eth-json-rpc-filters/tx-filter.js":
/*!********************************************************!*\
  !*** ./node_modules/eth-json-rpc-filters/tx-filter.js ***!
  \********************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("const BaseFilter = __webpack_require__(/*! ./base-filter */ \"(ssr)/./node_modules/eth-json-rpc-filters/base-filter.js\")\nconst getBlocksForRange = __webpack_require__(/*! ./getBlocksForRange */ \"(ssr)/./node_modules/eth-json-rpc-filters/getBlocksForRange.js\")\nconst { incrementHexInt } = __webpack_require__(/*! ./hexUtils */ \"(ssr)/./node_modules/eth-json-rpc-filters/hexUtils.js\")\n\nclass TxFilter extends BaseFilter {\n\n  constructor ({ provider }) {\n    super()\n    this.type = 'tx'\n    this.provider = provider\n  }\n\n  async update ({ oldBlock }) {\n    const toBlock = oldBlock\n    const fromBlock = incrementHexInt(oldBlock)\n    const blocks = await getBlocksForRange({ provider: this.provider, fromBlock, toBlock })\n    const blockTxHashes = []\n    for (const block of blocks) {\n      blockTxHashes.push(...block.transactions)\n    }\n    // add to results\n    this.addResults(blockTxHashes)\n  }\n\n}\n\nmodule.exports = TxFilter\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZXRoLWpzb24tcnBjLWZpbHRlcnMvdHgtZmlsdGVyLmpzIiwibWFwcGluZ3MiOiJBQUFBLG1CQUFtQixtQkFBTyxDQUFDLCtFQUFlO0FBQzFDLDBCQUEwQixtQkFBTyxDQUFDLDJGQUFxQjtBQUN2RCxRQUFRLGtCQUFrQixFQUFFLG1CQUFPLENBQUMseUVBQVk7O0FBRWhEOztBQUVBLGlCQUFpQixVQUFVO0FBQzNCO0FBQ0E7QUFDQTtBQUNBOztBQUVBLGtCQUFrQixVQUFVO0FBQzVCO0FBQ0E7QUFDQSw2Q0FBNkMsNkNBQTZDO0FBQzFGO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBOztBQUVBIiwic291cmNlcyI6WyJGOlxcQUkgY2hhaW5cXGNoYWluc2lnaHQtcHJvZHVjdGlvblxcbm9kZV9tb2R1bGVzXFxldGgtanNvbi1ycGMtZmlsdGVyc1xcdHgtZmlsdGVyLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImNvbnN0IEJhc2VGaWx0ZXIgPSByZXF1aXJlKCcuL2Jhc2UtZmlsdGVyJylcbmNvbnN0IGdldEJsb2Nrc0ZvclJhbmdlID0gcmVxdWlyZSgnLi9nZXRCbG9ja3NGb3JSYW5nZScpXG5jb25zdCB7IGluY3JlbWVudEhleEludCB9ID0gcmVxdWlyZSgnLi9oZXhVdGlscycpXG5cbmNsYXNzIFR4RmlsdGVyIGV4dGVuZHMgQmFzZUZpbHRlciB7XG5cbiAgY29uc3RydWN0b3IgKHsgcHJvdmlkZXIgfSkge1xuICAgIHN1cGVyKClcbiAgICB0aGlzLnR5cGUgPSAndHgnXG4gICAgdGhpcy5wcm92aWRlciA9IHByb3ZpZGVyXG4gIH1cblxuICBhc3luYyB1cGRhdGUgKHsgb2xkQmxvY2sgfSkge1xuICAgIGNvbnN0IHRvQmxvY2sgPSBvbGRCbG9ja1xuICAgIGNvbnN0IGZyb21CbG9jayA9IGluY3JlbWVudEhleEludChvbGRCbG9jaylcbiAgICBjb25zdCBibG9ja3MgPSBhd2FpdCBnZXRCbG9ja3NGb3JSYW5nZSh7IHByb3ZpZGVyOiB0aGlzLnByb3ZpZGVyLCBmcm9tQmxvY2ssIHRvQmxvY2sgfSlcbiAgICBjb25zdCBibG9ja1R4SGFzaGVzID0gW11cbiAgICBmb3IgKGNvbnN0IGJsb2NrIG9mIGJsb2Nrcykge1xuICAgICAgYmxvY2tUeEhhc2hlcy5wdXNoKC4uLmJsb2NrLnRyYW5zYWN0aW9ucylcbiAgICB9XG4gICAgLy8gYWRkIHRvIHJlc3VsdHNcbiAgICB0aGlzLmFkZFJlc3VsdHMoYmxvY2tUeEhhc2hlcylcbiAgfVxuXG59XG5cbm1vZHVsZS5leHBvcnRzID0gVHhGaWx0ZXJcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/eth-json-rpc-filters/tx-filter.js\n");

/***/ })

};
;