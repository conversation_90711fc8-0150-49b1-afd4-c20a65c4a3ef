// Crypto Engine Types and Interfaces

export interface Token {
  address: string;
  name: string;
  symbol: string;
  decimals: number;
  totalSupply: string;
  network: string;
  logoUrl?: string;
  verified?: boolean;
}

export interface TokenBalance {
  token: Token;
  balance: string;
  balanceFormatted: string;
  usdValue?: number;
}

export interface Wallet {
  address: string;
  privateKey?: string;
  mnemonic?: string;
  network: string;
  balances: TokenBalance[];
  nativeBalance: string;
  totalUsdValue: number;
}

export interface Transaction {
  hash: string;
  from: string;
  to: string;
  value: string;
  gasPrice: string;
  gasLimit: string;
  gasUsed?: string;
  status: 'pending' | 'confirmed' | 'failed';
  blockNumber?: number;
  timestamp?: Date;
  network: string;
}

export interface MarketData {
  symbol: string;
  name: string;
  price: number;
  priceChange24h: number;
  priceChangePercentage24h: number;
  volume24h: number;
  marketCap: number;
  circulatingSupply: number;
  totalSupply: number;
  rank: number;
  lastUpdated: Date;
}

export interface PriceAlert {
  id: string;
  symbol: string;
  targetPrice: number;
  condition: 'above' | 'below';
  isActive: boolean;
  userId: string;
  createdAt: Date;
  triggeredAt?: Date;
}

export interface LiquidityPool {
  address: string;
  token0: Token;
  token1: Token;
  reserve0: string;
  reserve1: string;
  totalSupply: string;
  fee: number;
  network: string;
  dex: string;
}

export interface SwapQuote {
  inputToken: Token;
  outputToken: Token;
  inputAmount: string;
  outputAmount: string;
  priceImpact: number;
  minimumReceived: string;
  route: string[];
  gasEstimate: string;
  dex: string;
}

export interface DEXTrade {
  id: string;
  inputToken: Token;
  outputToken: Token;
  inputAmount: string;
  outputAmount: string;
  trader: string;
  transactionHash: string;
  timestamp: Date;
  dex: string;
  network: string;
}

export interface SmartContract {
  address: string;
  name: string;
  abi: any[];
  bytecode?: string;
  network: string;
  verified: boolean;
  sourceCode?: string;
  compiler?: string;
  deployedAt?: Date;
}

export interface ContractCall {
  contract: string;
  method: string;
  params: any[];
  gasLimit?: string;
  gasPrice?: string;
  value?: string;
}

export interface NFT {
  tokenId: string;
  contractAddress: string;
  name: string;
  description?: string;
  image?: string;
  attributes?: Array<{
    trait_type: string;
    value: string | number;
  }>;
  owner: string;
  network: string;
}

export interface DeFiPosition {
  protocol: string;
  type: 'lending' | 'borrowing' | 'staking' | 'farming' | 'liquidity';
  token: Token;
  amount: string;
  apy: number;
  rewards?: TokenBalance[];
  network: string;
}

export interface GasEstimate {
  gasLimit: string;
  gasPrice: string;
  gasCost: string;
  gasCostUsd: number;
  estimatedTime: number; // in seconds
}

// Enums
export enum Network {
  ETHEREUM = 'ethereum',
  POLYGON = 'polygon',
  BSC = 'bsc',
  ARBITRUM = 'arbitrum',
  OPTIMISM = 'optimism',
  AVALANCHE = 'avalanche'
}

export enum TokenStandard {
  ERC20 = 'ERC20',
  ERC721 = 'ERC721',
  ERC1155 = 'ERC1155',
  BEP20 = 'BEP20'
}

export enum TransactionType {
  TRANSFER = 'transfer',
  SWAP = 'swap',
  APPROVE = 'approve',
  STAKE = 'stake',
  UNSTAKE = 'unstake',
  CLAIM = 'claim',
  CONTRACT_INTERACTION = 'contract_interaction'
}

export enum DEX {
  UNISWAP_V2 = 'uniswap_v2',
  UNISWAP_V3 = 'uniswap_v3',
  SUSHISWAP = 'sushiswap',
  PANCAKESWAP = 'pancakeswap',
  QUICKSWAP = 'quickswap',
  CURVE = 'curve',
  BALANCER = 'balancer'
}

export enum DeFiProtocol {
  AAVE = 'aave',
  COMPOUND = 'compound',
  MAKER = 'maker',
  YEARN = 'yearn',
  CONVEX = 'convex',
  LIDO = 'lido'
}
