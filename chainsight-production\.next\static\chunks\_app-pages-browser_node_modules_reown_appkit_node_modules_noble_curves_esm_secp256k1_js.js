"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_reown_appkit_node_modules_noble_curves_esm_secp256k1_js"],{

/***/ "(app-pages-browser)/./node_modules/@reown/appkit/node_modules/@noble/curves/esm/_shortw_utils.js":
/*!************************************************************************************!*\
  !*** ./node_modules/@reown/appkit/node_modules/@noble/curves/esm/_shortw_utils.js ***!
  \************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createCurve: () => (/* binding */ createCurve),\n/* harmony export */   getHash: () => (/* binding */ getHash)\n/* harmony export */ });\n/* harmony import */ var _noble_hashes_hmac__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @noble/hashes/hmac */ \"(app-pages-browser)/./node_modules/@reown/appkit/node_modules/@noble/hashes/esm/hmac.js\");\n/* harmony import */ var _noble_hashes_utils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @noble/hashes/utils */ \"(app-pages-browser)/./node_modules/@reown/appkit/node_modules/@noble/hashes/esm/utils.js\");\n/* harmony import */ var _abstract_weierstrass_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./abstract/weierstrass.js */ \"(app-pages-browser)/./node_modules/@reown/appkit/node_modules/@noble/curves/esm/abstract/weierstrass.js\");\n/**\n * Utilities for short weierstrass curves, combined with noble-hashes.\n * @module\n */\n/*! noble-curves - MIT License (c) 2022 Paul Miller (paulmillr.com) */\n\n\n\n/** connects noble-curves to noble-hashes */\nfunction getHash(hash) {\n    return {\n        hash,\n        hmac: (key, ...msgs) => (0,_noble_hashes_hmac__WEBPACK_IMPORTED_MODULE_0__.hmac)(hash, key, (0,_noble_hashes_utils__WEBPACK_IMPORTED_MODULE_1__.concatBytes)(...msgs)),\n        randomBytes: _noble_hashes_utils__WEBPACK_IMPORTED_MODULE_1__.randomBytes,\n    };\n}\nfunction createCurve(curveDef, defHash) {\n    const create = (hash) => (0,_abstract_weierstrass_js__WEBPACK_IMPORTED_MODULE_2__.weierstrass)({ ...curveDef, ...getHash(hash) });\n    return { ...create(defHash), create };\n}\n//# sourceMappingURL=_shortw_utils.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9AcmVvd24vYXBwa2l0L25vZGVfbW9kdWxlcy9Abm9ibGUvY3VydmVzL2VzbS9fc2hvcnR3X3V0aWxzLmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7O0FBQUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUMwQztBQUNxQjtBQUNQO0FBQ3hEO0FBQ087QUFDUDtBQUNBO0FBQ0EsZ0NBQWdDLHdEQUFJLFlBQVksZ0VBQVc7QUFDM0QsbUJBQW1CO0FBQ25CO0FBQ0E7QUFDTztBQUNQLDZCQUE2QixxRUFBVyxHQUFHLCtCQUErQjtBQUMxRSxhQUFhO0FBQ2I7QUFDQSIsInNvdXJjZXMiOlsiRjpcXEFJIGNoYWluXFxjaGFpbnNpZ2h0LXByb2R1Y3Rpb25cXG5vZGVfbW9kdWxlc1xcQHJlb3duXFxhcHBraXRcXG5vZGVfbW9kdWxlc1xcQG5vYmxlXFxjdXJ2ZXNcXGVzbVxcX3Nob3J0d191dGlscy5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIFV0aWxpdGllcyBmb3Igc2hvcnQgd2VpZXJzdHJhc3MgY3VydmVzLCBjb21iaW5lZCB3aXRoIG5vYmxlLWhhc2hlcy5cbiAqIEBtb2R1bGVcbiAqL1xuLyohIG5vYmxlLWN1cnZlcyAtIE1JVCBMaWNlbnNlIChjKSAyMDIyIFBhdWwgTWlsbGVyIChwYXVsbWlsbHIuY29tKSAqL1xuaW1wb3J0IHsgaG1hYyB9IGZyb20gJ0Bub2JsZS9oYXNoZXMvaG1hYyc7XG5pbXBvcnQgeyBjb25jYXRCeXRlcywgcmFuZG9tQnl0ZXMgfSBmcm9tICdAbm9ibGUvaGFzaGVzL3V0aWxzJztcbmltcG9ydCB7IHdlaWVyc3RyYXNzIH0gZnJvbSAnLi9hYnN0cmFjdC93ZWllcnN0cmFzcy5qcyc7XG4vKiogY29ubmVjdHMgbm9ibGUtY3VydmVzIHRvIG5vYmxlLWhhc2hlcyAqL1xuZXhwb3J0IGZ1bmN0aW9uIGdldEhhc2goaGFzaCkge1xuICAgIHJldHVybiB7XG4gICAgICAgIGhhc2gsXG4gICAgICAgIGhtYWM6IChrZXksIC4uLm1zZ3MpID0+IGhtYWMoaGFzaCwga2V5LCBjb25jYXRCeXRlcyguLi5tc2dzKSksXG4gICAgICAgIHJhbmRvbUJ5dGVzLFxuICAgIH07XG59XG5leHBvcnQgZnVuY3Rpb24gY3JlYXRlQ3VydmUoY3VydmVEZWYsIGRlZkhhc2gpIHtcbiAgICBjb25zdCBjcmVhdGUgPSAoaGFzaCkgPT4gd2VpZXJzdHJhc3MoeyAuLi5jdXJ2ZURlZiwgLi4uZ2V0SGFzaChoYXNoKSB9KTtcbiAgICByZXR1cm4geyAuLi5jcmVhdGUoZGVmSGFzaCksIGNyZWF0ZSB9O1xufVxuLy8jIHNvdXJjZU1hcHBpbmdVUkw9X3Nob3J0d191dGlscy5qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@reown/appkit/node_modules/@noble/curves/esm/_shortw_utils.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/@reown/appkit/node_modules/@noble/curves/esm/abstract/curve.js":
/*!*************************************************************************************!*\
  !*** ./node_modules/@reown/appkit/node_modules/@noble/curves/esm/abstract/curve.js ***!
  \*************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   pippenger: () => (/* binding */ pippenger),\n/* harmony export */   precomputeMSMUnsafe: () => (/* binding */ precomputeMSMUnsafe),\n/* harmony export */   validateBasic: () => (/* binding */ validateBasic),\n/* harmony export */   wNAF: () => (/* binding */ wNAF)\n/* harmony export */ });\n/* harmony import */ var _modular_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./modular.js */ \"(app-pages-browser)/./node_modules/@reown/appkit/node_modules/@noble/curves/esm/abstract/modular.js\");\n/* harmony import */ var _utils_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./utils.js */ \"(app-pages-browser)/./node_modules/@reown/appkit/node_modules/@noble/curves/esm/abstract/utils.js\");\n/**\n * Methods for elliptic curve multiplication by scalars.\n * Contains wNAF, pippenger\n * @module\n */\n/*! noble-curves - MIT License (c) 2022 Paul Miller (paulmillr.com) */\n\n\nconst _0n = BigInt(0);\nconst _1n = BigInt(1);\nfunction constTimeNegate(condition, item) {\n    const neg = item.negate();\n    return condition ? neg : item;\n}\nfunction validateW(W, bits) {\n    if (!Number.isSafeInteger(W) || W <= 0 || W > bits)\n        throw new Error('invalid window size, expected [1..' + bits + '], got W=' + W);\n}\nfunction calcWOpts(W, bits) {\n    validateW(W, bits);\n    const windows = Math.ceil(bits / W) + 1; // +1, because\n    const windowSize = 2 ** (W - 1); // -1 because we skip zero\n    return { windows, windowSize };\n}\nfunction validateMSMPoints(points, c) {\n    if (!Array.isArray(points))\n        throw new Error('array expected');\n    points.forEach((p, i) => {\n        if (!(p instanceof c))\n            throw new Error('invalid point at index ' + i);\n    });\n}\nfunction validateMSMScalars(scalars, field) {\n    if (!Array.isArray(scalars))\n        throw new Error('array of scalars expected');\n    scalars.forEach((s, i) => {\n        if (!field.isValid(s))\n            throw new Error('invalid scalar at index ' + i);\n    });\n}\n// Since points in different groups cannot be equal (different object constructor),\n// we can have single place to store precomputes\nconst pointPrecomputes = new WeakMap();\nconst pointWindowSizes = new WeakMap(); // This allows use make points immutable (nothing changes inside)\nfunction getW(P) {\n    return pointWindowSizes.get(P) || 1;\n}\n/**\n * Elliptic curve multiplication of Point by scalar. Fragile.\n * Scalars should always be less than curve order: this should be checked inside of a curve itself.\n * Creates precomputation tables for fast multiplication:\n * - private scalar is split by fixed size windows of W bits\n * - every window point is collected from window's table & added to accumulator\n * - since windows are different, same point inside tables won't be accessed more than once per calc\n * - each multiplication is 'Math.ceil(CURVE_ORDER / 𝑊) + 1' point additions (fixed for any scalar)\n * - +1 window is neccessary for wNAF\n * - wNAF reduces table size: 2x less memory + 2x faster generation, but 10% slower multiplication\n *\n * @todo Research returning 2d JS array of windows, instead of a single window.\n * This would allow windows to be in different memory locations\n */\nfunction wNAF(c, bits) {\n    return {\n        constTimeNegate,\n        hasPrecomputes(elm) {\n            return getW(elm) !== 1;\n        },\n        // non-const time multiplication ladder\n        unsafeLadder(elm, n, p = c.ZERO) {\n            let d = elm;\n            while (n > _0n) {\n                if (n & _1n)\n                    p = p.add(d);\n                d = d.double();\n                n >>= _1n;\n            }\n            return p;\n        },\n        /**\n         * Creates a wNAF precomputation window. Used for caching.\n         * Default window size is set by `utils.precompute()` and is equal to 8.\n         * Number of precomputed points depends on the curve size:\n         * 2^(𝑊−1) * (Math.ceil(𝑛 / 𝑊) + 1), where:\n         * - 𝑊 is the window size\n         * - 𝑛 is the bitlength of the curve order.\n         * For a 256-bit curve and window size 8, the number of precomputed points is 128 * 33 = 4224.\n         * @param elm Point instance\n         * @param W window size\n         * @returns precomputed point tables flattened to a single array\n         */\n        precomputeWindow(elm, W) {\n            const { windows, windowSize } = calcWOpts(W, bits);\n            const points = [];\n            let p = elm;\n            let base = p;\n            for (let window = 0; window < windows; window++) {\n                base = p;\n                points.push(base);\n                // =1, because we skip zero\n                for (let i = 1; i < windowSize; i++) {\n                    base = base.add(p);\n                    points.push(base);\n                }\n                p = base.double();\n            }\n            return points;\n        },\n        /**\n         * Implements ec multiplication using precomputed tables and w-ary non-adjacent form.\n         * @param W window size\n         * @param precomputes precomputed tables\n         * @param n scalar (we don't check here, but should be less than curve order)\n         * @returns real and fake (for const-time) points\n         */\n        wNAF(W, precomputes, n) {\n            // TODO: maybe check that scalar is less than group order? wNAF behavious is undefined otherwise\n            // But need to carefully remove other checks before wNAF. ORDER == bits here\n            const { windows, windowSize } = calcWOpts(W, bits);\n            let p = c.ZERO;\n            let f = c.BASE;\n            const mask = BigInt(2 ** W - 1); // Create mask with W ones: 0b1111 for W=4 etc.\n            const maxNumber = 2 ** W;\n            const shiftBy = BigInt(W);\n            for (let window = 0; window < windows; window++) {\n                const offset = window * windowSize;\n                // Extract W bits.\n                let wbits = Number(n & mask);\n                // Shift number by W bits.\n                n >>= shiftBy;\n                // If the bits are bigger than max size, we'll split those.\n                // +224 => 256 - 32\n                if (wbits > windowSize) {\n                    wbits -= maxNumber;\n                    n += _1n;\n                }\n                // This code was first written with assumption that 'f' and 'p' will never be infinity point:\n                // since each addition is multiplied by 2 ** W, it cannot cancel each other. However,\n                // there is negate now: it is possible that negated element from low value\n                // would be the same as high element, which will create carry into next window.\n                // It's not obvious how this can fail, but still worth investigating later.\n                // Check if we're onto Zero point.\n                // Add random point inside current window to f.\n                const offset1 = offset;\n                const offset2 = offset + Math.abs(wbits) - 1; // -1 because we skip zero\n                const cond1 = window % 2 !== 0;\n                const cond2 = wbits < 0;\n                if (wbits === 0) {\n                    // The most important part for const-time getPublicKey\n                    f = f.add(constTimeNegate(cond1, precomputes[offset1]));\n                }\n                else {\n                    p = p.add(constTimeNegate(cond2, precomputes[offset2]));\n                }\n            }\n            // JIT-compiler should not eliminate f here, since it will later be used in normalizeZ()\n            // Even if the variable is still unused, there are some checks which will\n            // throw an exception, so compiler needs to prove they won't happen, which is hard.\n            // At this point there is a way to F be infinity-point even if p is not,\n            // which makes it less const-time: around 1 bigint multiply.\n            return { p, f };\n        },\n        /**\n         * Implements ec unsafe (non const-time) multiplication using precomputed tables and w-ary non-adjacent form.\n         * @param W window size\n         * @param precomputes precomputed tables\n         * @param n scalar (we don't check here, but should be less than curve order)\n         * @param acc accumulator point to add result of multiplication\n         * @returns point\n         */\n        wNAFUnsafe(W, precomputes, n, acc = c.ZERO) {\n            const { windows, windowSize } = calcWOpts(W, bits);\n            const mask = BigInt(2 ** W - 1); // Create mask with W ones: 0b1111 for W=4 etc.\n            const maxNumber = 2 ** W;\n            const shiftBy = BigInt(W);\n            for (let window = 0; window < windows; window++) {\n                const offset = window * windowSize;\n                if (n === _0n)\n                    break; // No need to go over empty scalar\n                // Extract W bits.\n                let wbits = Number(n & mask);\n                // Shift number by W bits.\n                n >>= shiftBy;\n                // If the bits are bigger than max size, we'll split those.\n                // +224 => 256 - 32\n                if (wbits > windowSize) {\n                    wbits -= maxNumber;\n                    n += _1n;\n                }\n                if (wbits === 0)\n                    continue;\n                let curr = precomputes[offset + Math.abs(wbits) - 1]; // -1 because we skip zero\n                if (wbits < 0)\n                    curr = curr.negate();\n                // NOTE: by re-using acc, we can save a lot of additions in case of MSM\n                acc = acc.add(curr);\n            }\n            return acc;\n        },\n        getPrecomputes(W, P, transform) {\n            // Calculate precomputes on a first run, reuse them after\n            let comp = pointPrecomputes.get(P);\n            if (!comp) {\n                comp = this.precomputeWindow(P, W);\n                if (W !== 1)\n                    pointPrecomputes.set(P, transform(comp));\n            }\n            return comp;\n        },\n        wNAFCached(P, n, transform) {\n            const W = getW(P);\n            return this.wNAF(W, this.getPrecomputes(W, P, transform), n);\n        },\n        wNAFCachedUnsafe(P, n, transform, prev) {\n            const W = getW(P);\n            if (W === 1)\n                return this.unsafeLadder(P, n, prev); // For W=1 ladder is ~x2 faster\n            return this.wNAFUnsafe(W, this.getPrecomputes(W, P, transform), n, prev);\n        },\n        // We calculate precomputes for elliptic curve point multiplication\n        // using windowed method. This specifies window size and\n        // stores precomputed values. Usually only base point would be precomputed.\n        setWindowSize(P, W) {\n            validateW(W, bits);\n            pointWindowSizes.set(P, W);\n            pointPrecomputes.delete(P);\n        },\n    };\n}\n/**\n * Pippenger algorithm for multi-scalar multiplication (MSM, Pa + Qb + Rc + ...).\n * 30x faster vs naive addition on L=4096, 10x faster with precomputes.\n * For N=254bit, L=1, it does: 1024 ADD + 254 DBL. For L=5: 1536 ADD + 254 DBL.\n * Algorithmically constant-time (for same L), even when 1 point + scalar, or when scalar = 0.\n * @param c Curve Point constructor\n * @param fieldN field over CURVE.N - important that it's not over CURVE.P\n * @param points array of L curve points\n * @param scalars array of L scalars (aka private keys / bigints)\n */\nfunction pippenger(c, fieldN, points, scalars) {\n    // If we split scalars by some window (let's say 8 bits), every chunk will only\n    // take 256 buckets even if there are 4096 scalars, also re-uses double.\n    // TODO:\n    // - https://eprint.iacr.org/2024/750.pdf\n    // - https://tches.iacr.org/index.php/TCHES/article/view/10287\n    // 0 is accepted in scalars\n    validateMSMPoints(points, c);\n    validateMSMScalars(scalars, fieldN);\n    if (points.length !== scalars.length)\n        throw new Error('arrays of points and scalars must have equal length');\n    const zero = c.ZERO;\n    const wbits = (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.bitLen)(BigInt(points.length));\n    const windowSize = wbits > 12 ? wbits - 3 : wbits > 4 ? wbits - 2 : wbits ? 2 : 1; // in bits\n    const MASK = (1 << windowSize) - 1;\n    const buckets = new Array(MASK + 1).fill(zero); // +1 for zero array\n    const lastBits = Math.floor((fieldN.BITS - 1) / windowSize) * windowSize;\n    let sum = zero;\n    for (let i = lastBits; i >= 0; i -= windowSize) {\n        buckets.fill(zero);\n        for (let j = 0; j < scalars.length; j++) {\n            const scalar = scalars[j];\n            const wbits = Number((scalar >> BigInt(i)) & BigInt(MASK));\n            buckets[wbits] = buckets[wbits].add(points[j]);\n        }\n        let resI = zero; // not using this will do small speed-up, but will lose ct\n        // Skip first bucket, because it is zero\n        for (let j = buckets.length - 1, sumI = zero; j > 0; j--) {\n            sumI = sumI.add(buckets[j]);\n            resI = resI.add(sumI);\n        }\n        sum = sum.add(resI);\n        if (i !== 0)\n            for (let j = 0; j < windowSize; j++)\n                sum = sum.double();\n    }\n    return sum;\n}\n/**\n * Precomputed multi-scalar multiplication (MSM, Pa + Qb + Rc + ...).\n * @param c Curve Point constructor\n * @param fieldN field over CURVE.N - important that it's not over CURVE.P\n * @param points array of L curve points\n * @returns function which multiplies points with scaars\n */\nfunction precomputeMSMUnsafe(c, fieldN, points, windowSize) {\n    /**\n     * Performance Analysis of Window-based Precomputation\n     *\n     * Base Case (256-bit scalar, 8-bit window):\n     * - Standard precomputation requires:\n     *   - 31 additions per scalar × 256 scalars = 7,936 ops\n     *   - Plus 255 summary additions = 8,191 total ops\n     *   Note: Summary additions can be optimized via accumulator\n     *\n     * Chunked Precomputation Analysis:\n     * - Using 32 chunks requires:\n     *   - 255 additions per chunk\n     *   - 256 doublings\n     *   - Total: (255 × 32) + 256 = 8,416 ops\n     *\n     * Memory Usage Comparison:\n     * Window Size | Standard Points | Chunked Points\n     * ------------|-----------------|---------------\n     *     4-bit   |     520         |      15\n     *     8-bit   |    4,224        |     255\n     *    10-bit   |   13,824        |   1,023\n     *    16-bit   |  557,056        |  65,535\n     *\n     * Key Advantages:\n     * 1. Enables larger window sizes due to reduced memory overhead\n     * 2. More efficient for smaller scalar counts:\n     *    - 16 chunks: (16 × 255) + 256 = 4,336 ops\n     *    - ~2x faster than standard 8,191 ops\n     *\n     * Limitations:\n     * - Not suitable for plain precomputes (requires 256 constant doublings)\n     * - Performance degrades with larger scalar counts:\n     *   - Optimal for ~256 scalars\n     *   - Less efficient for 4096+ scalars (Pippenger preferred)\n     */\n    validateW(windowSize, fieldN.BITS);\n    validateMSMPoints(points, c);\n    const zero = c.ZERO;\n    const tableSize = 2 ** windowSize - 1; // table size (without zero)\n    const chunks = Math.ceil(fieldN.BITS / windowSize); // chunks of item\n    const MASK = BigInt((1 << windowSize) - 1);\n    const tables = points.map((p) => {\n        const res = [];\n        for (let i = 0, acc = p; i < tableSize; i++) {\n            res.push(acc);\n            acc = acc.add(p);\n        }\n        return res;\n    });\n    return (scalars) => {\n        validateMSMScalars(scalars, fieldN);\n        if (scalars.length > points.length)\n            throw new Error('array of scalars must be smaller than array of points');\n        let res = zero;\n        for (let i = 0; i < chunks; i++) {\n            // No need to double if accumulator is still zero.\n            if (res !== zero)\n                for (let j = 0; j < windowSize; j++)\n                    res = res.double();\n            const shiftBy = BigInt(chunks * windowSize - (i + 1) * windowSize);\n            for (let j = 0; j < scalars.length; j++) {\n                const n = scalars[j];\n                const curr = Number((n >> shiftBy) & MASK);\n                if (!curr)\n                    continue; // skip zero scalars chunks\n                res = res.add(tables[j][curr - 1]);\n            }\n        }\n        return res;\n    };\n}\nfunction validateBasic(curve) {\n    (0,_modular_js__WEBPACK_IMPORTED_MODULE_1__.validateField)(curve.Fp);\n    (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.validateObject)(curve, {\n        n: 'bigint',\n        h: 'bigint',\n        Gx: 'field',\n        Gy: 'field',\n    }, {\n        nBitLength: 'isSafeInteger',\n        nByteLength: 'isSafeInteger',\n    });\n    // Set defaults\n    return Object.freeze({\n        ...(0,_modular_js__WEBPACK_IMPORTED_MODULE_1__.nLength)(curve.n, curve.nBitLength),\n        ...curve,\n        ...{ p: curve.Fp.ORDER },\n    });\n}\n//# sourceMappingURL=curve.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@reown/appkit/node_modules/@noble/curves/esm/abstract/curve.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/@reown/appkit/node_modules/@noble/curves/esm/abstract/hash-to-curve.js":
/*!*********************************************************************************************!*\
  !*** ./node_modules/@reown/appkit/node_modules/@noble/curves/esm/abstract/hash-to-curve.js ***!
  \*********************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createHasher: () => (/* binding */ createHasher),\n/* harmony export */   expand_message_xmd: () => (/* binding */ expand_message_xmd),\n/* harmony export */   expand_message_xof: () => (/* binding */ expand_message_xof),\n/* harmony export */   hash_to_field: () => (/* binding */ hash_to_field),\n/* harmony export */   isogenyMap: () => (/* binding */ isogenyMap)\n/* harmony export */ });\n/* harmony import */ var _modular_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./modular.js */ \"(app-pages-browser)/./node_modules/@reown/appkit/node_modules/@noble/curves/esm/abstract/modular.js\");\n/* harmony import */ var _utils_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./utils.js */ \"(app-pages-browser)/./node_modules/@reown/appkit/node_modules/@noble/curves/esm/abstract/utils.js\");\n\n\n// Octet Stream to Integer. \"spec\" implementation of os2ip is 2.5x slower vs bytesToNumberBE.\nconst os2ip = _utils_js__WEBPACK_IMPORTED_MODULE_0__.bytesToNumberBE;\n// Integer to Octet Stream (numberToBytesBE)\nfunction i2osp(value, length) {\n    anum(value);\n    anum(length);\n    if (value < 0 || value >= 1 << (8 * length))\n        throw new Error('invalid I2OSP input: ' + value);\n    const res = Array.from({ length }).fill(0);\n    for (let i = length - 1; i >= 0; i--) {\n        res[i] = value & 0xff;\n        value >>>= 8;\n    }\n    return new Uint8Array(res);\n}\nfunction strxor(a, b) {\n    const arr = new Uint8Array(a.length);\n    for (let i = 0; i < a.length; i++) {\n        arr[i] = a[i] ^ b[i];\n    }\n    return arr;\n}\nfunction anum(item) {\n    if (!Number.isSafeInteger(item))\n        throw new Error('number expected');\n}\n/**\n * Produces a uniformly random byte string using a cryptographic hash function H that outputs b bits.\n * [RFC 9380 5.3.1](https://www.rfc-editor.org/rfc/rfc9380#section-5.3.1).\n */\nfunction expand_message_xmd(msg, DST, lenInBytes, H) {\n    (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.abytes)(msg);\n    (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.abytes)(DST);\n    anum(lenInBytes);\n    // https://www.rfc-editor.org/rfc/rfc9380#section-5.3.3\n    if (DST.length > 255)\n        DST = H((0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.concatBytes)((0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.utf8ToBytes)('H2C-OVERSIZE-DST-'), DST));\n    const { outputLen: b_in_bytes, blockLen: r_in_bytes } = H;\n    const ell = Math.ceil(lenInBytes / b_in_bytes);\n    if (lenInBytes > 65535 || ell > 255)\n        throw new Error('expand_message_xmd: invalid lenInBytes');\n    const DST_prime = (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.concatBytes)(DST, i2osp(DST.length, 1));\n    const Z_pad = i2osp(0, r_in_bytes);\n    const l_i_b_str = i2osp(lenInBytes, 2); // len_in_bytes_str\n    const b = new Array(ell);\n    const b_0 = H((0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.concatBytes)(Z_pad, msg, l_i_b_str, i2osp(0, 1), DST_prime));\n    b[0] = H((0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.concatBytes)(b_0, i2osp(1, 1), DST_prime));\n    for (let i = 1; i <= ell; i++) {\n        const args = [strxor(b_0, b[i - 1]), i2osp(i + 1, 1), DST_prime];\n        b[i] = H((0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.concatBytes)(...args));\n    }\n    const pseudo_random_bytes = (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.concatBytes)(...b);\n    return pseudo_random_bytes.slice(0, lenInBytes);\n}\n/**\n * Produces a uniformly random byte string using an extendable-output function (XOF) H.\n * 1. The collision resistance of H MUST be at least k bits.\n * 2. H MUST be an XOF that has been proved indifferentiable from\n *    a random oracle under a reasonable cryptographic assumption.\n * [RFC 9380 5.3.2](https://www.rfc-editor.org/rfc/rfc9380#section-5.3.2).\n */\nfunction expand_message_xof(msg, DST, lenInBytes, k, H) {\n    (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.abytes)(msg);\n    (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.abytes)(DST);\n    anum(lenInBytes);\n    // https://www.rfc-editor.org/rfc/rfc9380#section-5.3.3\n    // DST = H('H2C-OVERSIZE-DST-' || a_very_long_DST, Math.ceil((lenInBytes * k) / 8));\n    if (DST.length > 255) {\n        const dkLen = Math.ceil((2 * k) / 8);\n        DST = H.create({ dkLen }).update((0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.utf8ToBytes)('H2C-OVERSIZE-DST-')).update(DST).digest();\n    }\n    if (lenInBytes > 65535 || DST.length > 255)\n        throw new Error('expand_message_xof: invalid lenInBytes');\n    return (H.create({ dkLen: lenInBytes })\n        .update(msg)\n        .update(i2osp(lenInBytes, 2))\n        // 2. DST_prime = DST || I2OSP(len(DST), 1)\n        .update(DST)\n        .update(i2osp(DST.length, 1))\n        .digest());\n}\n/**\n * Hashes arbitrary-length byte strings to a list of one or more elements of a finite field F.\n * [RFC 9380 5.2](https://www.rfc-editor.org/rfc/rfc9380#section-5.2).\n * @param msg a byte string containing the message to hash\n * @param count the number of elements of F to output\n * @param options `{DST: string, p: bigint, m: number, k: number, expand: 'xmd' | 'xof', hash: H}`, see above\n * @returns [u_0, ..., u_(count - 1)], a list of field elements.\n */\nfunction hash_to_field(msg, count, options) {\n    (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.validateObject)(options, {\n        DST: 'stringOrUint8Array',\n        p: 'bigint',\n        m: 'isSafeInteger',\n        k: 'isSafeInteger',\n        hash: 'hash',\n    });\n    const { p, k, m, hash, expand, DST: _DST } = options;\n    (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.abytes)(msg);\n    anum(count);\n    const DST = typeof _DST === 'string' ? (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.utf8ToBytes)(_DST) : _DST;\n    const log2p = p.toString(2).length;\n    const L = Math.ceil((log2p + k) / 8); // section 5.1 of ietf draft link above\n    const len_in_bytes = count * m * L;\n    let prb; // pseudo_random_bytes\n    if (expand === 'xmd') {\n        prb = expand_message_xmd(msg, DST, len_in_bytes, hash);\n    }\n    else if (expand === 'xof') {\n        prb = expand_message_xof(msg, DST, len_in_bytes, k, hash);\n    }\n    else if (expand === '_internal_pass') {\n        // for internal tests only\n        prb = msg;\n    }\n    else {\n        throw new Error('expand must be \"xmd\" or \"xof\"');\n    }\n    const u = new Array(count);\n    for (let i = 0; i < count; i++) {\n        const e = new Array(m);\n        for (let j = 0; j < m; j++) {\n            const elm_offset = L * (j + i * m);\n            const tv = prb.subarray(elm_offset, elm_offset + L);\n            e[j] = (0,_modular_js__WEBPACK_IMPORTED_MODULE_1__.mod)(os2ip(tv), p);\n        }\n        u[i] = e;\n    }\n    return u;\n}\nfunction isogenyMap(field, map) {\n    // Make same order as in spec\n    const COEFF = map.map((i) => Array.from(i).reverse());\n    return (x, y) => {\n        const [xNum, xDen, yNum, yDen] = COEFF.map((val) => val.reduce((acc, i) => field.add(field.mul(acc, x), i)));\n        x = field.div(xNum, xDen); // xNum / xDen\n        y = field.mul(y, field.div(yNum, yDen)); // y * (yNum / yDev)\n        return { x: x, y: y };\n    };\n}\n/** Creates hash-to-curve methods from EC Point and mapToCurve function. */\nfunction createHasher(Point, mapToCurve, def) {\n    if (typeof mapToCurve !== 'function')\n        throw new Error('mapToCurve() must be defined');\n    return {\n        // Encodes byte string to elliptic curve.\n        // hash_to_curve from https://www.rfc-editor.org/rfc/rfc9380#section-3\n        hashToCurve(msg, options) {\n            const u = hash_to_field(msg, 2, { ...def, DST: def.DST, ...options });\n            const u0 = Point.fromAffine(mapToCurve(u[0]));\n            const u1 = Point.fromAffine(mapToCurve(u[1]));\n            const P = u0.add(u1).clearCofactor();\n            P.assertValidity();\n            return P;\n        },\n        // Encodes byte string to elliptic curve.\n        // encode_to_curve from https://www.rfc-editor.org/rfc/rfc9380#section-3\n        encodeToCurve(msg, options) {\n            const u = hash_to_field(msg, 1, { ...def, DST: def.encodeDST, ...options });\n            const P = Point.fromAffine(mapToCurve(u[0])).clearCofactor();\n            P.assertValidity();\n            return P;\n        },\n        // Same as encodeToCurve, but without hash\n        mapToCurve(scalars) {\n            if (!Array.isArray(scalars))\n                throw new Error('mapToCurve: expected array of bigints');\n            for (const i of scalars)\n                if (typeof i !== 'bigint')\n                    throw new Error('mapToCurve: expected array of bigints');\n            const P = Point.fromAffine(mapToCurve(scalars)).clearCofactor();\n            P.assertValidity();\n            return P;\n        },\n    };\n}\n//# sourceMappingURL=hash-to-curve.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@reown/appkit/node_modules/@noble/curves/esm/abstract/hash-to-curve.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/@reown/appkit/node_modules/@noble/curves/esm/abstract/modular.js":
/*!***************************************************************************************!*\
  !*** ./node_modules/@reown/appkit/node_modules/@noble/curves/esm/abstract/modular.js ***!
  \***************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Field: () => (/* binding */ Field),\n/* harmony export */   FpDiv: () => (/* binding */ FpDiv),\n/* harmony export */   FpInvertBatch: () => (/* binding */ FpInvertBatch),\n/* harmony export */   FpIsSquare: () => (/* binding */ FpIsSquare),\n/* harmony export */   FpLegendre: () => (/* binding */ FpLegendre),\n/* harmony export */   FpPow: () => (/* binding */ FpPow),\n/* harmony export */   FpSqrt: () => (/* binding */ FpSqrt),\n/* harmony export */   FpSqrtEven: () => (/* binding */ FpSqrtEven),\n/* harmony export */   FpSqrtOdd: () => (/* binding */ FpSqrtOdd),\n/* harmony export */   getFieldBytesLength: () => (/* binding */ getFieldBytesLength),\n/* harmony export */   getMinHashLength: () => (/* binding */ getMinHashLength),\n/* harmony export */   hashToPrivateScalar: () => (/* binding */ hashToPrivateScalar),\n/* harmony export */   invert: () => (/* binding */ invert),\n/* harmony export */   isNegativeLE: () => (/* binding */ isNegativeLE),\n/* harmony export */   mapHashToField: () => (/* binding */ mapHashToField),\n/* harmony export */   mod: () => (/* binding */ mod),\n/* harmony export */   nLength: () => (/* binding */ nLength),\n/* harmony export */   pow: () => (/* binding */ pow),\n/* harmony export */   pow2: () => (/* binding */ pow2),\n/* harmony export */   tonelliShanks: () => (/* binding */ tonelliShanks),\n/* harmony export */   validateField: () => (/* binding */ validateField)\n/* harmony export */ });\n/* harmony import */ var _utils_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./utils.js */ \"(app-pages-browser)/./node_modules/@reown/appkit/node_modules/@noble/curves/esm/abstract/utils.js\");\n/**\n * Utils for modular division and finite fields.\n * A finite field over 11 is integer number operations `mod 11`.\n * There is no division: it is replaced by modular multiplicative inverse.\n * @module\n */\n/*! noble-curves - MIT License (c) 2022 Paul Miller (paulmillr.com) */\n\n// prettier-ignore\nconst _0n = BigInt(0), _1n = BigInt(1), _2n = /* @__PURE__ */ BigInt(2), _3n = /* @__PURE__ */ BigInt(3);\n// prettier-ignore\nconst _4n = /* @__PURE__ */ BigInt(4), _5n = /* @__PURE__ */ BigInt(5), _8n = /* @__PURE__ */ BigInt(8);\n// prettier-ignore\nconst _9n = /* @__PURE__ */ BigInt(9), _16n = /* @__PURE__ */ BigInt(16);\n// Calculates a modulo b\nfunction mod(a, b) {\n    const result = a % b;\n    return result >= _0n ? result : b + result;\n}\n/**\n * Efficiently raise num to power and do modular division.\n * Unsafe in some contexts: uses ladder, so can expose bigint bits.\n * @todo use field version && remove\n * @example\n * pow(2n, 6n, 11n) // 64n % 11n == 9n\n */\nfunction pow(num, power, modulo) {\n    if (power < _0n)\n        throw new Error('invalid exponent, negatives unsupported');\n    if (modulo <= _0n)\n        throw new Error('invalid modulus');\n    if (modulo === _1n)\n        return _0n;\n    let res = _1n;\n    while (power > _0n) {\n        if (power & _1n)\n            res = (res * num) % modulo;\n        num = (num * num) % modulo;\n        power >>= _1n;\n    }\n    return res;\n}\n/** Does `x^(2^power)` mod p. `pow2(30, 4)` == `30^(2^4)` */\nfunction pow2(x, power, modulo) {\n    let res = x;\n    while (power-- > _0n) {\n        res *= res;\n        res %= modulo;\n    }\n    return res;\n}\n/**\n * Inverses number over modulo.\n * Implemented using [Euclidean GCD](https://brilliant.org/wiki/extended-euclidean-algorithm/).\n */\nfunction invert(number, modulo) {\n    if (number === _0n)\n        throw new Error('invert: expected non-zero number');\n    if (modulo <= _0n)\n        throw new Error('invert: expected positive modulus, got ' + modulo);\n    // Fermat's little theorem \"CT-like\" version inv(n) = n^(m-2) mod m is 30x slower.\n    let a = mod(number, modulo);\n    let b = modulo;\n    // prettier-ignore\n    let x = _0n, y = _1n, u = _1n, v = _0n;\n    while (a !== _0n) {\n        // JIT applies optimization if those two lines follow each other\n        const q = b / a;\n        const r = b % a;\n        const m = x - u * q;\n        const n = y - v * q;\n        // prettier-ignore\n        b = a, a = r, x = u, y = v, u = m, v = n;\n    }\n    const gcd = b;\n    if (gcd !== _1n)\n        throw new Error('invert: does not exist');\n    return mod(x, modulo);\n}\n/**\n * Tonelli-Shanks square root search algorithm.\n * 1. https://eprint.iacr.org/2012/685.pdf (page 12)\n * 2. Square Roots from 1; 24, 51, 10 to Dan Shanks\n * Will start an infinite loop if field order P is not prime.\n * @param P field order\n * @returns function that takes field Fp (created from P) and number n\n */\nfunction tonelliShanks(P) {\n    // Legendre constant: used to calculate Legendre symbol (a | p),\n    // which denotes the value of a^((p-1)/2) (mod p).\n    // (a | p) ≡ 1    if a is a square (mod p)\n    // (a | p) ≡ -1   if a is not a square (mod p)\n    // (a | p) ≡ 0    if a ≡ 0 (mod p)\n    const legendreC = (P - _1n) / _2n;\n    let Q, S, Z;\n    // Step 1: By factoring out powers of 2 from p - 1,\n    // find q and s such that p - 1 = q*(2^s) with q odd\n    for (Q = P - _1n, S = 0; Q % _2n === _0n; Q /= _2n, S++)\n        ;\n    // Step 2: Select a non-square z such that (z | p) ≡ -1 and set c ≡ zq\n    for (Z = _2n; Z < P && pow(Z, legendreC, P) !== P - _1n; Z++) {\n        // Crash instead of infinity loop, we cannot reasonable count until P.\n        if (Z > 1000)\n            throw new Error('Cannot find square root: likely non-prime P');\n    }\n    // Fast-path\n    if (S === 1) {\n        const p1div4 = (P + _1n) / _4n;\n        return function tonelliFast(Fp, n) {\n            const root = Fp.pow(n, p1div4);\n            if (!Fp.eql(Fp.sqr(root), n))\n                throw new Error('Cannot find square root');\n            return root;\n        };\n    }\n    // Slow-path\n    const Q1div2 = (Q + _1n) / _2n;\n    return function tonelliSlow(Fp, n) {\n        // Step 0: Check that n is indeed a square: (n | p) should not be ≡ -1\n        if (Fp.pow(n, legendreC) === Fp.neg(Fp.ONE))\n            throw new Error('Cannot find square root');\n        let r = S;\n        // TODO: will fail at Fp2/etc\n        let g = Fp.pow(Fp.mul(Fp.ONE, Z), Q); // will update both x and b\n        let x = Fp.pow(n, Q1div2); // first guess at the square root\n        let b = Fp.pow(n, Q); // first guess at the fudge factor\n        while (!Fp.eql(b, Fp.ONE)) {\n            if (Fp.eql(b, Fp.ZERO))\n                return Fp.ZERO; // https://en.wikipedia.org/wiki/Tonelli%E2%80%93Shanks_algorithm (4. If t = 0, return r = 0)\n            // Find m such b^(2^m)==1\n            let m = 1;\n            for (let t2 = Fp.sqr(b); m < r; m++) {\n                if (Fp.eql(t2, Fp.ONE))\n                    break;\n                t2 = Fp.sqr(t2); // t2 *= t2\n            }\n            // NOTE: r-m-1 can be bigger than 32, need to convert to bigint before shift, otherwise there will be overflow\n            const ge = Fp.pow(g, _1n << BigInt(r - m - 1)); // ge = 2^(r-m-1)\n            g = Fp.sqr(ge); // g = ge * ge\n            x = Fp.mul(x, ge); // x *= ge\n            b = Fp.mul(b, g); // b *= g\n            r = m;\n        }\n        return x;\n    };\n}\n/**\n * Square root for a finite field. It will try to check if optimizations are applicable and fall back to 4:\n *\n * 1. P ≡ 3 (mod 4)\n * 2. P ≡ 5 (mod 8)\n * 3. P ≡ 9 (mod 16)\n * 4. Tonelli-Shanks algorithm\n *\n * Different algorithms can give different roots, it is up to user to decide which one they want.\n * For example there is FpSqrtOdd/FpSqrtEven to choice root based on oddness (used for hash-to-curve).\n */\nfunction FpSqrt(P) {\n    // P ≡ 3 (mod 4)\n    // √n = n^((P+1)/4)\n    if (P % _4n === _3n) {\n        // Not all roots possible!\n        // const ORDER =\n        //   0x1a0111ea397fe69a4b1ba7b6434bacd764774b84f38512bf6730d2a0f6b0f6241eabfffeb153ffffb9feffffffffaaabn;\n        // const NUM = 72057594037927816n;\n        const p1div4 = (P + _1n) / _4n;\n        return function sqrt3mod4(Fp, n) {\n            const root = Fp.pow(n, p1div4);\n            // Throw if root**2 != n\n            if (!Fp.eql(Fp.sqr(root), n))\n                throw new Error('Cannot find square root');\n            return root;\n        };\n    }\n    // Atkin algorithm for q ≡ 5 (mod 8), https://eprint.iacr.org/2012/685.pdf (page 10)\n    if (P % _8n === _5n) {\n        const c1 = (P - _5n) / _8n;\n        return function sqrt5mod8(Fp, n) {\n            const n2 = Fp.mul(n, _2n);\n            const v = Fp.pow(n2, c1);\n            const nv = Fp.mul(n, v);\n            const i = Fp.mul(Fp.mul(nv, _2n), v);\n            const root = Fp.mul(nv, Fp.sub(i, Fp.ONE));\n            if (!Fp.eql(Fp.sqr(root), n))\n                throw new Error('Cannot find square root');\n            return root;\n        };\n    }\n    // P ≡ 9 (mod 16)\n    if (P % _16n === _9n) {\n        // NOTE: tonelli is too slow for bls-Fp2 calculations even on start\n        // Means we cannot use sqrt for constants at all!\n        //\n        // const c1 = Fp.sqrt(Fp.negate(Fp.ONE)); //  1. c1 = sqrt(-1) in F, i.e., (c1^2) == -1 in F\n        // const c2 = Fp.sqrt(c1);                //  2. c2 = sqrt(c1) in F, i.e., (c2^2) == c1 in F\n        // const c3 = Fp.sqrt(Fp.negate(c1));     //  3. c3 = sqrt(-c1) in F, i.e., (c3^2) == -c1 in F\n        // const c4 = (P + _7n) / _16n;           //  4. c4 = (q + 7) / 16        # Integer arithmetic\n        // sqrt = (x) => {\n        //   let tv1 = Fp.pow(x, c4);             //  1. tv1 = x^c4\n        //   let tv2 = Fp.mul(c1, tv1);           //  2. tv2 = c1 * tv1\n        //   const tv3 = Fp.mul(c2, tv1);         //  3. tv3 = c2 * tv1\n        //   let tv4 = Fp.mul(c3, tv1);           //  4. tv4 = c3 * tv1\n        //   const e1 = Fp.equals(Fp.square(tv2), x); //  5.  e1 = (tv2^2) == x\n        //   const e2 = Fp.equals(Fp.square(tv3), x); //  6.  e2 = (tv3^2) == x\n        //   tv1 = Fp.cmov(tv1, tv2, e1); //  7. tv1 = CMOV(tv1, tv2, e1)  # Select tv2 if (tv2^2) == x\n        //   tv2 = Fp.cmov(tv4, tv3, e2); //  8. tv2 = CMOV(tv4, tv3, e2)  # Select tv3 if (tv3^2) == x\n        //   const e3 = Fp.equals(Fp.square(tv2), x); //  9.  e3 = (tv2^2) == x\n        //   return Fp.cmov(tv1, tv2, e3); //  10.  z = CMOV(tv1, tv2, e3)  # Select the sqrt from tv1 and tv2\n        // }\n    }\n    // Other cases: Tonelli-Shanks algorithm\n    return tonelliShanks(P);\n}\n// Little-endian check for first LE bit (last BE bit);\nconst isNegativeLE = (num, modulo) => (mod(num, modulo) & _1n) === _1n;\n// prettier-ignore\nconst FIELD_FIELDS = [\n    'create', 'isValid', 'is0', 'neg', 'inv', 'sqrt', 'sqr',\n    'eql', 'add', 'sub', 'mul', 'pow', 'div',\n    'addN', 'subN', 'mulN', 'sqrN'\n];\nfunction validateField(field) {\n    const initial = {\n        ORDER: 'bigint',\n        MASK: 'bigint',\n        BYTES: 'isSafeInteger',\n        BITS: 'isSafeInteger',\n    };\n    const opts = FIELD_FIELDS.reduce((map, val) => {\n        map[val] = 'function';\n        return map;\n    }, initial);\n    return (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.validateObject)(field, opts);\n}\n// Generic field functions\n/**\n * Same as `pow` but for Fp: non-constant-time.\n * Unsafe in some contexts: uses ladder, so can expose bigint bits.\n */\nfunction FpPow(f, num, power) {\n    // Should have same speed as pow for bigints\n    // TODO: benchmark!\n    if (power < _0n)\n        throw new Error('invalid exponent, negatives unsupported');\n    if (power === _0n)\n        return f.ONE;\n    if (power === _1n)\n        return num;\n    let p = f.ONE;\n    let d = num;\n    while (power > _0n) {\n        if (power & _1n)\n            p = f.mul(p, d);\n        d = f.sqr(d);\n        power >>= _1n;\n    }\n    return p;\n}\n/**\n * Efficiently invert an array of Field elements.\n * `inv(0)` will return `undefined` here: make sure to throw an error.\n */\nfunction FpInvertBatch(f, nums) {\n    const tmp = new Array(nums.length);\n    // Walk from first to last, multiply them by each other MOD p\n    const lastMultiplied = nums.reduce((acc, num, i) => {\n        if (f.is0(num))\n            return acc;\n        tmp[i] = acc;\n        return f.mul(acc, num);\n    }, f.ONE);\n    // Invert last element\n    const inverted = f.inv(lastMultiplied);\n    // Walk from last to first, multiply them by inverted each other MOD p\n    nums.reduceRight((acc, num, i) => {\n        if (f.is0(num))\n            return acc;\n        tmp[i] = f.mul(acc, tmp[i]);\n        return f.mul(acc, num);\n    }, inverted);\n    return tmp;\n}\nfunction FpDiv(f, lhs, rhs) {\n    return f.mul(lhs, typeof rhs === 'bigint' ? invert(rhs, f.ORDER) : f.inv(rhs));\n}\n/**\n * Legendre symbol.\n * * (a | p) ≡ 1    if a is a square (mod p), quadratic residue\n * * (a | p) ≡ -1   if a is not a square (mod p), quadratic non residue\n * * (a | p) ≡ 0    if a ≡ 0 (mod p)\n */\nfunction FpLegendre(order) {\n    const legendreConst = (order - _1n) / _2n; // Integer arithmetic\n    return (f, x) => f.pow(x, legendreConst);\n}\n// This function returns True whenever the value x is a square in the field F.\nfunction FpIsSquare(f) {\n    const legendre = FpLegendre(f.ORDER);\n    return (x) => {\n        const p = legendre(f, x);\n        return f.eql(p, f.ZERO) || f.eql(p, f.ONE);\n    };\n}\n// CURVE.n lengths\nfunction nLength(n, nBitLength) {\n    // Bit size, byte size of CURVE.n\n    const _nBitLength = nBitLength !== undefined ? nBitLength : n.toString(2).length;\n    const nByteLength = Math.ceil(_nBitLength / 8);\n    return { nBitLength: _nBitLength, nByteLength };\n}\n/**\n * Initializes a finite field over prime.\n * Major performance optimizations:\n * * a) denormalized operations like mulN instead of mul\n * * b) same object shape: never add or remove keys\n * * c) Object.freeze\n * Fragile: always run a benchmark on a change.\n * Security note: operations don't check 'isValid' for all elements for performance reasons,\n * it is caller responsibility to check this.\n * This is low-level code, please make sure you know what you're doing.\n * @param ORDER prime positive bigint\n * @param bitLen how many bits the field consumes\n * @param isLE (def: false) if encoding / decoding should be in little-endian\n * @param redef optional faster redefinitions of sqrt and other methods\n */\nfunction Field(ORDER, bitLen, isLE = false, redef = {}) {\n    if (ORDER <= _0n)\n        throw new Error('invalid field: expected ORDER > 0, got ' + ORDER);\n    const { nBitLength: BITS, nByteLength: BYTES } = nLength(ORDER, bitLen);\n    if (BYTES > 2048)\n        throw new Error('invalid field: expected ORDER of <= 2048 bytes');\n    let sqrtP; // cached sqrtP\n    const f = Object.freeze({\n        ORDER,\n        isLE,\n        BITS,\n        BYTES,\n        MASK: (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.bitMask)(BITS),\n        ZERO: _0n,\n        ONE: _1n,\n        create: (num) => mod(num, ORDER),\n        isValid: (num) => {\n            if (typeof num !== 'bigint')\n                throw new Error('invalid field element: expected bigint, got ' + typeof num);\n            return _0n <= num && num < ORDER; // 0 is valid element, but it's not invertible\n        },\n        is0: (num) => num === _0n,\n        isOdd: (num) => (num & _1n) === _1n,\n        neg: (num) => mod(-num, ORDER),\n        eql: (lhs, rhs) => lhs === rhs,\n        sqr: (num) => mod(num * num, ORDER),\n        add: (lhs, rhs) => mod(lhs + rhs, ORDER),\n        sub: (lhs, rhs) => mod(lhs - rhs, ORDER),\n        mul: (lhs, rhs) => mod(lhs * rhs, ORDER),\n        pow: (num, power) => FpPow(f, num, power),\n        div: (lhs, rhs) => mod(lhs * invert(rhs, ORDER), ORDER),\n        // Same as above, but doesn't normalize\n        sqrN: (num) => num * num,\n        addN: (lhs, rhs) => lhs + rhs,\n        subN: (lhs, rhs) => lhs - rhs,\n        mulN: (lhs, rhs) => lhs * rhs,\n        inv: (num) => invert(num, ORDER),\n        sqrt: redef.sqrt ||\n            ((n) => {\n                if (!sqrtP)\n                    sqrtP = FpSqrt(ORDER);\n                return sqrtP(f, n);\n            }),\n        invertBatch: (lst) => FpInvertBatch(f, lst),\n        // TODO: do we really need constant cmov?\n        // We don't have const-time bigints anyway, so probably will be not very useful\n        cmov: (a, b, c) => (c ? b : a),\n        toBytes: (num) => (isLE ? (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.numberToBytesLE)(num, BYTES) : (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.numberToBytesBE)(num, BYTES)),\n        fromBytes: (bytes) => {\n            if (bytes.length !== BYTES)\n                throw new Error('Field.fromBytes: expected ' + BYTES + ' bytes, got ' + bytes.length);\n            return isLE ? (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.bytesToNumberLE)(bytes) : (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.bytesToNumberBE)(bytes);\n        },\n    });\n    return Object.freeze(f);\n}\nfunction FpSqrtOdd(Fp, elm) {\n    if (!Fp.isOdd)\n        throw new Error(\"Field doesn't have isOdd\");\n    const root = Fp.sqrt(elm);\n    return Fp.isOdd(root) ? root : Fp.neg(root);\n}\nfunction FpSqrtEven(Fp, elm) {\n    if (!Fp.isOdd)\n        throw new Error(\"Field doesn't have isOdd\");\n    const root = Fp.sqrt(elm);\n    return Fp.isOdd(root) ? Fp.neg(root) : root;\n}\n/**\n * \"Constant-time\" private key generation utility.\n * Same as mapKeyToField, but accepts less bytes (40 instead of 48 for 32-byte field).\n * Which makes it slightly more biased, less secure.\n * @deprecated use `mapKeyToField` instead\n */\nfunction hashToPrivateScalar(hash, groupOrder, isLE = false) {\n    hash = (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.ensureBytes)('privateHash', hash);\n    const hashLen = hash.length;\n    const minLen = nLength(groupOrder).nByteLength + 8;\n    if (minLen < 24 || hashLen < minLen || hashLen > 1024)\n        throw new Error('hashToPrivateScalar: expected ' + minLen + '-1024 bytes of input, got ' + hashLen);\n    const num = isLE ? (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.bytesToNumberLE)(hash) : (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.bytesToNumberBE)(hash);\n    return mod(num, groupOrder - _1n) + _1n;\n}\n/**\n * Returns total number of bytes consumed by the field element.\n * For example, 32 bytes for usual 256-bit weierstrass curve.\n * @param fieldOrder number of field elements, usually CURVE.n\n * @returns byte length of field\n */\nfunction getFieldBytesLength(fieldOrder) {\n    if (typeof fieldOrder !== 'bigint')\n        throw new Error('field order must be bigint');\n    const bitLength = fieldOrder.toString(2).length;\n    return Math.ceil(bitLength / 8);\n}\n/**\n * Returns minimal amount of bytes that can be safely reduced\n * by field order.\n * Should be 2^-128 for 128-bit curve such as P256.\n * @param fieldOrder number of field elements, usually CURVE.n\n * @returns byte length of target hash\n */\nfunction getMinHashLength(fieldOrder) {\n    const length = getFieldBytesLength(fieldOrder);\n    return length + Math.ceil(length / 2);\n}\n/**\n * \"Constant-time\" private key generation utility.\n * Can take (n + n/2) or more bytes of uniform input e.g. from CSPRNG or KDF\n * and convert them into private scalar, with the modulo bias being negligible.\n * Needs at least 48 bytes of input for 32-byte private key.\n * https://research.kudelskisecurity.com/2020/07/28/the-definitive-guide-to-modulo-bias-and-how-to-avoid-it/\n * FIPS 186-5, A.2 https://csrc.nist.gov/publications/detail/fips/186/5/final\n * RFC 9380, https://www.rfc-editor.org/rfc/rfc9380#section-5\n * @param hash hash output from SHA3 or a similar function\n * @param groupOrder size of subgroup - (e.g. secp256k1.CURVE.n)\n * @param isLE interpret hash bytes as LE num\n * @returns valid private scalar\n */\nfunction mapHashToField(key, fieldOrder, isLE = false) {\n    const len = key.length;\n    const fieldLen = getFieldBytesLength(fieldOrder);\n    const minLen = getMinHashLength(fieldOrder);\n    // No small numbers: need to understand bias story. No huge numbers: easier to detect JS timings.\n    if (len < 16 || len < minLen || len > 1024)\n        throw new Error('expected ' + minLen + '-1024 bytes of input, got ' + len);\n    const num = isLE ? (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.bytesToNumberLE)(key) : (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.bytesToNumberBE)(key);\n    // `mod(x, 11)` can sometimes produce 0. `mod(x, 10) + 1` is the same, but no 0\n    const reduced = mod(num, fieldOrder - _1n) + _1n;\n    return isLE ? (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.numberToBytesLE)(reduced, fieldLen) : (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.numberToBytesBE)(reduced, fieldLen);\n}\n//# sourceMappingURL=modular.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@reown/appkit/node_modules/@noble/curves/esm/abstract/modular.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/@reown/appkit/node_modules/@noble/curves/esm/abstract/utils.js":
/*!*************************************************************************************!*\
  !*** ./node_modules/@reown/appkit/node_modules/@noble/curves/esm/abstract/utils.js ***!
  \*************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   aInRange: () => (/* binding */ aInRange),\n/* harmony export */   abool: () => (/* binding */ abool),\n/* harmony export */   abytes: () => (/* binding */ abytes),\n/* harmony export */   bitGet: () => (/* binding */ bitGet),\n/* harmony export */   bitLen: () => (/* binding */ bitLen),\n/* harmony export */   bitMask: () => (/* binding */ bitMask),\n/* harmony export */   bitSet: () => (/* binding */ bitSet),\n/* harmony export */   bytesToHex: () => (/* binding */ bytesToHex),\n/* harmony export */   bytesToNumberBE: () => (/* binding */ bytesToNumberBE),\n/* harmony export */   bytesToNumberLE: () => (/* binding */ bytesToNumberLE),\n/* harmony export */   concatBytes: () => (/* binding */ concatBytes),\n/* harmony export */   createHmacDrbg: () => (/* binding */ createHmacDrbg),\n/* harmony export */   ensureBytes: () => (/* binding */ ensureBytes),\n/* harmony export */   equalBytes: () => (/* binding */ equalBytes),\n/* harmony export */   hexToBytes: () => (/* binding */ hexToBytes),\n/* harmony export */   hexToNumber: () => (/* binding */ hexToNumber),\n/* harmony export */   inRange: () => (/* binding */ inRange),\n/* harmony export */   isBytes: () => (/* binding */ isBytes),\n/* harmony export */   memoized: () => (/* binding */ memoized),\n/* harmony export */   notImplemented: () => (/* binding */ notImplemented),\n/* harmony export */   numberToBytesBE: () => (/* binding */ numberToBytesBE),\n/* harmony export */   numberToBytesLE: () => (/* binding */ numberToBytesLE),\n/* harmony export */   numberToHexUnpadded: () => (/* binding */ numberToHexUnpadded),\n/* harmony export */   numberToVarBytesBE: () => (/* binding */ numberToVarBytesBE),\n/* harmony export */   utf8ToBytes: () => (/* binding */ utf8ToBytes),\n/* harmony export */   validateObject: () => (/* binding */ validateObject)\n/* harmony export */ });\n/**\n * Hex, bytes and number utilities.\n * @module\n */\n/*! noble-curves - MIT License (c) 2022 Paul Miller (paulmillr.com) */\n// 100 lines of code in the file are duplicated from noble-hashes (utils).\n// This is OK: `abstract` directory does not use noble-hashes.\n// User may opt-in into using different hashing library. This way, noble-hashes\n// won't be included into their bundle.\nconst _0n = /* @__PURE__ */ BigInt(0);\nconst _1n = /* @__PURE__ */ BigInt(1);\nconst _2n = /* @__PURE__ */ BigInt(2);\nfunction isBytes(a) {\n    return a instanceof Uint8Array || (ArrayBuffer.isView(a) && a.constructor.name === 'Uint8Array');\n}\nfunction abytes(item) {\n    if (!isBytes(item))\n        throw new Error('Uint8Array expected');\n}\nfunction abool(title, value) {\n    if (typeof value !== 'boolean')\n        throw new Error(title + ' boolean expected, got ' + value);\n}\n// Array where index 0xf0 (240) is mapped to string 'f0'\nconst hexes = /* @__PURE__ */ Array.from({ length: 256 }, (_, i) => i.toString(16).padStart(2, '0'));\n/**\n * @example bytesToHex(Uint8Array.from([0xca, 0xfe, 0x01, 0x23])) // 'cafe0123'\n */\nfunction bytesToHex(bytes) {\n    abytes(bytes);\n    // pre-caching improves the speed 6x\n    let hex = '';\n    for (let i = 0; i < bytes.length; i++) {\n        hex += hexes[bytes[i]];\n    }\n    return hex;\n}\nfunction numberToHexUnpadded(num) {\n    const hex = num.toString(16);\n    return hex.length & 1 ? '0' + hex : hex;\n}\nfunction hexToNumber(hex) {\n    if (typeof hex !== 'string')\n        throw new Error('hex string expected, got ' + typeof hex);\n    return hex === '' ? _0n : BigInt('0x' + hex); // Big Endian\n}\n// We use optimized technique to convert hex string to byte array\nconst asciis = { _0: 48, _9: 57, A: 65, F: 70, a: 97, f: 102 };\nfunction asciiToBase16(ch) {\n    if (ch >= asciis._0 && ch <= asciis._9)\n        return ch - asciis._0; // '2' => 50-48\n    if (ch >= asciis.A && ch <= asciis.F)\n        return ch - (asciis.A - 10); // 'B' => 66-(65-10)\n    if (ch >= asciis.a && ch <= asciis.f)\n        return ch - (asciis.a - 10); // 'b' => 98-(97-10)\n    return;\n}\n/**\n * @example hexToBytes('cafe0123') // Uint8Array.from([0xca, 0xfe, 0x01, 0x23])\n */\nfunction hexToBytes(hex) {\n    if (typeof hex !== 'string')\n        throw new Error('hex string expected, got ' + typeof hex);\n    const hl = hex.length;\n    const al = hl / 2;\n    if (hl % 2)\n        throw new Error('hex string expected, got unpadded hex of length ' + hl);\n    const array = new Uint8Array(al);\n    for (let ai = 0, hi = 0; ai < al; ai++, hi += 2) {\n        const n1 = asciiToBase16(hex.charCodeAt(hi));\n        const n2 = asciiToBase16(hex.charCodeAt(hi + 1));\n        if (n1 === undefined || n2 === undefined) {\n            const char = hex[hi] + hex[hi + 1];\n            throw new Error('hex string expected, got non-hex character \"' + char + '\" at index ' + hi);\n        }\n        array[ai] = n1 * 16 + n2; // multiply first octet, e.g. 'a3' => 10*16+3 => 160 + 3 => 163\n    }\n    return array;\n}\n// BE: Big Endian, LE: Little Endian\nfunction bytesToNumberBE(bytes) {\n    return hexToNumber(bytesToHex(bytes));\n}\nfunction bytesToNumberLE(bytes) {\n    abytes(bytes);\n    return hexToNumber(bytesToHex(Uint8Array.from(bytes).reverse()));\n}\nfunction numberToBytesBE(n, len) {\n    return hexToBytes(n.toString(16).padStart(len * 2, '0'));\n}\nfunction numberToBytesLE(n, len) {\n    return numberToBytesBE(n, len).reverse();\n}\n// Unpadded, rarely used\nfunction numberToVarBytesBE(n) {\n    return hexToBytes(numberToHexUnpadded(n));\n}\n/**\n * Takes hex string or Uint8Array, converts to Uint8Array.\n * Validates output length.\n * Will throw error for other types.\n * @param title descriptive title for an error e.g. 'private key'\n * @param hex hex string or Uint8Array\n * @param expectedLength optional, will compare to result array's length\n * @returns\n */\nfunction ensureBytes(title, hex, expectedLength) {\n    let res;\n    if (typeof hex === 'string') {\n        try {\n            res = hexToBytes(hex);\n        }\n        catch (e) {\n            throw new Error(title + ' must be hex string or Uint8Array, cause: ' + e);\n        }\n    }\n    else if (isBytes(hex)) {\n        // Uint8Array.from() instead of hash.slice() because node.js Buffer\n        // is instance of Uint8Array, and its slice() creates **mutable** copy\n        res = Uint8Array.from(hex);\n    }\n    else {\n        throw new Error(title + ' must be hex string or Uint8Array');\n    }\n    const len = res.length;\n    if (typeof expectedLength === 'number' && len !== expectedLength)\n        throw new Error(title + ' of length ' + expectedLength + ' expected, got ' + len);\n    return res;\n}\n/**\n * Copies several Uint8Arrays into one.\n */\nfunction concatBytes(...arrays) {\n    let sum = 0;\n    for (let i = 0; i < arrays.length; i++) {\n        const a = arrays[i];\n        abytes(a);\n        sum += a.length;\n    }\n    const res = new Uint8Array(sum);\n    for (let i = 0, pad = 0; i < arrays.length; i++) {\n        const a = arrays[i];\n        res.set(a, pad);\n        pad += a.length;\n    }\n    return res;\n}\n// Compares 2 u8a-s in kinda constant time\nfunction equalBytes(a, b) {\n    if (a.length !== b.length)\n        return false;\n    let diff = 0;\n    for (let i = 0; i < a.length; i++)\n        diff |= a[i] ^ b[i];\n    return diff === 0;\n}\n/**\n * @example utf8ToBytes('abc') // new Uint8Array([97, 98, 99])\n */\nfunction utf8ToBytes(str) {\n    if (typeof str !== 'string')\n        throw new Error('string expected');\n    return new Uint8Array(new TextEncoder().encode(str)); // https://bugzil.la/1681809\n}\n// Is positive bigint\nconst isPosBig = (n) => typeof n === 'bigint' && _0n <= n;\nfunction inRange(n, min, max) {\n    return isPosBig(n) && isPosBig(min) && isPosBig(max) && min <= n && n < max;\n}\n/**\n * Asserts min <= n < max. NOTE: It's < max and not <= max.\n * @example\n * aInRange('x', x, 1n, 256n); // would assume x is in (1n..255n)\n */\nfunction aInRange(title, n, min, max) {\n    // Why min <= n < max and not a (min < n < max) OR b (min <= n <= max)?\n    // consider P=256n, min=0n, max=P\n    // - a for min=0 would require -1:          `inRange('x', x, -1n, P)`\n    // - b would commonly require subtraction:  `inRange('x', x, 0n, P - 1n)`\n    // - our way is the cleanest:               `inRange('x', x, 0n, P)\n    if (!inRange(n, min, max))\n        throw new Error('expected valid ' + title + ': ' + min + ' <= n < ' + max + ', got ' + n);\n}\n// Bit operations\n/**\n * Calculates amount of bits in a bigint.\n * Same as `n.toString(2).length`\n */\nfunction bitLen(n) {\n    let len;\n    for (len = 0; n > _0n; n >>= _1n, len += 1)\n        ;\n    return len;\n}\n/**\n * Gets single bit at position.\n * NOTE: first bit position is 0 (same as arrays)\n * Same as `!!+Array.from(n.toString(2)).reverse()[pos]`\n */\nfunction bitGet(n, pos) {\n    return (n >> BigInt(pos)) & _1n;\n}\n/**\n * Sets single bit at position.\n */\nfunction bitSet(n, pos, value) {\n    return n | ((value ? _1n : _0n) << BigInt(pos));\n}\n/**\n * Calculate mask for N bits. Not using ** operator with bigints because of old engines.\n * Same as BigInt(`0b${Array(i).fill('1').join('')}`)\n */\nconst bitMask = (n) => (_2n << BigInt(n - 1)) - _1n;\n// DRBG\nconst u8n = (data) => new Uint8Array(data); // creates Uint8Array\nconst u8fr = (arr) => Uint8Array.from(arr); // another shortcut\n/**\n * Minimal HMAC-DRBG from NIST 800-90 for RFC6979 sigs.\n * @returns function that will call DRBG until 2nd arg returns something meaningful\n * @example\n *   const drbg = createHmacDRBG<Key>(32, 32, hmac);\n *   drbg(seed, bytesToKey); // bytesToKey must return Key or undefined\n */\nfunction createHmacDrbg(hashLen, qByteLen, hmacFn) {\n    if (typeof hashLen !== 'number' || hashLen < 2)\n        throw new Error('hashLen must be a number');\n    if (typeof qByteLen !== 'number' || qByteLen < 2)\n        throw new Error('qByteLen must be a number');\n    if (typeof hmacFn !== 'function')\n        throw new Error('hmacFn must be a function');\n    // Step B, Step C: set hashLen to 8*ceil(hlen/8)\n    let v = u8n(hashLen); // Minimal non-full-spec HMAC-DRBG from NIST 800-90 for RFC6979 sigs.\n    let k = u8n(hashLen); // Steps B and C of RFC6979 3.2: set hashLen, in our case always same\n    let i = 0; // Iterations counter, will throw when over 1000\n    const reset = () => {\n        v.fill(1);\n        k.fill(0);\n        i = 0;\n    };\n    const h = (...b) => hmacFn(k, v, ...b); // hmac(k)(v, ...values)\n    const reseed = (seed = u8n()) => {\n        // HMAC-DRBG reseed() function. Steps D-G\n        k = h(u8fr([0x00]), seed); // k = hmac(k || v || 0x00 || seed)\n        v = h(); // v = hmac(k || v)\n        if (seed.length === 0)\n            return;\n        k = h(u8fr([0x01]), seed); // k = hmac(k || v || 0x01 || seed)\n        v = h(); // v = hmac(k || v)\n    };\n    const gen = () => {\n        // HMAC-DRBG generate() function\n        if (i++ >= 1000)\n            throw new Error('drbg: tried 1000 values');\n        let len = 0;\n        const out = [];\n        while (len < qByteLen) {\n            v = h();\n            const sl = v.slice();\n            out.push(sl);\n            len += v.length;\n        }\n        return concatBytes(...out);\n    };\n    const genUntil = (seed, pred) => {\n        reset();\n        reseed(seed); // Steps D-G\n        let res = undefined; // Step H: grind until k is in [1..n-1]\n        while (!(res = pred(gen())))\n            reseed();\n        reset();\n        return res;\n    };\n    return genUntil;\n}\n// Validating curves and fields\nconst validatorFns = {\n    bigint: (val) => typeof val === 'bigint',\n    function: (val) => typeof val === 'function',\n    boolean: (val) => typeof val === 'boolean',\n    string: (val) => typeof val === 'string',\n    stringOrUint8Array: (val) => typeof val === 'string' || isBytes(val),\n    isSafeInteger: (val) => Number.isSafeInteger(val),\n    array: (val) => Array.isArray(val),\n    field: (val, object) => object.Fp.isValid(val),\n    hash: (val) => typeof val === 'function' && Number.isSafeInteger(val.outputLen),\n};\n// type Record<K extends string | number | symbol, T> = { [P in K]: T; }\nfunction validateObject(object, validators, optValidators = {}) {\n    const checkField = (fieldName, type, isOptional) => {\n        const checkVal = validatorFns[type];\n        if (typeof checkVal !== 'function')\n            throw new Error('invalid validator function');\n        const val = object[fieldName];\n        if (isOptional && val === undefined)\n            return;\n        if (!checkVal(val, object)) {\n            throw new Error('param ' + String(fieldName) + ' is invalid. Expected ' + type + ', got ' + val);\n        }\n    };\n    for (const [fieldName, type] of Object.entries(validators))\n        checkField(fieldName, type, false);\n    for (const [fieldName, type] of Object.entries(optValidators))\n        checkField(fieldName, type, true);\n    return object;\n}\n// validate type tests\n// const o: { a: number; b: number; c: number } = { a: 1, b: 5, c: 6 };\n// const z0 = validateObject(o, { a: 'isSafeInteger' }, { c: 'bigint' }); // Ok!\n// // Should fail type-check\n// const z1 = validateObject(o, { a: 'tmp' }, { c: 'zz' });\n// const z2 = validateObject(o, { a: 'isSafeInteger' }, { c: 'zz' });\n// const z3 = validateObject(o, { test: 'boolean', z: 'bug' });\n// const z4 = validateObject(o, { a: 'boolean', z: 'bug' });\n/**\n * throws not implemented error\n */\nconst notImplemented = () => {\n    throw new Error('not implemented');\n};\n/**\n * Memoizes (caches) computation result.\n * Uses WeakMap: the value is going auto-cleaned by GC after last reference is removed.\n */\nfunction memoized(fn) {\n    const map = new WeakMap();\n    return (arg, ...args) => {\n        const val = map.get(arg);\n        if (val !== undefined)\n            return val;\n        const computed = fn(arg, ...args);\n        map.set(arg, computed);\n        return computed;\n    };\n}\n//# sourceMappingURL=utils.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@reown/appkit/node_modules/@noble/curves/esm/abstract/utils.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/@reown/appkit/node_modules/@noble/curves/esm/abstract/weierstrass.js":
/*!*******************************************************************************************!*\
  !*** ./node_modules/@reown/appkit/node_modules/@noble/curves/esm/abstract/weierstrass.js ***!
  \*******************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DER: () => (/* binding */ DER),\n/* harmony export */   DERErr: () => (/* binding */ DERErr),\n/* harmony export */   SWUFpSqrtRatio: () => (/* binding */ SWUFpSqrtRatio),\n/* harmony export */   mapToCurveSimpleSWU: () => (/* binding */ mapToCurveSimpleSWU),\n/* harmony export */   weierstrass: () => (/* binding */ weierstrass),\n/* harmony export */   weierstrassPoints: () => (/* binding */ weierstrassPoints)\n/* harmony export */ });\n/* harmony import */ var _curve_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./curve.js */ \"(app-pages-browser)/./node_modules/@reown/appkit/node_modules/@noble/curves/esm/abstract/curve.js\");\n/* harmony import */ var _modular_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./modular.js */ \"(app-pages-browser)/./node_modules/@reown/appkit/node_modules/@noble/curves/esm/abstract/modular.js\");\n/* harmony import */ var _utils_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./utils.js */ \"(app-pages-browser)/./node_modules/@reown/appkit/node_modules/@noble/curves/esm/abstract/utils.js\");\n/**\n * Short Weierstrass curve methods. The formula is: y² = x³ + ax + b.\n *\n * ### Design rationale for types\n *\n * * Interaction between classes from different curves should fail:\n *   `k256.Point.BASE.add(p256.Point.BASE)`\n * * For this purpose we want to use `instanceof` operator, which is fast and works during runtime\n * * Different calls of `curve()` would return different classes -\n *   `curve(params) !== curve(params)`: if somebody decided to monkey-patch their curve,\n *   it won't affect others\n *\n * TypeScript can't infer types for classes created inside a function. Classes is one instance\n * of nominative types in TypeScript and interfaces only check for shape, so it's hard to create\n * unique type for every function call.\n *\n * We can use generic types via some param, like curve opts, but that would:\n *     1. Enable interaction between `curve(params)` and `curve(params)` (curves of same params)\n *     which is hard to debug.\n *     2. Params can be generic and we can't enforce them to be constant value:\n *     if somebody creates curve from non-constant params,\n *     it would be allowed to interact with other curves with non-constant params\n *\n * @todo https://www.typescriptlang.org/docs/handbook/release-notes/typescript-2-7.html#unique-symbol\n * @module\n */\n/*! noble-curves - MIT License (c) 2022 Paul Miller (paulmillr.com) */\n\n\n\n\nfunction validateSigVerOpts(opts) {\n    if (opts.lowS !== undefined)\n        (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.abool)('lowS', opts.lowS);\n    if (opts.prehash !== undefined)\n        (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.abool)('prehash', opts.prehash);\n}\nfunction validatePointOpts(curve) {\n    const opts = (0,_curve_js__WEBPACK_IMPORTED_MODULE_1__.validateBasic)(curve);\n    _utils_js__WEBPACK_IMPORTED_MODULE_0__.validateObject(opts, {\n        a: 'field',\n        b: 'field',\n    }, {\n        allowedPrivateKeyLengths: 'array',\n        wrapPrivateKey: 'boolean',\n        isTorsionFree: 'function',\n        clearCofactor: 'function',\n        allowInfinityPoint: 'boolean',\n        fromBytes: 'function',\n        toBytes: 'function',\n    });\n    const { endo, Fp, a } = opts;\n    if (endo) {\n        if (!Fp.eql(a, Fp.ZERO)) {\n            throw new Error('invalid endomorphism, can only be defined for Koblitz curves that have a=0');\n        }\n        if (typeof endo !== 'object' ||\n            typeof endo.beta !== 'bigint' ||\n            typeof endo.splitScalar !== 'function') {\n            throw new Error('invalid endomorphism, expected beta: bigint and splitScalar: function');\n        }\n    }\n    return Object.freeze({ ...opts });\n}\nconst { bytesToNumberBE: b2n, hexToBytes: h2b } = _utils_js__WEBPACK_IMPORTED_MODULE_0__;\nclass DERErr extends Error {\n    constructor(m = '') {\n        super(m);\n    }\n}\n/**\n * ASN.1 DER encoding utilities. ASN is very complex & fragile. Format:\n *\n *     [0x30 (SEQUENCE), bytelength, 0x02 (INTEGER), intLength, R, 0x02 (INTEGER), intLength, S]\n *\n * Docs: https://letsencrypt.org/docs/a-warm-welcome-to-asn1-and-der/, https://luca.ntop.org/Teaching/Appunti/asn1.html\n */\nconst DER = {\n    // asn.1 DER encoding utils\n    Err: DERErr,\n    // Basic building block is TLV (Tag-Length-Value)\n    _tlv: {\n        encode: (tag, data) => {\n            const { Err: E } = DER;\n            if (tag < 0 || tag > 256)\n                throw new E('tlv.encode: wrong tag');\n            if (data.length & 1)\n                throw new E('tlv.encode: unpadded data');\n            const dataLen = data.length / 2;\n            const len = _utils_js__WEBPACK_IMPORTED_MODULE_0__.numberToHexUnpadded(dataLen);\n            if ((len.length / 2) & 128)\n                throw new E('tlv.encode: long form length too big');\n            // length of length with long form flag\n            const lenLen = dataLen > 127 ? _utils_js__WEBPACK_IMPORTED_MODULE_0__.numberToHexUnpadded((len.length / 2) | 128) : '';\n            const t = _utils_js__WEBPACK_IMPORTED_MODULE_0__.numberToHexUnpadded(tag);\n            return t + lenLen + len + data;\n        },\n        // v - value, l - left bytes (unparsed)\n        decode(tag, data) {\n            const { Err: E } = DER;\n            let pos = 0;\n            if (tag < 0 || tag > 256)\n                throw new E('tlv.encode: wrong tag');\n            if (data.length < 2 || data[pos++] !== tag)\n                throw new E('tlv.decode: wrong tlv');\n            const first = data[pos++];\n            const isLong = !!(first & 128); // First bit of first length byte is flag for short/long form\n            let length = 0;\n            if (!isLong)\n                length = first;\n            else {\n                // Long form: [longFlag(1bit), lengthLength(7bit), length (BE)]\n                const lenLen = first & 127;\n                if (!lenLen)\n                    throw new E('tlv.decode(long): indefinite length not supported');\n                if (lenLen > 4)\n                    throw new E('tlv.decode(long): byte length is too big'); // this will overflow u32 in js\n                const lengthBytes = data.subarray(pos, pos + lenLen);\n                if (lengthBytes.length !== lenLen)\n                    throw new E('tlv.decode: length bytes not complete');\n                if (lengthBytes[0] === 0)\n                    throw new E('tlv.decode(long): zero leftmost byte');\n                for (const b of lengthBytes)\n                    length = (length << 8) | b;\n                pos += lenLen;\n                if (length < 128)\n                    throw new E('tlv.decode(long): not minimal encoding');\n            }\n            const v = data.subarray(pos, pos + length);\n            if (v.length !== length)\n                throw new E('tlv.decode: wrong value length');\n            return { v, l: data.subarray(pos + length) };\n        },\n    },\n    // https://crypto.stackexchange.com/a/57734 Leftmost bit of first byte is 'negative' flag,\n    // since we always use positive integers here. It must always be empty:\n    // - add zero byte if exists\n    // - if next byte doesn't have a flag, leading zero is not allowed (minimal encoding)\n    _int: {\n        encode(num) {\n            const { Err: E } = DER;\n            if (num < _0n)\n                throw new E('integer: negative integers are not allowed');\n            let hex = _utils_js__WEBPACK_IMPORTED_MODULE_0__.numberToHexUnpadded(num);\n            // Pad with zero byte if negative flag is present\n            if (Number.parseInt(hex[0], 16) & 0b1000)\n                hex = '00' + hex;\n            if (hex.length & 1)\n                throw new E('unexpected DER parsing assertion: unpadded hex');\n            return hex;\n        },\n        decode(data) {\n            const { Err: E } = DER;\n            if (data[0] & 128)\n                throw new E('invalid signature integer: negative');\n            if (data[0] === 0x00 && !(data[1] & 128))\n                throw new E('invalid signature integer: unnecessary leading zero');\n            return b2n(data);\n        },\n    },\n    toSig(hex) {\n        // parse DER signature\n        const { Err: E, _int: int, _tlv: tlv } = DER;\n        const data = typeof hex === 'string' ? h2b(hex) : hex;\n        _utils_js__WEBPACK_IMPORTED_MODULE_0__.abytes(data);\n        const { v: seqBytes, l: seqLeftBytes } = tlv.decode(0x30, data);\n        if (seqLeftBytes.length)\n            throw new E('invalid signature: left bytes after parsing');\n        const { v: rBytes, l: rLeftBytes } = tlv.decode(0x02, seqBytes);\n        const { v: sBytes, l: sLeftBytes } = tlv.decode(0x02, rLeftBytes);\n        if (sLeftBytes.length)\n            throw new E('invalid signature: left bytes after parsing');\n        return { r: int.decode(rBytes), s: int.decode(sBytes) };\n    },\n    hexFromSig(sig) {\n        const { _tlv: tlv, _int: int } = DER;\n        const rs = tlv.encode(0x02, int.encode(sig.r));\n        const ss = tlv.encode(0x02, int.encode(sig.s));\n        const seq = rs + ss;\n        return tlv.encode(0x30, seq);\n    },\n};\n// Be friendly to bad ECMAScript parsers by not using bigint literals\n// prettier-ignore\nconst _0n = BigInt(0), _1n = BigInt(1), _2n = BigInt(2), _3n = BigInt(3), _4n = BigInt(4);\nfunction weierstrassPoints(opts) {\n    const CURVE = validatePointOpts(opts);\n    const { Fp } = CURVE; // All curves has same field / group length as for now, but they can differ\n    const Fn = (0,_modular_js__WEBPACK_IMPORTED_MODULE_2__.Field)(CURVE.n, CURVE.nBitLength);\n    const toBytes = CURVE.toBytes ||\n        ((_c, point, _isCompressed) => {\n            const a = point.toAffine();\n            return _utils_js__WEBPACK_IMPORTED_MODULE_0__.concatBytes(Uint8Array.from([0x04]), Fp.toBytes(a.x), Fp.toBytes(a.y));\n        });\n    const fromBytes = CURVE.fromBytes ||\n        ((bytes) => {\n            // const head = bytes[0];\n            const tail = bytes.subarray(1);\n            // if (head !== 0x04) throw new Error('Only non-compressed encoding is supported');\n            const x = Fp.fromBytes(tail.subarray(0, Fp.BYTES));\n            const y = Fp.fromBytes(tail.subarray(Fp.BYTES, 2 * Fp.BYTES));\n            return { x, y };\n        });\n    /**\n     * y² = x³ + ax + b: Short weierstrass curve formula\n     * @returns y²\n     */\n    function weierstrassEquation(x) {\n        const { a, b } = CURVE;\n        const x2 = Fp.sqr(x); // x * x\n        const x3 = Fp.mul(x2, x); // x2 * x\n        return Fp.add(Fp.add(x3, Fp.mul(x, a)), b); // x3 + a * x + b\n    }\n    // Validate whether the passed curve params are valid.\n    // We check if curve equation works for generator point.\n    // `assertValidity()` won't work: `isTorsionFree()` is not available at this point in bls12-381.\n    // ProjectivePoint class has not been initialized yet.\n    if (!Fp.eql(Fp.sqr(CURVE.Gy), weierstrassEquation(CURVE.Gx)))\n        throw new Error('bad generator point: equation left != right');\n    // Valid group elements reside in range 1..n-1\n    function isWithinCurveOrder(num) {\n        return _utils_js__WEBPACK_IMPORTED_MODULE_0__.inRange(num, _1n, CURVE.n);\n    }\n    // Validates if priv key is valid and converts it to bigint.\n    // Supports options allowedPrivateKeyLengths and wrapPrivateKey.\n    function normPrivateKeyToScalar(key) {\n        const { allowedPrivateKeyLengths: lengths, nByteLength, wrapPrivateKey, n: N } = CURVE;\n        if (lengths && typeof key !== 'bigint') {\n            if (_utils_js__WEBPACK_IMPORTED_MODULE_0__.isBytes(key))\n                key = _utils_js__WEBPACK_IMPORTED_MODULE_0__.bytesToHex(key);\n            // Normalize to hex string, pad. E.g. P521 would norm 130-132 char hex to 132-char bytes\n            if (typeof key !== 'string' || !lengths.includes(key.length))\n                throw new Error('invalid private key');\n            key = key.padStart(nByteLength * 2, '0');\n        }\n        let num;\n        try {\n            num =\n                typeof key === 'bigint'\n                    ? key\n                    : _utils_js__WEBPACK_IMPORTED_MODULE_0__.bytesToNumberBE((0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.ensureBytes)('private key', key, nByteLength));\n        }\n        catch (error) {\n            throw new Error('invalid private key, expected hex or ' + nByteLength + ' bytes, got ' + typeof key);\n        }\n        if (wrapPrivateKey)\n            num = (0,_modular_js__WEBPACK_IMPORTED_MODULE_2__.mod)(num, N); // disabled by default, enabled for BLS\n        _utils_js__WEBPACK_IMPORTED_MODULE_0__.aInRange('private key', num, _1n, N); // num in range [1..N-1]\n        return num;\n    }\n    function assertPrjPoint(other) {\n        if (!(other instanceof Point))\n            throw new Error('ProjectivePoint expected');\n    }\n    // Memoized toAffine / validity check. They are heavy. Points are immutable.\n    // Converts Projective point to affine (x, y) coordinates.\n    // Can accept precomputed Z^-1 - for example, from invertBatch.\n    // (x, y, z) ∋ (x=x/z, y=y/z)\n    const toAffineMemo = (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.memoized)((p, iz) => {\n        const { px: x, py: y, pz: z } = p;\n        // Fast-path for normalized points\n        if (Fp.eql(z, Fp.ONE))\n            return { x, y };\n        const is0 = p.is0();\n        // If invZ was 0, we return zero point. However we still want to execute\n        // all operations, so we replace invZ with a random number, 1.\n        if (iz == null)\n            iz = is0 ? Fp.ONE : Fp.inv(z);\n        const ax = Fp.mul(x, iz);\n        const ay = Fp.mul(y, iz);\n        const zz = Fp.mul(z, iz);\n        if (is0)\n            return { x: Fp.ZERO, y: Fp.ZERO };\n        if (!Fp.eql(zz, Fp.ONE))\n            throw new Error('invZ was invalid');\n        return { x: ax, y: ay };\n    });\n    // NOTE: on exception this will crash 'cached' and no value will be set.\n    // Otherwise true will be return\n    const assertValidMemo = (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.memoized)((p) => {\n        if (p.is0()) {\n            // (0, 1, 0) aka ZERO is invalid in most contexts.\n            // In BLS, ZERO can be serialized, so we allow it.\n            // (0, 0, 0) is invalid representation of ZERO.\n            if (CURVE.allowInfinityPoint && !Fp.is0(p.py))\n                return;\n            throw new Error('bad point: ZERO');\n        }\n        // Some 3rd-party test vectors require different wording between here & `fromCompressedHex`\n        const { x, y } = p.toAffine();\n        // Check if x, y are valid field elements\n        if (!Fp.isValid(x) || !Fp.isValid(y))\n            throw new Error('bad point: x or y not FE');\n        const left = Fp.sqr(y); // y²\n        const right = weierstrassEquation(x); // x³ + ax + b\n        if (!Fp.eql(left, right))\n            throw new Error('bad point: equation left != right');\n        if (!p.isTorsionFree())\n            throw new Error('bad point: not in prime-order subgroup');\n        return true;\n    });\n    /**\n     * Projective Point works in 3d / projective (homogeneous) coordinates: (x, y, z) ∋ (x=x/z, y=y/z)\n     * Default Point works in 2d / affine coordinates: (x, y)\n     * We're doing calculations in projective, because its operations don't require costly inversion.\n     */\n    class Point {\n        constructor(px, py, pz) {\n            this.px = px;\n            this.py = py;\n            this.pz = pz;\n            if (px == null || !Fp.isValid(px))\n                throw new Error('x required');\n            if (py == null || !Fp.isValid(py))\n                throw new Error('y required');\n            if (pz == null || !Fp.isValid(pz))\n                throw new Error('z required');\n            Object.freeze(this);\n        }\n        // Does not validate if the point is on-curve.\n        // Use fromHex instead, or call assertValidity() later.\n        static fromAffine(p) {\n            const { x, y } = p || {};\n            if (!p || !Fp.isValid(x) || !Fp.isValid(y))\n                throw new Error('invalid affine point');\n            if (p instanceof Point)\n                throw new Error('projective point not allowed');\n            const is0 = (i) => Fp.eql(i, Fp.ZERO);\n            // fromAffine(x:0, y:0) would produce (x:0, y:0, z:1), but we need (x:0, y:1, z:0)\n            if (is0(x) && is0(y))\n                return Point.ZERO;\n            return new Point(x, y, Fp.ONE);\n        }\n        get x() {\n            return this.toAffine().x;\n        }\n        get y() {\n            return this.toAffine().y;\n        }\n        /**\n         * Takes a bunch of Projective Points but executes only one\n         * inversion on all of them. Inversion is very slow operation,\n         * so this improves performance massively.\n         * Optimization: converts a list of projective points to a list of identical points with Z=1.\n         */\n        static normalizeZ(points) {\n            const toInv = Fp.invertBatch(points.map((p) => p.pz));\n            return points.map((p, i) => p.toAffine(toInv[i])).map(Point.fromAffine);\n        }\n        /**\n         * Converts hash string or Uint8Array to Point.\n         * @param hex short/long ECDSA hex\n         */\n        static fromHex(hex) {\n            const P = Point.fromAffine(fromBytes((0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.ensureBytes)('pointHex', hex)));\n            P.assertValidity();\n            return P;\n        }\n        // Multiplies generator point by privateKey.\n        static fromPrivateKey(privateKey) {\n            return Point.BASE.multiply(normPrivateKeyToScalar(privateKey));\n        }\n        // Multiscalar Multiplication\n        static msm(points, scalars) {\n            return (0,_curve_js__WEBPACK_IMPORTED_MODULE_1__.pippenger)(Point, Fn, points, scalars);\n        }\n        // \"Private method\", don't use it directly\n        _setWindowSize(windowSize) {\n            wnaf.setWindowSize(this, windowSize);\n        }\n        // A point on curve is valid if it conforms to equation.\n        assertValidity() {\n            assertValidMemo(this);\n        }\n        hasEvenY() {\n            const { y } = this.toAffine();\n            if (Fp.isOdd)\n                return !Fp.isOdd(y);\n            throw new Error(\"Field doesn't support isOdd\");\n        }\n        /**\n         * Compare one point to another.\n         */\n        equals(other) {\n            assertPrjPoint(other);\n            const { px: X1, py: Y1, pz: Z1 } = this;\n            const { px: X2, py: Y2, pz: Z2 } = other;\n            const U1 = Fp.eql(Fp.mul(X1, Z2), Fp.mul(X2, Z1));\n            const U2 = Fp.eql(Fp.mul(Y1, Z2), Fp.mul(Y2, Z1));\n            return U1 && U2;\n        }\n        /**\n         * Flips point to one corresponding to (x, -y) in Affine coordinates.\n         */\n        negate() {\n            return new Point(this.px, Fp.neg(this.py), this.pz);\n        }\n        // Renes-Costello-Batina exception-free doubling formula.\n        // There is 30% faster Jacobian formula, but it is not complete.\n        // https://eprint.iacr.org/2015/1060, algorithm 3\n        // Cost: 8M + 3S + 3*a + 2*b3 + 15add.\n        double() {\n            const { a, b } = CURVE;\n            const b3 = Fp.mul(b, _3n);\n            const { px: X1, py: Y1, pz: Z1 } = this;\n            let X3 = Fp.ZERO, Y3 = Fp.ZERO, Z3 = Fp.ZERO; // prettier-ignore\n            let t0 = Fp.mul(X1, X1); // step 1\n            let t1 = Fp.mul(Y1, Y1);\n            let t2 = Fp.mul(Z1, Z1);\n            let t3 = Fp.mul(X1, Y1);\n            t3 = Fp.add(t3, t3); // step 5\n            Z3 = Fp.mul(X1, Z1);\n            Z3 = Fp.add(Z3, Z3);\n            X3 = Fp.mul(a, Z3);\n            Y3 = Fp.mul(b3, t2);\n            Y3 = Fp.add(X3, Y3); // step 10\n            X3 = Fp.sub(t1, Y3);\n            Y3 = Fp.add(t1, Y3);\n            Y3 = Fp.mul(X3, Y3);\n            X3 = Fp.mul(t3, X3);\n            Z3 = Fp.mul(b3, Z3); // step 15\n            t2 = Fp.mul(a, t2);\n            t3 = Fp.sub(t0, t2);\n            t3 = Fp.mul(a, t3);\n            t3 = Fp.add(t3, Z3);\n            Z3 = Fp.add(t0, t0); // step 20\n            t0 = Fp.add(Z3, t0);\n            t0 = Fp.add(t0, t2);\n            t0 = Fp.mul(t0, t3);\n            Y3 = Fp.add(Y3, t0);\n            t2 = Fp.mul(Y1, Z1); // step 25\n            t2 = Fp.add(t2, t2);\n            t0 = Fp.mul(t2, t3);\n            X3 = Fp.sub(X3, t0);\n            Z3 = Fp.mul(t2, t1);\n            Z3 = Fp.add(Z3, Z3); // step 30\n            Z3 = Fp.add(Z3, Z3);\n            return new Point(X3, Y3, Z3);\n        }\n        // Renes-Costello-Batina exception-free addition formula.\n        // There is 30% faster Jacobian formula, but it is not complete.\n        // https://eprint.iacr.org/2015/1060, algorithm 1\n        // Cost: 12M + 0S + 3*a + 3*b3 + 23add.\n        add(other) {\n            assertPrjPoint(other);\n            const { px: X1, py: Y1, pz: Z1 } = this;\n            const { px: X2, py: Y2, pz: Z2 } = other;\n            let X3 = Fp.ZERO, Y3 = Fp.ZERO, Z3 = Fp.ZERO; // prettier-ignore\n            const a = CURVE.a;\n            const b3 = Fp.mul(CURVE.b, _3n);\n            let t0 = Fp.mul(X1, X2); // step 1\n            let t1 = Fp.mul(Y1, Y2);\n            let t2 = Fp.mul(Z1, Z2);\n            let t3 = Fp.add(X1, Y1);\n            let t4 = Fp.add(X2, Y2); // step 5\n            t3 = Fp.mul(t3, t4);\n            t4 = Fp.add(t0, t1);\n            t3 = Fp.sub(t3, t4);\n            t4 = Fp.add(X1, Z1);\n            let t5 = Fp.add(X2, Z2); // step 10\n            t4 = Fp.mul(t4, t5);\n            t5 = Fp.add(t0, t2);\n            t4 = Fp.sub(t4, t5);\n            t5 = Fp.add(Y1, Z1);\n            X3 = Fp.add(Y2, Z2); // step 15\n            t5 = Fp.mul(t5, X3);\n            X3 = Fp.add(t1, t2);\n            t5 = Fp.sub(t5, X3);\n            Z3 = Fp.mul(a, t4);\n            X3 = Fp.mul(b3, t2); // step 20\n            Z3 = Fp.add(X3, Z3);\n            X3 = Fp.sub(t1, Z3);\n            Z3 = Fp.add(t1, Z3);\n            Y3 = Fp.mul(X3, Z3);\n            t1 = Fp.add(t0, t0); // step 25\n            t1 = Fp.add(t1, t0);\n            t2 = Fp.mul(a, t2);\n            t4 = Fp.mul(b3, t4);\n            t1 = Fp.add(t1, t2);\n            t2 = Fp.sub(t0, t2); // step 30\n            t2 = Fp.mul(a, t2);\n            t4 = Fp.add(t4, t2);\n            t0 = Fp.mul(t1, t4);\n            Y3 = Fp.add(Y3, t0);\n            t0 = Fp.mul(t5, t4); // step 35\n            X3 = Fp.mul(t3, X3);\n            X3 = Fp.sub(X3, t0);\n            t0 = Fp.mul(t3, t1);\n            Z3 = Fp.mul(t5, Z3);\n            Z3 = Fp.add(Z3, t0); // step 40\n            return new Point(X3, Y3, Z3);\n        }\n        subtract(other) {\n            return this.add(other.negate());\n        }\n        is0() {\n            return this.equals(Point.ZERO);\n        }\n        wNAF(n) {\n            return wnaf.wNAFCached(this, n, Point.normalizeZ);\n        }\n        /**\n         * Non-constant-time multiplication. Uses double-and-add algorithm.\n         * It's faster, but should only be used when you don't care about\n         * an exposed private key e.g. sig verification, which works over *public* keys.\n         */\n        multiplyUnsafe(sc) {\n            const { endo, n: N } = CURVE;\n            _utils_js__WEBPACK_IMPORTED_MODULE_0__.aInRange('scalar', sc, _0n, N);\n            const I = Point.ZERO;\n            if (sc === _0n)\n                return I;\n            if (this.is0() || sc === _1n)\n                return this;\n            // Case a: no endomorphism. Case b: has precomputes.\n            if (!endo || wnaf.hasPrecomputes(this))\n                return wnaf.wNAFCachedUnsafe(this, sc, Point.normalizeZ);\n            // Case c: endomorphism\n            let { k1neg, k1, k2neg, k2 } = endo.splitScalar(sc);\n            let k1p = I;\n            let k2p = I;\n            let d = this;\n            while (k1 > _0n || k2 > _0n) {\n                if (k1 & _1n)\n                    k1p = k1p.add(d);\n                if (k2 & _1n)\n                    k2p = k2p.add(d);\n                d = d.double();\n                k1 >>= _1n;\n                k2 >>= _1n;\n            }\n            if (k1neg)\n                k1p = k1p.negate();\n            if (k2neg)\n                k2p = k2p.negate();\n            k2p = new Point(Fp.mul(k2p.px, endo.beta), k2p.py, k2p.pz);\n            return k1p.add(k2p);\n        }\n        /**\n         * Constant time multiplication.\n         * Uses wNAF method. Windowed method may be 10% faster,\n         * but takes 2x longer to generate and consumes 2x memory.\n         * Uses precomputes when available.\n         * Uses endomorphism for Koblitz curves.\n         * @param scalar by which the point would be multiplied\n         * @returns New point\n         */\n        multiply(scalar) {\n            const { endo, n: N } = CURVE;\n            _utils_js__WEBPACK_IMPORTED_MODULE_0__.aInRange('scalar', scalar, _1n, N);\n            let point, fake; // Fake point is used to const-time mult\n            if (endo) {\n                const { k1neg, k1, k2neg, k2 } = endo.splitScalar(scalar);\n                let { p: k1p, f: f1p } = this.wNAF(k1);\n                let { p: k2p, f: f2p } = this.wNAF(k2);\n                k1p = wnaf.constTimeNegate(k1neg, k1p);\n                k2p = wnaf.constTimeNegate(k2neg, k2p);\n                k2p = new Point(Fp.mul(k2p.px, endo.beta), k2p.py, k2p.pz);\n                point = k1p.add(k2p);\n                fake = f1p.add(f2p);\n            }\n            else {\n                const { p, f } = this.wNAF(scalar);\n                point = p;\n                fake = f;\n            }\n            // Normalize `z` for both points, but return only real one\n            return Point.normalizeZ([point, fake])[0];\n        }\n        /**\n         * Efficiently calculate `aP + bQ`. Unsafe, can expose private key, if used incorrectly.\n         * Not using Strauss-Shamir trick: precomputation tables are faster.\n         * The trick could be useful if both P and Q are not G (not in our case).\n         * @returns non-zero affine point\n         */\n        multiplyAndAddUnsafe(Q, a, b) {\n            const G = Point.BASE; // No Strauss-Shamir trick: we have 10% faster G precomputes\n            const mul = (P, a // Select faster multiply() method\n            ) => (a === _0n || a === _1n || !P.equals(G) ? P.multiplyUnsafe(a) : P.multiply(a));\n            const sum = mul(this, a).add(mul(Q, b));\n            return sum.is0() ? undefined : sum;\n        }\n        // Converts Projective point to affine (x, y) coordinates.\n        // Can accept precomputed Z^-1 - for example, from invertBatch.\n        // (x, y, z) ∋ (x=x/z, y=y/z)\n        toAffine(iz) {\n            return toAffineMemo(this, iz);\n        }\n        isTorsionFree() {\n            const { h: cofactor, isTorsionFree } = CURVE;\n            if (cofactor === _1n)\n                return true; // No subgroups, always torsion-free\n            if (isTorsionFree)\n                return isTorsionFree(Point, this);\n            throw new Error('isTorsionFree() has not been declared for the elliptic curve');\n        }\n        clearCofactor() {\n            const { h: cofactor, clearCofactor } = CURVE;\n            if (cofactor === _1n)\n                return this; // Fast-path\n            if (clearCofactor)\n                return clearCofactor(Point, this);\n            return this.multiplyUnsafe(CURVE.h);\n        }\n        toRawBytes(isCompressed = true) {\n            (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.abool)('isCompressed', isCompressed);\n            this.assertValidity();\n            return toBytes(Point, this, isCompressed);\n        }\n        toHex(isCompressed = true) {\n            (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.abool)('isCompressed', isCompressed);\n            return _utils_js__WEBPACK_IMPORTED_MODULE_0__.bytesToHex(this.toRawBytes(isCompressed));\n        }\n    }\n    Point.BASE = new Point(CURVE.Gx, CURVE.Gy, Fp.ONE);\n    Point.ZERO = new Point(Fp.ZERO, Fp.ONE, Fp.ZERO);\n    const _bits = CURVE.nBitLength;\n    const wnaf = (0,_curve_js__WEBPACK_IMPORTED_MODULE_1__.wNAF)(Point, CURVE.endo ? Math.ceil(_bits / 2) : _bits);\n    // Validate if generator point is on curve\n    return {\n        CURVE,\n        ProjectivePoint: Point,\n        normPrivateKeyToScalar,\n        weierstrassEquation,\n        isWithinCurveOrder,\n    };\n}\nfunction validateOpts(curve) {\n    const opts = (0,_curve_js__WEBPACK_IMPORTED_MODULE_1__.validateBasic)(curve);\n    _utils_js__WEBPACK_IMPORTED_MODULE_0__.validateObject(opts, {\n        hash: 'hash',\n        hmac: 'function',\n        randomBytes: 'function',\n    }, {\n        bits2int: 'function',\n        bits2int_modN: 'function',\n        lowS: 'boolean',\n    });\n    return Object.freeze({ lowS: true, ...opts });\n}\n/**\n * Creates short weierstrass curve and ECDSA signature methods for it.\n * @example\n * import { Field } from '@noble/curves/abstract/modular';\n * // Before that, define BigInt-s: a, b, p, n, Gx, Gy\n * const curve = weierstrass({ a, b, Fp: Field(p), n, Gx, Gy, h: 1n })\n */\nfunction weierstrass(curveDef) {\n    const CURVE = validateOpts(curveDef);\n    const { Fp, n: CURVE_ORDER } = CURVE;\n    const compressedLen = Fp.BYTES + 1; // e.g. 33 for 32\n    const uncompressedLen = 2 * Fp.BYTES + 1; // e.g. 65 for 32\n    function modN(a) {\n        return (0,_modular_js__WEBPACK_IMPORTED_MODULE_2__.mod)(a, CURVE_ORDER);\n    }\n    function invN(a) {\n        return (0,_modular_js__WEBPACK_IMPORTED_MODULE_2__.invert)(a, CURVE_ORDER);\n    }\n    const { ProjectivePoint: Point, normPrivateKeyToScalar, weierstrassEquation, isWithinCurveOrder, } = weierstrassPoints({\n        ...CURVE,\n        toBytes(_c, point, isCompressed) {\n            const a = point.toAffine();\n            const x = Fp.toBytes(a.x);\n            const cat = _utils_js__WEBPACK_IMPORTED_MODULE_0__.concatBytes;\n            (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.abool)('isCompressed', isCompressed);\n            if (isCompressed) {\n                return cat(Uint8Array.from([point.hasEvenY() ? 0x02 : 0x03]), x);\n            }\n            else {\n                return cat(Uint8Array.from([0x04]), x, Fp.toBytes(a.y));\n            }\n        },\n        fromBytes(bytes) {\n            const len = bytes.length;\n            const head = bytes[0];\n            const tail = bytes.subarray(1);\n            // this.assertValidity() is done inside of fromHex\n            if (len === compressedLen && (head === 0x02 || head === 0x03)) {\n                const x = _utils_js__WEBPACK_IMPORTED_MODULE_0__.bytesToNumberBE(tail);\n                if (!_utils_js__WEBPACK_IMPORTED_MODULE_0__.inRange(x, _1n, Fp.ORDER))\n                    throw new Error('Point is not on curve');\n                const y2 = weierstrassEquation(x); // y² = x³ + ax + b\n                let y;\n                try {\n                    y = Fp.sqrt(y2); // y = y² ^ (p+1)/4\n                }\n                catch (sqrtError) {\n                    const suffix = sqrtError instanceof Error ? ': ' + sqrtError.message : '';\n                    throw new Error('Point is not on curve' + suffix);\n                }\n                const isYOdd = (y & _1n) === _1n;\n                // ECDSA\n                const isHeadOdd = (head & 1) === 1;\n                if (isHeadOdd !== isYOdd)\n                    y = Fp.neg(y);\n                return { x, y };\n            }\n            else if (len === uncompressedLen && head === 0x04) {\n                const x = Fp.fromBytes(tail.subarray(0, Fp.BYTES));\n                const y = Fp.fromBytes(tail.subarray(Fp.BYTES, 2 * Fp.BYTES));\n                return { x, y };\n            }\n            else {\n                const cl = compressedLen;\n                const ul = uncompressedLen;\n                throw new Error('invalid Point, expected length of ' + cl + ', or uncompressed ' + ul + ', got ' + len);\n            }\n        },\n    });\n    const numToNByteStr = (num) => _utils_js__WEBPACK_IMPORTED_MODULE_0__.bytesToHex(_utils_js__WEBPACK_IMPORTED_MODULE_0__.numberToBytesBE(num, CURVE.nByteLength));\n    function isBiggerThanHalfOrder(number) {\n        const HALF = CURVE_ORDER >> _1n;\n        return number > HALF;\n    }\n    function normalizeS(s) {\n        return isBiggerThanHalfOrder(s) ? modN(-s) : s;\n    }\n    // slice bytes num\n    const slcNum = (b, from, to) => _utils_js__WEBPACK_IMPORTED_MODULE_0__.bytesToNumberBE(b.slice(from, to));\n    /**\n     * ECDSA signature with its (r, s) properties. Supports DER & compact representations.\n     */\n    class Signature {\n        constructor(r, s, recovery) {\n            this.r = r;\n            this.s = s;\n            this.recovery = recovery;\n            this.assertValidity();\n        }\n        // pair (bytes of r, bytes of s)\n        static fromCompact(hex) {\n            const l = CURVE.nByteLength;\n            hex = (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.ensureBytes)('compactSignature', hex, l * 2);\n            return new Signature(slcNum(hex, 0, l), slcNum(hex, l, 2 * l));\n        }\n        // DER encoded ECDSA signature\n        // https://bitcoin.stackexchange.com/questions/57644/what-are-the-parts-of-a-bitcoin-transaction-input-script\n        static fromDER(hex) {\n            const { r, s } = DER.toSig((0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.ensureBytes)('DER', hex));\n            return new Signature(r, s);\n        }\n        assertValidity() {\n            _utils_js__WEBPACK_IMPORTED_MODULE_0__.aInRange('r', this.r, _1n, CURVE_ORDER); // r in [1..N]\n            _utils_js__WEBPACK_IMPORTED_MODULE_0__.aInRange('s', this.s, _1n, CURVE_ORDER); // s in [1..N]\n        }\n        addRecoveryBit(recovery) {\n            return new Signature(this.r, this.s, recovery);\n        }\n        recoverPublicKey(msgHash) {\n            const { r, s, recovery: rec } = this;\n            const h = bits2int_modN((0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.ensureBytes)('msgHash', msgHash)); // Truncate hash\n            if (rec == null || ![0, 1, 2, 3].includes(rec))\n                throw new Error('recovery id invalid');\n            const radj = rec === 2 || rec === 3 ? r + CURVE.n : r;\n            if (radj >= Fp.ORDER)\n                throw new Error('recovery id 2 or 3 invalid');\n            const prefix = (rec & 1) === 0 ? '02' : '03';\n            const R = Point.fromHex(prefix + numToNByteStr(radj));\n            const ir = invN(radj); // r^-1\n            const u1 = modN(-h * ir); // -hr^-1\n            const u2 = modN(s * ir); // sr^-1\n            const Q = Point.BASE.multiplyAndAddUnsafe(R, u1, u2); // (sr^-1)R-(hr^-1)G = -(hr^-1)G + (sr^-1)\n            if (!Q)\n                throw new Error('point at infinify'); // unsafe is fine: no priv data leaked\n            Q.assertValidity();\n            return Q;\n        }\n        // Signatures should be low-s, to prevent malleability.\n        hasHighS() {\n            return isBiggerThanHalfOrder(this.s);\n        }\n        normalizeS() {\n            return this.hasHighS() ? new Signature(this.r, modN(-this.s), this.recovery) : this;\n        }\n        // DER-encoded\n        toDERRawBytes() {\n            return _utils_js__WEBPACK_IMPORTED_MODULE_0__.hexToBytes(this.toDERHex());\n        }\n        toDERHex() {\n            return DER.hexFromSig({ r: this.r, s: this.s });\n        }\n        // padded bytes of r, then padded bytes of s\n        toCompactRawBytes() {\n            return _utils_js__WEBPACK_IMPORTED_MODULE_0__.hexToBytes(this.toCompactHex());\n        }\n        toCompactHex() {\n            return numToNByteStr(this.r) + numToNByteStr(this.s);\n        }\n    }\n    const utils = {\n        isValidPrivateKey(privateKey) {\n            try {\n                normPrivateKeyToScalar(privateKey);\n                return true;\n            }\n            catch (error) {\n                return false;\n            }\n        },\n        normPrivateKeyToScalar: normPrivateKeyToScalar,\n        /**\n         * Produces cryptographically secure private key from random of size\n         * (groupLen + ceil(groupLen / 2)) with modulo bias being negligible.\n         */\n        randomPrivateKey: () => {\n            const length = (0,_modular_js__WEBPACK_IMPORTED_MODULE_2__.getMinHashLength)(CURVE.n);\n            return (0,_modular_js__WEBPACK_IMPORTED_MODULE_2__.mapHashToField)(CURVE.randomBytes(length), CURVE.n);\n        },\n        /**\n         * Creates precompute table for an arbitrary EC point. Makes point \"cached\".\n         * Allows to massively speed-up `point.multiply(scalar)`.\n         * @returns cached point\n         * @example\n         * const fast = utils.precompute(8, ProjectivePoint.fromHex(someonesPubKey));\n         * fast.multiply(privKey); // much faster ECDH now\n         */\n        precompute(windowSize = 8, point = Point.BASE) {\n            point._setWindowSize(windowSize);\n            point.multiply(BigInt(3)); // 3 is arbitrary, just need any number here\n            return point;\n        },\n    };\n    /**\n     * Computes public key for a private key. Checks for validity of the private key.\n     * @param privateKey private key\n     * @param isCompressed whether to return compact (default), or full key\n     * @returns Public key, full when isCompressed=false; short when isCompressed=true\n     */\n    function getPublicKey(privateKey, isCompressed = true) {\n        return Point.fromPrivateKey(privateKey).toRawBytes(isCompressed);\n    }\n    /**\n     * Quick and dirty check for item being public key. Does not validate hex, or being on-curve.\n     */\n    function isProbPub(item) {\n        const arr = _utils_js__WEBPACK_IMPORTED_MODULE_0__.isBytes(item);\n        const str = typeof item === 'string';\n        const len = (arr || str) && item.length;\n        if (arr)\n            return len === compressedLen || len === uncompressedLen;\n        if (str)\n            return len === 2 * compressedLen || len === 2 * uncompressedLen;\n        if (item instanceof Point)\n            return true;\n        return false;\n    }\n    /**\n     * ECDH (Elliptic Curve Diffie Hellman).\n     * Computes shared public key from private key and public key.\n     * Checks: 1) private key validity 2) shared key is on-curve.\n     * Does NOT hash the result.\n     * @param privateA private key\n     * @param publicB different public key\n     * @param isCompressed whether to return compact (default), or full key\n     * @returns shared public key\n     */\n    function getSharedSecret(privateA, publicB, isCompressed = true) {\n        if (isProbPub(privateA))\n            throw new Error('first arg must be private key');\n        if (!isProbPub(publicB))\n            throw new Error('second arg must be public key');\n        const b = Point.fromHex(publicB); // check for being on-curve\n        return b.multiply(normPrivateKeyToScalar(privateA)).toRawBytes(isCompressed);\n    }\n    // RFC6979: ensure ECDSA msg is X bytes and < N. RFC suggests optional truncating via bits2octets.\n    // FIPS 186-4 4.6 suggests the leftmost min(nBitLen, outLen) bits, which matches bits2int.\n    // bits2int can produce res>N, we can do mod(res, N) since the bitLen is the same.\n    // int2octets can't be used; pads small msgs with 0: unacceptatble for trunc as per RFC vectors\n    const bits2int = CURVE.bits2int ||\n        function (bytes) {\n            // Our custom check \"just in case\"\n            if (bytes.length > 8192)\n                throw new Error('input is too large');\n            // For curves with nBitLength % 8 !== 0: bits2octets(bits2octets(m)) !== bits2octets(m)\n            // for some cases, since bytes.length * 8 is not actual bitLength.\n            const num = _utils_js__WEBPACK_IMPORTED_MODULE_0__.bytesToNumberBE(bytes); // check for == u8 done here\n            const delta = bytes.length * 8 - CURVE.nBitLength; // truncate to nBitLength leftmost bits\n            return delta > 0 ? num >> BigInt(delta) : num;\n        };\n    const bits2int_modN = CURVE.bits2int_modN ||\n        function (bytes) {\n            return modN(bits2int(bytes)); // can't use bytesToNumberBE here\n        };\n    // NOTE: pads output with zero as per spec\n    const ORDER_MASK = _utils_js__WEBPACK_IMPORTED_MODULE_0__.bitMask(CURVE.nBitLength);\n    /**\n     * Converts to bytes. Checks if num in `[0..ORDER_MASK-1]` e.g.: `[0..2^256-1]`.\n     */\n    function int2octets(num) {\n        _utils_js__WEBPACK_IMPORTED_MODULE_0__.aInRange('num < 2^' + CURVE.nBitLength, num, _0n, ORDER_MASK);\n        // works with order, can have different size than numToField!\n        return _utils_js__WEBPACK_IMPORTED_MODULE_0__.numberToBytesBE(num, CURVE.nByteLength);\n    }\n    // Steps A, D of RFC6979 3.2\n    // Creates RFC6979 seed; converts msg/privKey to numbers.\n    // Used only in sign, not in verify.\n    // NOTE: we cannot assume here that msgHash has same amount of bytes as curve order,\n    // this will be invalid at least for P521. Also it can be bigger for P224 + SHA256\n    function prepSig(msgHash, privateKey, opts = defaultSigOpts) {\n        if (['recovered', 'canonical'].some((k) => k in opts))\n            throw new Error('sign() legacy options not supported');\n        const { hash, randomBytes } = CURVE;\n        let { lowS, prehash, extraEntropy: ent } = opts; // generates low-s sigs by default\n        if (lowS == null)\n            lowS = true; // RFC6979 3.2: we skip step A, because we already provide hash\n        msgHash = (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.ensureBytes)('msgHash', msgHash);\n        validateSigVerOpts(opts);\n        if (prehash)\n            msgHash = (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.ensureBytes)('prehashed msgHash', hash(msgHash));\n        // We can't later call bits2octets, since nested bits2int is broken for curves\n        // with nBitLength % 8 !== 0. Because of that, we unwrap it here as int2octets call.\n        // const bits2octets = (bits) => int2octets(bits2int_modN(bits))\n        const h1int = bits2int_modN(msgHash);\n        const d = normPrivateKeyToScalar(privateKey); // validate private key, convert to bigint\n        const seedArgs = [int2octets(d), int2octets(h1int)];\n        // extraEntropy. RFC6979 3.6: additional k' (optional).\n        if (ent != null && ent !== false) {\n            // K = HMAC_K(V || 0x00 || int2octets(x) || bits2octets(h1) || k')\n            const e = ent === true ? randomBytes(Fp.BYTES) : ent; // generate random bytes OR pass as-is\n            seedArgs.push((0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.ensureBytes)('extraEntropy', e)); // check for being bytes\n        }\n        const seed = _utils_js__WEBPACK_IMPORTED_MODULE_0__.concatBytes(...seedArgs); // Step D of RFC6979 3.2\n        const m = h1int; // NOTE: no need to call bits2int second time here, it is inside truncateHash!\n        // Converts signature params into point w r/s, checks result for validity.\n        function k2sig(kBytes) {\n            // RFC 6979 Section 3.2, step 3: k = bits2int(T)\n            const k = bits2int(kBytes); // Cannot use fields methods, since it is group element\n            if (!isWithinCurveOrder(k))\n                return; // Important: all mod() calls here must be done over N\n            const ik = invN(k); // k^-1 mod n\n            const q = Point.BASE.multiply(k).toAffine(); // q = Gk\n            const r = modN(q.x); // r = q.x mod n\n            if (r === _0n)\n                return;\n            // Can use scalar blinding b^-1(bm + bdr) where b ∈ [1,q−1] according to\n            // https://tches.iacr.org/index.php/TCHES/article/view/7337/6509. We've decided against it:\n            // a) dependency on CSPRNG b) 15% slowdown c) doesn't really help since bigints are not CT\n            const s = modN(ik * modN(m + r * d)); // Not using blinding here\n            if (s === _0n)\n                return;\n            let recovery = (q.x === r ? 0 : 2) | Number(q.y & _1n); // recovery bit (2 or 3, when q.x > n)\n            let normS = s;\n            if (lowS && isBiggerThanHalfOrder(s)) {\n                normS = normalizeS(s); // if lowS was passed, ensure s is always\n                recovery ^= 1; // // in the bottom half of N\n            }\n            return new Signature(r, normS, recovery); // use normS, not s\n        }\n        return { seed, k2sig };\n    }\n    const defaultSigOpts = { lowS: CURVE.lowS, prehash: false };\n    const defaultVerOpts = { lowS: CURVE.lowS, prehash: false };\n    /**\n     * Signs message hash with a private key.\n     * ```\n     * sign(m, d, k) where\n     *   (x, y) = G × k\n     *   r = x mod n\n     *   s = (m + dr)/k mod n\n     * ```\n     * @param msgHash NOT message. msg needs to be hashed to `msgHash`, or use `prehash`.\n     * @param privKey private key\n     * @param opts lowS for non-malleable sigs. extraEntropy for mixing randomness into k. prehash will hash first arg.\n     * @returns signature with recovery param\n     */\n    function sign(msgHash, privKey, opts = defaultSigOpts) {\n        const { seed, k2sig } = prepSig(msgHash, privKey, opts); // Steps A, D of RFC6979 3.2.\n        const C = CURVE;\n        const drbg = _utils_js__WEBPACK_IMPORTED_MODULE_0__.createHmacDrbg(C.hash.outputLen, C.nByteLength, C.hmac);\n        return drbg(seed, k2sig); // Steps B, C, D, E, F, G\n    }\n    // Enable precomputes. Slows down first publicKey computation by 20ms.\n    Point.BASE._setWindowSize(8);\n    // utils.precompute(8, ProjectivePoint.BASE)\n    /**\n     * Verifies a signature against message hash and public key.\n     * Rejects lowS signatures by default: to override,\n     * specify option `{lowS: false}`. Implements section 4.1.4 from https://www.secg.org/sec1-v2.pdf:\n     *\n     * ```\n     * verify(r, s, h, P) where\n     *   U1 = hs^-1 mod n\n     *   U2 = rs^-1 mod n\n     *   R = U1⋅G - U2⋅P\n     *   mod(R.x, n) == r\n     * ```\n     */\n    function verify(signature, msgHash, publicKey, opts = defaultVerOpts) {\n        const sg = signature;\n        msgHash = (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.ensureBytes)('msgHash', msgHash);\n        publicKey = (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.ensureBytes)('publicKey', publicKey);\n        const { lowS, prehash, format } = opts;\n        // Verify opts, deduce signature format\n        validateSigVerOpts(opts);\n        if ('strict' in opts)\n            throw new Error('options.strict was renamed to lowS');\n        if (format !== undefined && format !== 'compact' && format !== 'der')\n            throw new Error('format must be compact or der');\n        const isHex = typeof sg === 'string' || _utils_js__WEBPACK_IMPORTED_MODULE_0__.isBytes(sg);\n        const isObj = !isHex &&\n            !format &&\n            typeof sg === 'object' &&\n            sg !== null &&\n            typeof sg.r === 'bigint' &&\n            typeof sg.s === 'bigint';\n        if (!isHex && !isObj)\n            throw new Error('invalid signature, expected Uint8Array, hex string or Signature instance');\n        let _sig = undefined;\n        let P;\n        try {\n            if (isObj)\n                _sig = new Signature(sg.r, sg.s);\n            if (isHex) {\n                // Signature can be represented in 2 ways: compact (2*nByteLength) & DER (variable-length).\n                // Since DER can also be 2*nByteLength bytes, we check for it first.\n                try {\n                    if (format !== 'compact')\n                        _sig = Signature.fromDER(sg);\n                }\n                catch (derError) {\n                    if (!(derError instanceof DER.Err))\n                        throw derError;\n                }\n                if (!_sig && format !== 'der')\n                    _sig = Signature.fromCompact(sg);\n            }\n            P = Point.fromHex(publicKey);\n        }\n        catch (error) {\n            return false;\n        }\n        if (!_sig)\n            return false;\n        if (lowS && _sig.hasHighS())\n            return false;\n        if (prehash)\n            msgHash = CURVE.hash(msgHash);\n        const { r, s } = _sig;\n        const h = bits2int_modN(msgHash); // Cannot use fields methods, since it is group element\n        const is = invN(s); // s^-1\n        const u1 = modN(h * is); // u1 = hs^-1 mod n\n        const u2 = modN(r * is); // u2 = rs^-1 mod n\n        const R = Point.BASE.multiplyAndAddUnsafe(P, u1, u2)?.toAffine(); // R = u1⋅G + u2⋅P\n        if (!R)\n            return false;\n        const v = modN(R.x);\n        return v === r;\n    }\n    return {\n        CURVE,\n        getPublicKey,\n        getSharedSecret,\n        sign,\n        verify,\n        ProjectivePoint: Point,\n        Signature,\n        utils,\n    };\n}\n/**\n * Implementation of the Shallue and van de Woestijne method for any weierstrass curve.\n * TODO: check if there is a way to merge this with uvRatio in Edwards; move to modular.\n * b = True and y = sqrt(u / v) if (u / v) is square in F, and\n * b = False and y = sqrt(Z * (u / v)) otherwise.\n * @param Fp\n * @param Z\n * @returns\n */\nfunction SWUFpSqrtRatio(Fp, Z) {\n    // Generic implementation\n    const q = Fp.ORDER;\n    let l = _0n;\n    for (let o = q - _1n; o % _2n === _0n; o /= _2n)\n        l += _1n;\n    const c1 = l; // 1. c1, the largest integer such that 2^c1 divides q - 1.\n    // We need 2n ** c1 and 2n ** (c1-1). We can't use **; but we can use <<.\n    // 2n ** c1 == 2n << (c1-1)\n    const _2n_pow_c1_1 = _2n << (c1 - _1n - _1n);\n    const _2n_pow_c1 = _2n_pow_c1_1 * _2n;\n    const c2 = (q - _1n) / _2n_pow_c1; // 2. c2 = (q - 1) / (2^c1)  # Integer arithmetic\n    const c3 = (c2 - _1n) / _2n; // 3. c3 = (c2 - 1) / 2            # Integer arithmetic\n    const c4 = _2n_pow_c1 - _1n; // 4. c4 = 2^c1 - 1                # Integer arithmetic\n    const c5 = _2n_pow_c1_1; // 5. c5 = 2^(c1 - 1)                  # Integer arithmetic\n    const c6 = Fp.pow(Z, c2); // 6. c6 = Z^c2\n    const c7 = Fp.pow(Z, (c2 + _1n) / _2n); // 7. c7 = Z^((c2 + 1) / 2)\n    let sqrtRatio = (u, v) => {\n        let tv1 = c6; // 1. tv1 = c6\n        let tv2 = Fp.pow(v, c4); // 2. tv2 = v^c4\n        let tv3 = Fp.sqr(tv2); // 3. tv3 = tv2^2\n        tv3 = Fp.mul(tv3, v); // 4. tv3 = tv3 * v\n        let tv5 = Fp.mul(u, tv3); // 5. tv5 = u * tv3\n        tv5 = Fp.pow(tv5, c3); // 6. tv5 = tv5^c3\n        tv5 = Fp.mul(tv5, tv2); // 7. tv5 = tv5 * tv2\n        tv2 = Fp.mul(tv5, v); // 8. tv2 = tv5 * v\n        tv3 = Fp.mul(tv5, u); // 9. tv3 = tv5 * u\n        let tv4 = Fp.mul(tv3, tv2); // 10. tv4 = tv3 * tv2\n        tv5 = Fp.pow(tv4, c5); // 11. tv5 = tv4^c5\n        let isQR = Fp.eql(tv5, Fp.ONE); // 12. isQR = tv5 == 1\n        tv2 = Fp.mul(tv3, c7); // 13. tv2 = tv3 * c7\n        tv5 = Fp.mul(tv4, tv1); // 14. tv5 = tv4 * tv1\n        tv3 = Fp.cmov(tv2, tv3, isQR); // 15. tv3 = CMOV(tv2, tv3, isQR)\n        tv4 = Fp.cmov(tv5, tv4, isQR); // 16. tv4 = CMOV(tv5, tv4, isQR)\n        // 17. for i in (c1, c1 - 1, ..., 2):\n        for (let i = c1; i > _1n; i--) {\n            let tv5 = i - _2n; // 18.    tv5 = i - 2\n            tv5 = _2n << (tv5 - _1n); // 19.    tv5 = 2^tv5\n            let tvv5 = Fp.pow(tv4, tv5); // 20.    tv5 = tv4^tv5\n            const e1 = Fp.eql(tvv5, Fp.ONE); // 21.    e1 = tv5 == 1\n            tv2 = Fp.mul(tv3, tv1); // 22.    tv2 = tv3 * tv1\n            tv1 = Fp.mul(tv1, tv1); // 23.    tv1 = tv1 * tv1\n            tvv5 = Fp.mul(tv4, tv1); // 24.    tv5 = tv4 * tv1\n            tv3 = Fp.cmov(tv2, tv3, e1); // 25.    tv3 = CMOV(tv2, tv3, e1)\n            tv4 = Fp.cmov(tvv5, tv4, e1); // 26.    tv4 = CMOV(tv5, tv4, e1)\n        }\n        return { isValid: isQR, value: tv3 };\n    };\n    if (Fp.ORDER % _4n === _3n) {\n        // sqrt_ratio_3mod4(u, v)\n        const c1 = (Fp.ORDER - _3n) / _4n; // 1. c1 = (q - 3) / 4     # Integer arithmetic\n        const c2 = Fp.sqrt(Fp.neg(Z)); // 2. c2 = sqrt(-Z)\n        sqrtRatio = (u, v) => {\n            let tv1 = Fp.sqr(v); // 1. tv1 = v^2\n            const tv2 = Fp.mul(u, v); // 2. tv2 = u * v\n            tv1 = Fp.mul(tv1, tv2); // 3. tv1 = tv1 * tv2\n            let y1 = Fp.pow(tv1, c1); // 4. y1 = tv1^c1\n            y1 = Fp.mul(y1, tv2); // 5. y1 = y1 * tv2\n            const y2 = Fp.mul(y1, c2); // 6. y2 = y1 * c2\n            const tv3 = Fp.mul(Fp.sqr(y1), v); // 7. tv3 = y1^2; 8. tv3 = tv3 * v\n            const isQR = Fp.eql(tv3, u); // 9. isQR = tv3 == u\n            let y = Fp.cmov(y2, y1, isQR); // 10. y = CMOV(y2, y1, isQR)\n            return { isValid: isQR, value: y }; // 11. return (isQR, y) isQR ? y : y*c2\n        };\n    }\n    // No curves uses that\n    // if (Fp.ORDER % _8n === _5n) // sqrt_ratio_5mod8\n    return sqrtRatio;\n}\n/**\n * Simplified Shallue-van de Woestijne-Ulas Method\n * https://www.rfc-editor.org/rfc/rfc9380#section-6.6.2\n */\nfunction mapToCurveSimpleSWU(Fp, opts) {\n    (0,_modular_js__WEBPACK_IMPORTED_MODULE_2__.validateField)(Fp);\n    if (!Fp.isValid(opts.A) || !Fp.isValid(opts.B) || !Fp.isValid(opts.Z))\n        throw new Error('mapToCurveSimpleSWU: invalid opts');\n    const sqrtRatio = SWUFpSqrtRatio(Fp, opts.Z);\n    if (!Fp.isOdd)\n        throw new Error('Fp.isOdd is not implemented!');\n    // Input: u, an element of F.\n    // Output: (x, y), a point on E.\n    return (u) => {\n        // prettier-ignore\n        let tv1, tv2, tv3, tv4, tv5, tv6, x, y;\n        tv1 = Fp.sqr(u); // 1.  tv1 = u^2\n        tv1 = Fp.mul(tv1, opts.Z); // 2.  tv1 = Z * tv1\n        tv2 = Fp.sqr(tv1); // 3.  tv2 = tv1^2\n        tv2 = Fp.add(tv2, tv1); // 4.  tv2 = tv2 + tv1\n        tv3 = Fp.add(tv2, Fp.ONE); // 5.  tv3 = tv2 + 1\n        tv3 = Fp.mul(tv3, opts.B); // 6.  tv3 = B * tv3\n        tv4 = Fp.cmov(opts.Z, Fp.neg(tv2), !Fp.eql(tv2, Fp.ZERO)); // 7.  tv4 = CMOV(Z, -tv2, tv2 != 0)\n        tv4 = Fp.mul(tv4, opts.A); // 8.  tv4 = A * tv4\n        tv2 = Fp.sqr(tv3); // 9.  tv2 = tv3^2\n        tv6 = Fp.sqr(tv4); // 10. tv6 = tv4^2\n        tv5 = Fp.mul(tv6, opts.A); // 11. tv5 = A * tv6\n        tv2 = Fp.add(tv2, tv5); // 12. tv2 = tv2 + tv5\n        tv2 = Fp.mul(tv2, tv3); // 13. tv2 = tv2 * tv3\n        tv6 = Fp.mul(tv6, tv4); // 14. tv6 = tv6 * tv4\n        tv5 = Fp.mul(tv6, opts.B); // 15. tv5 = B * tv6\n        tv2 = Fp.add(tv2, tv5); // 16. tv2 = tv2 + tv5\n        x = Fp.mul(tv1, tv3); // 17.   x = tv1 * tv3\n        const { isValid, value } = sqrtRatio(tv2, tv6); // 18. (is_gx1_square, y1) = sqrt_ratio(tv2, tv6)\n        y = Fp.mul(tv1, u); // 19.   y = tv1 * u  -> Z * u^3 * y1\n        y = Fp.mul(y, value); // 20.   y = y * y1\n        x = Fp.cmov(x, tv3, isValid); // 21.   x = CMOV(x, tv3, is_gx1_square)\n        y = Fp.cmov(y, value, isValid); // 22.   y = CMOV(y, y1, is_gx1_square)\n        const e1 = Fp.isOdd(u) === Fp.isOdd(y); // 23.  e1 = sgn0(u) == sgn0(y)\n        y = Fp.cmov(Fp.neg(y), y, e1); // 24.   y = CMOV(-y, y, e1)\n        x = Fp.div(x, tv4); // 25.   x = x / tv4\n        return { x, y };\n    };\n}\n//# sourceMappingURL=weierstrass.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@reown/appkit/node_modules/@noble/curves/esm/abstract/weierstrass.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/@reown/appkit/node_modules/@noble/curves/esm/secp256k1.js":
/*!********************************************************************************!*\
  !*** ./node_modules/@reown/appkit/node_modules/@noble/curves/esm/secp256k1.js ***!
  \********************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   encodeToCurve: () => (/* binding */ encodeToCurve),\n/* harmony export */   hashToCurve: () => (/* binding */ hashToCurve),\n/* harmony export */   schnorr: () => (/* binding */ schnorr),\n/* harmony export */   secp256k1: () => (/* binding */ secp256k1)\n/* harmony export */ });\n/* harmony import */ var _noble_hashes_sha256__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @noble/hashes/sha256 */ \"(app-pages-browser)/./node_modules/@reown/appkit/node_modules/@noble/hashes/esm/sha256.js\");\n/* harmony import */ var _noble_hashes_utils__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @noble/hashes/utils */ \"(app-pages-browser)/./node_modules/@reown/appkit/node_modules/@noble/hashes/esm/utils.js\");\n/* harmony import */ var _shortw_utils_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./_shortw_utils.js */ \"(app-pages-browser)/./node_modules/@reown/appkit/node_modules/@noble/curves/esm/_shortw_utils.js\");\n/* harmony import */ var _abstract_hash_to_curve_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./abstract/hash-to-curve.js */ \"(app-pages-browser)/./node_modules/@reown/appkit/node_modules/@noble/curves/esm/abstract/hash-to-curve.js\");\n/* harmony import */ var _abstract_modular_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./abstract/modular.js */ \"(app-pages-browser)/./node_modules/@reown/appkit/node_modules/@noble/curves/esm/abstract/modular.js\");\n/* harmony import */ var _abstract_utils_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./abstract/utils.js */ \"(app-pages-browser)/./node_modules/@reown/appkit/node_modules/@noble/curves/esm/abstract/utils.js\");\n/* harmony import */ var _abstract_weierstrass_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./abstract/weierstrass.js */ \"(app-pages-browser)/./node_modules/@reown/appkit/node_modules/@noble/curves/esm/abstract/weierstrass.js\");\n/**\n * NIST secp256k1. See [pdf](https://www.secg.org/sec2-v2.pdf).\n *\n * Seems to be rigid (not backdoored)\n * [as per discussion](https://bitcointalk.org/index.php?topic=289795.msg3183975#msg3183975).\n *\n * secp256k1 belongs to Koblitz curves: it has efficiently computable endomorphism.\n * Endomorphism uses 2x less RAM, speeds up precomputation by 2x and ECDH / key recovery by 20%.\n * For precomputed wNAF it trades off 1/2 init time & 1/3 ram for 20% perf hit.\n * [See explanation](https://gist.github.com/paulmillr/eb670806793e84df628a7c434a873066).\n * @module\n */\n/*! noble-curves - MIT License (c) 2022 Paul Miller (paulmillr.com) */\n\n\n\n\n\n\n\nconst secp256k1P = BigInt('0xfffffffffffffffffffffffffffffffffffffffffffffffffffffffefffffc2f');\nconst secp256k1N = BigInt('0xfffffffffffffffffffffffffffffffebaaedce6af48a03bbfd25e8cd0364141');\nconst _1n = BigInt(1);\nconst _2n = BigInt(2);\nconst divNearest = (a, b) => (a + b / _2n) / b;\n/**\n * √n = n^((p+1)/4) for fields p = 3 mod 4. We unwrap the loop and multiply bit-by-bit.\n * (P+1n/4n).toString(2) would produce bits [223x 1, 0, 22x 1, 4x 0, 11, 00]\n */\nfunction sqrtMod(y) {\n    const P = secp256k1P;\n    // prettier-ignore\n    const _3n = BigInt(3), _6n = BigInt(6), _11n = BigInt(11), _22n = BigInt(22);\n    // prettier-ignore\n    const _23n = BigInt(23), _44n = BigInt(44), _88n = BigInt(88);\n    const b2 = (y * y * y) % P; // x^3, 11\n    const b3 = (b2 * b2 * y) % P; // x^7\n    const b6 = ((0,_abstract_modular_js__WEBPACK_IMPORTED_MODULE_0__.pow2)(b3, _3n, P) * b3) % P;\n    const b9 = ((0,_abstract_modular_js__WEBPACK_IMPORTED_MODULE_0__.pow2)(b6, _3n, P) * b3) % P;\n    const b11 = ((0,_abstract_modular_js__WEBPACK_IMPORTED_MODULE_0__.pow2)(b9, _2n, P) * b2) % P;\n    const b22 = ((0,_abstract_modular_js__WEBPACK_IMPORTED_MODULE_0__.pow2)(b11, _11n, P) * b11) % P;\n    const b44 = ((0,_abstract_modular_js__WEBPACK_IMPORTED_MODULE_0__.pow2)(b22, _22n, P) * b22) % P;\n    const b88 = ((0,_abstract_modular_js__WEBPACK_IMPORTED_MODULE_0__.pow2)(b44, _44n, P) * b44) % P;\n    const b176 = ((0,_abstract_modular_js__WEBPACK_IMPORTED_MODULE_0__.pow2)(b88, _88n, P) * b88) % P;\n    const b220 = ((0,_abstract_modular_js__WEBPACK_IMPORTED_MODULE_0__.pow2)(b176, _44n, P) * b44) % P;\n    const b223 = ((0,_abstract_modular_js__WEBPACK_IMPORTED_MODULE_0__.pow2)(b220, _3n, P) * b3) % P;\n    const t1 = ((0,_abstract_modular_js__WEBPACK_IMPORTED_MODULE_0__.pow2)(b223, _23n, P) * b22) % P;\n    const t2 = ((0,_abstract_modular_js__WEBPACK_IMPORTED_MODULE_0__.pow2)(t1, _6n, P) * b2) % P;\n    const root = (0,_abstract_modular_js__WEBPACK_IMPORTED_MODULE_0__.pow2)(t2, _2n, P);\n    if (!Fpk1.eql(Fpk1.sqr(root), y))\n        throw new Error('Cannot find square root');\n    return root;\n}\nconst Fpk1 = (0,_abstract_modular_js__WEBPACK_IMPORTED_MODULE_0__.Field)(secp256k1P, undefined, undefined, { sqrt: sqrtMod });\n/**\n * secp256k1 short weierstrass curve and ECDSA signatures over it.\n *\n * @example\n * import { secp256k1 } from '@noble/curves/secp256k1';\n *\n * const priv = secp256k1.utils.randomPrivateKey();\n * const pub = secp256k1.getPublicKey(priv);\n * const msg = new Uint8Array(32).fill(1); // message hash (not message) in ecdsa\n * const sig = secp256k1.sign(msg, priv); // `{prehash: true}` option is available\n * const isValid = secp256k1.verify(sig, msg, pub) === true;\n */\nconst secp256k1 = (0,_shortw_utils_js__WEBPACK_IMPORTED_MODULE_1__.createCurve)({\n    a: BigInt(0), // equation params: a, b\n    b: BigInt(7),\n    Fp: Fpk1, // Field's prime: 2n**256n - 2n**32n - 2n**9n - 2n**8n - 2n**7n - 2n**6n - 2n**4n - 1n\n    n: secp256k1N, // Curve order, total count of valid points in the field\n    // Base point (x, y) aka generator point\n    Gx: BigInt('55066263022277343669578718895168534326250603453777594175500187360389116729240'),\n    Gy: BigInt('32670510020758816978083085130507043184471273380659243275938904335757337482424'),\n    h: BigInt(1), // Cofactor\n    lowS: true, // Allow only low-S signatures by default in sign() and verify()\n    endo: {\n        // Endomorphism, see above\n        beta: BigInt('0x7ae96a2b657c07106e64479eac3434e99cf0497512f58995c1396c28719501ee'),\n        splitScalar: (k) => {\n            const n = secp256k1N;\n            const a1 = BigInt('0x3086d221a7d46bcde86c90e49284eb15');\n            const b1 = -_1n * BigInt('0xe4437ed6010e88286f547fa90abfe4c3');\n            const a2 = BigInt('0x114ca50f7a8e2f3f657c1108d9d44cfd8');\n            const b2 = a1;\n            const POW_2_128 = BigInt('0x100000000000000000000000000000000'); // (2n**128n).toString(16)\n            const c1 = divNearest(b2 * k, n);\n            const c2 = divNearest(-b1 * k, n);\n            let k1 = (0,_abstract_modular_js__WEBPACK_IMPORTED_MODULE_0__.mod)(k - c1 * a1 - c2 * a2, n);\n            let k2 = (0,_abstract_modular_js__WEBPACK_IMPORTED_MODULE_0__.mod)(-c1 * b1 - c2 * b2, n);\n            const k1neg = k1 > POW_2_128;\n            const k2neg = k2 > POW_2_128;\n            if (k1neg)\n                k1 = n - k1;\n            if (k2neg)\n                k2 = n - k2;\n            if (k1 > POW_2_128 || k2 > POW_2_128) {\n                throw new Error('splitScalar: Endomorphism failed, k=' + k);\n            }\n            return { k1neg, k1, k2neg, k2 };\n        },\n    },\n}, _noble_hashes_sha256__WEBPACK_IMPORTED_MODULE_2__.sha256);\n// Schnorr signatures are superior to ECDSA from above. Below is Schnorr-specific BIP0340 code.\n// https://github.com/bitcoin/bips/blob/master/bip-0340.mediawiki\nconst _0n = BigInt(0);\n/** An object mapping tags to their tagged hash prefix of [SHA256(tag) | SHA256(tag)] */\nconst TAGGED_HASH_PREFIXES = {};\nfunction taggedHash(tag, ...messages) {\n    let tagP = TAGGED_HASH_PREFIXES[tag];\n    if (tagP === undefined) {\n        const tagH = (0,_noble_hashes_sha256__WEBPACK_IMPORTED_MODULE_2__.sha256)(Uint8Array.from(tag, (c) => c.charCodeAt(0)));\n        tagP = (0,_abstract_utils_js__WEBPACK_IMPORTED_MODULE_3__.concatBytes)(tagH, tagH);\n        TAGGED_HASH_PREFIXES[tag] = tagP;\n    }\n    return (0,_noble_hashes_sha256__WEBPACK_IMPORTED_MODULE_2__.sha256)((0,_abstract_utils_js__WEBPACK_IMPORTED_MODULE_3__.concatBytes)(tagP, ...messages));\n}\n// ECDSA compact points are 33-byte. Schnorr is 32: we strip first byte 0x02 or 0x03\nconst pointToBytes = (point) => point.toRawBytes(true).slice(1);\nconst numTo32b = (n) => (0,_abstract_utils_js__WEBPACK_IMPORTED_MODULE_3__.numberToBytesBE)(n, 32);\nconst modP = (x) => (0,_abstract_modular_js__WEBPACK_IMPORTED_MODULE_0__.mod)(x, secp256k1P);\nconst modN = (x) => (0,_abstract_modular_js__WEBPACK_IMPORTED_MODULE_0__.mod)(x, secp256k1N);\nconst Point = secp256k1.ProjectivePoint;\nconst GmulAdd = (Q, a, b) => Point.BASE.multiplyAndAddUnsafe(Q, a, b);\n// Calculate point, scalar and bytes\nfunction schnorrGetExtPubKey(priv) {\n    let d_ = secp256k1.utils.normPrivateKeyToScalar(priv); // same method executed in fromPrivateKey\n    let p = Point.fromPrivateKey(d_); // P = d'⋅G; 0 < d' < n check is done inside\n    const scalar = p.hasEvenY() ? d_ : modN(-d_);\n    return { scalar: scalar, bytes: pointToBytes(p) };\n}\n/**\n * lift_x from BIP340. Convert 32-byte x coordinate to elliptic curve point.\n * @returns valid point checked for being on-curve\n */\nfunction lift_x(x) {\n    (0,_abstract_utils_js__WEBPACK_IMPORTED_MODULE_3__.aInRange)('x', x, _1n, secp256k1P); // Fail if x ≥ p.\n    const xx = modP(x * x);\n    const c = modP(xx * x + BigInt(7)); // Let c = x³ + 7 mod p.\n    let y = sqrtMod(c); // Let y = c^(p+1)/4 mod p.\n    if (y % _2n !== _0n)\n        y = modP(-y); // Return the unique point P such that x(P) = x and\n    const p = new Point(x, y, _1n); // y(P) = y if y mod 2 = 0 or y(P) = p-y otherwise.\n    p.assertValidity();\n    return p;\n}\nconst num = _abstract_utils_js__WEBPACK_IMPORTED_MODULE_3__.bytesToNumberBE;\n/**\n * Create tagged hash, convert it to bigint, reduce modulo-n.\n */\nfunction challenge(...args) {\n    return modN(num(taggedHash('BIP0340/challenge', ...args)));\n}\n/**\n * Schnorr public key is just `x` coordinate of Point as per BIP340.\n */\nfunction schnorrGetPublicKey(privateKey) {\n    return schnorrGetExtPubKey(privateKey).bytes; // d'=int(sk). Fail if d'=0 or d'≥n. Ret bytes(d'⋅G)\n}\n/**\n * Creates Schnorr signature as per BIP340. Verifies itself before returning anything.\n * auxRand is optional and is not the sole source of k generation: bad CSPRNG won't be dangerous.\n */\nfunction schnorrSign(message, privateKey, auxRand = (0,_noble_hashes_utils__WEBPACK_IMPORTED_MODULE_4__.randomBytes)(32)) {\n    const m = (0,_abstract_utils_js__WEBPACK_IMPORTED_MODULE_3__.ensureBytes)('message', message);\n    const { bytes: px, scalar: d } = schnorrGetExtPubKey(privateKey); // checks for isWithinCurveOrder\n    const a = (0,_abstract_utils_js__WEBPACK_IMPORTED_MODULE_3__.ensureBytes)('auxRand', auxRand, 32); // Auxiliary random data a: a 32-byte array\n    const t = numTo32b(d ^ num(taggedHash('BIP0340/aux', a))); // Let t be the byte-wise xor of bytes(d) and hash/aux(a)\n    const rand = taggedHash('BIP0340/nonce', t, px, m); // Let rand = hash/nonce(t || bytes(P) || m)\n    const k_ = modN(num(rand)); // Let k' = int(rand) mod n\n    if (k_ === _0n)\n        throw new Error('sign failed: k is zero'); // Fail if k' = 0.\n    const { bytes: rx, scalar: k } = schnorrGetExtPubKey(k_); // Let R = k'⋅G.\n    const e = challenge(rx, px, m); // Let e = int(hash/challenge(bytes(R) || bytes(P) || m)) mod n.\n    const sig = new Uint8Array(64); // Let sig = bytes(R) || bytes((k + ed) mod n).\n    sig.set(rx, 0);\n    sig.set(numTo32b(modN(k + e * d)), 32);\n    // If Verify(bytes(P), m, sig) (see below) returns failure, abort\n    if (!schnorrVerify(sig, m, px))\n        throw new Error('sign: Invalid signature produced');\n    return sig;\n}\n/**\n * Verifies Schnorr signature.\n * Will swallow errors & return false except for initial type validation of arguments.\n */\nfunction schnorrVerify(signature, message, publicKey) {\n    const sig = (0,_abstract_utils_js__WEBPACK_IMPORTED_MODULE_3__.ensureBytes)('signature', signature, 64);\n    const m = (0,_abstract_utils_js__WEBPACK_IMPORTED_MODULE_3__.ensureBytes)('message', message);\n    const pub = (0,_abstract_utils_js__WEBPACK_IMPORTED_MODULE_3__.ensureBytes)('publicKey', publicKey, 32);\n    try {\n        const P = lift_x(num(pub)); // P = lift_x(int(pk)); fail if that fails\n        const r = num(sig.subarray(0, 32)); // Let r = int(sig[0:32]); fail if r ≥ p.\n        if (!(0,_abstract_utils_js__WEBPACK_IMPORTED_MODULE_3__.inRange)(r, _1n, secp256k1P))\n            return false;\n        const s = num(sig.subarray(32, 64)); // Let s = int(sig[32:64]); fail if s ≥ n.\n        if (!(0,_abstract_utils_js__WEBPACK_IMPORTED_MODULE_3__.inRange)(s, _1n, secp256k1N))\n            return false;\n        const e = challenge(numTo32b(r), pointToBytes(P), m); // int(challenge(bytes(r)||bytes(P)||m))%n\n        const R = GmulAdd(P, s, modN(-e)); // R = s⋅G - e⋅P\n        if (!R || !R.hasEvenY() || R.toAffine().x !== r)\n            return false; // -eP == (n-e)P\n        return true; // Fail if is_infinite(R) / not has_even_y(R) / x(R) ≠ r.\n    }\n    catch (error) {\n        return false;\n    }\n}\n/**\n * Schnorr signatures over secp256k1.\n * https://github.com/bitcoin/bips/blob/master/bip-0340.mediawiki\n * @example\n * import { schnorr } from '@noble/curves/secp256k1';\n * const priv = schnorr.utils.randomPrivateKey();\n * const pub = schnorr.getPublicKey(priv);\n * const msg = new TextEncoder().encode('hello');\n * const sig = schnorr.sign(msg, priv);\n * const isValid = schnorr.verify(sig, msg, pub);\n */\nconst schnorr = /* @__PURE__ */ (() => ({\n    getPublicKey: schnorrGetPublicKey,\n    sign: schnorrSign,\n    verify: schnorrVerify,\n    utils: {\n        randomPrivateKey: secp256k1.utils.randomPrivateKey,\n        lift_x,\n        pointToBytes,\n        numberToBytesBE: _abstract_utils_js__WEBPACK_IMPORTED_MODULE_3__.numberToBytesBE,\n        bytesToNumberBE: _abstract_utils_js__WEBPACK_IMPORTED_MODULE_3__.bytesToNumberBE,\n        taggedHash,\n        mod: _abstract_modular_js__WEBPACK_IMPORTED_MODULE_0__.mod,\n    },\n}))();\nconst isoMap = /* @__PURE__ */ (() => (0,_abstract_hash_to_curve_js__WEBPACK_IMPORTED_MODULE_5__.isogenyMap)(Fpk1, [\n    // xNum\n    [\n        '0x8e38e38e38e38e38e38e38e38e38e38e38e38e38e38e38e38e38e38daaaaa8c7',\n        '0x7d3d4c80bc321d5b9f315cea7fd44c5d595d2fc0bf63b92dfff1044f17c6581',\n        '0x534c328d23f234e6e2a413deca25caece4506144037c40314ecbd0b53d9dd262',\n        '0x8e38e38e38e38e38e38e38e38e38e38e38e38e38e38e38e38e38e38daaaaa88c',\n    ],\n    // xDen\n    [\n        '0xd35771193d94918a9ca34ccbb7b640dd86cd409542f8487d9fe6b745781eb49b',\n        '0xedadc6f64383dc1df7c4b2d51b54225406d36b641f5e41bbc52a56612a8c6d14',\n        '0x0000000000000000000000000000000000000000000000000000000000000001', // LAST 1\n    ],\n    // yNum\n    [\n        '0x4bda12f684bda12f684bda12f684bda12f684bda12f684bda12f684b8e38e23c',\n        '0xc75e0c32d5cb7c0fa9d0a54b12a0a6d5647ab046d686da6fdffc90fc201d71a3',\n        '0x29a6194691f91a73715209ef6512e576722830a201be2018a765e85a9ecee931',\n        '0x2f684bda12f684bda12f684bda12f684bda12f684bda12f684bda12f38e38d84',\n    ],\n    // yDen\n    [\n        '0xfffffffffffffffffffffffffffffffffffffffffffffffffffffffefffff93b',\n        '0x7a06534bb8bdb49fd5e9e6632722c2989467c1bfc8e8d978dfb425d2685c2573',\n        '0x6484aa716545ca2cf3a70c3fa8fe337e0a3d21162f0d6299a7bf8192bfd2a76f',\n        '0x0000000000000000000000000000000000000000000000000000000000000001', // LAST 1\n    ],\n].map((i) => i.map((j) => BigInt(j)))))();\nconst mapSWU = /* @__PURE__ */ (() => (0,_abstract_weierstrass_js__WEBPACK_IMPORTED_MODULE_6__.mapToCurveSimpleSWU)(Fpk1, {\n    A: BigInt('0x3f8731abdd661adca08a5558f0f5d272e953d363cb6f0e5d405447c01a444533'),\n    B: BigInt('1771'),\n    Z: Fpk1.create(BigInt('-11')),\n}))();\nconst htf = /* @__PURE__ */ (() => (0,_abstract_hash_to_curve_js__WEBPACK_IMPORTED_MODULE_5__.createHasher)(secp256k1.ProjectivePoint, (scalars) => {\n    const { x, y } = mapSWU(Fpk1.create(scalars[0]));\n    return isoMap(x, y);\n}, {\n    DST: 'secp256k1_XMD:SHA-256_SSWU_RO_',\n    encodeDST: 'secp256k1_XMD:SHA-256_SSWU_NU_',\n    p: Fpk1.ORDER,\n    m: 1,\n    k: 128,\n    expand: 'xmd',\n    hash: _noble_hashes_sha256__WEBPACK_IMPORTED_MODULE_2__.sha256,\n}))();\n/** secp256k1 hash-to-curve from [RFC 9380](https://www.rfc-editor.org/rfc/rfc9380). */\nconst hashToCurve = /* @__PURE__ */ (() => htf.hashToCurve)();\n/** secp256k1 encode-to-curve from [RFC 9380](https://www.rfc-editor.org/rfc/rfc9380). */\nconst encodeToCurve = /* @__PURE__ */ (() => htf.encodeToCurve)();\n//# sourceMappingURL=secp256k1.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@reown/appkit/node_modules/@noble/curves/esm/secp256k1.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/@reown/appkit/node_modules/@noble/hashes/esm/_md.js":
/*!**************************************************************************!*\
  !*** ./node_modules/@reown/appkit/node_modules/@noble/hashes/esm/_md.js ***!
  \**************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Chi: () => (/* binding */ Chi),\n/* harmony export */   HashMD: () => (/* binding */ HashMD),\n/* harmony export */   Maj: () => (/* binding */ Maj),\n/* harmony export */   setBigUint64: () => (/* binding */ setBigUint64)\n/* harmony export */ });\n/* harmony import */ var _assert_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./_assert.js */ \"(app-pages-browser)/./node_modules/@reown/appkit/node_modules/@noble/hashes/esm/_assert.js\");\n/* harmony import */ var _utils_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./utils.js */ \"(app-pages-browser)/./node_modules/@reown/appkit/node_modules/@noble/hashes/esm/utils.js\");\n/**\n * Internal Merkle-Damgard hash utils.\n * @module\n */\n\n\n/** Polyfill for Safari 14. https://caniuse.com/mdn-javascript_builtins_dataview_setbiguint64 */\nfunction setBigUint64(view, byteOffset, value, isLE) {\n    if (typeof view.setBigUint64 === 'function')\n        return view.setBigUint64(byteOffset, value, isLE);\n    const _32n = BigInt(32);\n    const _u32_max = BigInt(0xffffffff);\n    const wh = Number((value >> _32n) & _u32_max);\n    const wl = Number(value & _u32_max);\n    const h = isLE ? 4 : 0;\n    const l = isLE ? 0 : 4;\n    view.setUint32(byteOffset + h, wh, isLE);\n    view.setUint32(byteOffset + l, wl, isLE);\n}\n/** Choice: a ? b : c */\nfunction Chi(a, b, c) {\n    return (a & b) ^ (~a & c);\n}\n/** Majority function, true if any two inputs is true. */\nfunction Maj(a, b, c) {\n    return (a & b) ^ (a & c) ^ (b & c);\n}\n/**\n * Merkle-Damgard hash construction base class.\n * Could be used to create MD5, RIPEMD, SHA1, SHA2.\n */\nclass HashMD extends _utils_js__WEBPACK_IMPORTED_MODULE_0__.Hash {\n    constructor(blockLen, outputLen, padOffset, isLE) {\n        super();\n        this.blockLen = blockLen;\n        this.outputLen = outputLen;\n        this.padOffset = padOffset;\n        this.isLE = isLE;\n        this.finished = false;\n        this.length = 0;\n        this.pos = 0;\n        this.destroyed = false;\n        this.buffer = new Uint8Array(blockLen);\n        this.view = (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.createView)(this.buffer);\n    }\n    update(data) {\n        (0,_assert_js__WEBPACK_IMPORTED_MODULE_1__.aexists)(this);\n        const { view, buffer, blockLen } = this;\n        data = (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.toBytes)(data);\n        const len = data.length;\n        for (let pos = 0; pos < len;) {\n            const take = Math.min(blockLen - this.pos, len - pos);\n            // Fast path: we have at least one block in input, cast it to view and process\n            if (take === blockLen) {\n                const dataView = (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.createView)(data);\n                for (; blockLen <= len - pos; pos += blockLen)\n                    this.process(dataView, pos);\n                continue;\n            }\n            buffer.set(data.subarray(pos, pos + take), this.pos);\n            this.pos += take;\n            pos += take;\n            if (this.pos === blockLen) {\n                this.process(view, 0);\n                this.pos = 0;\n            }\n        }\n        this.length += data.length;\n        this.roundClean();\n        return this;\n    }\n    digestInto(out) {\n        (0,_assert_js__WEBPACK_IMPORTED_MODULE_1__.aexists)(this);\n        (0,_assert_js__WEBPACK_IMPORTED_MODULE_1__.aoutput)(out, this);\n        this.finished = true;\n        // Padding\n        // We can avoid allocation of buffer for padding completely if it\n        // was previously not allocated here. But it won't change performance.\n        const { buffer, view, blockLen, isLE } = this;\n        let { pos } = this;\n        // append the bit '1' to the message\n        buffer[pos++] = 0b10000000;\n        this.buffer.subarray(pos).fill(0);\n        // we have less than padOffset left in buffer, so we cannot put length in\n        // current block, need process it and pad again\n        if (this.padOffset > blockLen - pos) {\n            this.process(view, 0);\n            pos = 0;\n        }\n        // Pad until full block byte with zeros\n        for (let i = pos; i < blockLen; i++)\n            buffer[i] = 0;\n        // Note: sha512 requires length to be 128bit integer, but length in JS will overflow before that\n        // You need to write around 2 exabytes (u64_max / 8 / (1024**6)) for this to happen.\n        // So we just write lowest 64 bits of that value.\n        setBigUint64(view, blockLen - 8, BigInt(this.length * 8), isLE);\n        this.process(view, 0);\n        const oview = (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.createView)(out);\n        const len = this.outputLen;\n        // NOTE: we do division by 4 later, which should be fused in single op with modulo by JIT\n        if (len % 4)\n            throw new Error('_sha2: outputLen should be aligned to 32bit');\n        const outLen = len / 4;\n        const state = this.get();\n        if (outLen > state.length)\n            throw new Error('_sha2: outputLen bigger than state');\n        for (let i = 0; i < outLen; i++)\n            oview.setUint32(4 * i, state[i], isLE);\n    }\n    digest() {\n        const { buffer, outputLen } = this;\n        this.digestInto(buffer);\n        const res = buffer.slice(0, outputLen);\n        this.destroy();\n        return res;\n    }\n    _cloneInto(to) {\n        to || (to = new this.constructor());\n        to.set(...this.get());\n        const { blockLen, buffer, length, finished, destroyed, pos } = this;\n        to.length = length;\n        to.pos = pos;\n        to.finished = finished;\n        to.destroyed = destroyed;\n        if (length % blockLen)\n            to.buffer.set(buffer);\n        return to;\n    }\n}\n//# sourceMappingURL=_md.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@reown/appkit/node_modules/@noble/hashes/esm/_md.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/@reown/appkit/node_modules/@noble/hashes/esm/hmac.js":
/*!***************************************************************************!*\
  !*** ./node_modules/@reown/appkit/node_modules/@noble/hashes/esm/hmac.js ***!
  \***************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   HMAC: () => (/* binding */ HMAC),\n/* harmony export */   hmac: () => (/* binding */ hmac)\n/* harmony export */ });\n/* harmony import */ var _assert_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./_assert.js */ \"(app-pages-browser)/./node_modules/@reown/appkit/node_modules/@noble/hashes/esm/_assert.js\");\n/* harmony import */ var _utils_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./utils.js */ \"(app-pages-browser)/./node_modules/@reown/appkit/node_modules/@noble/hashes/esm/utils.js\");\n/**\n * HMAC: RFC2104 message authentication code.\n * @module\n */\n\n\nclass HMAC extends _utils_js__WEBPACK_IMPORTED_MODULE_0__.Hash {\n    constructor(hash, _key) {\n        super();\n        this.finished = false;\n        this.destroyed = false;\n        (0,_assert_js__WEBPACK_IMPORTED_MODULE_1__.ahash)(hash);\n        const key = (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.toBytes)(_key);\n        this.iHash = hash.create();\n        if (typeof this.iHash.update !== 'function')\n            throw new Error('Expected instance of class which extends utils.Hash');\n        this.blockLen = this.iHash.blockLen;\n        this.outputLen = this.iHash.outputLen;\n        const blockLen = this.blockLen;\n        const pad = new Uint8Array(blockLen);\n        // blockLen can be bigger than outputLen\n        pad.set(key.length > blockLen ? hash.create().update(key).digest() : key);\n        for (let i = 0; i < pad.length; i++)\n            pad[i] ^= 0x36;\n        this.iHash.update(pad);\n        // By doing update (processing of first block) of outer hash here we can re-use it between multiple calls via clone\n        this.oHash = hash.create();\n        // Undo internal XOR && apply outer XOR\n        for (let i = 0; i < pad.length; i++)\n            pad[i] ^= 0x36 ^ 0x5c;\n        this.oHash.update(pad);\n        pad.fill(0);\n    }\n    update(buf) {\n        (0,_assert_js__WEBPACK_IMPORTED_MODULE_1__.aexists)(this);\n        this.iHash.update(buf);\n        return this;\n    }\n    digestInto(out) {\n        (0,_assert_js__WEBPACK_IMPORTED_MODULE_1__.aexists)(this);\n        (0,_assert_js__WEBPACK_IMPORTED_MODULE_1__.abytes)(out, this.outputLen);\n        this.finished = true;\n        this.iHash.digestInto(out);\n        this.oHash.update(out);\n        this.oHash.digestInto(out);\n        this.destroy();\n    }\n    digest() {\n        const out = new Uint8Array(this.oHash.outputLen);\n        this.digestInto(out);\n        return out;\n    }\n    _cloneInto(to) {\n        // Create new instance without calling constructor since key already in state and we don't know it.\n        to || (to = Object.create(Object.getPrototypeOf(this), {}));\n        const { oHash, iHash, finished, destroyed, blockLen, outputLen } = this;\n        to = to;\n        to.finished = finished;\n        to.destroyed = destroyed;\n        to.blockLen = blockLen;\n        to.outputLen = outputLen;\n        to.oHash = oHash._cloneInto(to.oHash);\n        to.iHash = iHash._cloneInto(to.iHash);\n        return to;\n    }\n    destroy() {\n        this.destroyed = true;\n        this.oHash.destroy();\n        this.iHash.destroy();\n    }\n}\n/**\n * HMAC: RFC2104 message authentication code.\n * @param hash - function that would be used e.g. sha256\n * @param key - message key\n * @param message - message data\n * @example\n * import { hmac } from '@noble/hashes/hmac';\n * import { sha256 } from '@noble/hashes/sha2';\n * const mac1 = hmac(sha256, 'key', 'message');\n */\nconst hmac = (hash, key, message) => new HMAC(hash, key).update(message).digest();\nhmac.create = (hash, key) => new HMAC(hash, key);\n//# sourceMappingURL=hmac.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@reown/appkit/node_modules/@noble/hashes/esm/hmac.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/@reown/appkit/node_modules/@noble/hashes/esm/sha256.js":
/*!*****************************************************************************!*\
  !*** ./node_modules/@reown/appkit/node_modules/@noble/hashes/esm/sha256.js ***!
  \*****************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SHA256: () => (/* binding */ SHA256),\n/* harmony export */   sha224: () => (/* binding */ sha224),\n/* harmony export */   sha256: () => (/* binding */ sha256)\n/* harmony export */ });\n/* harmony import */ var _md_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./_md.js */ \"(app-pages-browser)/./node_modules/@reown/appkit/node_modules/@noble/hashes/esm/_md.js\");\n/* harmony import */ var _utils_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./utils.js */ \"(app-pages-browser)/./node_modules/@reown/appkit/node_modules/@noble/hashes/esm/utils.js\");\n/**\n * SHA2-256 a.k.a. sha256. In JS, it is the fastest hash, even faster than Blake3.\n *\n * To break sha256 using birthday attack, attackers need to try 2^128 hashes.\n * BTC network is doing 2^70 hashes/sec (2^95 hashes/year) as per 2025.\n *\n * Check out [FIPS 180-4](https://nvlpubs.nist.gov/nistpubs/FIPS/NIST.FIPS.180-4.pdf).\n * @module\n */\n\n\n/** Round constants: first 32 bits of fractional parts of the cube roots of the first 64 primes 2..311). */\n// prettier-ignore\nconst SHA256_K = /* @__PURE__ */ new Uint32Array([\n    0x428a2f98, 0x71374491, 0xb5c0fbcf, 0xe9b5dba5, 0x3956c25b, 0x59f111f1, 0x923f82a4, 0xab1c5ed5,\n    0xd807aa98, 0x12835b01, 0x243185be, 0x550c7dc3, 0x72be5d74, 0x80deb1fe, 0x9bdc06a7, 0xc19bf174,\n    0xe49b69c1, 0xefbe4786, 0x0fc19dc6, 0x240ca1cc, 0x2de92c6f, 0x4a7484aa, 0x5cb0a9dc, 0x76f988da,\n    0x983e5152, 0xa831c66d, 0xb00327c8, 0xbf597fc7, 0xc6e00bf3, 0xd5a79147, 0x06ca6351, 0x14292967,\n    0x27b70a85, 0x2e1b2138, 0x4d2c6dfc, 0x53380d13, 0x650a7354, 0x766a0abb, 0x81c2c92e, 0x92722c85,\n    0xa2bfe8a1, 0xa81a664b, 0xc24b8b70, 0xc76c51a3, 0xd192e819, 0xd6990624, 0xf40e3585, 0x106aa070,\n    0x19a4c116, 0x1e376c08, 0x2748774c, 0x34b0bcb5, 0x391c0cb3, 0x4ed8aa4a, 0x5b9cca4f, 0x682e6ff3,\n    0x748f82ee, 0x78a5636f, 0x84c87814, 0x8cc70208, 0x90befffa, 0xa4506ceb, 0xbef9a3f7, 0xc67178f2\n]);\n/** Initial state: first 32 bits of fractional parts of the square roots of the first 8 primes 2..19. */\n// prettier-ignore\nconst SHA256_IV = /* @__PURE__ */ new Uint32Array([\n    0x6a09e667, 0xbb67ae85, 0x3c6ef372, 0xa54ff53a, 0x510e527f, 0x9b05688c, 0x1f83d9ab, 0x5be0cd19\n]);\n/**\n * Temporary buffer, not used to store anything between runs.\n * Named this way because it matches specification.\n */\nconst SHA256_W = /* @__PURE__ */ new Uint32Array(64);\nclass SHA256 extends _md_js__WEBPACK_IMPORTED_MODULE_0__.HashMD {\n    constructor() {\n        super(64, 32, 8, false);\n        // We cannot use array here since array allows indexing by variable\n        // which means optimizer/compiler cannot use registers.\n        this.A = SHA256_IV[0] | 0;\n        this.B = SHA256_IV[1] | 0;\n        this.C = SHA256_IV[2] | 0;\n        this.D = SHA256_IV[3] | 0;\n        this.E = SHA256_IV[4] | 0;\n        this.F = SHA256_IV[5] | 0;\n        this.G = SHA256_IV[6] | 0;\n        this.H = SHA256_IV[7] | 0;\n    }\n    get() {\n        const { A, B, C, D, E, F, G, H } = this;\n        return [A, B, C, D, E, F, G, H];\n    }\n    // prettier-ignore\n    set(A, B, C, D, E, F, G, H) {\n        this.A = A | 0;\n        this.B = B | 0;\n        this.C = C | 0;\n        this.D = D | 0;\n        this.E = E | 0;\n        this.F = F | 0;\n        this.G = G | 0;\n        this.H = H | 0;\n    }\n    process(view, offset) {\n        // Extend the first 16 words into the remaining 48 words w[16..63] of the message schedule array\n        for (let i = 0; i < 16; i++, offset += 4)\n            SHA256_W[i] = view.getUint32(offset, false);\n        for (let i = 16; i < 64; i++) {\n            const W15 = SHA256_W[i - 15];\n            const W2 = SHA256_W[i - 2];\n            const s0 = (0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.rotr)(W15, 7) ^ (0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.rotr)(W15, 18) ^ (W15 >>> 3);\n            const s1 = (0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.rotr)(W2, 17) ^ (0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.rotr)(W2, 19) ^ (W2 >>> 10);\n            SHA256_W[i] = (s1 + SHA256_W[i - 7] + s0 + SHA256_W[i - 16]) | 0;\n        }\n        // Compression function main loop, 64 rounds\n        let { A, B, C, D, E, F, G, H } = this;\n        for (let i = 0; i < 64; i++) {\n            const sigma1 = (0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.rotr)(E, 6) ^ (0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.rotr)(E, 11) ^ (0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.rotr)(E, 25);\n            const T1 = (H + sigma1 + (0,_md_js__WEBPACK_IMPORTED_MODULE_0__.Chi)(E, F, G) + SHA256_K[i] + SHA256_W[i]) | 0;\n            const sigma0 = (0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.rotr)(A, 2) ^ (0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.rotr)(A, 13) ^ (0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.rotr)(A, 22);\n            const T2 = (sigma0 + (0,_md_js__WEBPACK_IMPORTED_MODULE_0__.Maj)(A, B, C)) | 0;\n            H = G;\n            G = F;\n            F = E;\n            E = (D + T1) | 0;\n            D = C;\n            C = B;\n            B = A;\n            A = (T1 + T2) | 0;\n        }\n        // Add the compressed chunk to the current hash value\n        A = (A + this.A) | 0;\n        B = (B + this.B) | 0;\n        C = (C + this.C) | 0;\n        D = (D + this.D) | 0;\n        E = (E + this.E) | 0;\n        F = (F + this.F) | 0;\n        G = (G + this.G) | 0;\n        H = (H + this.H) | 0;\n        this.set(A, B, C, D, E, F, G, H);\n    }\n    roundClean() {\n        SHA256_W.fill(0);\n    }\n    destroy() {\n        this.set(0, 0, 0, 0, 0, 0, 0, 0);\n        this.buffer.fill(0);\n    }\n}\n/**\n * Constants taken from https://nvlpubs.nist.gov/nistpubs/FIPS/NIST.FIPS.180-4.pdf.\n */\nclass SHA224 extends SHA256 {\n    constructor() {\n        super();\n        this.A = 0xc1059ed8 | 0;\n        this.B = 0x367cd507 | 0;\n        this.C = 0x3070dd17 | 0;\n        this.D = 0xf70e5939 | 0;\n        this.E = 0xffc00b31 | 0;\n        this.F = 0x68581511 | 0;\n        this.G = 0x64f98fa7 | 0;\n        this.H = 0xbefa4fa4 | 0;\n        this.outputLen = 28;\n    }\n}\n/** SHA2-256 hash function */\nconst sha256 = /* @__PURE__ */ (0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.wrapConstructor)(() => new SHA256());\n/** SHA2-224 hash function */\nconst sha224 = /* @__PURE__ */ (0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.wrapConstructor)(() => new SHA224());\n//# sourceMappingURL=sha256.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@reown/appkit/node_modules/@noble/hashes/esm/sha256.js\n"));

/***/ })

}]);