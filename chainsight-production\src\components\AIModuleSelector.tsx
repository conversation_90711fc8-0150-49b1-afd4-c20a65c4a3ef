'use client';

import { motion } from 'framer-motion';
import { 
  Bitcoin, 
  TrendingUp, 
  Scale, 
  Palette, 
  Scan, 
  MessageCircle, 
  Globe,
  Zap
} from 'lucide-react';
import { LoadingSpinner } from './ui/LoadingSpinner';

interface AIModuleSelectorProps {
  modules: any[];
  activeModule: string;
  onModuleChange: (module: string) => void;
  loading?: boolean;
}

const moduleConfig = {
  crypto: {
    icon: Bitcoin,
    name: 'Crypto & Blockchain',
    description: 'Real-time crypto analysis',
    color: 'from-orange-500 to-yellow-500',
    status: 'online'
  },
  finance: {
    icon: TrendingUp,
    name: 'AI Finance',
    description: 'Market intelligence',
    color: 'from-green-500 to-emerald-500',
    status: 'online'
  },
  legal: {
    icon: Scale,
    name: 'Legal & HR AI',
    description: 'Document analysis',
    color: 'from-blue-500 to-cyan-500',
    status: 'online'
  },
  design: {
    icon: Palette,
    name: 'Design AI',
    description: 'Creative assistance',
    color: 'from-purple-500 to-pink-500',
    status: 'online'
  },
  facial: {
    icon: <PERSON>an,
    name: 'Facial Recognition',
    description: 'Biometric analysis',
    color: 'from-red-500 to-rose-500',
    status: 'online'
  },
  support: {
    icon: MessageCircle,
    name: 'Customer Support',
    description: 'Intelligent assistance',
    color: 'from-indigo-500 to-purple-500',
    status: 'online'
  },
  web3: {
    icon: Globe,
    name: 'Web3 & DeFi',
    description: 'Blockchain development',
    color: 'from-teal-500 to-cyan-500',
    status: 'online'
  }
};

export function AIModuleSelector({ 
  modules, 
  activeModule, 
  onModuleChange, 
  loading 
}: AIModuleSelectorProps) {
  const moduleKeys = Object.keys(moduleConfig);

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="text-center">
        <motion.h2
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="text-3xl font-bold text-dark-100 mb-2"
        >
          AI Module <span className="bg-gradient-to-r from-primary-400 to-primary-500 bg-clip-text text-transparent">
            Selection
          </span>
        </motion.h2>
        <p className="text-dark-300">
          Choose your AI agent specialization
        </p>
      </div>

      {/* Module Grid */}
      <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-7 gap-4">
        {moduleKeys.map((moduleKey, index) => {
          const config = moduleConfig[moduleKey as keyof typeof moduleConfig];
          const Icon = config.icon;
          const isActive = activeModule === moduleKey;
          
          return (
            <motion.button
              key={moduleKey}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: index * 0.1 }}
              onClick={() => onModuleChange(moduleKey)}
              disabled={loading}
              className={`
                relative group p-4 rounded-2xl border transition-all duration-300 glass
                ${isActive
                  ? 'bg-gradient-to-r ' + config.color + ' border-primary-500/30 shadow-lg shadow-primary-500/20'
                  : 'border-dark-700 hover:border-primary-500/30 hover:bg-dark-800/50'
                }
              `}
            >
              {/* Status Indicator */}
              <div className="absolute top-2 right-2">
                <div className={`
                  w-2 h-2 rounded-full
                  ${config.status === 'online' ? 'bg-green-400' : 'bg-gray-400'}
                  ${config.status === 'online' ? 'animate-pulse' : ''}
                `} />
              </div>

              {/* Icon */}
              <div className="flex flex-col items-center space-y-2">
                <div className={`
                  p-3 rounded-xl transition-all duration-300
                  ${isActive 
                    ? 'bg-white/20' 
                    : 'bg-white/10 group-hover:bg-white/15'
                  }
                `}>
                  <Icon className={`
                    w-6 h-6 transition-colors duration-300
                    ${isActive ? 'text-white' : 'text-dark-300 group-hover:text-primary-400'}
                  `} />
                </div>

                {/* Name */}
                <div className="text-center">
                  <h3 className={`
                    text-sm font-semibold transition-colors duration-300
                    ${isActive ? 'text-white' : 'text-dark-200 group-hover:text-white'}
                  `}>
                    {config.name}
                  </h3>
                  <p className={`
                    text-xs transition-colors duration-300
                    ${isActive ? 'text-white/80' : 'text-dark-400 group-hover:text-dark-300'}
                  `}>
                    {config.description}
                  </p>
                </div>
              </div>

              {/* Active Indicator */}
              {isActive && (
                <motion.div
                  layoutId="activeModule"
                  className="absolute inset-0 rounded-2xl border-2 border-primary-400/50"
                  transition={{ type: "spring", bounce: 0.2, duration: 0.6 }}
                />
              )}

              {/* Loading Overlay */}
              {loading && isActive && (
                <div className="absolute inset-0 bg-black/20 rounded-2xl flex items-center justify-center">
                  <LoadingSpinner size="sm" />
                </div>
              )}
            </motion.button>
          );
        })}
      </div>

      {/* Active Module Info */}
      <motion.div
        key={activeModule}
        initial={{ opacity: 0, y: 10 }}
        animate={{ opacity: 1, y: 0 }}
        className="bg-black/20 backdrop-blur-xl border border-white/10 rounded-xl p-4"
      >
        <div className="flex items-center space-x-3">
          <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse" />
          <span className="text-white font-medium">
            {moduleConfig[activeModule as keyof typeof moduleConfig]?.name} Module Active
          </span>
          <Zap className="w-4 h-4 text-yellow-400" />
        </div>
      </motion.div>
    </div>
  );
}
