// Finance Analyzer Types and Interfaces

export interface StockData {
  symbol: string;
  name: string;
  price: number;
  change: number;
  changePercent: number;
  volume: number;
  marketCap: number;
  peRatio?: number;
  eps?: number;
  dividend?: number;
  beta?: number;
  sector?: string;
  industry?: string;
  lastUpdated: Date;
}

export interface HistoricalData {
  date: Date;
  open: number;
  high: number;
  low: number;
  close: number;
  volume: number;
  adjustedClose?: number;
}

export interface TechnicalIndicator {
  name: string;
  value: number | number[];
  signal?: 'buy' | 'sell' | 'hold';
  timestamp: Date;
  parameters?: Record<string, any>;
}

export interface PriceTarget {
  symbol: string;
  currentPrice: number;
  targetPrice: number;
  timeframe: string;
  confidence: number;
  reasoning: string;
  analyst?: string;
  date: Date;
}

export interface MarketPrediction {
  symbol: string;
  predictionType: 'price' | 'trend' | 'volatility';
  timeframe: string;
  prediction: number | string;
  confidence: number;
  factors: string[];
  model: string;
  createdAt: Date;
}

export interface Portfolio {
  id: string;
  name: string;
  userId: string;
  holdings: PortfolioHolding[];
  totalValue: number;
  totalCost: number;
  totalReturn: number;
  totalReturnPercent: number;
  dayChange: number;
  dayChangePercent: number;
  createdAt: Date;
  updatedAt: Date;
}

export interface PortfolioHolding {
  symbol: string;
  name: string;
  shares: number;
  averageCost: number;
  currentPrice: number;
  marketValue: number;
  totalCost: number;
  unrealizedGain: number;
  unrealizedGainPercent: number;
  dayChange: number;
  dayChangePercent: number;
  weight: number; // percentage of portfolio
}

export interface RiskMetrics {
  beta: number;
  alpha: number;
  sharpeRatio: number;
  volatility: number;
  maxDrawdown: number;
  var95: number; // Value at Risk 95%
  var99: number; // Value at Risk 99%
  correlationToMarket: number;
  riskScore: number; // 1-10 scale
}

export interface PerformanceMetrics {
  totalReturn: number;
  annualizedReturn: number;
  volatility: number;
  sharpeRatio: number;
  sortinoRatio: number;
  calmarRatio: number;
  maxDrawdown: number;
  winRate: number;
  profitFactor: number;
  averageWin: number;
  averageLoss: number;
}

export interface TradingSignal {
  symbol: string;
  signal: 'buy' | 'sell' | 'hold';
  strength: number; // 1-10
  price: number;
  targetPrice?: number;
  stopLoss?: number;
  timeframe: string;
  indicators: string[];
  reasoning: string;
  confidence: number;
  createdAt: Date;
}

export interface MarketSentiment {
  symbol: string;
  sentiment: 'bullish' | 'bearish' | 'neutral';
  score: number; // -1 to 1
  sources: Array<{
    source: string;
    sentiment: number;
    weight: number;
  }>;
  newsCount: number;
  socialMentions: number;
  lastUpdated: Date;
}

export interface EconomicIndicator {
  name: string;
  value: number;
  previousValue: number;
  change: number;
  changePercent: number;
  unit: string;
  frequency: string;
  releaseDate: Date;
  nextReleaseDate?: Date;
  impact: 'high' | 'medium' | 'low';
}

export interface Sector {
  name: string;
  performance: {
    day: number;
    week: number;
    month: number;
    quarter: number;
    year: number;
  };
  marketCap: number;
  peRatio: number;
  topStocks: string[];
  trend: 'up' | 'down' | 'sideways';
}

export interface OptionsData {
  symbol: string;
  strike: number;
  expiration: Date;
  type: 'call' | 'put';
  bid: number;
  ask: number;
  last: number;
  volume: number;
  openInterest: number;
  impliedVolatility: number;
  delta: number;
  gamma: number;
  theta: number;
  vega: number;
}

export interface DividendData {
  symbol: string;
  exDate: Date;
  payDate: Date;
  amount: number;
  yield: number;
  payoutRatio: number;
  growthRate: number;
  frequency: 'monthly' | 'quarterly' | 'semi-annual' | 'annual';
}

// Enums
export enum TimeFrame {
  ONE_DAY = '1d',
  FIVE_DAYS = '5d',
  ONE_MONTH = '1mo',
  THREE_MONTHS = '3mo',
  SIX_MONTHS = '6mo',
  ONE_YEAR = '1y',
  TWO_YEARS = '2y',
  FIVE_YEARS = '5y',
  TEN_YEARS = '10y',
  YTD = 'ytd',
  MAX = 'max'
}

export enum IndicatorType {
  SMA = 'SMA',
  EMA = 'EMA',
  RSI = 'RSI',
  MACD = 'MACD',
  BOLLINGER_BANDS = 'BollingerBands',
  STOCHASTIC = 'Stochastic',
  WILLIAMS_R = 'Williams%R',
  CCI = 'CCI',
  ADX = 'ADX',
  ATR = 'ATR'
}

export enum MarketCondition {
  BULL_MARKET = 'bull_market',
  BEAR_MARKET = 'bear_market',
  SIDEWAYS = 'sideways',
  VOLATILE = 'volatile',
  TRENDING_UP = 'trending_up',
  TRENDING_DOWN = 'trending_down'
}

export enum RiskLevel {
  VERY_LOW = 'very_low',
  LOW = 'low',
  MODERATE = 'moderate',
  HIGH = 'high',
  VERY_HIGH = 'very_high'
}
