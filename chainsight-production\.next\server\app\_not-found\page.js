/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/_not-found/page";
exports.ids = ["app/_not-found/page"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2F_not-found%2Fpage&page=%2F_not-found%2Fpage&appPaths=&pagePath=..%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-error.js&appDir=F%3A%5CAI%20chain%5Cchainsight-production%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=F%3A%5CAI%20chain%5Cchainsight-production&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2F_not-found%2Fpage&page=%2F_not-found%2Fpage&appPaths=&pagePath=..%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-error.js&appDir=F%3A%5CAI%20chain%5Cchainsight-production%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=F%3A%5CAI%20chain%5Cchainsight-production&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/module.compiled.js?cc4a\");\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\nconst notFound0 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23));\nconst module1 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\"));\nconst module2 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23));\nconst module3 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/forbidden-error */ \"(rsc)/./node_modules/next/dist/client/components/forbidden-error.js\", 23));\nconst module4 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/unauthorized-error */ \"(rsc)/./node_modules/next/dist/client/components/unauthorized-error.js\", 23));\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n          children: [\"/_not-found\", {\n            children: ['__PAGE__', {}, {\n              page: [\n                notFound0,\n                \"next/dist/client/components/not-found-error\"\n              ]\n            }]\n          }, {}]\n        },\n        {\n        'layout': [module1, \"F:\\\\AI chain\\\\chainsight-production\\\\src\\\\app\\\\layout.tsx\"],\n'not-found': [module2, \"next/dist/client/components/not-found-error\"],\n'forbidden': [module3, \"next/dist/client/components/forbidden-error\"],\n'unauthorized': [module4, \"next/dist/client/components/unauthorized-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/_not-found/page\",\n        pathname: \"/_not-found\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: '',\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2F_not-found%2Fpage&page=%2F_not-found%2Fpage&appPaths=&pagePath=..%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-error.js&appDir=F%3A%5CAI%20chain%5Cchainsight-production%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=F%3A%5CAI%20chain%5Cchainsight-production&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22F%3A%5C%5CAI%20chain%5C%5Cchainsight-production%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5CAI%20chain%5C%5Cchainsight-production%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5CAI%20chain%5C%5Cchainsight-production%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5CAI%20chain%5C%5Cchainsight-production%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5CAI%20chain%5C%5Cchainsight-production%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5CAI%20chain%5C%5Cchainsight-production%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5CAI%20chain%5C%5Cchainsight-production%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5CAI%20chain%5C%5Cchainsight-production%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22F%3A%5C%5CAI%20chain%5C%5Cchainsight-production%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5CAI%20chain%5C%5Cchainsight-production%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5CAI%20chain%5C%5Cchainsight-production%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5CAI%20chain%5C%5Cchainsight-production%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5CAI%20chain%5C%5Cchainsight-production%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5CAI%20chain%5C%5Cchainsight-production%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5CAI%20chain%5C%5Cchainsight-production%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5CAI%20chain%5C%5Cchainsight-production%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(rsc)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(rsc)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(rsc)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22F%3A%5C%5CAI%20chain%5C%5Cchainsight-production%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5CAI%20chain%5C%5Cchainsight-production%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5CAI%20chain%5C%5Cchainsight-production%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5CAI%20chain%5C%5Cchainsight-production%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5CAI%20chain%5C%5Cchainsight-production%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5CAI%20chain%5C%5Cchainsight-production%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5CAI%20chain%5C%5Cchainsight-production%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5CAI%20chain%5C%5Cchainsight-production%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22F%3A%5C%5CAI%20chain%5C%5Cchainsight-production%5C%5Csrc%5C%5Capp%5C%5Clayout.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22F%3A%5C%5CAI%20chain%5C%5Cchainsight-production%5C%5Csrc%5C%5Capp%5C%5Clayout.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkYlM0ElNUMlNUNBSSUyMGNoYWluJTVDJTVDY2hhaW5zaWdodC1wcm9kdWN0aW9uJTVDJTVDc3JjJTVDJTVDYXBwJTVDJTVDbGF5b3V0LnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsb0pBQThGIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJGOlxcXFxBSSBjaGFpblxcXFxjaGFpbnNpZ2h0LXByb2R1Y3Rpb25cXFxcc3JjXFxcXGFwcFxcXFxsYXlvdXQudHN4XCIpO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22F%3A%5C%5CAI%20chain%5C%5Cchainsight-production%5C%5Csrc%5C%5Capp%5C%5Clayout.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"F:\\\\AI chain\\\\chainsight-production\\\\src\\\\app\\\\layout.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"F:\\AI chain\\chainsight-production\\src\\app\\layout.tsx",
"default",
));


/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22F%3A%5C%5CAI%20chain%5C%5Cchainsight-production%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5CAI%20chain%5C%5Cchainsight-production%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5CAI%20chain%5C%5Cchainsight-production%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5CAI%20chain%5C%5Cchainsight-production%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5CAI%20chain%5C%5Cchainsight-production%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5CAI%20chain%5C%5Cchainsight-production%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5CAI%20chain%5C%5Cchainsight-production%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5CAI%20chain%5C%5Cchainsight-production%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22F%3A%5C%5CAI%20chain%5C%5Cchainsight-production%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5CAI%20chain%5C%5Cchainsight-production%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5CAI%20chain%5C%5Cchainsight-production%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5CAI%20chain%5C%5Cchainsight-production%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5CAI%20chain%5C%5Cchainsight-production%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5CAI%20chain%5C%5Cchainsight-production%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5CAI%20chain%5C%5Cchainsight-production%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5CAI%20chain%5C%5Cchainsight-production%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(ssr)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22F%3A%5C%5CAI%20chain%5C%5Cchainsight-production%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5CAI%20chain%5C%5Cchainsight-production%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5CAI%20chain%5C%5Cchainsight-production%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5CAI%20chain%5C%5Cchainsight-production%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5CAI%20chain%5C%5Cchainsight-production%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5CAI%20chain%5C%5Cchainsight-production%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5CAI%20chain%5C%5Cchainsight-production%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5CAI%20chain%5C%5Cchainsight-production%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22F%3A%5C%5CAI%20chain%5C%5Cchainsight-production%5C%5Csrc%5C%5Capp%5C%5Clayout.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22F%3A%5C%5CAI%20chain%5C%5Cchainsight-production%5C%5Csrc%5C%5Capp%5C%5Clayout.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(ssr)/./src/app/layout.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkYlM0ElNUMlNUNBSSUyMGNoYWluJTVDJTVDY2hhaW5zaWdodC1wcm9kdWN0aW9uJTVDJTVDc3JjJTVDJTVDYXBwJTVDJTVDbGF5b3V0LnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsb0pBQThGIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJGOlxcXFxBSSBjaGFpblxcXFxjaGFpbnNpZ2h0LXByb2R1Y3Rpb25cXFxcc3JjXFxcXGFwcFxcXFxsYXlvdXQudHN4XCIpO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22F%3A%5C%5CAI%20chain%5C%5Cchainsight-production%5C%5Csrc%5C%5Capp%5C%5Clayout.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"ea751876dccc\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJGOlxcQUkgY2hhaW5cXGNoYWluc2lnaHQtcHJvZHVjdGlvblxcc3JjXFxhcHBcXGdsb2JhbHMuY3NzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IFwiZWE3NTE4NzZkY2NjXCJcbmlmIChtb2R1bGUuaG90KSB7IG1vZHVsZS5ob3QuYWNjZXB0KCkgfVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./src/app/globals.css\n");

/***/ }),

/***/ "(ssr)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variable_font_inter_variableName_inter___WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"],\"variable\":\"--font-inter\"}],\"variableName\":\"inter\"} */ \"(ssr)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"],\\\"variable\\\":\\\"--font-inter\\\"}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variable_font_inter_variableName_inter___WEBPACK_IMPORTED_MODULE_13___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variable_font_inter_variableName_inter___WEBPACK_IMPORTED_MODULE_13__);\n/* harmony import */ var wagmi__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! wagmi */ \"(ssr)/./node_modules/wagmi/dist/esm/context.js\");\n/* harmony import */ var _rainbow_me_rainbowkit__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @rainbow-me/rainbowkit */ \"(ssr)/./node_modules/@rainbow-me/rainbowkit/dist/index.js\");\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @tanstack/react-query */ \"(ssr)/./node_modules/@tanstack/query-core/build/modern/queryClient.js\");\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @tanstack/react-query */ \"(ssr)/./node_modules/@tanstack/react-query/build/modern/QueryClientProvider.js\");\n/* harmony import */ var wagmi_chains__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! wagmi/chains */ \"(ssr)/./node_modules/viem/_esm/chains/definitions/mainnet.js\");\n/* harmony import */ var wagmi_chains__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! wagmi/chains */ \"(ssr)/./node_modules/viem/_esm/chains/definitions/polygon.js\");\n/* harmony import */ var wagmi_chains__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! wagmi/chains */ \"(ssr)/./node_modules/viem/_esm/chains/definitions/optimism.js\");\n/* harmony import */ var wagmi_chains__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! wagmi/chains */ \"(ssr)/./node_modules/viem/_esm/chains/definitions/arbitrum.js\");\n/* harmony import */ var wagmi_chains__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! wagmi/chains */ \"(ssr)/./node_modules/viem/_esm/chains/definitions/base.js\");\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react-hot-toast */ \"(ssr)/./node_modules/react-hot-toast/dist/index.mjs\");\n/* harmony import */ var _components_ErrorBoundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ErrorBoundary */ \"(ssr)/./src/components/ErrorBoundary.tsx\");\n/* harmony import */ var _components_ConsoleErrorDisplay__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ConsoleErrorDisplay */ \"(ssr)/./src/components/ConsoleErrorDisplay.tsx\");\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./globals.css */ \"(ssr)/./src/app/globals.css\");\n/* harmony import */ var _rainbow_me_rainbowkit_styles_css__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @rainbow-me/rainbowkit/styles.css */ \"(ssr)/./node_modules/@rainbow-me/rainbowkit/dist/index.css\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\n\n\n\n\n// Web3 Configuration\nconst config = (0,_rainbow_me_rainbowkit__WEBPACK_IMPORTED_MODULE_6__.getDefaultConfig)({\n    appName: 'Chainsight by Connectouch',\n    projectId: process.env.NEXT_PUBLIC_WALLETCONNECT_PROJECT_ID || 'chainsight-demo',\n    chains: [\n        wagmi_chains__WEBPACK_IMPORTED_MODULE_7__.mainnet,\n        wagmi_chains__WEBPACK_IMPORTED_MODULE_8__.polygon,\n        wagmi_chains__WEBPACK_IMPORTED_MODULE_9__.optimism,\n        wagmi_chains__WEBPACK_IMPORTED_MODULE_10__.arbitrum,\n        wagmi_chains__WEBPACK_IMPORTED_MODULE_11__.base\n    ],\n    ssr: true\n});\nconst queryClient = new _tanstack_react_query__WEBPACK_IMPORTED_MODULE_12__.QueryClient();\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"head\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"title\", {\n                        children: \"Chainsight - AI-Powered Blockchain Platform\"\n                    }, void 0, false, {\n                        fileName: \"F:\\\\AI chain\\\\chainsight-production\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 37,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"description\",\n                        content: \"Modular fullstack AI agent platform with crypto, Web3, finance, and AI capabilities\"\n                    }, void 0, false, {\n                        fileName: \"F:\\\\AI chain\\\\chainsight-production\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 38,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"viewport\",\n                        content: \"width=device-width, initial-scale=1\"\n                    }, void 0, false, {\n                        fileName: \"F:\\\\AI chain\\\\chainsight-production\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 39,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"F:\\\\AI chain\\\\chainsight-production\\\\src\\\\app\\\\layout.tsx\",\n                lineNumber: 36,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n                className: `${(next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variable_font_inter_variableName_inter___WEBPACK_IMPORTED_MODULE_13___default().className)} antialiased`,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ErrorBoundary__WEBPACK_IMPORTED_MODULE_2__.ErrorBoundary, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(wagmi__WEBPACK_IMPORTED_MODULE_14__.WagmiProvider, {\n                        config: config,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_tanstack_react_query__WEBPACK_IMPORTED_MODULE_15__.QueryClientProvider, {\n                            client: queryClient,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_rainbow_me_rainbowkit__WEBPACK_IMPORTED_MODULE_6__.RainbowKitProvider, {\n                                children: [\n                                    children,\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_hot_toast__WEBPACK_IMPORTED_MODULE_1__.Toaster, {\n                                        position: \"top-right\",\n                                        toastOptions: {\n                                            duration: 4000,\n                                            style: {\n                                                background: 'rgba(0, 0, 0, 0.8)',\n                                                color: '#fff',\n                                                border: '1px solid rgba(255, 255, 255, 0.1)',\n                                                backdropFilter: 'blur(10px)'\n                                            }\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"F:\\\\AI chain\\\\chainsight-production\\\\src\\\\app\\\\layout.tsx\",\n                                        lineNumber: 47,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ConsoleErrorDisplay__WEBPACK_IMPORTED_MODULE_3__.ConsoleErrorDisplay, {}, void 0, false, {\n                                        fileName: \"F:\\\\AI chain\\\\chainsight-production\\\\src\\\\app\\\\layout.tsx\",\n                                        lineNumber: 59,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"F:\\\\AI chain\\\\chainsight-production\\\\src\\\\app\\\\layout.tsx\",\n                                lineNumber: 45,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"F:\\\\AI chain\\\\chainsight-production\\\\src\\\\app\\\\layout.tsx\",\n                            lineNumber: 44,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"F:\\\\AI chain\\\\chainsight-production\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 43,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"F:\\\\AI chain\\\\chainsight-production\\\\src\\\\app\\\\layout.tsx\",\n                    lineNumber: 42,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"F:\\\\AI chain\\\\chainsight-production\\\\src\\\\app\\\\layout.tsx\",\n                lineNumber: 41,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"F:\\\\AI chain\\\\chainsight-production\\\\src\\\\app\\\\layout.tsx\",\n        lineNumber: 35,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ConsoleErrorDisplay.tsx":
/*!************************************************!*\
  !*** ./src/components/ConsoleErrorDisplay.tsx ***!
  \************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ConsoleErrorDisplay: () => (/* binding */ ConsoleErrorDisplay)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ChevronDown_ChevronUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ChevronDown,ChevronUp,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/triangle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ChevronDown_ChevronUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ChevronDown,ChevronUp,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/chevron-up.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ChevronDown_ChevronUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ChevronDown,ChevronUp,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/chevron-down.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ChevronDown_ChevronUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ChevronDown,ChevronUp,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* __next_internal_client_entry_do_not_use__ ConsoleErrorDisplay auto */ \n\n\n\nfunction ConsoleErrorDisplay() {\n    const [errors, setErrors] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isVisible, setIsVisible] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isExpanded, setIsExpanded] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ConsoleErrorDisplay.useEffect\": ()=>{\n            // Only show in development\n            if (false) {}\n            const errorHandler = {\n                \"ConsoleErrorDisplay.useEffect.errorHandler\": (event)=>{\n                    const newError = {\n                        id: Date.now().toString(),\n                        message: event.message,\n                        stack: event.error?.stack,\n                        timestamp: new Date(),\n                        type: 'error'\n                    };\n                    setErrors({\n                        \"ConsoleErrorDisplay.useEffect.errorHandler\": (prev)=>[\n                                ...prev.slice(-9),\n                                newError\n                            ]\n                    }[\"ConsoleErrorDisplay.useEffect.errorHandler\"]); // Keep last 10 errors\n                    setIsVisible(true);\n                }\n            }[\"ConsoleErrorDisplay.useEffect.errorHandler\"];\n            const rejectionHandler = {\n                \"ConsoleErrorDisplay.useEffect.rejectionHandler\": (event)=>{\n                    const newError = {\n                        id: Date.now().toString(),\n                        message: `Unhandled Promise Rejection: ${event.reason}`,\n                        timestamp: new Date(),\n                        type: 'error'\n                    };\n                    setErrors({\n                        \"ConsoleErrorDisplay.useEffect.rejectionHandler\": (prev)=>[\n                                ...prev.slice(-9),\n                                newError\n                            ]\n                    }[\"ConsoleErrorDisplay.useEffect.rejectionHandler\"]);\n                    setIsVisible(true);\n                }\n            }[\"ConsoleErrorDisplay.useEffect.rejectionHandler\"];\n            // Override console methods to catch warnings and errors\n            const originalError = console.error;\n            const originalWarn = console.warn;\n            console.error = ({\n                \"ConsoleErrorDisplay.useEffect\": (...args)=>{\n                    const message = args.map({\n                        \"ConsoleErrorDisplay.useEffect.message\": (arg)=>typeof arg === 'object' ? JSON.stringify(arg) : String(arg)\n                    }[\"ConsoleErrorDisplay.useEffect.message\"]).join(' ');\n                    if (!message.includes('Chainsight Debug')) {\n                        const newError = {\n                            id: Date.now().toString(),\n                            message,\n                            timestamp: new Date(),\n                            type: 'error'\n                        };\n                        setErrors({\n                            \"ConsoleErrorDisplay.useEffect\": (prev)=>[\n                                    ...prev.slice(-9),\n                                    newError\n                                ]\n                        }[\"ConsoleErrorDisplay.useEffect\"]);\n                        setIsVisible(true);\n                    }\n                    originalError.apply(console, args);\n                }\n            })[\"ConsoleErrorDisplay.useEffect\"];\n            console.warn = ({\n                \"ConsoleErrorDisplay.useEffect\": (...args)=>{\n                    const message = args.map({\n                        \"ConsoleErrorDisplay.useEffect.message\": (arg)=>typeof arg === 'object' ? JSON.stringify(arg) : String(arg)\n                    }[\"ConsoleErrorDisplay.useEffect.message\"]).join(' ');\n                    const newError = {\n                        id: Date.now().toString(),\n                        message,\n                        timestamp: new Date(),\n                        type: 'warning'\n                    };\n                    setErrors({\n                        \"ConsoleErrorDisplay.useEffect\": (prev)=>[\n                                ...prev.slice(-9),\n                                newError\n                            ]\n                    }[\"ConsoleErrorDisplay.useEffect\"]);\n                    setIsVisible(true);\n                    originalWarn.apply(console, args);\n                }\n            })[\"ConsoleErrorDisplay.useEffect\"];\n            window.addEventListener('error', errorHandler);\n            window.addEventListener('unhandledrejection', rejectionHandler);\n            return ({\n                \"ConsoleErrorDisplay.useEffect\": ()=>{\n                    window.removeEventListener('error', errorHandler);\n                    window.removeEventListener('unhandledrejection', rejectionHandler);\n                    console.error = originalError;\n                    console.warn = originalWarn;\n                }\n            })[\"ConsoleErrorDisplay.useEffect\"];\n        }\n    }[\"ConsoleErrorDisplay.useEffect\"], []);\n    const clearErrors = ()=>{\n        setErrors([]);\n        setIsVisible(false);\n    };\n    const getErrorColor = (type)=>{\n        switch(type){\n            case 'error':\n                return 'text-red-400 bg-red-500/10 border-red-500/20';\n            case 'warning':\n                return 'text-yellow-400 bg-yellow-500/10 border-yellow-500/20';\n            default:\n                return 'text-blue-400 bg-blue-500/10 border-blue-500/20';\n        }\n    };\n    if (!isVisible || errors.length === 0) return null;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.AnimatePresence, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n            initial: {\n                opacity: 0,\n                y: 50\n            },\n            animate: {\n                opacity: 1,\n                y: 0\n            },\n            exit: {\n                opacity: 0,\n                y: 50\n            },\n            className: \"fixed bottom-4 right-4 z-50 max-w-md\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-cosmic-dark/95 backdrop-blur-xl border border-red-500/30 rounded-lg shadow-2xl\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between p-3 border-b border-red-500/20\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ChevronDown_ChevronUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                        className: \"w-5 h-5 text-red-400\"\n                                    }, void 0, false, {\n                                        fileName: \"F:\\\\AI chain\\\\chainsight-production\\\\src\\\\components\\\\ConsoleErrorDisplay.tsx\",\n                                        lineNumber: 129,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-red-400 font-semibold\",\n                                        children: [\n                                            \"Console Errors (\",\n                                            errors.length,\n                                            \")\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"F:\\\\AI chain\\\\chainsight-production\\\\src\\\\components\\\\ConsoleErrorDisplay.tsx\",\n                                        lineNumber: 130,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"F:\\\\AI chain\\\\chainsight-production\\\\src\\\\components\\\\ConsoleErrorDisplay.tsx\",\n                                lineNumber: 128,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setIsExpanded(!isExpanded),\n                                        className: \"p-1 hover:bg-white/10 rounded transition-colors\",\n                                        children: isExpanded ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ChevronDown_ChevronUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                            className: \"w-4 h-4 text-gray-400\"\n                                        }, void 0, false, {\n                                            fileName: \"F:\\\\AI chain\\\\chainsight-production\\\\src\\\\components\\\\ConsoleErrorDisplay.tsx\",\n                                            lineNumber: 140,\n                                            columnNumber: 19\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ChevronDown_ChevronUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                            className: \"w-4 h-4 text-gray-400\"\n                                        }, void 0, false, {\n                                            fileName: \"F:\\\\AI chain\\\\chainsight-production\\\\src\\\\components\\\\ConsoleErrorDisplay.tsx\",\n                                            lineNumber: 142,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"F:\\\\AI chain\\\\chainsight-production\\\\src\\\\components\\\\ConsoleErrorDisplay.tsx\",\n                                        lineNumber: 135,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: clearErrors,\n                                        className: \"p-1 hover:bg-white/10 rounded transition-colors\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ChevronDown_ChevronUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                            className: \"w-4 h-4 text-gray-400\"\n                                        }, void 0, false, {\n                                            fileName: \"F:\\\\AI chain\\\\chainsight-production\\\\src\\\\components\\\\ConsoleErrorDisplay.tsx\",\n                                            lineNumber: 149,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"F:\\\\AI chain\\\\chainsight-production\\\\src\\\\components\\\\ConsoleErrorDisplay.tsx\",\n                                        lineNumber: 145,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"F:\\\\AI chain\\\\chainsight-production\\\\src\\\\components\\\\ConsoleErrorDisplay.tsx\",\n                                lineNumber: 134,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"F:\\\\AI chain\\\\chainsight-production\\\\src\\\\components\\\\ConsoleErrorDisplay.tsx\",\n                        lineNumber: 127,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.AnimatePresence, {\n                        children: isExpanded && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                            initial: {\n                                height: 0\n                            },\n                            animate: {\n                                height: 'auto'\n                            },\n                            exit: {\n                                height: 0\n                            },\n                            className: \"overflow-hidden\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"max-h-64 overflow-y-auto p-3 space-y-2\",\n                                children: errors.map((error)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: `p-2 rounded border ${getErrorColor(error.type)}`,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-start justify-between\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex-1 min-w-0\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm font-medium truncate\",\n                                                            children: error.type.toUpperCase()\n                                                        }, void 0, false, {\n                                                            fileName: \"F:\\\\AI chain\\\\chainsight-production\\\\src\\\\components\\\\ConsoleErrorDisplay.tsx\",\n                                                            lineNumber: 171,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-xs text-gray-300 mt-1 break-words\",\n                                                            children: error.message\n                                                        }, void 0, false, {\n                                                            fileName: \"F:\\\\AI chain\\\\chainsight-production\\\\src\\\\components\\\\ConsoleErrorDisplay.tsx\",\n                                                            lineNumber: 174,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        error.stack && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"details\", {\n                                                            className: \"mt-1\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"summary\", {\n                                                                    className: \"text-xs text-gray-400 cursor-pointer\",\n                                                                    children: \"Stack trace\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"F:\\\\AI chain\\\\chainsight-production\\\\src\\\\components\\\\ConsoleErrorDisplay.tsx\",\n                                                                    lineNumber: 179,\n                                                                    columnNumber: 31\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pre\", {\n                                                                    className: \"text-xs text-gray-400 mt-1 overflow-auto max-h-20\",\n                                                                    children: error.stack\n                                                                }, void 0, false, {\n                                                                    fileName: \"F:\\\\AI chain\\\\chainsight-production\\\\src\\\\components\\\\ConsoleErrorDisplay.tsx\",\n                                                                    lineNumber: 182,\n                                                                    columnNumber: 31\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"F:\\\\AI chain\\\\chainsight-production\\\\src\\\\components\\\\ConsoleErrorDisplay.tsx\",\n                                                            lineNumber: 178,\n                                                            columnNumber: 29\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"F:\\\\AI chain\\\\chainsight-production\\\\src\\\\components\\\\ConsoleErrorDisplay.tsx\",\n                                                    lineNumber: 170,\n                                                    columnNumber: 25\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-xs text-gray-500 ml-2 flex-shrink-0\",\n                                                    children: error.timestamp.toLocaleTimeString()\n                                                }, void 0, false, {\n                                                    fileName: \"F:\\\\AI chain\\\\chainsight-production\\\\src\\\\components\\\\ConsoleErrorDisplay.tsx\",\n                                                    lineNumber: 188,\n                                                    columnNumber: 25\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"F:\\\\AI chain\\\\chainsight-production\\\\src\\\\components\\\\ConsoleErrorDisplay.tsx\",\n                                            lineNumber: 169,\n                                            columnNumber: 23\n                                        }, this)\n                                    }, error.id, false, {\n                                        fileName: \"F:\\\\AI chain\\\\chainsight-production\\\\src\\\\components\\\\ConsoleErrorDisplay.tsx\",\n                                        lineNumber: 165,\n                                        columnNumber: 21\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"F:\\\\AI chain\\\\chainsight-production\\\\src\\\\components\\\\ConsoleErrorDisplay.tsx\",\n                                lineNumber: 163,\n                                columnNumber: 17\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"F:\\\\AI chain\\\\chainsight-production\\\\src\\\\components\\\\ConsoleErrorDisplay.tsx\",\n                            lineNumber: 157,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"F:\\\\AI chain\\\\chainsight-production\\\\src\\\\components\\\\ConsoleErrorDisplay.tsx\",\n                        lineNumber: 155,\n                        columnNumber: 11\n                    }, this),\n                    !isExpanded && errors.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-3\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-sm text-gray-300\",\n                            children: [\n                                \"Latest: \",\n                                errors[errors.length - 1]?.message.slice(0, 50),\n                                errors[errors.length - 1]?.message.length > 50 ? '...' : ''\n                            ]\n                        }, void 0, true, {\n                            fileName: \"F:\\\\AI chain\\\\chainsight-production\\\\src\\\\components\\\\ConsoleErrorDisplay.tsx\",\n                            lineNumber: 202,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"F:\\\\AI chain\\\\chainsight-production\\\\src\\\\components\\\\ConsoleErrorDisplay.tsx\",\n                        lineNumber: 201,\n                        columnNumber: 13\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"F:\\\\AI chain\\\\chainsight-production\\\\src\\\\components\\\\ConsoleErrorDisplay.tsx\",\n                lineNumber: 125,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"F:\\\\AI chain\\\\chainsight-production\\\\src\\\\components\\\\ConsoleErrorDisplay.tsx\",\n            lineNumber: 119,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"F:\\\\AI chain\\\\chainsight-production\\\\src\\\\components\\\\ConsoleErrorDisplay.tsx\",\n        lineNumber: 118,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ConsoleErrorDisplay.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ErrorBoundary.tsx":
/*!******************************************!*\
  !*** ./src/components/ErrorBoundary.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ErrorBoundary: () => (/* binding */ ErrorBoundary),\n/* harmony export */   useErrorHandler: () => (/* binding */ useErrorHandler)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Bug_Home_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Bug,Home,RefreshCw!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/triangle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Bug_Home_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Bug,Home,RefreshCw!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/bug.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Bug_Home_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Bug,Home,RefreshCw!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Bug_Home_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Bug,Home,RefreshCw!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/house.js\");\n/* __next_internal_client_entry_do_not_use__ ErrorBoundary,useErrorHandler auto */ \n\n\nclass ErrorBoundary extends react__WEBPACK_IMPORTED_MODULE_1__.Component {\n    constructor(props){\n        super(props), this.handleReload = ()=>{\n            window.location.reload();\n        }, this.handleGoHome = ()=>{\n            window.location.href = '/';\n        };\n        this.state = {\n            hasError: false,\n            error: null,\n            errorInfo: null\n        };\n    }\n    static getDerivedStateFromError(error) {\n        return {\n            hasError: true,\n            error,\n            errorInfo: null\n        };\n    }\n    componentDidCatch(error, errorInfo) {\n        console.error('🚨 Chainsight Error Boundary Caught:', error);\n        console.error('📍 Error Info:', errorInfo);\n        this.setState({\n            error,\n            errorInfo\n        });\n        // Log to external service in production\n        if (false) {}\n    }\n    render() {\n        if (this.state.hasError) {\n            if (this.props.fallback) {\n                return this.props.fallback;\n            }\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"min-h-screen bg-gradient-to-br from-cosmic-dark via-cosmic-mid to-cosmic-light flex items-center justify-center p-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-2xl w-full\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"glass rounded-2xl p-8 text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-center mb-6\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-4 bg-red-500/20 rounded-full\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Bug_Home_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                            className: \"w-12 h-12 text-red-400\"\n                                        }, void 0, false, {\n                                            fileName: \"F:\\\\AI chain\\\\chainsight-production\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                                            lineNumber: 77,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"F:\\\\AI chain\\\\chainsight-production\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                                        lineNumber: 76,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"F:\\\\AI chain\\\\chainsight-production\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                                    lineNumber: 75,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-3xl font-bold text-white mb-4\",\n                                    children: \"Oops! Something went wrong\"\n                                }, void 0, false, {\n                                    fileName: \"F:\\\\AI chain\\\\chainsight-production\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                                    lineNumber: 81,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-300 mb-8 text-lg\",\n                                    children: \"The Chainsight platform encountered an unexpected error. Don't worry, our AI agents are working to fix this!\"\n                                }, void 0, false, {\n                                    fileName: \"F:\\\\AI chain\\\\chainsight-production\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                                    lineNumber: 85,\n                                    columnNumber: 15\n                                }, this),\n                                 true && this.state.error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mb-8 p-4 bg-red-900/20 rounded-lg border border-red-500/30 text-left\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-red-400 font-semibold mb-2 flex items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Bug_Home_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                    className: \"w-4 h-4 mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"F:\\\\AI chain\\\\chainsight-production\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                                                    lineNumber: 94,\n                                                    columnNumber: 21\n                                                }, this),\n                                                \"Development Error Details:\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"F:\\\\AI chain\\\\chainsight-production\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                                            lineNumber: 93,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pre\", {\n                                            className: \"text-sm text-red-300 overflow-auto max-h-40\",\n                                            children: this.state.error.message\n                                        }, void 0, false, {\n                                            fileName: \"F:\\\\AI chain\\\\chainsight-production\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                                            lineNumber: 97,\n                                            columnNumber: 19\n                                        }, this),\n                                        this.state.error.stack && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"details\", {\n                                            className: \"mt-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"summary\", {\n                                                    className: \"text-red-400 cursor-pointer\",\n                                                    children: \"Stack Trace\"\n                                                }, void 0, false, {\n                                                    fileName: \"F:\\\\AI chain\\\\chainsight-production\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                                                    lineNumber: 102,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pre\", {\n                                                    className: \"text-xs text-red-300 mt-2 overflow-auto max-h-32\",\n                                                    children: this.state.error.stack\n                                                }, void 0, false, {\n                                                    fileName: \"F:\\\\AI chain\\\\chainsight-production\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                                                    lineNumber: 103,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"F:\\\\AI chain\\\\chainsight-production\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                                            lineNumber: 101,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"F:\\\\AI chain\\\\chainsight-production\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                                    lineNumber: 92,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-col sm:flex-row gap-4 justify-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: this.handleReload,\n                                            className: \"flex items-center justify-center px-6 py-3 bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 text-white font-semibold rounded-lg transition-all duration-300 transform hover:scale-105\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Bug_Home_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                    className: \"w-5 h-5 mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"F:\\\\AI chain\\\\chainsight-production\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                                                    lineNumber: 117,\n                                                    columnNumber: 19\n                                                }, this),\n                                                \"Reload Platform\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"F:\\\\AI chain\\\\chainsight-production\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                                            lineNumber: 113,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: this.handleGoHome,\n                                            className: \"flex items-center justify-center px-6 py-3 bg-gradient-to-r from-gray-600 to-gray-700 hover:from-gray-700 hover:to-gray-800 text-white font-semibold rounded-lg transition-all duration-300 transform hover:scale-105\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Bug_Home_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                    className: \"w-5 h-5 mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"F:\\\\AI chain\\\\chainsight-production\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                                                    lineNumber: 125,\n                                                    columnNumber: 19\n                                                }, this),\n                                                \"Go Home\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"F:\\\\AI chain\\\\chainsight-production\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                                            lineNumber: 121,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"F:\\\\AI chain\\\\chainsight-production\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                                    lineNumber: 112,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mt-8 p-4 bg-blue-900/20 rounded-lg border border-blue-500/30\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-blue-300 text-sm\",\n                                        children: \"If this error persists, please contact our support team with the error details above.\"\n                                    }, void 0, false, {\n                                        fileName: \"F:\\\\AI chain\\\\chainsight-production\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                                        lineNumber: 132,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"F:\\\\AI chain\\\\chainsight-production\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                                    lineNumber: 131,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"F:\\\\AI chain\\\\chainsight-production\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                            lineNumber: 74,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute inset-0 -z-10\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"absolute top-1/4 left-1/4 w-64 h-64 bg-red-500/10 rounded-full blur-3xl\"\n                                }, void 0, false, {\n                                    fileName: \"F:\\\\AI chain\\\\chainsight-production\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                                    lineNumber: 140,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"absolute bottom-1/4 right-1/4 w-64 h-64 bg-blue-500/10 rounded-full blur-3xl\"\n                                }, void 0, false, {\n                                    fileName: \"F:\\\\AI chain\\\\chainsight-production\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                                    lineNumber: 141,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"F:\\\\AI chain\\\\chainsight-production\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                            lineNumber: 139,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"F:\\\\AI chain\\\\chainsight-production\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                    lineNumber: 72,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"F:\\\\AI chain\\\\chainsight-production\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                lineNumber: 71,\n                columnNumber: 9\n            }, this);\n        }\n        return this.props.children;\n    }\n}\n// Hook version for functional components\nfunction useErrorHandler() {\n    const handleError = (error, errorInfo)=>{\n        console.error('🚨 Chainsight Error Handler:', error);\n        if (errorInfo) {\n            console.error('📍 Error Context:', errorInfo);\n        }\n    };\n    return {\n        handleError\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ErrorBoundary.tsx\n");

/***/ }),

/***/ "../app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "assert":
/*!*************************!*\
  !*** external "assert" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("assert");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "child_process":
/*!********************************!*\
  !*** external "child_process" ***!
  \********************************/
/***/ ((module) => {

"use strict";
module.exports = require("child_process");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "module":
/*!*************************!*\
  !*** external "module" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("module");

/***/ }),

/***/ "net":
/*!**********************!*\
  !*** external "net" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("net");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "node:crypto":
/*!******************************!*\
  !*** external "node:crypto" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:crypto");

/***/ }),

/***/ "os":
/*!*********************!*\
  !*** external "os" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("os");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "pino-pretty":
/*!******************************!*\
  !*** external "pino-pretty" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("pino-pretty");

/***/ }),

/***/ "punycode":
/*!***************************!*\
  !*** external "punycode" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("punycode");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tls":
/*!**********************!*\
  !*** external "tls" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tls");

/***/ }),

/***/ "tty":
/*!**********************!*\
  !*** external "tty" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tty");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ }),

/***/ "worker_threads":
/*!*********************************!*\
  !*** external "worker_threads" ***!
  \*********************************/
/***/ ((module) => {

"use strict";
module.exports = require("worker_threads");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/@rainbow-me","vendor-chunks/viem","vendor-chunks/@noble","vendor-chunks/next","vendor-chunks/ox","vendor-chunks/abitype","vendor-chunks/framer-motion","vendor-chunks/motion-dom","vendor-chunks/@wagmi","vendor-chunks/wagmi","vendor-chunks/@tanstack","vendor-chunks/motion-utils","vendor-chunks/lucide-react","vendor-chunks/react-remove-scroll","vendor-chunks/@vanilla-extract","vendor-chunks/@swc","vendor-chunks/react-style-singleton","vendor-chunks/react-remove-scroll-bar","vendor-chunks/use-callback-ref","vendor-chunks/mipd","vendor-chunks/cuer","vendor-chunks/use-sidecar","vendor-chunks/eventemitter3","vendor-chunks/tslib","vendor-chunks/react-hot-toast","vendor-chunks/qr","vendor-chunks/goober","vendor-chunks/clsx","vendor-chunks/ua-parser-js","vendor-chunks/get-nonce"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2F_not-found%2Fpage&page=%2F_not-found%2Fpage&appPaths=&pagePath=..%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-error.js&appDir=F%3A%5CAI%20chain%5Cchainsight-production%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=F%3A%5CAI%20chain%5Cchainsight-production&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();