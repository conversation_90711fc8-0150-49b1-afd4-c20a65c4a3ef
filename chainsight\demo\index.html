<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Chainsight by Connectouch - Live Demo</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdn.socket.io/4.7.0/socket.io.min.js"></script>
    <style>
        body {
            background: linear-gradient(135deg, #0a0a0a 0%, #1a1a1a 100%);
            color: #ffffff;
            font-family: 'Inter', sans-serif;
        }
        .gold-accent {
            color: #ffd700;
        }
        .gold-border {
            border-color: #ffd700;
        }
        .glass-effect {
            background: rgba(255, 255, 255, 0.05);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 215, 0, 0.2);
        }
        .pulse-gold {
            animation: pulse-gold 2s infinite;
        }
        @keyframes pulse-gold {
            0%, 100% { box-shadow: 0 0 5px rgba(255, 215, 0, 0.5); }
            50% { box-shadow: 0 0 20px rgba(255, 215, 0, 0.8); }
        }
        .typing-indicator {
            display: inline-block;
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background-color: #ffd700;
            animation: typing 1.4s infinite ease-in-out;
        }
        .typing-indicator:nth-child(1) { animation-delay: -0.32s; }
        .typing-indicator:nth-child(2) { animation-delay: -0.16s; }
        @keyframes typing {
            0%, 80%, 100% { transform: scale(0); opacity: 0.5; }
            40% { transform: scale(1); opacity: 1; }
        }
    </style>
</head>
<body class="min-h-screen">
    <!-- Header -->
    <header class="glass-effect border-b border-gold-accent/20 p-4">
        <div class="container mx-auto flex items-center justify-between">
            <div class="flex items-center space-x-3">
                <div class="w-10 h-10 bg-gradient-to-r from-yellow-400 to-yellow-600 rounded-lg flex items-center justify-center">
                    <span class="text-black font-bold text-xl">C</span>
                </div>
                <div>
                    <h1 class="text-2xl font-bold gold-accent">Chainsight</h1>
                    <p class="text-sm text-gray-400">by Connectouch</p>
                </div>
            </div>
            <div class="flex items-center space-x-4">
                <div id="connection-status" class="flex items-center space-x-2">
                    <div class="w-3 h-3 bg-green-500 rounded-full pulse-gold"></div>
                    <span class="text-sm">Connected</span>
                </div>
                <div class="text-sm">
                    <span class="text-gray-400">Backend:</span>
                    <span class="gold-accent">Running</span>
                </div>
            </div>
        </div>
    </header>

    <!-- Main Dashboard -->
    <main class="container mx-auto p-6 grid grid-cols-1 lg:grid-cols-3 gap-6">
        <!-- Market Data Widget -->
        <div class="lg:col-span-2 glass-effect rounded-xl p-6">
            <div class="flex items-center justify-between mb-4">
                <h2 class="text-xl font-semibold gold-accent">Live Market Data</h2>
                <div class="text-sm text-gray-400">Real-time updates</div>
            </div>
            <div id="market-data" class="space-y-3">
                <!-- Market data will be populated here -->
            </div>
        </div>

        <!-- AI Chat Interface -->
        <div class="glass-effect rounded-xl p-6 flex flex-col h-96">
            <div class="flex items-center justify-between mb-4">
                <h2 class="text-xl font-semibold gold-accent">AI Assistant</h2>
                <div id="ai-status" class="text-sm text-green-400">Online</div>
            </div>
            <div id="chat-messages" class="flex-1 overflow-y-auto space-y-3 mb-4">
                <div class="bg-gray-800 rounded-lg p-3">
                    <div class="text-sm text-gray-400 mb-1">AI Assistant</div>
                    <div>Welcome to Chainsight! I'm your AI assistant. How can I help you today?</div>
                </div>
            </div>
            <div class="flex space-x-2">
                <input 
                    id="chat-input" 
                    type="text" 
                    placeholder="Type your message..." 
                    class="flex-1 bg-gray-800 border border-gray-600 rounded-lg px-3 py-2 text-white focus:border-gold-accent focus:outline-none"
                >
                <button 
                    id="send-btn" 
                    class="bg-gradient-to-r from-yellow-400 to-yellow-600 text-black px-4 py-2 rounded-lg font-semibold hover:from-yellow-500 hover:to-yellow-700 transition-all"
                >
                    Send
                </button>
            </div>
        </div>

        <!-- Notifications Panel -->
        <div class="glass-effect rounded-xl p-6">
            <div class="flex items-center justify-between mb-4">
                <h2 class="text-xl font-semibold gold-accent">Notifications</h2>
                <div id="notification-count" class="bg-red-500 text-white text-xs px-2 py-1 rounded-full">3</div>
            </div>
            <div id="notifications" class="space-y-3">
                <!-- Notifications will be populated here -->
            </div>
        </div>

        <!-- System Status -->
        <div class="lg:col-span-2 glass-effect rounded-xl p-6">
            <h2 class="text-xl font-semibold gold-accent mb-4">System Status</h2>
            <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
                <div class="text-center">
                    <div class="text-2xl font-bold text-green-400">✓</div>
                    <div class="text-sm text-gray-400">Backend API</div>
                </div>
                <div class="text-center">
                    <div class="text-2xl font-bold text-green-400">✓</div>
                    <div class="text-sm text-gray-400">WebSocket</div>
                </div>
                <div class="text-center">
                    <div class="text-2xl font-bold text-green-400">✓</div>
                    <div class="text-sm text-gray-400">AI Services</div>
                </div>
                <div class="text-center">
                    <div class="text-2xl font-bold text-green-400">✓</div>
                    <div class="text-sm text-gray-400">Real-time Data</div>
                </div>
            </div>
        </div>
    </main>

    <script>
        // WebSocket connection to backend
        let socket;
        let isConnected = false;

        // Initialize WebSocket connection
        function initWebSocket() {
            try {
                socket = io('http://localhost:8000');
                
                socket.on('connect', () => {
                    isConnected = true;
                    updateConnectionStatus(true);
                    console.log('Connected to Chainsight backend');
                });

                socket.on('disconnect', () => {
                    isConnected = false;
                    updateConnectionStatus(false);
                    console.log('Disconnected from backend');
                });

                socket.on('market_data_update', (data) => {
                    updateMarketData(data);
                });

                socket.on('chat_response', (data) => {
                    addChatMessage('AI Assistant', data.message, 'ai');
                });

                socket.on('notification', (data) => {
                    addNotification(data);
                });

            } catch (error) {
                console.log('WebSocket connection failed, using mock data');
                useMockData();
            }
        }

        // Update connection status
        function updateConnectionStatus(connected) {
            const statusEl = document.getElementById('connection-status');
            const indicator = statusEl.querySelector('div');
            const text = statusEl.querySelector('span');
            
            if (connected) {
                indicator.className = 'w-3 h-3 bg-green-500 rounded-full pulse-gold';
                text.textContent = 'Connected';
            } else {
                indicator.className = 'w-3 h-3 bg-red-500 rounded-full';
                text.textContent = 'Disconnected';
            }
        }

        // Update market data
        function updateMarketData(data) {
            const container = document.getElementById('market-data');
            container.innerHTML = '';
            
            const marketData = data || [
                { symbol: 'BTC', price: 45000, change: 2.5, volume: '1.2B' },
                { symbol: 'ETH', price: 3200, change: -1.2, volume: '800M' },
                { symbol: 'USDT', price: 1.0, change: 0.0, volume: '2.1B' }
            ];

            marketData.forEach(item => {
                const changeColor = item.change >= 0 ? 'text-green-400' : 'text-red-400';
                const changeSymbol = item.change >= 0 ? '+' : '';
                
                container.innerHTML += `
                    <div class="flex items-center justify-between p-3 bg-gray-800 rounded-lg">
                        <div class="flex items-center space-x-3">
                            <div class="w-8 h-8 bg-gradient-to-r from-yellow-400 to-yellow-600 rounded-full flex items-center justify-center text-black font-bold text-sm">
                                ${item.symbol.charAt(0)}
                            </div>
                            <div>
                                <div class="font-semibold">${item.symbol}</div>
                                <div class="text-sm text-gray-400">Vol: ${item.volume}</div>
                            </div>
                        </div>
                        <div class="text-right">
                            <div class="font-semibold">$${item.price.toLocaleString()}</div>
                            <div class="${changeColor} text-sm">${changeSymbol}${item.change}%</div>
                        </div>
                    </div>
                `;
            });
        }

        // Add chat message
        function addChatMessage(sender, message, type = 'user') {
            const container = document.getElementById('chat-messages');
            const bgColor = type === 'ai' ? 'bg-gray-800' : 'bg-blue-600';
            
            container.innerHTML += `
                <div class="${bgColor} rounded-lg p-3">
                    <div class="text-sm text-gray-400 mb-1">${sender}</div>
                    <div>${message}</div>
                </div>
            `;
            
            container.scrollTop = container.scrollHeight;
        }

        // Add notification
        function addNotification(data) {
            const container = document.getElementById('notifications');
            const priorityColor = data.priority === 'high' ? 'border-red-500' : 
                                 data.priority === 'medium' ? 'border-yellow-500' : 'border-blue-500';
            
            container.innerHTML += `
                <div class="border-l-4 ${priorityColor} bg-gray-800 p-3 rounded">
                    <div class="font-semibold text-sm">${data.title}</div>
                    <div class="text-xs text-gray-400 mt-1">${data.message}</div>
                </div>
            `;
        }

        // Use mock data when WebSocket fails
        function useMockData() {
            updateConnectionStatus(false);
            updateMarketData();
            
            // Simulate periodic updates
            setInterval(() => {
                const mockData = [
                    { symbol: 'BTC', price: 45000 + Math.random() * 1000, change: (Math.random() - 0.5) * 10, volume: '1.2B' },
                    { symbol: 'ETH', price: 3200 + Math.random() * 200, change: (Math.random() - 0.5) * 8, volume: '800M' },
                    { symbol: 'USDT', price: 1.0, change: 0.0, volume: '2.1B' }
                ];
                updateMarketData(mockData);
            }, 5000);
        }

        // Chat functionality
        document.getElementById('send-btn').addEventListener('click', sendMessage);
        document.getElementById('chat-input').addEventListener('keypress', (e) => {
            if (e.key === 'Enter') sendMessage();
        });

        function sendMessage() {
            const input = document.getElementById('chat-input');
            const message = input.value.trim();
            
            if (message) {
                addChatMessage('You', message, 'user');
                input.value = '';
                
                // Show typing indicator
                const container = document.getElementById('chat-messages');
                container.innerHTML += `
                    <div class="bg-gray-800 rounded-lg p-3" id="typing-indicator">
                        <div class="text-sm text-gray-400 mb-1">AI Assistant</div>
                        <div class="flex space-x-1">
                            <div class="typing-indicator"></div>
                            <div class="typing-indicator"></div>
                            <div class="typing-indicator"></div>
                        </div>
                    </div>
                `;
                container.scrollTop = container.scrollHeight;
                
                // Simulate AI response
                setTimeout(() => {
                    document.getElementById('typing-indicator')?.remove();
                    addChatMessage('AI Assistant', `I understand you said: "${message}". This is a demo response from the Chainsight AI system.`, 'ai');
                }, 2000);
            }
        }

        // Initialize the application
        document.addEventListener('DOMContentLoaded', () => {
            initWebSocket();
            
            // Add some initial notifications
            addNotification({
                title: 'System Online',
                message: 'Chainsight platform is running successfully',
                priority: 'info'
            });
            
            addNotification({
                title: 'Market Alert',
                message: 'BTC price increased by 2.5%',
                priority: 'medium'
            });
            
            addNotification({
                title: 'AI Update',
                message: 'New AI models deployed',
                priority: 'low'
            });
        });
    </script>
</body>
</html>
