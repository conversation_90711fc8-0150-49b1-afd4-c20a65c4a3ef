"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_reown_appkit-ui_dist_esm_src_assets_svg_google_js"],{

/***/ "(app-pages-browser)/./node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/google.js":
/*!*************************************************************************!*\
  !*** ./node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/google.js ***!
  \*************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   googleSvg: () => (/* binding */ googleSvg)\n/* harmony export */ });\n/* harmony import */ var lit__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! lit */ \"(app-pages-browser)/./node_modules/lit/index.js\");\n\nconst googleSvg = (0,lit__WEBPACK_IMPORTED_MODULE_0__.svg) `<svg fill=\"none\" viewBox=\"0 0 40 40\">\n  <path\n    fill=\"#4285F4\"\n    d=\"M32.74 20.3c0-.93-.08-1.81-.24-2.66H20.26v5.03h7a6 6 0 0 1-2.62 3.91v3.28h4.22c2.46-2.27 3.88-5.6 3.88-9.56Z\"\n  />\n  <path\n    fill=\"#34A853\"\n    d=\"M20.26 33a12.4 12.4 0 0 0 8.6-3.14l-4.22-3.28a7.74 7.74 0 0 1-4.38 1.26 7.76 7.76 0 0 1-7.28-5.36H8.65v3.36A12.99 12.99 0 0 0 20.26 33Z\"\n  />\n  <path\n    fill=\"#FBBC05\"\n    d=\"M12.98 22.47a7.79 7.79 0 0 1 0-4.94v-3.36H8.65a12.84 12.84 0 0 0 0 11.66l3.37-2.63.96-.73Z\"\n  />\n  <path\n    fill=\"#EA4335\"\n    d=\"M20.26 12.18a7.1 7.1 0 0 1 4.98 1.93l3.72-3.72A12.47 12.47 0 0 0 20.26 7c-5.08 0-9.47 2.92-11.6 7.17l4.32 3.36a7.76 7.76 0 0 1 7.28-5.35Z\"\n  />\n</svg>`;\n//# sourceMappingURL=google.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9AcmVvd24vYXBwa2l0LXVpL2Rpc3QvZXNtL3NyYy9hc3NldHMvc3ZnL2dvb2dsZS5qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUEwQjtBQUNuQixrQkFBa0Isd0NBQUc7QUFDNUI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBIiwic291cmNlcyI6WyJGOlxcQUkgY2hhaW5cXGNoYWluc2lnaHQtcHJvZHVjdGlvblxcbm9kZV9tb2R1bGVzXFxAcmVvd25cXGFwcGtpdC11aVxcZGlzdFxcZXNtXFxzcmNcXGFzc2V0c1xcc3ZnXFxnb29nbGUuanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgc3ZnIH0gZnJvbSAnbGl0JztcbmV4cG9ydCBjb25zdCBnb29nbGVTdmcgPSBzdmcgYDxzdmcgZmlsbD1cIm5vbmVcIiB2aWV3Qm94PVwiMCAwIDQwIDQwXCI+XG4gIDxwYXRoXG4gICAgZmlsbD1cIiM0Mjg1RjRcIlxuICAgIGQ9XCJNMzIuNzQgMjAuM2MwLS45My0uMDgtMS44MS0uMjQtMi42NkgyMC4yNnY1LjAzaDdhNiA2IDAgMCAxLTIuNjIgMy45MXYzLjI4aDQuMjJjMi40Ni0yLjI3IDMuODgtNS42IDMuODgtOS41NlpcIlxuICAvPlxuICA8cGF0aFxuICAgIGZpbGw9XCIjMzRBODUzXCJcbiAgICBkPVwiTTIwLjI2IDMzYTEyLjQgMTIuNCAwIDAgMCA4LjYtMy4xNGwtNC4yMi0zLjI4YTcuNzQgNy43NCAwIDAgMS00LjM4IDEuMjYgNy43NiA3Ljc2IDAgMCAxLTcuMjgtNS4zNkg4LjY1djMuMzZBMTIuOTkgMTIuOTkgMCAwIDAgMjAuMjYgMzNaXCJcbiAgLz5cbiAgPHBhdGhcbiAgICBmaWxsPVwiI0ZCQkMwNVwiXG4gICAgZD1cIk0xMi45OCAyMi40N2E3Ljc5IDcuNzkgMCAwIDEgMC00Ljk0di0zLjM2SDguNjVhMTIuODQgMTIuODQgMCAwIDAgMCAxMS42NmwzLjM3LTIuNjMuOTYtLjczWlwiXG4gIC8+XG4gIDxwYXRoXG4gICAgZmlsbD1cIiNFQTQzMzVcIlxuICAgIGQ9XCJNMjAuMjYgMTIuMThhNy4xIDcuMSAwIDAgMSA0Ljk4IDEuOTNsMy43Mi0zLjcyQTEyLjQ3IDEyLjQ3IDAgMCAwIDIwLjI2IDdjLTUuMDggMC05LjQ3IDIuOTItMTEuNiA3LjE3bDQuMzIgMy4zNmE3Ljc2IDcuNzYgMCAwIDEgNy4yOC01LjM1WlwiXG4gIC8+XG48L3N2Zz5gO1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9Z29vZ2xlLmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/google.js\n"));

/***/ })

}]);