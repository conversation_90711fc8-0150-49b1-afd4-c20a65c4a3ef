"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_viem__esm_utils_ccip_js"],{

/***/ "(app-pages-browser)/./node_modules/viem/_esm/errors/ccip.js":
/*!***********************************************!*\
  !*** ./node_modules/viem/_esm/errors/ccip.js ***!
  \***********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   OffchainLookupError: () => (/* binding */ OffchainLookupError),\n/* harmony export */   OffchainLookupResponseMalformedError: () => (/* binding */ OffchainLookupResponseMalformedError),\n/* harmony export */   OffchainLookupSenderMismatchError: () => (/* binding */ OffchainLookupSenderMismatchError)\n/* harmony export */ });\n/* harmony import */ var _utils_stringify_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../utils/stringify.js */ \"(app-pages-browser)/./node_modules/viem/_esm/utils/stringify.js\");\n/* harmony import */ var _base_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./base.js */ \"(app-pages-browser)/./node_modules/viem/_esm/errors/base.js\");\n/* harmony import */ var _utils_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./utils.js */ \"(app-pages-browser)/./node_modules/viem/_esm/errors/utils.js\");\n\n\n\nclass OffchainLookupError extends _base_js__WEBPACK_IMPORTED_MODULE_0__.BaseError {\n    constructor({ callbackSelector, cause, data, extraData, sender, urls, }) {\n        super(cause.shortMessage ||\n            'An error occurred while fetching for an offchain result.', {\n            cause,\n            metaMessages: [\n                ...(cause.metaMessages || []),\n                cause.metaMessages?.length ? '' : [],\n                'Offchain Gateway Call:',\n                urls && [\n                    '  Gateway URL(s):',\n                    ...urls.map((url) => `    ${(0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.getUrl)(url)}`),\n                ],\n                `  Sender: ${sender}`,\n                `  Data: ${data}`,\n                `  Callback selector: ${callbackSelector}`,\n                `  Extra data: ${extraData}`,\n            ].flat(),\n            name: 'OffchainLookupError',\n        });\n    }\n}\nclass OffchainLookupResponseMalformedError extends _base_js__WEBPACK_IMPORTED_MODULE_0__.BaseError {\n    constructor({ result, url }) {\n        super('Offchain gateway response is malformed. Response data must be a hex value.', {\n            metaMessages: [\n                `Gateway URL: ${(0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.getUrl)(url)}`,\n                `Response: ${(0,_utils_stringify_js__WEBPACK_IMPORTED_MODULE_2__.stringify)(result)}`,\n            ],\n            name: 'OffchainLookupResponseMalformedError',\n        });\n    }\n}\nclass OffchainLookupSenderMismatchError extends _base_js__WEBPACK_IMPORTED_MODULE_0__.BaseError {\n    constructor({ sender, to }) {\n        super('Reverted sender address does not match target contract address (`to`).', {\n            metaMessages: [\n                `Contract address: ${to}`,\n                `OffchainLookup sender address: ${sender}`,\n            ],\n            name: 'OffchainLookupSenderMismatchError',\n        });\n    }\n}\n//# sourceMappingURL=ccip.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/viem/_esm/errors/ccip.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/viem/_esm/utils/ccip.js":
/*!**********************************************!*\
  !*** ./node_modules/viem/_esm/utils/ccip.js ***!
  \**********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ccipRequest: () => (/* binding */ ccipRequest),\n/* harmony export */   offchainLookup: () => (/* binding */ offchainLookup),\n/* harmony export */   offchainLookupAbiItem: () => (/* binding */ offchainLookupAbiItem),\n/* harmony export */   offchainLookupSignature: () => (/* binding */ offchainLookupSignature)\n/* harmony export */ });\n/* harmony import */ var _actions_public_call_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../actions/public/call.js */ \"(app-pages-browser)/./node_modules/viem/_esm/actions/public/call.js\");\n/* harmony import */ var _errors_ccip_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../errors/ccip.js */ \"(app-pages-browser)/./node_modules/viem/_esm/errors/ccip.js\");\n/* harmony import */ var _errors_request_js__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../errors/request.js */ \"(app-pages-browser)/./node_modules/viem/_esm/errors/request.js\");\n/* harmony import */ var _abi_decodeErrorResult_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./abi/decodeErrorResult.js */ \"(app-pages-browser)/./node_modules/viem/_esm/utils/abi/decodeErrorResult.js\");\n/* harmony import */ var _abi_encodeAbiParameters_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./abi/encodeAbiParameters.js */ \"(app-pages-browser)/./node_modules/viem/_esm/utils/abi/encodeAbiParameters.js\");\n/* harmony import */ var _address_isAddressEqual_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./address/isAddressEqual.js */ \"(app-pages-browser)/./node_modules/viem/_esm/utils/address/isAddressEqual.js\");\n/* harmony import */ var _data_concat_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./data/concat.js */ \"(app-pages-browser)/./node_modules/viem/_esm/utils/data/concat.js\");\n/* harmony import */ var _data_isHex_js__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./data/isHex.js */ \"(app-pages-browser)/./node_modules/viem/_esm/utils/data/isHex.js\");\n/* harmony import */ var _ens_localBatchGatewayRequest_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./ens/localBatchGatewayRequest.js */ \"(app-pages-browser)/./node_modules/viem/_esm/utils/ens/localBatchGatewayRequest.js\");\n/* harmony import */ var _stringify_js__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./stringify.js */ \"(app-pages-browser)/./node_modules/viem/_esm/utils/stringify.js\");\n\n\n\n\n\n\n\n\n\n\nconst offchainLookupSignature = '0x556f1830';\nconst offchainLookupAbiItem = {\n    name: 'OffchainLookup',\n    type: 'error',\n    inputs: [\n        {\n            name: 'sender',\n            type: 'address',\n        },\n        {\n            name: 'urls',\n            type: 'string[]',\n        },\n        {\n            name: 'callData',\n            type: 'bytes',\n        },\n        {\n            name: 'callbackFunction',\n            type: 'bytes4',\n        },\n        {\n            name: 'extraData',\n            type: 'bytes',\n        },\n    ],\n};\nasync function offchainLookup(client, { blockNumber, blockTag, data, to, }) {\n    const { args } = (0,_abi_decodeErrorResult_js__WEBPACK_IMPORTED_MODULE_0__.decodeErrorResult)({\n        data,\n        abi: [offchainLookupAbiItem],\n    });\n    const [sender, urls, callData, callbackSelector, extraData] = args;\n    const { ccipRead } = client;\n    const ccipRequest_ = ccipRead && typeof ccipRead?.request === 'function'\n        ? ccipRead.request\n        : ccipRequest;\n    try {\n        if (!(0,_address_isAddressEqual_js__WEBPACK_IMPORTED_MODULE_1__.isAddressEqual)(to, sender))\n            throw new _errors_ccip_js__WEBPACK_IMPORTED_MODULE_2__.OffchainLookupSenderMismatchError({ sender, to });\n        const result = urls.includes(_ens_localBatchGatewayRequest_js__WEBPACK_IMPORTED_MODULE_3__.localBatchGatewayUrl)\n            ? await (0,_ens_localBatchGatewayRequest_js__WEBPACK_IMPORTED_MODULE_3__.localBatchGatewayRequest)({\n                data: callData,\n                ccipRequest: ccipRequest_,\n            })\n            : await ccipRequest_({ data: callData, sender, urls });\n        const { data: data_ } = await (0,_actions_public_call_js__WEBPACK_IMPORTED_MODULE_4__.call)(client, {\n            blockNumber,\n            blockTag,\n            data: (0,_data_concat_js__WEBPACK_IMPORTED_MODULE_5__.concat)([\n                callbackSelector,\n                (0,_abi_encodeAbiParameters_js__WEBPACK_IMPORTED_MODULE_6__.encodeAbiParameters)([{ type: 'bytes' }, { type: 'bytes' }], [result, extraData]),\n            ]),\n            to,\n        });\n        return data_;\n    }\n    catch (err) {\n        throw new _errors_ccip_js__WEBPACK_IMPORTED_MODULE_2__.OffchainLookupError({\n            callbackSelector,\n            cause: err,\n            data,\n            extraData,\n            sender,\n            urls,\n        });\n    }\n}\nasync function ccipRequest({ data, sender, urls, }) {\n    let error = new Error('An unknown error occurred.');\n    for (let i = 0; i < urls.length; i++) {\n        const url = urls[i];\n        const method = url.includes('{data}') ? 'GET' : 'POST';\n        const body = method === 'POST' ? { data, sender } : undefined;\n        const headers = method === 'POST' ? { 'Content-Type': 'application/json' } : {};\n        try {\n            const response = await fetch(url.replace('{sender}', sender.toLowerCase()).replace('{data}', data), {\n                body: JSON.stringify(body),\n                headers,\n                method,\n            });\n            let result;\n            if (response.headers.get('Content-Type')?.startsWith('application/json')) {\n                result = (await response.json()).data;\n            }\n            else {\n                result = (await response.text());\n            }\n            if (!response.ok) {\n                error = new _errors_request_js__WEBPACK_IMPORTED_MODULE_7__.HttpRequestError({\n                    body,\n                    details: result?.error\n                        ? (0,_stringify_js__WEBPACK_IMPORTED_MODULE_8__.stringify)(result.error)\n                        : response.statusText,\n                    headers: response.headers,\n                    status: response.status,\n                    url,\n                });\n                continue;\n            }\n            if (!(0,_data_isHex_js__WEBPACK_IMPORTED_MODULE_9__.isHex)(result)) {\n                error = new _errors_ccip_js__WEBPACK_IMPORTED_MODULE_2__.OffchainLookupResponseMalformedError({\n                    result,\n                    url,\n                });\n                continue;\n            }\n            return result;\n        }\n        catch (err) {\n            error = new _errors_request_js__WEBPACK_IMPORTED_MODULE_7__.HttpRequestError({\n                body,\n                details: err.message,\n                url,\n            });\n        }\n    }\n    throw error;\n}\n//# sourceMappingURL=ccip.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/viem/_esm/utils/ccip.js\n"));

/***/ })

}]);