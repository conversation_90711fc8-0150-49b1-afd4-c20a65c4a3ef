'use client';

import { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { Navigation } from '@/components/Navigation';
import { ModularDashboard } from '@/components/ModularDashboard';
import { AIModuleSelector } from '@/components/AIModuleSelector';
import { RealTimeNotifications } from '@/components/RealTimeNotifications';
import { SystemStatus } from '@/components/SystemStatus';
import { ClientOnly } from '@/components/ClientOnly';
import { useAIModules } from '@/hooks/useAIModules';
import { useRealTimeData } from '@/hooks/useRealTimeData';

export default function HomePage() {
  const [activeModule, setActiveModule] = useState('crypto');
  const { modules, isLoading: modulesLoading } = useAIModules();
  const { connectionStatus, marketData } = useRealTimeData();

  useEffect(() => {
    // Initialize production platform
    console.log('🚀 Chainsight Production Platform Initialized');
  }, []);

  return (
    <div className="min-h-screen bg-gradient-to-br from-dark-900 via-dark-950 to-dark-900">
      <ClientOnly>
        <Navigation />
      </ClientOnly>

      <main className="pt-20">
        {/* Hero Section */}
        <section className="relative overflow-hidden min-h-screen flex items-center justify-center">
          {/* Background Effects */}
          <div className="absolute inset-0 bg-gradient-to-br from-dark-900 via-dark-950 to-dark-900" />
          <div className="absolute top-1/4 left-1/4 w-96 h-96 bg-primary-500/10 rounded-full blur-3xl" />
          <div className="absolute bottom-1/4 right-1/4 w-96 h-96 bg-primary-400/10 rounded-full blur-3xl" />

          <div className="relative max-w-7xl mx-auto px-4 py-20 text-center">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              className="space-y-12"
            >
              {/* Main heading */}
              <motion.h1
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.4, duration: 0.8 }}
                className="text-5xl md:text-7xl font-bold text-white leading-tight"
              >
                <span className="block">Chainsight by</span>
                <span className="block text-glow text-primary-500">Connectouch</span>
              </motion.h1>

              {/* Subtitle */}
              <motion.p
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.6, duration: 0.8 }}
                className="text-xl md:text-2xl text-dark-300 max-w-3xl mx-auto leading-relaxed"
              >
                Modular Fullstack AI Agent with real-time blockchain analytics,
                predictive finance models, and luxury user experience
              </motion.p>

              {/* Module Selector */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.8, duration: 0.8 }}
                className="max-w-4xl mx-auto"
              >
                <ClientOnly>
                  <AIModuleSelector
                    activeModule={activeModule}
                    onModuleChange={setActiveModule}
                    modules={modules}
                    loading={modulesLoading}
                  />
                </ClientOnly>
              </motion.div>
            </motion.div>
          </div>
        </section>

        {/* Dashboard Section */}
        <section className="py-20">
          <div className="max-w-7xl mx-auto px-4">
            <ClientOnly>
              <ModularDashboard
                activeModule={activeModule}
                marketData={marketData}
                modules={modules}
              />
            </ClientOnly>
          </div>
        </section>

        {/* System Status */}
        <div className="fixed bottom-4 right-4">
          <ClientOnly>
            <SystemStatus status={connectionStatus} />
          </ClientOnly>
        </div>

        {/* Real-time Notifications */}
        <ClientOnly>
          <RealTimeNotifications />
        </ClientOnly>
      </main>
    </div>
  );
}
