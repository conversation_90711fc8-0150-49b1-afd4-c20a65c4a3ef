'use client';

import { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { AlertTriangle, X, ChevronDown, ChevronUp } from 'lucide-react';

interface ConsoleError {
  id: string;
  message: string;
  stack?: string;
  timestamp: Date;
  type: 'error' | 'warning' | 'info';
}

export function ConsoleErrorDisplay() {
  const [errors, setErrors] = useState<ConsoleError[]>([]);
  const [isVisible, setIsVisible] = useState(false);
  const [isExpanded, setIsExpanded] = useState(false);

  useEffect(() => {
    // Only show in development
    if (process.env.NODE_ENV !== 'development') return;

    const errorHandler = (event: ErrorEvent) => {
      const newError: ConsoleError = {
        id: Date.now().toString(),
        message: event.message,
        stack: event.error?.stack,
        timestamp: new Date(),
        type: 'error',
      };

      setErrors(prev => [...prev.slice(-9), newError]); // Keep last 10 errors
      setIsVisible(true);
    };

    const rejectionHandler = (event: PromiseRejectionEvent) => {
      const newError: ConsoleError = {
        id: Date.now().toString(),
        message: `Unhandled Promise Rejection: ${event.reason}`,
        timestamp: new Date(),
        type: 'error',
      };

      setErrors(prev => [...prev.slice(-9), newError]);
      setIsVisible(true);
    };

    // Override console methods to catch warnings and errors
    const originalError = console.error;
    const originalWarn = console.warn;

    console.error = (...args) => {
      const message = args.map(arg => 
        typeof arg === 'object' ? JSON.stringify(arg) : String(arg)
      ).join(' ');

      if (!message.includes('Chainsight Debug')) { // Avoid recursive logging
        const newError: ConsoleError = {
          id: Date.now().toString(),
          message,
          timestamp: new Date(),
          type: 'error',
        };

        setErrors(prev => [...prev.slice(-9), newError]);
        setIsVisible(true);
      }

      originalError.apply(console, args);
    };

    console.warn = (...args) => {
      const message = args.map(arg => 
        typeof arg === 'object' ? JSON.stringify(arg) : String(arg)
      ).join(' ');

      const newError: ConsoleError = {
        id: Date.now().toString(),
        message,
        timestamp: new Date(),
        type: 'warning',
      };

      setErrors(prev => [...prev.slice(-9), newError]);
      setIsVisible(true);

      originalWarn.apply(console, args);
    };

    window.addEventListener('error', errorHandler);
    window.addEventListener('unhandledrejection', rejectionHandler);

    return () => {
      window.removeEventListener('error', errorHandler);
      window.removeEventListener('unhandledrejection', rejectionHandler);
      console.error = originalError;
      console.warn = originalWarn;
    };
  }, []);

  const clearErrors = () => {
    setErrors([]);
    setIsVisible(false);
  };

  const getErrorColor = (type: string) => {
    switch (type) {
      case 'error': return 'text-red-400 bg-red-500/10 border-red-500/20';
      case 'warning': return 'text-yellow-400 bg-yellow-500/10 border-yellow-500/20';
      default: return 'text-blue-400 bg-blue-500/10 border-blue-500/20';
    }
  };

  if (!isVisible || errors.length === 0) return null;

  return (
    <AnimatePresence>
      <motion.div
        initial={{ opacity: 0, y: 50 }}
        animate={{ opacity: 1, y: 0 }}
        exit={{ opacity: 0, y: 50 }}
        className="fixed bottom-4 right-4 z-50 max-w-md"
      >
        <div className="bg-cosmic-dark/95 backdrop-blur-xl border border-red-500/30 rounded-lg shadow-2xl">
          {/* Header */}
          <div className="flex items-center justify-between p-3 border-b border-red-500/20">
            <div className="flex items-center space-x-2">
              <AlertTriangle className="w-5 h-5 text-red-400" />
              <span className="text-red-400 font-semibold">
                Console Errors ({errors.length})
              </span>
            </div>
            <div className="flex items-center space-x-1">
              <button
                onClick={() => setIsExpanded(!isExpanded)}
                className="p-1 hover:bg-white/10 rounded transition-colors"
              >
                {isExpanded ? (
                  <ChevronUp className="w-4 h-4 text-gray-400" />
                ) : (
                  <ChevronDown className="w-4 h-4 text-gray-400" />
                )}
              </button>
              <button
                onClick={clearErrors}
                className="p-1 hover:bg-white/10 rounded transition-colors"
              >
                <X className="w-4 h-4 text-gray-400" />
              </button>
            </div>
          </div>

          {/* Error List */}
          <AnimatePresence>
            {isExpanded && (
              <motion.div
                initial={{ height: 0 }}
                animate={{ height: 'auto' }}
                exit={{ height: 0 }}
                className="overflow-hidden"
              >
                <div className="max-h-64 overflow-y-auto p-3 space-y-2">
                  {errors.map((error) => (
                    <div
                      key={error.id}
                      className={`p-2 rounded border ${getErrorColor(error.type)}`}
                    >
                      <div className="flex items-start justify-between">
                        <div className="flex-1 min-w-0">
                          <p className="text-sm font-medium truncate">
                            {error.type.toUpperCase()}
                          </p>
                          <p className="text-xs text-gray-300 mt-1 break-words">
                            {error.message}
                          </p>
                          {error.stack && (
                            <details className="mt-1">
                              <summary className="text-xs text-gray-400 cursor-pointer">
                                Stack trace
                              </summary>
                              <pre className="text-xs text-gray-400 mt-1 overflow-auto max-h-20">
                                {error.stack}
                              </pre>
                            </details>
                          )}
                        </div>
                        <span className="text-xs text-gray-500 ml-2 flex-shrink-0">
                          {error.timestamp.toLocaleTimeString()}
                        </span>
                      </div>
                    </div>
                  ))}
                </div>
              </motion.div>
            )}
          </AnimatePresence>

          {/* Quick Summary */}
          {!isExpanded && errors.length > 0 && (
            <div className="p-3">
              <p className="text-sm text-gray-300">
                Latest: {errors[errors.length - 1]?.message.slice(0, 50)}
                {errors[errors.length - 1]?.message.length > 50 ? '...' : ''}
              </p>
            </div>
          )}
        </div>
      </motion.div>
    </AnimatePresence>
  );
}
