/* ==================== THEME VARIABLES ====================
   Global CSS custom properties for consistent theming
   ========================================================= */

:root {
    /* Primary Color Gradients */
    --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    --secondary-gradient: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    --accent-gradient: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    --success-gradient: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
    --warning-gradient: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
    --dark-gradient: linear-gradient(135deg, #0c0c0c 0%, #1a1a2e 50%, #16213e 100%);

    /* Glass Morphism Effects */
    --glass-bg: rgba(255, 255, 255, 0.1);
    --glass-border: rgba(255, 255, 255, 0.2);
    --glass-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.37);
    --glass-backdrop: blur(8px);

    /* Neon Colors */
    --neon-blue: #00d4ff;
    --neon-purple: #b537f2;
    --neon-pink: #ff006e;
    --neon-green: #39ff14;
    --neon-orange: #ff8c00;
    --neon-gold: #ffd700;
    --neon-cyan: #00ffff;

    /* Luxury Black & Gold Theme */
    --luxury-black: #0a0a0a;
    --luxury-gold: #d4af37;
    --luxury-gold-light: #f7e98e;
    --luxury-gold-dark: #b8860b;
    --luxury-silver: #c0c0c0;
    --luxury-bronze: #cd7f32;

    /* Semantic Colors */
    --color-success: #10b981;
    --color-warning: #f59e0b;
    --color-error: #ef4444;
    --color-info: #3b82f6;
    --color-neutral: #6b7280;

    /* Background Colors */
    --bg-primary: var(--dark-gradient);
    --bg-secondary: rgba(26, 26, 46, 0.8);
    --bg-tertiary: rgba(22, 33, 62, 0.6);
    --bg-card: var(--glass-bg);
    --bg-overlay: rgba(0, 0, 0, 0.7);

    /* Text Colors */
    --text-primary: #ffffff;
    --text-secondary: #e5e7eb;
    --text-tertiary: #9ca3af;
    --text-muted: #6b7280;
    --text-accent: var(--neon-blue);

    /* Border Colors */
    --border-primary: var(--glass-border);
    --border-secondary: rgba(255, 255, 255, 0.1);
    --border-accent: var(--neon-blue);
    --border-success: var(--color-success);
    --border-warning: var(--color-warning);
    --border-error: var(--color-error);

    /* Spacing Scale */
    --space-xs: 0.25rem;    /* 4px */
    --space-sm: 0.5rem;     /* 8px */
    --space-md: 1rem;       /* 16px */
    --space-lg: 1.5rem;     /* 24px */
    --space-xl: 2rem;       /* 32px */
    --space-2xl: 3rem;      /* 48px */
    --space-3xl: 4rem;      /* 64px */

    /* Font Families */
    --font-primary: 'Rajdhani', sans-serif;
    --font-secondary: 'Orbitron', monospace;
    --font-mono: 'Courier New', monospace;

    /* Font Sizes */
    --text-xs: 0.75rem;     /* 12px */
    --text-sm: 0.875rem;    /* 14px */
    --text-base: 1rem;      /* 16px */
    --text-lg: 1.125rem;    /* 18px */
    --text-xl: 1.25rem;     /* 20px */
    --text-2xl: 1.5rem;     /* 24px */
    --text-3xl: 1.875rem;   /* 30px */
    --text-4xl: 2.25rem;    /* 36px */
    --text-5xl: 3rem;       /* 48px */

    /* Font Weights */
    --font-light: 300;
    --font-normal: 400;
    --font-medium: 500;
    --font-semibold: 600;
    --font-bold: 700;
    --font-extrabold: 800;
    --font-black: 900;

    /* Border Radius */
    --radius-sm: 0.25rem;   /* 4px */
    --radius-md: 0.5rem;    /* 8px */
    --radius-lg: 0.75rem;   /* 12px */
    --radius-xl: 1rem;      /* 16px */
    --radius-2xl: 1.5rem;   /* 24px */
    --radius-full: 9999px;

    /* Shadows */
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
    --shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
    --shadow-neon: 0 0 20px var(--neon-blue);
    --shadow-gold: 0 0 20px var(--luxury-gold);

    /* Transitions */
    --transition-fast: 150ms ease-in-out;
    --transition-normal: 300ms ease-in-out;
    --transition-slow: 500ms ease-in-out;

    /* Z-Index Scale */
    --z-dropdown: 1000;
    --z-sticky: 1020;
    --z-fixed: 1030;
    --z-modal-backdrop: 1040;
    --z-modal: 1050;
    --z-popover: 1060;
    --z-tooltip: 1070;
    --z-toast: 1080;

    /* Breakpoints (for reference in JS) */
    --breakpoint-sm: 640px;
    --breakpoint-md: 768px;
    --breakpoint-lg: 1024px;
    --breakpoint-xl: 1280px;
    --breakpoint-2xl: 1536px;
}

/* Dark Theme Overrides */
[data-theme="dark"] {
    --bg-primary: linear-gradient(135deg, #000000 0%, #1a1a2e 50%, #16213e 100%);
    --text-primary: #ffffff;
    --text-secondary: #f3f4f6;
}

/* Light Theme Overrides */
[data-theme="light"] {
    --bg-primary: linear-gradient(135deg, #ffffff 0%, #f8fafc 50%, #e2e8f0 100%);
    --text-primary: #1f2937;
    --text-secondary: #374151;
    --glass-bg: rgba(0, 0, 0, 0.1);
    --glass-border: rgba(0, 0, 0, 0.2);
}

/* High Contrast Theme */
[data-theme="high-contrast"] {
    --bg-primary: #000000;
    --text-primary: #ffffff;
    --border-primary: #ffffff;
    --neon-blue: #00ffff;
    --neon-green: #00ff00;
}

/* Reduced Motion */
@media (prefers-reduced-motion: reduce) {
    :root {
        --transition-fast: none;
        --transition-normal: none;
        --transition-slow: none;
    }
}
