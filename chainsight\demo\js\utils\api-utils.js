/* ==================== API UTILITIES ====================
   HTTP request and API interaction utilities
   ================================================== */

class APIUtils {
    constructor(baseURL = '', defaultHeaders = {}) {
        this.baseURL = baseURL;
        this.defaultHeaders = {
            'Content-Type': 'application/json',
            ...defaultHeaders
        };
        this.interceptors = {
            request: [],
            response: []
        };
    }

    /**
     * Add request interceptor
     * @param {Function} interceptor - Request interceptor function
     */
    addRequestInterceptor(interceptor) {
        this.interceptors.request.push(interceptor);
    }

    /**
     * Add response interceptor
     * @param {Function} interceptor - Response interceptor function
     */
    addResponseInterceptor(interceptor) {
        this.interceptors.response.push(interceptor);
    }

    /**
     * Apply request interceptors
     * @param {Object} config - Request configuration
     * @returns {Object} - Modified configuration
     */
    async applyRequestInterceptors(config) {
        let modifiedConfig = { ...config };
        
        for (const interceptor of this.interceptors.request) {
            modifiedConfig = await interceptor(modifiedConfig);
        }
        
        return modifiedConfig;
    }

    /**
     * Apply response interceptors
     * @param {Response} response - Fetch response
     * @returns {Response} - Modified response
     */
    async applyResponseInterceptors(response) {
        let modifiedResponse = response;
        
        for (const interceptor of this.interceptors.response) {
            modifiedResponse = await interceptor(modifiedResponse);
        }
        
        return modifiedResponse;
    }

    /**
     * Build full URL
     * @param {string} endpoint - API endpoint
     * @returns {string} - Full URL
     */
    buildURL(endpoint) {
        if (endpoint.startsWith('http')) {
            return endpoint;
        }
        
        const base = this.baseURL.endsWith('/') ? this.baseURL.slice(0, -1) : this.baseURL;
        const path = endpoint.startsWith('/') ? endpoint : `/${endpoint}`;
        
        return `${base}${path}`;
    }

    /**
     * Build query string from object
     * @param {Object} params - Query parameters
     * @returns {string} - Query string
     */
    buildQueryString(params) {
        if (!params || typeof params !== 'object') return '';
        
        const searchParams = new URLSearchParams();
        
        Object.entries(params).forEach(([key, value]) => {
            if (value !== null && value !== undefined) {
                if (Array.isArray(value)) {
                    value.forEach(v => searchParams.append(key, v));
                } else {
                    searchParams.append(key, value);
                }
            }
        });
        
        const queryString = searchParams.toString();
        return queryString ? `?${queryString}` : '';
    }

    /**
     * Make HTTP request
     * @param {string} endpoint - API endpoint
     * @param {Object} options - Request options
     * @returns {Promise} - Response promise
     */
    async request(endpoint, options = {}) {
        const config = await this.applyRequestInterceptors({
            method: 'GET',
            headers: { ...this.defaultHeaders },
            ...options
        });

        const url = this.buildURL(endpoint) + this.buildQueryString(config.params);
        
        try {
            const response = await fetch(url, {
                method: config.method,
                headers: config.headers,
                body: config.body ? JSON.stringify(config.body) : undefined,
                ...config
            });

            const processedResponse = await this.applyResponseInterceptors(response);
            
            if (!processedResponse.ok) {
                throw new APIError(
                    `HTTP ${processedResponse.status}: ${processedResponse.statusText}`,
                    processedResponse.status,
                    processedResponse
                );
            }

            return processedResponse;
        } catch (error) {
            if (error instanceof APIError) {
                throw error;
            }
            
            throw new APIError(
                `Network error: ${error.message}`,
                0,
                null,
                error
            );
        }
    }

    /**
     * GET request
     * @param {string} endpoint - API endpoint
     * @param {Object} params - Query parameters
     * @param {Object} options - Request options
     * @returns {Promise} - Response promise
     */
    async get(endpoint, params = {}, options = {}) {
        return this.request(endpoint, {
            method: 'GET',
            params,
            ...options
        });
    }

    /**
     * POST request
     * @param {string} endpoint - API endpoint
     * @param {Object} data - Request body data
     * @param {Object} options - Request options
     * @returns {Promise} - Response promise
     */
    async post(endpoint, data = {}, options = {}) {
        return this.request(endpoint, {
            method: 'POST',
            body: data,
            ...options
        });
    }

    /**
     * PUT request
     * @param {string} endpoint - API endpoint
     * @param {Object} data - Request body data
     * @param {Object} options - Request options
     * @returns {Promise} - Response promise
     */
    async put(endpoint, data = {}, options = {}) {
        return this.request(endpoint, {
            method: 'PUT',
            body: data,
            ...options
        });
    }

    /**
     * PATCH request
     * @param {string} endpoint - API endpoint
     * @param {Object} data - Request body data
     * @param {Object} options - Request options
     * @returns {Promise} - Response promise
     */
    async patch(endpoint, data = {}, options = {}) {
        return this.request(endpoint, {
            method: 'PATCH',
            body: data,
            ...options
        });
    }

    /**
     * DELETE request
     * @param {string} endpoint - API endpoint
     * @param {Object} options - Request options
     * @returns {Promise} - Response promise
     */
    async delete(endpoint, options = {}) {
        return this.request(endpoint, {
            method: 'DELETE',
            ...options
        });
    }

    /**
     * Upload file
     * @param {string} endpoint - Upload endpoint
     * @param {File|FormData} file - File or FormData to upload
     * @param {Object} options - Request options
     * @returns {Promise} - Response promise
     */
    async upload(endpoint, file, options = {}) {
        const formData = file instanceof FormData ? file : new FormData();
        
        if (file instanceof File) {
            formData.append('file', file);
        }

        const headers = { ...this.defaultHeaders };
        delete headers['Content-Type']; // Let browser set multipart boundary

        return this.request(endpoint, {
            method: 'POST',
            headers,
            body: formData,
            ...options
        });
    }

    /**
     * Download file
     * @param {string} endpoint - Download endpoint
     * @param {string} filename - Filename for download
     * @param {Object} options - Request options
     */
    async download(endpoint, filename, options = {}) {
        const response = await this.request(endpoint, options);
        const blob = await response.blob();
        
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = filename;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        window.URL.revokeObjectURL(url);
    }

    /**
     * Make multiple parallel requests
     * @param {Array} requests - Array of request configurations
     * @returns {Promise} - Promise resolving to array of responses
     */
    async parallel(requests) {
        const promises = requests.map(req => {
            if (typeof req === 'string') {
                return this.get(req);
            }
            
            const { endpoint, method = 'GET', ...options } = req;
            return this.request(endpoint, { method, ...options });
        });

        return Promise.all(promises);
    }

    /**
     * Retry request with exponential backoff
     * @param {Function} requestFn - Request function
     * @param {Object} options - Retry options
     * @returns {Promise} - Response promise
     */
    async retry(requestFn, options = {}) {
        const {
            maxRetries = 3,
            baseDelay = 1000,
            maxDelay = 10000,
            backoffFactor = 2,
            retryCondition = (error) => error.status >= 500
        } = options;

        let lastError;
        
        for (let attempt = 0; attempt <= maxRetries; attempt++) {
            try {
                return await requestFn();
            } catch (error) {
                lastError = error;
                
                if (attempt === maxRetries || !retryCondition(error)) {
                    throw error;
                }
                
                const delay = Math.min(
                    baseDelay * Math.pow(backoffFactor, attempt),
                    maxDelay
                );
                
                await new Promise(resolve => setTimeout(resolve, delay));
            }
        }
        
        throw lastError;
    }

    /**
     * Create request with timeout
     * @param {Function} requestFn - Request function
     * @param {number} timeout - Timeout in milliseconds
     * @returns {Promise} - Response promise
     */
    async withTimeout(requestFn, timeout = 30000) {
        return Promise.race([
            requestFn(),
            new Promise((_, reject) => {
                setTimeout(() => {
                    reject(new APIError('Request timeout', 408));
                }, timeout);
            })
        ]);
    }
}

/**
 * Custom API Error class
 */
class APIError extends Error {
    constructor(message, status = 0, response = null, originalError = null) {
        super(message);
        this.name = 'APIError';
        this.status = status;
        this.response = response;
        this.originalError = originalError;
    }

    /**
     * Check if error is network related
     * @returns {boolean}
     */
    isNetworkError() {
        return this.status === 0;
    }

    /**
     * Check if error is client error (4xx)
     * @returns {boolean}
     */
    isClientError() {
        return this.status >= 400 && this.status < 500;
    }

    /**
     * Check if error is server error (5xx)
     * @returns {boolean}
     */
    isServerError() {
        return this.status >= 500;
    }
}

// Create default instance
const apiUtils = new APIUtils();

// Export for module systems
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { APIUtils, APIError, apiUtils };
}

// Global assignment for browser
if (typeof window !== 'undefined') {
    window.APIUtils = APIUtils;
    window.APIError = APIError;
    window.apiUtils = apiUtils;
}
