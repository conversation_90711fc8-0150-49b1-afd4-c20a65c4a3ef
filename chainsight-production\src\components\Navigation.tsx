'use client';

import { useState } from 'react';
import { motion } from 'framer-motion';
import Link from 'next/link';
import {
  Menu,
  X,
  Zap,
  TrendingUp,
  Bo<PERSON>,
  Palette,
  Users,
  Shield,
  Camera,
  Coins,
  Settings,
  User,
  Bug
} from 'lucide-react';
import { DebugPanel } from './DebugPanel';

const navItems = [
  { name: 'Dashboard', href: '/', icon: TrendingUp },
  { name: 'Crypto Tools', href: '/crypto', icon: Coins },
  { name: 'AI Finance', href: '/finance', icon: TrendingUp },
  { name: 'Design Studio', href: '/design', icon: Palette },
  { name: 'HR & Legal', href: '/hr-legal', icon: Shield },
  { name: 'Face Scanner', href: '/face-scanner', icon: Camera },
  { name: 'Support', href: '/support', icon: Bot },
];

export function Navigation() {
  const [isOpen, setIsOpen] = useState(false);
  const [isDebugOpen, setIsDebugOpen] = useState(false);

  return (
    <nav className="fixed top-0 w-full z-50 glass border-b border-primary-500/20">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between items-center h-16">
          {/* Logo */}
          <Link href="/" className="flex items-center space-x-2">
            <motion.div
              whileHover={{ rotate: 360 }}
              transition={{ duration: 0.5 }}
              className="w-8 h-8 bg-gold-gradient rounded-lg flex items-center justify-center"
            >
              <Zap className="w-5 h-5 text-dark-900" />
            </motion.div>
            <span className="text-xl font-bold text-white">
              Chain<span className="text-primary-500">sight</span>
            </span>
          </Link>

          {/* Desktop Navigation */}
          <div className="hidden md:flex items-center space-x-8">
            {navItems.map((item) => (
              <Link
                key={item.name}
                href={item.href}
                className="flex items-center space-x-1 text-dark-300 hover:text-primary-500 transition-colors duration-200"
              >
                <item.icon className="w-4 h-4" />
                <span>{item.name}</span>
              </Link>
            ))}
          </div>

          {/* User Menu */}
          <div className="hidden md:flex items-center space-x-3">
            <button className="p-2 bg-white/10 rounded-lg hover:bg-white/20 transition-colors">
              <Settings className="w-4 h-4 text-gray-300" />
            </button>
            <button className="p-2 bg-white/10 rounded-lg hover:bg-white/20 transition-colors">
              <User className="w-4 h-4 text-gray-300" />
            </button>
            {/* Debug Button (Development Only) */}
            {process.env.NODE_ENV === 'development' && (
              <button
                onClick={() => setIsDebugOpen(true)}
                className="p-2 bg-blue-500/20 rounded-lg hover:bg-blue-500/30 transition-colors"
                title="Debug Panel"
              >
                <Bug className="w-4 h-4 text-blue-400" />
              </button>
            )}
            <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse" />
          </div>

          {/* Mobile menu button */}
          <div className="md:hidden">
            <button
              onClick={() => setIsOpen(!isOpen)}
              className="text-dark-300 hover:text-primary-500 transition-colors duration-200"
            >
              {isOpen ? <X className="w-6 h-6" /> : <Menu className="w-6 h-6" />}
            </button>
          </div>
        </div>
      </div>

      {/* Mobile Navigation */}
      {isOpen && (
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          exit={{ opacity: 0, y: -20 }}
          className="md:hidden glass border-t border-primary-500/20"
        >
          <div className="px-2 pt-2 pb-3 space-y-1">
            {navItems.map((item) => (
              <Link
                key={item.name}
                href={item.href}
                className="flex items-center space-x-2 px-3 py-2 text-dark-300 hover:text-primary-500 hover:bg-dark-800/50 rounded-md transition-all duration-200"
                onClick={() => setIsOpen(false)}
              >
                <item.icon className="w-4 h-4" />
                <span>{item.name}</span>
              </Link>
            ))}
            <div className="pt-4 border-t border-primary-500/20">
              <button
                onClick={() => {
                  open();
                  setIsOpen(false);
                }}
                className="w-full px-3 py-2 bg-primary-500 text-dark-900 rounded-md font-medium hover:bg-primary-400 transition-colors duration-200"
              >
                {isConnected 
                  ? `${address?.slice(0, 6)}...${address?.slice(-4)}`
                  : 'Connect Wallet'
                }
              </button>
            </div>
          </div>
        </motion.div>
      )}

      {/* Debug Panel */}
      <DebugPanel
        isOpen={isDebugOpen}
        onClose={() => setIsDebugOpen(false)}
      />
    </nav>
  );
}
