from sqlalchemy import create_engine, MetaData
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker
from sqlalchemy.pool import StaticPool
import os
from dotenv import load_dotenv
import redis
import asyncio

load_dotenv()

# PostgreSQL Database
DATABASE_URL = os.getenv("DATABASE_URL", "postgresql://chainsight:chainsight@localhost:5432/chainsight")

engine = create_engine(
    DATABASE_URL,
    poolclass=StaticPool,
    pool_pre_ping=True,
    echo=False
)

SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
Base = declarative_base()

# Redis for caching and real-time data
REDIS_URL = os.getenv("REDIS_URL", "redis://localhost:6379")
redis_client = redis.from_url(REDIS_URL, decode_responses=True)

# Database dependency
def get_db():
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()

# Redis dependency
def get_redis():
    return redis_client

async def init_db():
    """Initialize database tables"""
    try:
        # Create all tables
        Base.metadata.create_all(bind=engine)
        
        # Test Redis connection
        redis_client.ping()
        
        print("✅ Database and Redis connections established")
    except Exception as e:
        print(f"❌ Database initialization failed: {e}")
        raise e

# Database models
from sqlalchemy import Column, Integer, String, DateTime, Text, Boolean, Float, JSON
from datetime import datetime

class User(Base):
    __tablename__ = "users"
    
    id = Column(Integer, primary_key=True, index=True)
    email = Column(String, unique=True, index=True)
    username = Column(String, unique=True, index=True)
    hashed_password = Column(String)
    wallet_address = Column(String, nullable=True)
    is_active = Column(Boolean, default=True)
    created_at = Column(DateTime, default=datetime.utcnow)

class ChatMessage(Base):
    __tablename__ = "chat_messages"
    
    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, index=True)
    message = Column(Text)
    response = Column(Text)
    timestamp = Column(DateTime, default=datetime.utcnow)
    session_id = Column(String, index=True)

class TokenContract(Base):
    __tablename__ = "token_contracts"
    
    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, index=True)
    contract_address = Column(String, unique=True)
    token_name = Column(String)
    token_symbol = Column(String)
    total_supply = Column(String)
    network = Column(String)
    created_at = Column(DateTime, default=datetime.utcnow)

class MarketPrediction(Base):
    __tablename__ = "market_predictions"
    
    id = Column(Integer, primary_key=True, index=True)
    symbol = Column(String, index=True)
    prediction_type = Column(String)  # 'price', 'trend', 'volatility'
    predicted_value = Column(Float)
    confidence_score = Column(Float)
    actual_value = Column(Float, nullable=True)
    prediction_date = Column(DateTime, default=datetime.utcnow)
    target_date = Column(DateTime)

class DesignAsset(Base):
    __tablename__ = "design_assets"
    
    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, index=True)
    asset_name = Column(String)
    asset_type = Column(String)  # 'lottie', 'svg', 'animation'
    asset_data = Column(JSON)
    file_url = Column(String, nullable=True)
    created_at = Column(DateTime, default=datetime.utcnow)

class FaceAnalysis(Base):
    __tablename__ = "face_analyses"
    
    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, index=True, nullable=True)
    image_hash = Column(String, index=True)
    detected_age = Column(Integer, nullable=True)
    detected_emotion = Column(String, nullable=True)
    confidence_scores = Column(JSON)
    analysis_timestamp = Column(DateTime, default=datetime.utcnow)
