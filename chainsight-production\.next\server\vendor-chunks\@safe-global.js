"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@safe-global";
exports.ids = ["vendor-chunks/@safe-global"];
exports.modules = {

/***/ "(ssr)/./node_modules/@safe-global/safe-apps-provider/dist/index.js":
/*!********************************************************************!*\
  !*** ./node_modules/@safe-global/safe-apps-provider/dist/index.js ***!
  \********************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.SafeAppProvider = void 0;\nvar provider_1 = __webpack_require__(/*! ./provider */ \"(ssr)/./node_modules/@safe-global/safe-apps-provider/dist/provider.js\");\nObject.defineProperty(exports, \"SafeAppProvider\", ({ enumerable: true, get: function () { return provider_1.SafeAppProvider; } }));\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHNhZmUtZ2xvYmFsL3NhZmUtYXBwcy1wcm92aWRlci9kaXN0L2luZGV4LmpzIiwibWFwcGluZ3MiOiJBQUFhO0FBQ2IsOENBQTZDLEVBQUUsYUFBYSxFQUFDO0FBQzdELHVCQUF1QjtBQUN2QixpQkFBaUIsbUJBQU8sQ0FBQyx5RkFBWTtBQUNyQyxtREFBa0QsRUFBRSxxQ0FBcUMsc0NBQXNDLEVBQUM7QUFDaEkiLCJzb3VyY2VzIjpbIkY6XFxBSSBjaGFpblxcY2hhaW5zaWdodC1wcm9kdWN0aW9uXFxub2RlX21vZHVsZXNcXEBzYWZlLWdsb2JhbFxcc2FmZS1hcHBzLXByb3ZpZGVyXFxkaXN0XFxpbmRleC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBzdHJpY3RcIjtcbk9iamVjdC5kZWZpbmVQcm9wZXJ0eShleHBvcnRzLCBcIl9fZXNNb2R1bGVcIiwgeyB2YWx1ZTogdHJ1ZSB9KTtcbmV4cG9ydHMuU2FmZUFwcFByb3ZpZGVyID0gdm9pZCAwO1xudmFyIHByb3ZpZGVyXzEgPSByZXF1aXJlKFwiLi9wcm92aWRlclwiKTtcbk9iamVjdC5kZWZpbmVQcm9wZXJ0eShleHBvcnRzLCBcIlNhZmVBcHBQcm92aWRlclwiLCB7IGVudW1lcmFibGU6IHRydWUsIGdldDogZnVuY3Rpb24gKCkgeyByZXR1cm4gcHJvdmlkZXJfMS5TYWZlQXBwUHJvdmlkZXI7IH0gfSk7XG4vLyMgc291cmNlTWFwcGluZ1VSTD1pbmRleC5qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@safe-global/safe-apps-provider/dist/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@safe-global/safe-apps-provider/dist/provider.js":
/*!***********************************************************************!*\
  !*** ./node_modules/@safe-global/safe-apps-provider/dist/provider.js ***!
  \***********************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.SafeAppProvider = void 0;\nconst safe_apps_sdk_1 = __webpack_require__(/*! @safe-global/safe-apps-sdk */ \"(ssr)/./node_modules/@safe-global/safe-apps-sdk/dist/cjs/index.js\");\nconst events_1 = __webpack_require__(/*! events */ \"events\");\nconst utils_1 = __webpack_require__(/*! ./utils */ \"(ssr)/./node_modules/@safe-global/safe-apps-provider/dist/utils.js\");\n// The API is based on Ethereum JavaScript API Provider Standard. Link: https://eips.ethereum.org/EIPS/eip-1193\nclass SafeAppProvider extends events_1.EventEmitter {\n    constructor(safe, sdk) {\n        super();\n        this.submittedTxs = new Map();\n        this.safe = safe;\n        this.sdk = sdk;\n    }\n    async connect() {\n        this.emit('connect', { chainId: this.chainId });\n        return;\n    }\n    async disconnect() {\n        return;\n    }\n    get chainId() {\n        return this.safe.chainId;\n    }\n    // eslint-disable-next-line @typescript-eslint/no-explicit-any\n    async request(request) {\n        const { method, params = [] } = request;\n        switch (method) {\n            case 'eth_accounts':\n                return [this.safe.safeAddress];\n            case 'net_version':\n            case 'eth_chainId':\n                return (0, utils_1.numberToHex)(this.chainId);\n            case 'personal_sign': {\n                const [message, address] = params;\n                if (this.safe.safeAddress.toLowerCase() !== address.toLowerCase()) {\n                    throw new Error('The address or message hash is invalid');\n                }\n                const response = await this.sdk.txs.signMessage(message);\n                const signature = 'signature' in response ? response.signature : undefined;\n                return signature || '0x';\n            }\n            case 'eth_sign': {\n                const [address, messageHash] = params;\n                if (this.safe.safeAddress.toLowerCase() !== address.toLowerCase() || !messageHash.startsWith('0x')) {\n                    throw new Error('The address or message hash is invalid');\n                }\n                const response = await this.sdk.txs.signMessage(messageHash);\n                const signature = 'signature' in response ? response.signature : undefined;\n                return signature || '0x';\n            }\n            case 'eth_signTypedData':\n            case 'eth_signTypedData_v4': {\n                const [address, typedData] = params;\n                const parsedTypedData = typeof typedData === 'string' ? JSON.parse(typedData) : typedData;\n                if (this.safe.safeAddress.toLowerCase() !== address.toLowerCase()) {\n                    throw new Error('The address is invalid');\n                }\n                const response = await this.sdk.txs.signTypedMessage(parsedTypedData);\n                const signature = 'signature' in response ? response.signature : undefined;\n                return signature || '0x';\n            }\n            case 'eth_sendTransaction':\n                // `value` or `data` can be explicitly set as `undefined` for example in Viem. The spread will overwrite the fallback value.\n                const tx = {\n                    ...params[0],\n                    value: params[0].value || '0',\n                    data: params[0].data || '0x',\n                };\n                // Some ethereum libraries might pass the gas as a hex-encoded string\n                // We need to convert it to a number because the SDK expects a number and our backend only supports\n                // Decimal numbers\n                if (typeof tx.gas === 'string' && tx.gas.startsWith('0x')) {\n                    tx.gas = parseInt(tx.gas, 16);\n                }\n                const resp = await this.sdk.txs.send({\n                    txs: [tx],\n                    params: { safeTxGas: tx.gas },\n                });\n                // Store fake transaction\n                this.submittedTxs.set(resp.safeTxHash, {\n                    from: this.safe.safeAddress,\n                    hash: resp.safeTxHash,\n                    gas: 0,\n                    gasPrice: '0x00',\n                    nonce: 0,\n                    input: tx.data,\n                    value: tx.value,\n                    to: tx.to,\n                    blockHash: null,\n                    blockNumber: null,\n                    transactionIndex: null,\n                });\n                return resp.safeTxHash;\n            case 'eth_blockNumber':\n                const block = await this.sdk.eth.getBlockByNumber(['latest']);\n                return block.number;\n            case 'eth_getBalance':\n                return this.sdk.eth.getBalance([(0, utils_1.getLowerCase)(params[0]), params[1]]);\n            case 'eth_getCode':\n                return this.sdk.eth.getCode([(0, utils_1.getLowerCase)(params[0]), params[1]]);\n            case 'eth_getTransactionCount':\n                return this.sdk.eth.getTransactionCount([(0, utils_1.getLowerCase)(params[0]), params[1]]);\n            case 'eth_getStorageAt':\n                return this.sdk.eth.getStorageAt([(0, utils_1.getLowerCase)(params[0]), params[1], params[2]]);\n            case 'eth_getBlockByNumber':\n                return this.sdk.eth.getBlockByNumber([params[0], params[1]]);\n            case 'eth_getBlockByHash':\n                return this.sdk.eth.getBlockByHash([params[0], params[1]]);\n            case 'eth_getTransactionByHash':\n                let txHash = params[0];\n                try {\n                    const resp = await this.sdk.txs.getBySafeTxHash(txHash);\n                    txHash = resp.txHash || txHash;\n                }\n                catch (e) { }\n                // Use fake transaction if we don't have a real tx hash\n                if (this.submittedTxs.has(txHash)) {\n                    return this.submittedTxs.get(txHash);\n                }\n                return this.sdk.eth.getTransactionByHash([txHash]).then((tx) => {\n                    // We set the tx hash to the one requested, as some provider assert this\n                    if (tx) {\n                        tx.hash = params[0];\n                    }\n                    return tx;\n                });\n            case 'eth_getTransactionReceipt': {\n                let txHash = params[0];\n                try {\n                    const resp = await this.sdk.txs.getBySafeTxHash(txHash);\n                    txHash = resp.txHash || txHash;\n                }\n                catch (e) { }\n                return this.sdk.eth.getTransactionReceipt([txHash]).then((tx) => {\n                    // We set the tx hash to the one requested, as some provider assert this\n                    if (tx) {\n                        tx.transactionHash = params[0];\n                    }\n                    return tx;\n                });\n            }\n            case 'eth_estimateGas': {\n                return this.sdk.eth.getEstimateGas(params[0]);\n            }\n            case 'eth_call': {\n                return this.sdk.eth.call([params[0], params[1]]);\n            }\n            case 'eth_getLogs':\n                return this.sdk.eth.getPastLogs([params[0]]);\n            case 'eth_gasPrice':\n                return this.sdk.eth.getGasPrice();\n            case 'wallet_getPermissions':\n                return this.sdk.wallet.getPermissions();\n            case 'wallet_requestPermissions':\n                return this.sdk.wallet.requestPermissions(params[0]);\n            case 'safe_setSettings':\n                return this.sdk.eth.setSafeSettings([params[0]]);\n            case 'wallet_sendCalls': {\n                const { from, calls, chainId } = params[0];\n                if (chainId !== (0, utils_1.numberToHex)(this.chainId)) {\n                    throw new Error(`Safe is not on chain ${chainId}`);\n                }\n                if (from !== this.safe.safeAddress) {\n                    throw Error('Invalid from address');\n                }\n                const txs = calls.map((call, i) => {\n                    if (!call.to) {\n                        throw new Error(`Invalid call #${i}: missing \"to\" field`);\n                    }\n                    return {\n                        to: call.to,\n                        data: call.data ?? '0x',\n                        value: call.value ?? (0, utils_1.numberToHex)(0),\n                    };\n                });\n                const { safeTxHash } = await this.sdk.txs.send({ txs });\n                const result = {\n                    id: safeTxHash,\n                };\n                return result;\n            }\n            case 'wallet_getCallsStatus': {\n                const safeTxHash = params[0];\n                const CallStatus = {\n                    [safe_apps_sdk_1.TransactionStatus.AWAITING_CONFIRMATIONS]: 100,\n                    [safe_apps_sdk_1.TransactionStatus.AWAITING_EXECUTION]: 100,\n                    [safe_apps_sdk_1.TransactionStatus.SUCCESS]: 200,\n                    [safe_apps_sdk_1.TransactionStatus.CANCELLED]: 400,\n                    [safe_apps_sdk_1.TransactionStatus.FAILED]: 500,\n                };\n                const tx = await this.sdk.txs.getBySafeTxHash(safeTxHash);\n                const result = {\n                    version: '1.0',\n                    id: safeTxHash,\n                    chainId: (0, utils_1.numberToHex)(this.chainId),\n                    status: CallStatus[tx.txStatus],\n                };\n                // Transaction is queued\n                if (!tx.txHash) {\n                    return result;\n                }\n                // If transaction is executing, receipt is null\n                const receipt = await this.sdk.eth.getTransactionReceipt([tx.txHash]);\n                if (!receipt) {\n                    return result;\n                }\n                const calls = tx.txData?.dataDecoded?.method !== 'multiSend'\n                    ? 1\n                    : // Number of batched transactions\n                        tx.txData.dataDecoded.parameters?.[0].valueDecoded?.length ?? 1;\n                // Typed as number; is hex\n                const blockNumber = Number(receipt.blockNumber);\n                const gasUsed = Number(receipt.gasUsed);\n                result.receipts = Array(calls).fill({\n                    logs: receipt.logs,\n                    status: (0, utils_1.numberToHex)(tx.txStatus === safe_apps_sdk_1.TransactionStatus.SUCCESS ? 1 : 0),\n                    blockHash: receipt.blockHash,\n                    blockNumber: (0, utils_1.numberToHex)(blockNumber),\n                    gasUsed: (0, utils_1.numberToHex)(gasUsed),\n                    transactionHash: tx.txHash,\n                });\n                return result;\n            }\n            case 'wallet_showCallsStatus': {\n                // Cannot open transaction details page via SDK\n                throw new Error(`\"${request.method}\" not supported`);\n            }\n            case 'wallet_getCapabilities': {\n                return {\n                    [(0, utils_1.numberToHex)(this.chainId)]: {\n                        atomicBatch: {\n                            supported: true,\n                        },\n                    },\n                };\n            }\n            default:\n                throw Error(`\"${request.method}\" not implemented`);\n        }\n    }\n    // this method is needed for ethers v4\n    // https://github.com/ethers-io/ethers.js/blob/427e16826eb15d52d25c4f01027f8db22b74b76c/src.ts/providers/web3-provider.ts#L41-L55\n    send(request, callback) {\n        if (!request)\n            callback('Undefined request');\n        this.request(request)\n            .then((result) => callback(null, { jsonrpc: '2.0', id: request.id, result }))\n            .catch((error) => callback(error, null));\n    }\n}\nexports.SafeAppProvider = SafeAppProvider;\n//# sourceMappingURL=provider.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHNhZmUtZ2xvYmFsL3NhZmUtYXBwcy1wcm92aWRlci9kaXN0L3Byb3ZpZGVyLmpzIiwibWFwcGluZ3MiOiJBQUFhO0FBQ2IsOENBQTZDLEVBQUUsYUFBYSxFQUFDO0FBQzdELHVCQUF1QjtBQUN2Qix3QkFBd0IsbUJBQU8sQ0FBQyxxR0FBNEI7QUFDNUQsaUJBQWlCLG1CQUFPLENBQUMsc0JBQVE7QUFDakMsZ0JBQWdCLG1CQUFPLENBQUMsbUZBQVM7QUFDakM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsK0JBQStCLHVCQUF1QjtBQUN0RDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGdCQUFnQixzQkFBc0I7QUFDdEM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLDhCQUE4QixtQkFBbUI7QUFDakQsaUJBQWlCO0FBQ2pCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsaUJBQWlCO0FBQ2pCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGlCQUFpQjtBQUNqQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGlCQUFpQjtBQUNqQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSx3QkFBd0IsdUJBQXVCO0FBQy9DO0FBQ0EsNERBQTRELFFBQVE7QUFDcEU7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EseURBQXlELEVBQUU7QUFDM0Q7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsaUJBQWlCO0FBQ2pCLHdCQUF3QixhQUFhLDRCQUE0QixLQUFLO0FBQ3RFO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esb0NBQW9DO0FBQ3BDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGlCQUFpQjtBQUNqQjtBQUNBO0FBQ0E7QUFDQTtBQUNBLG9DQUFvQyxlQUFlO0FBQ25EO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHlCQUF5QjtBQUN6QixxQkFBcUI7QUFDckI7QUFDQTtBQUNBO0FBQ0EsZ0NBQWdDLGVBQWU7QUFDL0M7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLCtDQUErQyx3Q0FBd0M7QUFDdkY7QUFDQTtBQUNBO0FBQ0EsdUJBQXVCO0FBQ3ZCIiwic291cmNlcyI6WyJGOlxcQUkgY2hhaW5cXGNoYWluc2lnaHQtcHJvZHVjdGlvblxcbm9kZV9tb2R1bGVzXFxAc2FmZS1nbG9iYWxcXHNhZmUtYXBwcy1wcm92aWRlclxcZGlzdFxccHJvdmlkZXIuanMiXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2Ugc3RyaWN0XCI7XG5PYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgXCJfX2VzTW9kdWxlXCIsIHsgdmFsdWU6IHRydWUgfSk7XG5leHBvcnRzLlNhZmVBcHBQcm92aWRlciA9IHZvaWQgMDtcbmNvbnN0IHNhZmVfYXBwc19zZGtfMSA9IHJlcXVpcmUoXCJAc2FmZS1nbG9iYWwvc2FmZS1hcHBzLXNka1wiKTtcbmNvbnN0IGV2ZW50c18xID0gcmVxdWlyZShcImV2ZW50c1wiKTtcbmNvbnN0IHV0aWxzXzEgPSByZXF1aXJlKFwiLi91dGlsc1wiKTtcbi8vIFRoZSBBUEkgaXMgYmFzZWQgb24gRXRoZXJldW0gSmF2YVNjcmlwdCBBUEkgUHJvdmlkZXIgU3RhbmRhcmQuIExpbms6IGh0dHBzOi8vZWlwcy5ldGhlcmV1bS5vcmcvRUlQUy9laXAtMTE5M1xuY2xhc3MgU2FmZUFwcFByb3ZpZGVyIGV4dGVuZHMgZXZlbnRzXzEuRXZlbnRFbWl0dGVyIHtcbiAgICBjb25zdHJ1Y3RvcihzYWZlLCBzZGspIHtcbiAgICAgICAgc3VwZXIoKTtcbiAgICAgICAgdGhpcy5zdWJtaXR0ZWRUeHMgPSBuZXcgTWFwKCk7XG4gICAgICAgIHRoaXMuc2FmZSA9IHNhZmU7XG4gICAgICAgIHRoaXMuc2RrID0gc2RrO1xuICAgIH1cbiAgICBhc3luYyBjb25uZWN0KCkge1xuICAgICAgICB0aGlzLmVtaXQoJ2Nvbm5lY3QnLCB7IGNoYWluSWQ6IHRoaXMuY2hhaW5JZCB9KTtcbiAgICAgICAgcmV0dXJuO1xuICAgIH1cbiAgICBhc3luYyBkaXNjb25uZWN0KCkge1xuICAgICAgICByZXR1cm47XG4gICAgfVxuICAgIGdldCBjaGFpbklkKCkge1xuICAgICAgICByZXR1cm4gdGhpcy5zYWZlLmNoYWluSWQ7XG4gICAgfVxuICAgIC8vIGVzbGludC1kaXNhYmxlLW5leHQtbGluZSBAdHlwZXNjcmlwdC1lc2xpbnQvbm8tZXhwbGljaXQtYW55XG4gICAgYXN5bmMgcmVxdWVzdChyZXF1ZXN0KSB7XG4gICAgICAgIGNvbnN0IHsgbWV0aG9kLCBwYXJhbXMgPSBbXSB9ID0gcmVxdWVzdDtcbiAgICAgICAgc3dpdGNoIChtZXRob2QpIHtcbiAgICAgICAgICAgIGNhc2UgJ2V0aF9hY2NvdW50cyc6XG4gICAgICAgICAgICAgICAgcmV0dXJuIFt0aGlzLnNhZmUuc2FmZUFkZHJlc3NdO1xuICAgICAgICAgICAgY2FzZSAnbmV0X3ZlcnNpb24nOlxuICAgICAgICAgICAgY2FzZSAnZXRoX2NoYWluSWQnOlxuICAgICAgICAgICAgICAgIHJldHVybiAoMCwgdXRpbHNfMS5udW1iZXJUb0hleCkodGhpcy5jaGFpbklkKTtcbiAgICAgICAgICAgIGNhc2UgJ3BlcnNvbmFsX3NpZ24nOiB7XG4gICAgICAgICAgICAgICAgY29uc3QgW21lc3NhZ2UsIGFkZHJlc3NdID0gcGFyYW1zO1xuICAgICAgICAgICAgICAgIGlmICh0aGlzLnNhZmUuc2FmZUFkZHJlc3MudG9Mb3dlckNhc2UoKSAhPT0gYWRkcmVzcy50b0xvd2VyQ2FzZSgpKSB7XG4gICAgICAgICAgICAgICAgICAgIHRocm93IG5ldyBFcnJvcignVGhlIGFkZHJlc3Mgb3IgbWVzc2FnZSBoYXNoIGlzIGludmFsaWQnKTtcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCB0aGlzLnNkay50eHMuc2lnbk1lc3NhZ2UobWVzc2FnZSk7XG4gICAgICAgICAgICAgICAgY29uc3Qgc2lnbmF0dXJlID0gJ3NpZ25hdHVyZScgaW4gcmVzcG9uc2UgPyByZXNwb25zZS5zaWduYXR1cmUgOiB1bmRlZmluZWQ7XG4gICAgICAgICAgICAgICAgcmV0dXJuIHNpZ25hdHVyZSB8fCAnMHgnO1xuICAgICAgICAgICAgfVxuICAgICAgICAgICAgY2FzZSAnZXRoX3NpZ24nOiB7XG4gICAgICAgICAgICAgICAgY29uc3QgW2FkZHJlc3MsIG1lc3NhZ2VIYXNoXSA9IHBhcmFtcztcbiAgICAgICAgICAgICAgICBpZiAodGhpcy5zYWZlLnNhZmVBZGRyZXNzLnRvTG93ZXJDYXNlKCkgIT09IGFkZHJlc3MudG9Mb3dlckNhc2UoKSB8fCAhbWVzc2FnZUhhc2guc3RhcnRzV2l0aCgnMHgnKSkge1xuICAgICAgICAgICAgICAgICAgICB0aHJvdyBuZXcgRXJyb3IoJ1RoZSBhZGRyZXNzIG9yIG1lc3NhZ2UgaGFzaCBpcyBpbnZhbGlkJyk7XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgdGhpcy5zZGsudHhzLnNpZ25NZXNzYWdlKG1lc3NhZ2VIYXNoKTtcbiAgICAgICAgICAgICAgICBjb25zdCBzaWduYXR1cmUgPSAnc2lnbmF0dXJlJyBpbiByZXNwb25zZSA/IHJlc3BvbnNlLnNpZ25hdHVyZSA6IHVuZGVmaW5lZDtcbiAgICAgICAgICAgICAgICByZXR1cm4gc2lnbmF0dXJlIHx8ICcweCc7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICBjYXNlICdldGhfc2lnblR5cGVkRGF0YSc6XG4gICAgICAgICAgICBjYXNlICdldGhfc2lnblR5cGVkRGF0YV92NCc6IHtcbiAgICAgICAgICAgICAgICBjb25zdCBbYWRkcmVzcywgdHlwZWREYXRhXSA9IHBhcmFtcztcbiAgICAgICAgICAgICAgICBjb25zdCBwYXJzZWRUeXBlZERhdGEgPSB0eXBlb2YgdHlwZWREYXRhID09PSAnc3RyaW5nJyA/IEpTT04ucGFyc2UodHlwZWREYXRhKSA6IHR5cGVkRGF0YTtcbiAgICAgICAgICAgICAgICBpZiAodGhpcy5zYWZlLnNhZmVBZGRyZXNzLnRvTG93ZXJDYXNlKCkgIT09IGFkZHJlc3MudG9Mb3dlckNhc2UoKSkge1xuICAgICAgICAgICAgICAgICAgICB0aHJvdyBuZXcgRXJyb3IoJ1RoZSBhZGRyZXNzIGlzIGludmFsaWQnKTtcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCB0aGlzLnNkay50eHMuc2lnblR5cGVkTWVzc2FnZShwYXJzZWRUeXBlZERhdGEpO1xuICAgICAgICAgICAgICAgIGNvbnN0IHNpZ25hdHVyZSA9ICdzaWduYXR1cmUnIGluIHJlc3BvbnNlID8gcmVzcG9uc2Uuc2lnbmF0dXJlIDogdW5kZWZpbmVkO1xuICAgICAgICAgICAgICAgIHJldHVybiBzaWduYXR1cmUgfHwgJzB4JztcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIGNhc2UgJ2V0aF9zZW5kVHJhbnNhY3Rpb24nOlxuICAgICAgICAgICAgICAgIC8vIGB2YWx1ZWAgb3IgYGRhdGFgIGNhbiBiZSBleHBsaWNpdGx5IHNldCBhcyBgdW5kZWZpbmVkYCBmb3IgZXhhbXBsZSBpbiBWaWVtLiBUaGUgc3ByZWFkIHdpbGwgb3ZlcndyaXRlIHRoZSBmYWxsYmFjayB2YWx1ZS5cbiAgICAgICAgICAgICAgICBjb25zdCB0eCA9IHtcbiAgICAgICAgICAgICAgICAgICAgLi4ucGFyYW1zWzBdLFxuICAgICAgICAgICAgICAgICAgICB2YWx1ZTogcGFyYW1zWzBdLnZhbHVlIHx8ICcwJyxcbiAgICAgICAgICAgICAgICAgICAgZGF0YTogcGFyYW1zWzBdLmRhdGEgfHwgJzB4JyxcbiAgICAgICAgICAgICAgICB9O1xuICAgICAgICAgICAgICAgIC8vIFNvbWUgZXRoZXJldW0gbGlicmFyaWVzIG1pZ2h0IHBhc3MgdGhlIGdhcyBhcyBhIGhleC1lbmNvZGVkIHN0cmluZ1xuICAgICAgICAgICAgICAgIC8vIFdlIG5lZWQgdG8gY29udmVydCBpdCB0byBhIG51bWJlciBiZWNhdXNlIHRoZSBTREsgZXhwZWN0cyBhIG51bWJlciBhbmQgb3VyIGJhY2tlbmQgb25seSBzdXBwb3J0c1xuICAgICAgICAgICAgICAgIC8vIERlY2ltYWwgbnVtYmVyc1xuICAgICAgICAgICAgICAgIGlmICh0eXBlb2YgdHguZ2FzID09PSAnc3RyaW5nJyAmJiB0eC5nYXMuc3RhcnRzV2l0aCgnMHgnKSkge1xuICAgICAgICAgICAgICAgICAgICB0eC5nYXMgPSBwYXJzZUludCh0eC5nYXMsIDE2KTtcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgY29uc3QgcmVzcCA9IGF3YWl0IHRoaXMuc2RrLnR4cy5zZW5kKHtcbiAgICAgICAgICAgICAgICAgICAgdHhzOiBbdHhdLFxuICAgICAgICAgICAgICAgICAgICBwYXJhbXM6IHsgc2FmZVR4R2FzOiB0eC5nYXMgfSxcbiAgICAgICAgICAgICAgICB9KTtcbiAgICAgICAgICAgICAgICAvLyBTdG9yZSBmYWtlIHRyYW5zYWN0aW9uXG4gICAgICAgICAgICAgICAgdGhpcy5zdWJtaXR0ZWRUeHMuc2V0KHJlc3Auc2FmZVR4SGFzaCwge1xuICAgICAgICAgICAgICAgICAgICBmcm9tOiB0aGlzLnNhZmUuc2FmZUFkZHJlc3MsXG4gICAgICAgICAgICAgICAgICAgIGhhc2g6IHJlc3Auc2FmZVR4SGFzaCxcbiAgICAgICAgICAgICAgICAgICAgZ2FzOiAwLFxuICAgICAgICAgICAgICAgICAgICBnYXNQcmljZTogJzB4MDAnLFxuICAgICAgICAgICAgICAgICAgICBub25jZTogMCxcbiAgICAgICAgICAgICAgICAgICAgaW5wdXQ6IHR4LmRhdGEsXG4gICAgICAgICAgICAgICAgICAgIHZhbHVlOiB0eC52YWx1ZSxcbiAgICAgICAgICAgICAgICAgICAgdG86IHR4LnRvLFxuICAgICAgICAgICAgICAgICAgICBibG9ja0hhc2g6IG51bGwsXG4gICAgICAgICAgICAgICAgICAgIGJsb2NrTnVtYmVyOiBudWxsLFxuICAgICAgICAgICAgICAgICAgICB0cmFuc2FjdGlvbkluZGV4OiBudWxsLFxuICAgICAgICAgICAgICAgIH0pO1xuICAgICAgICAgICAgICAgIHJldHVybiByZXNwLnNhZmVUeEhhc2g7XG4gICAgICAgICAgICBjYXNlICdldGhfYmxvY2tOdW1iZXInOlxuICAgICAgICAgICAgICAgIGNvbnN0IGJsb2NrID0gYXdhaXQgdGhpcy5zZGsuZXRoLmdldEJsb2NrQnlOdW1iZXIoWydsYXRlc3QnXSk7XG4gICAgICAgICAgICAgICAgcmV0dXJuIGJsb2NrLm51bWJlcjtcbiAgICAgICAgICAgIGNhc2UgJ2V0aF9nZXRCYWxhbmNlJzpcbiAgICAgICAgICAgICAgICByZXR1cm4gdGhpcy5zZGsuZXRoLmdldEJhbGFuY2UoWygwLCB1dGlsc18xLmdldExvd2VyQ2FzZSkocGFyYW1zWzBdKSwgcGFyYW1zWzFdXSk7XG4gICAgICAgICAgICBjYXNlICdldGhfZ2V0Q29kZSc6XG4gICAgICAgICAgICAgICAgcmV0dXJuIHRoaXMuc2RrLmV0aC5nZXRDb2RlKFsoMCwgdXRpbHNfMS5nZXRMb3dlckNhc2UpKHBhcmFtc1swXSksIHBhcmFtc1sxXV0pO1xuICAgICAgICAgICAgY2FzZSAnZXRoX2dldFRyYW5zYWN0aW9uQ291bnQnOlxuICAgICAgICAgICAgICAgIHJldHVybiB0aGlzLnNkay5ldGguZ2V0VHJhbnNhY3Rpb25Db3VudChbKDAsIHV0aWxzXzEuZ2V0TG93ZXJDYXNlKShwYXJhbXNbMF0pLCBwYXJhbXNbMV1dKTtcbiAgICAgICAgICAgIGNhc2UgJ2V0aF9nZXRTdG9yYWdlQXQnOlxuICAgICAgICAgICAgICAgIHJldHVybiB0aGlzLnNkay5ldGguZ2V0U3RvcmFnZUF0KFsoMCwgdXRpbHNfMS5nZXRMb3dlckNhc2UpKHBhcmFtc1swXSksIHBhcmFtc1sxXSwgcGFyYW1zWzJdXSk7XG4gICAgICAgICAgICBjYXNlICdldGhfZ2V0QmxvY2tCeU51bWJlcic6XG4gICAgICAgICAgICAgICAgcmV0dXJuIHRoaXMuc2RrLmV0aC5nZXRCbG9ja0J5TnVtYmVyKFtwYXJhbXNbMF0sIHBhcmFtc1sxXV0pO1xuICAgICAgICAgICAgY2FzZSAnZXRoX2dldEJsb2NrQnlIYXNoJzpcbiAgICAgICAgICAgICAgICByZXR1cm4gdGhpcy5zZGsuZXRoLmdldEJsb2NrQnlIYXNoKFtwYXJhbXNbMF0sIHBhcmFtc1sxXV0pO1xuICAgICAgICAgICAgY2FzZSAnZXRoX2dldFRyYW5zYWN0aW9uQnlIYXNoJzpcbiAgICAgICAgICAgICAgICBsZXQgdHhIYXNoID0gcGFyYW1zWzBdO1xuICAgICAgICAgICAgICAgIHRyeSB7XG4gICAgICAgICAgICAgICAgICAgIGNvbnN0IHJlc3AgPSBhd2FpdCB0aGlzLnNkay50eHMuZ2V0QnlTYWZlVHhIYXNoKHR4SGFzaCk7XG4gICAgICAgICAgICAgICAgICAgIHR4SGFzaCA9IHJlc3AudHhIYXNoIHx8IHR4SGFzaDtcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgY2F0Y2ggKGUpIHsgfVxuICAgICAgICAgICAgICAgIC8vIFVzZSBmYWtlIHRyYW5zYWN0aW9uIGlmIHdlIGRvbid0IGhhdmUgYSByZWFsIHR4IGhhc2hcbiAgICAgICAgICAgICAgICBpZiAodGhpcy5zdWJtaXR0ZWRUeHMuaGFzKHR4SGFzaCkpIHtcbiAgICAgICAgICAgICAgICAgICAgcmV0dXJuIHRoaXMuc3VibWl0dGVkVHhzLmdldCh0eEhhc2gpO1xuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICByZXR1cm4gdGhpcy5zZGsuZXRoLmdldFRyYW5zYWN0aW9uQnlIYXNoKFt0eEhhc2hdKS50aGVuKCh0eCkgPT4ge1xuICAgICAgICAgICAgICAgICAgICAvLyBXZSBzZXQgdGhlIHR4IGhhc2ggdG8gdGhlIG9uZSByZXF1ZXN0ZWQsIGFzIHNvbWUgcHJvdmlkZXIgYXNzZXJ0IHRoaXNcbiAgICAgICAgICAgICAgICAgICAgaWYgKHR4KSB7XG4gICAgICAgICAgICAgICAgICAgICAgICB0eC5oYXNoID0gcGFyYW1zWzBdO1xuICAgICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgICAgIHJldHVybiB0eDtcbiAgICAgICAgICAgICAgICB9KTtcbiAgICAgICAgICAgIGNhc2UgJ2V0aF9nZXRUcmFuc2FjdGlvblJlY2VpcHQnOiB7XG4gICAgICAgICAgICAgICAgbGV0IHR4SGFzaCA9IHBhcmFtc1swXTtcbiAgICAgICAgICAgICAgICB0cnkge1xuICAgICAgICAgICAgICAgICAgICBjb25zdCByZXNwID0gYXdhaXQgdGhpcy5zZGsudHhzLmdldEJ5U2FmZVR4SGFzaCh0eEhhc2gpO1xuICAgICAgICAgICAgICAgICAgICB0eEhhc2ggPSByZXNwLnR4SGFzaCB8fCB0eEhhc2g7XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIGNhdGNoIChlKSB7IH1cbiAgICAgICAgICAgICAgICByZXR1cm4gdGhpcy5zZGsuZXRoLmdldFRyYW5zYWN0aW9uUmVjZWlwdChbdHhIYXNoXSkudGhlbigodHgpID0+IHtcbiAgICAgICAgICAgICAgICAgICAgLy8gV2Ugc2V0IHRoZSB0eCBoYXNoIHRvIHRoZSBvbmUgcmVxdWVzdGVkLCBhcyBzb21lIHByb3ZpZGVyIGFzc2VydCB0aGlzXG4gICAgICAgICAgICAgICAgICAgIGlmICh0eCkge1xuICAgICAgICAgICAgICAgICAgICAgICAgdHgudHJhbnNhY3Rpb25IYXNoID0gcGFyYW1zWzBdO1xuICAgICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgICAgIHJldHVybiB0eDtcbiAgICAgICAgICAgICAgICB9KTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIGNhc2UgJ2V0aF9lc3RpbWF0ZUdhcyc6IHtcbiAgICAgICAgICAgICAgICByZXR1cm4gdGhpcy5zZGsuZXRoLmdldEVzdGltYXRlR2FzKHBhcmFtc1swXSk7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICBjYXNlICdldGhfY2FsbCc6IHtcbiAgICAgICAgICAgICAgICByZXR1cm4gdGhpcy5zZGsuZXRoLmNhbGwoW3BhcmFtc1swXSwgcGFyYW1zWzFdXSk7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICBjYXNlICdldGhfZ2V0TG9ncyc6XG4gICAgICAgICAgICAgICAgcmV0dXJuIHRoaXMuc2RrLmV0aC5nZXRQYXN0TG9ncyhbcGFyYW1zWzBdXSk7XG4gICAgICAgICAgICBjYXNlICdldGhfZ2FzUHJpY2UnOlxuICAgICAgICAgICAgICAgIHJldHVybiB0aGlzLnNkay5ldGguZ2V0R2FzUHJpY2UoKTtcbiAgICAgICAgICAgIGNhc2UgJ3dhbGxldF9nZXRQZXJtaXNzaW9ucyc6XG4gICAgICAgICAgICAgICAgcmV0dXJuIHRoaXMuc2RrLndhbGxldC5nZXRQZXJtaXNzaW9ucygpO1xuICAgICAgICAgICAgY2FzZSAnd2FsbGV0X3JlcXVlc3RQZXJtaXNzaW9ucyc6XG4gICAgICAgICAgICAgICAgcmV0dXJuIHRoaXMuc2RrLndhbGxldC5yZXF1ZXN0UGVybWlzc2lvbnMocGFyYW1zWzBdKTtcbiAgICAgICAgICAgIGNhc2UgJ3NhZmVfc2V0U2V0dGluZ3MnOlxuICAgICAgICAgICAgICAgIHJldHVybiB0aGlzLnNkay5ldGguc2V0U2FmZVNldHRpbmdzKFtwYXJhbXNbMF1dKTtcbiAgICAgICAgICAgIGNhc2UgJ3dhbGxldF9zZW5kQ2FsbHMnOiB7XG4gICAgICAgICAgICAgICAgY29uc3QgeyBmcm9tLCBjYWxscywgY2hhaW5JZCB9ID0gcGFyYW1zWzBdO1xuICAgICAgICAgICAgICAgIGlmIChjaGFpbklkICE9PSAoMCwgdXRpbHNfMS5udW1iZXJUb0hleCkodGhpcy5jaGFpbklkKSkge1xuICAgICAgICAgICAgICAgICAgICB0aHJvdyBuZXcgRXJyb3IoYFNhZmUgaXMgbm90IG9uIGNoYWluICR7Y2hhaW5JZH1gKTtcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgaWYgKGZyb20gIT09IHRoaXMuc2FmZS5zYWZlQWRkcmVzcykge1xuICAgICAgICAgICAgICAgICAgICB0aHJvdyBFcnJvcignSW52YWxpZCBmcm9tIGFkZHJlc3MnKTtcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgY29uc3QgdHhzID0gY2FsbHMubWFwKChjYWxsLCBpKSA9PiB7XG4gICAgICAgICAgICAgICAgICAgIGlmICghY2FsbC50bykge1xuICAgICAgICAgICAgICAgICAgICAgICAgdGhyb3cgbmV3IEVycm9yKGBJbnZhbGlkIGNhbGwgIyR7aX06IG1pc3NpbmcgXCJ0b1wiIGZpZWxkYCk7XG4gICAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICAgICAgcmV0dXJuIHtcbiAgICAgICAgICAgICAgICAgICAgICAgIHRvOiBjYWxsLnRvLFxuICAgICAgICAgICAgICAgICAgICAgICAgZGF0YTogY2FsbC5kYXRhID8/ICcweCcsXG4gICAgICAgICAgICAgICAgICAgICAgICB2YWx1ZTogY2FsbC52YWx1ZSA/PyAoMCwgdXRpbHNfMS5udW1iZXJUb0hleCkoMCksXG4gICAgICAgICAgICAgICAgICAgIH07XG4gICAgICAgICAgICAgICAgfSk7XG4gICAgICAgICAgICAgICAgY29uc3QgeyBzYWZlVHhIYXNoIH0gPSBhd2FpdCB0aGlzLnNkay50eHMuc2VuZCh7IHR4cyB9KTtcbiAgICAgICAgICAgICAgICBjb25zdCByZXN1bHQgPSB7XG4gICAgICAgICAgICAgICAgICAgIGlkOiBzYWZlVHhIYXNoLFxuICAgICAgICAgICAgICAgIH07XG4gICAgICAgICAgICAgICAgcmV0dXJuIHJlc3VsdDtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIGNhc2UgJ3dhbGxldF9nZXRDYWxsc1N0YXR1cyc6IHtcbiAgICAgICAgICAgICAgICBjb25zdCBzYWZlVHhIYXNoID0gcGFyYW1zWzBdO1xuICAgICAgICAgICAgICAgIGNvbnN0IENhbGxTdGF0dXMgPSB7XG4gICAgICAgICAgICAgICAgICAgIFtzYWZlX2FwcHNfc2RrXzEuVHJhbnNhY3Rpb25TdGF0dXMuQVdBSVRJTkdfQ09ORklSTUFUSU9OU106IDEwMCxcbiAgICAgICAgICAgICAgICAgICAgW3NhZmVfYXBwc19zZGtfMS5UcmFuc2FjdGlvblN0YXR1cy5BV0FJVElOR19FWEVDVVRJT05dOiAxMDAsXG4gICAgICAgICAgICAgICAgICAgIFtzYWZlX2FwcHNfc2RrXzEuVHJhbnNhY3Rpb25TdGF0dXMuU1VDQ0VTU106IDIwMCxcbiAgICAgICAgICAgICAgICAgICAgW3NhZmVfYXBwc19zZGtfMS5UcmFuc2FjdGlvblN0YXR1cy5DQU5DRUxMRURdOiA0MDAsXG4gICAgICAgICAgICAgICAgICAgIFtzYWZlX2FwcHNfc2RrXzEuVHJhbnNhY3Rpb25TdGF0dXMuRkFJTEVEXTogNTAwLFxuICAgICAgICAgICAgICAgIH07XG4gICAgICAgICAgICAgICAgY29uc3QgdHggPSBhd2FpdCB0aGlzLnNkay50eHMuZ2V0QnlTYWZlVHhIYXNoKHNhZmVUeEhhc2gpO1xuICAgICAgICAgICAgICAgIGNvbnN0IHJlc3VsdCA9IHtcbiAgICAgICAgICAgICAgICAgICAgdmVyc2lvbjogJzEuMCcsXG4gICAgICAgICAgICAgICAgICAgIGlkOiBzYWZlVHhIYXNoLFxuICAgICAgICAgICAgICAgICAgICBjaGFpbklkOiAoMCwgdXRpbHNfMS5udW1iZXJUb0hleCkodGhpcy5jaGFpbklkKSxcbiAgICAgICAgICAgICAgICAgICAgc3RhdHVzOiBDYWxsU3RhdHVzW3R4LnR4U3RhdHVzXSxcbiAgICAgICAgICAgICAgICB9O1xuICAgICAgICAgICAgICAgIC8vIFRyYW5zYWN0aW9uIGlzIHF1ZXVlZFxuICAgICAgICAgICAgICAgIGlmICghdHgudHhIYXNoKSB7XG4gICAgICAgICAgICAgICAgICAgIHJldHVybiByZXN1bHQ7XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIC8vIElmIHRyYW5zYWN0aW9uIGlzIGV4ZWN1dGluZywgcmVjZWlwdCBpcyBudWxsXG4gICAgICAgICAgICAgICAgY29uc3QgcmVjZWlwdCA9IGF3YWl0IHRoaXMuc2RrLmV0aC5nZXRUcmFuc2FjdGlvblJlY2VpcHQoW3R4LnR4SGFzaF0pO1xuICAgICAgICAgICAgICAgIGlmICghcmVjZWlwdCkge1xuICAgICAgICAgICAgICAgICAgICByZXR1cm4gcmVzdWx0O1xuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICBjb25zdCBjYWxscyA9IHR4LnR4RGF0YT8uZGF0YURlY29kZWQ/Lm1ldGhvZCAhPT0gJ211bHRpU2VuZCdcbiAgICAgICAgICAgICAgICAgICAgPyAxXG4gICAgICAgICAgICAgICAgICAgIDogLy8gTnVtYmVyIG9mIGJhdGNoZWQgdHJhbnNhY3Rpb25zXG4gICAgICAgICAgICAgICAgICAgICAgICB0eC50eERhdGEuZGF0YURlY29kZWQucGFyYW1ldGVycz8uWzBdLnZhbHVlRGVjb2RlZD8ubGVuZ3RoID8/IDE7XG4gICAgICAgICAgICAgICAgLy8gVHlwZWQgYXMgbnVtYmVyOyBpcyBoZXhcbiAgICAgICAgICAgICAgICBjb25zdCBibG9ja051bWJlciA9IE51bWJlcihyZWNlaXB0LmJsb2NrTnVtYmVyKTtcbiAgICAgICAgICAgICAgICBjb25zdCBnYXNVc2VkID0gTnVtYmVyKHJlY2VpcHQuZ2FzVXNlZCk7XG4gICAgICAgICAgICAgICAgcmVzdWx0LnJlY2VpcHRzID0gQXJyYXkoY2FsbHMpLmZpbGwoe1xuICAgICAgICAgICAgICAgICAgICBsb2dzOiByZWNlaXB0LmxvZ3MsXG4gICAgICAgICAgICAgICAgICAgIHN0YXR1czogKDAsIHV0aWxzXzEubnVtYmVyVG9IZXgpKHR4LnR4U3RhdHVzID09PSBzYWZlX2FwcHNfc2RrXzEuVHJhbnNhY3Rpb25TdGF0dXMuU1VDQ0VTUyA/IDEgOiAwKSxcbiAgICAgICAgICAgICAgICAgICAgYmxvY2tIYXNoOiByZWNlaXB0LmJsb2NrSGFzaCxcbiAgICAgICAgICAgICAgICAgICAgYmxvY2tOdW1iZXI6ICgwLCB1dGlsc18xLm51bWJlclRvSGV4KShibG9ja051bWJlciksXG4gICAgICAgICAgICAgICAgICAgIGdhc1VzZWQ6ICgwLCB1dGlsc18xLm51bWJlclRvSGV4KShnYXNVc2VkKSxcbiAgICAgICAgICAgICAgICAgICAgdHJhbnNhY3Rpb25IYXNoOiB0eC50eEhhc2gsXG4gICAgICAgICAgICAgICAgfSk7XG4gICAgICAgICAgICAgICAgcmV0dXJuIHJlc3VsdDtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIGNhc2UgJ3dhbGxldF9zaG93Q2FsbHNTdGF0dXMnOiB7XG4gICAgICAgICAgICAgICAgLy8gQ2Fubm90IG9wZW4gdHJhbnNhY3Rpb24gZGV0YWlscyBwYWdlIHZpYSBTREtcbiAgICAgICAgICAgICAgICB0aHJvdyBuZXcgRXJyb3IoYFwiJHtyZXF1ZXN0Lm1ldGhvZH1cIiBub3Qgc3VwcG9ydGVkYCk7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICBjYXNlICd3YWxsZXRfZ2V0Q2FwYWJpbGl0aWVzJzoge1xuICAgICAgICAgICAgICAgIHJldHVybiB7XG4gICAgICAgICAgICAgICAgICAgIFsoMCwgdXRpbHNfMS5udW1iZXJUb0hleCkodGhpcy5jaGFpbklkKV06IHtcbiAgICAgICAgICAgICAgICAgICAgICAgIGF0b21pY0JhdGNoOiB7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgc3VwcG9ydGVkOiB0cnVlLFxuICAgICAgICAgICAgICAgICAgICAgICAgfSxcbiAgICAgICAgICAgICAgICAgICAgfSxcbiAgICAgICAgICAgICAgICB9O1xuICAgICAgICAgICAgfVxuICAgICAgICAgICAgZGVmYXVsdDpcbiAgICAgICAgICAgICAgICB0aHJvdyBFcnJvcihgXCIke3JlcXVlc3QubWV0aG9kfVwiIG5vdCBpbXBsZW1lbnRlZGApO1xuICAgICAgICB9XG4gICAgfVxuICAgIC8vIHRoaXMgbWV0aG9kIGlzIG5lZWRlZCBmb3IgZXRoZXJzIHY0XG4gICAgLy8gaHR0cHM6Ly9naXRodWIuY29tL2V0aGVycy1pby9ldGhlcnMuanMvYmxvYi80MjdlMTY4MjZlYjE1ZDUyZDI1YzRmMDEwMjdmOGRiMjJiNzRiNzZjL3NyYy50cy9wcm92aWRlcnMvd2ViMy1wcm92aWRlci50cyNMNDEtTDU1XG4gICAgc2VuZChyZXF1ZXN0LCBjYWxsYmFjaykge1xuICAgICAgICBpZiAoIXJlcXVlc3QpXG4gICAgICAgICAgICBjYWxsYmFjaygnVW5kZWZpbmVkIHJlcXVlc3QnKTtcbiAgICAgICAgdGhpcy5yZXF1ZXN0KHJlcXVlc3QpXG4gICAgICAgICAgICAudGhlbigocmVzdWx0KSA9PiBjYWxsYmFjayhudWxsLCB7IGpzb25ycGM6ICcyLjAnLCBpZDogcmVxdWVzdC5pZCwgcmVzdWx0IH0pKVxuICAgICAgICAgICAgLmNhdGNoKChlcnJvcikgPT4gY2FsbGJhY2soZXJyb3IsIG51bGwpKTtcbiAgICB9XG59XG5leHBvcnRzLlNhZmVBcHBQcm92aWRlciA9IFNhZmVBcHBQcm92aWRlcjtcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPXByb3ZpZGVyLmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@safe-global/safe-apps-provider/dist/provider.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@safe-global/safe-apps-provider/dist/utils.js":
/*!********************************************************************!*\
  !*** ./node_modules/@safe-global/safe-apps-provider/dist/utils.js ***!
  \********************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.numberToHex = exports.getLowerCase = void 0;\nfunction getLowerCase(value) {\n    if (value) {\n        return value.toLowerCase();\n    }\n    return value;\n}\nexports.getLowerCase = getLowerCase;\nfunction numberToHex(value) {\n    return `0x${value.toString(16)}`;\n}\nexports.numberToHex = numberToHex;\n//# sourceMappingURL=utils.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHNhZmUtZ2xvYmFsL3NhZmUtYXBwcy1wcm92aWRlci9kaXN0L3V0aWxzLmpzIiwibWFwcGluZ3MiOiJBQUFhO0FBQ2IsOENBQTZDLEVBQUUsYUFBYSxFQUFDO0FBQzdELG1CQUFtQixHQUFHLG9CQUFvQjtBQUMxQztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxvQkFBb0I7QUFDcEI7QUFDQSxnQkFBZ0IsbUJBQW1CO0FBQ25DO0FBQ0EsbUJBQW1CO0FBQ25CIiwic291cmNlcyI6WyJGOlxcQUkgY2hhaW5cXGNoYWluc2lnaHQtcHJvZHVjdGlvblxcbm9kZV9tb2R1bGVzXFxAc2FmZS1nbG9iYWxcXHNhZmUtYXBwcy1wcm92aWRlclxcZGlzdFxcdXRpbHMuanMiXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2Ugc3RyaWN0XCI7XG5PYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgXCJfX2VzTW9kdWxlXCIsIHsgdmFsdWU6IHRydWUgfSk7XG5leHBvcnRzLm51bWJlclRvSGV4ID0gZXhwb3J0cy5nZXRMb3dlckNhc2UgPSB2b2lkIDA7XG5mdW5jdGlvbiBnZXRMb3dlckNhc2UodmFsdWUpIHtcbiAgICBpZiAodmFsdWUpIHtcbiAgICAgICAgcmV0dXJuIHZhbHVlLnRvTG93ZXJDYXNlKCk7XG4gICAgfVxuICAgIHJldHVybiB2YWx1ZTtcbn1cbmV4cG9ydHMuZ2V0TG93ZXJDYXNlID0gZ2V0TG93ZXJDYXNlO1xuZnVuY3Rpb24gbnVtYmVyVG9IZXgodmFsdWUpIHtcbiAgICByZXR1cm4gYDB4JHt2YWx1ZS50b1N0cmluZygxNil9YDtcbn1cbmV4cG9ydHMubnVtYmVyVG9IZXggPSBudW1iZXJUb0hleDtcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPXV0aWxzLmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@safe-global/safe-apps-provider/dist/utils.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@safe-global/safe-apps-sdk/dist/cjs/communication/index.js":
/*!*********************************************************************************!*\
  !*** ./node_modules/@safe-global/safe-apps-sdk/dist/cjs/communication/index.js ***!
  \*********************************************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

eval("\nvar __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    var desc = Object.getOwnPropertyDescriptor(m, k);\n    if (!desc || (\"get\" in desc ? !m.__esModule : desc.writable || desc.configurable)) {\n      desc = { enumerable: true, get: function() { return m[k]; } };\n    }\n    Object.defineProperty(o, k2, desc);\n}) : (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    o[k2] = m[k];\n}));\nvar __exportStar = (this && this.__exportStar) || function(m, exports) {\n    for (var p in m) if (p !== \"default\" && !Object.prototype.hasOwnProperty.call(exports, p)) __createBinding(exports, m, p);\n};\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nconst messageFormatter_js_1 = __webpack_require__(/*! ./messageFormatter.js */ \"(ssr)/./node_modules/@safe-global/safe-apps-sdk/dist/cjs/communication/messageFormatter.js\");\nclass PostMessageCommunicator {\n    constructor(allowedOrigins = null, debugMode = false) {\n        this.allowedOrigins = null;\n        this.callbacks = new Map();\n        this.debugMode = false;\n        this.isServer = typeof window === 'undefined';\n        this.isValidMessage = ({ origin, data, source }) => {\n            const emptyOrMalformed = !data;\n            const sentFromParentEl = !this.isServer && source === window.parent;\n            const majorVersionNumber = typeof data.version !== 'undefined' && parseInt(data.version.split('.')[0]);\n            const allowedSDKVersion = typeof majorVersionNumber === 'number' && majorVersionNumber >= 1;\n            let validOrigin = true;\n            if (Array.isArray(this.allowedOrigins)) {\n                validOrigin = this.allowedOrigins.find((regExp) => regExp.test(origin)) !== undefined;\n            }\n            return !emptyOrMalformed && sentFromParentEl && allowedSDKVersion && validOrigin;\n        };\n        this.logIncomingMessage = (msg) => {\n            console.info(`Safe Apps SDK v1: A message was received from origin ${msg.origin}. `, msg.data);\n        };\n        this.onParentMessage = (msg) => {\n            if (this.isValidMessage(msg)) {\n                this.debugMode && this.logIncomingMessage(msg);\n                this.handleIncomingMessage(msg.data);\n            }\n        };\n        this.handleIncomingMessage = (payload) => {\n            const { id } = payload;\n            const cb = this.callbacks.get(id);\n            if (cb) {\n                cb(payload);\n                this.callbacks.delete(id);\n            }\n        };\n        this.send = (method, params) => {\n            const request = messageFormatter_js_1.MessageFormatter.makeRequest(method, params);\n            if (this.isServer) {\n                throw new Error(\"Window doesn't exist\");\n            }\n            window.parent.postMessage(request, '*');\n            return new Promise((resolve, reject) => {\n                this.callbacks.set(request.id, (response) => {\n                    if (!response.success) {\n                        reject(new Error(response.error));\n                        return;\n                    }\n                    resolve(response);\n                });\n            });\n        };\n        this.allowedOrigins = allowedOrigins;\n        this.debugMode = debugMode;\n        if (!this.isServer) {\n            window.addEventListener('message', this.onParentMessage);\n        }\n    }\n}\nexports[\"default\"] = PostMessageCommunicator;\n__exportStar(__webpack_require__(/*! ./methods.js */ \"(ssr)/./node_modules/@safe-global/safe-apps-sdk/dist/cjs/communication/methods.js\"), exports);\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@safe-global/safe-apps-sdk/dist/cjs/communication/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@safe-global/safe-apps-sdk/dist/cjs/communication/messageFormatter.js":
/*!********************************************************************************************!*\
  !*** ./node_modules/@safe-global/safe-apps-sdk/dist/cjs/communication/messageFormatter.js ***!
  \********************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.MessageFormatter = void 0;\nconst version_js_1 = __webpack_require__(/*! ../version.js */ \"(ssr)/./node_modules/@safe-global/safe-apps-sdk/dist/cjs/version.js\");\nconst utils_js_1 = __webpack_require__(/*! ./utils.js */ \"(ssr)/./node_modules/@safe-global/safe-apps-sdk/dist/cjs/communication/utils.js\");\nclass MessageFormatter {\n}\nexports.MessageFormatter = MessageFormatter;\nMessageFormatter.makeRequest = (method, params) => {\n    const id = (0, utils_js_1.generateRequestId)();\n    return {\n        id,\n        method,\n        params,\n        env: {\n            sdkVersion: (0, version_js_1.getSDKVersion)(),\n        },\n    };\n};\nMessageFormatter.makeResponse = (id, data, version) => ({\n    id,\n    success: true,\n    version,\n    data,\n});\nMessageFormatter.makeErrorResponse = (id, error, version) => ({\n    id,\n    success: false,\n    error,\n    version,\n});\n//# sourceMappingURL=messageFormatter.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHNhZmUtZ2xvYmFsL3NhZmUtYXBwcy1zZGsvZGlzdC9janMvY29tbXVuaWNhdGlvbi9tZXNzYWdlRm9ybWF0dGVyLmpzIiwibWFwcGluZ3MiOiJBQUFhO0FBQ2IsOENBQTZDLEVBQUUsYUFBYSxFQUFDO0FBQzdELHdCQUF3QjtBQUN4QixxQkFBcUIsbUJBQU8sQ0FBQywwRkFBZTtBQUM1QyxtQkFBbUIsbUJBQU8sQ0FBQyxtR0FBWTtBQUN2QztBQUNBO0FBQ0Esd0JBQXdCO0FBQ3hCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxTQUFTO0FBQ1Q7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxDQUFDO0FBQ0Q7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLENBQUM7QUFDRCIsInNvdXJjZXMiOlsiRjpcXEFJIGNoYWluXFxjaGFpbnNpZ2h0LXByb2R1Y3Rpb25cXG5vZGVfbW9kdWxlc1xcQHNhZmUtZ2xvYmFsXFxzYWZlLWFwcHMtc2RrXFxkaXN0XFxjanNcXGNvbW11bmljYXRpb25cXG1lc3NhZ2VGb3JtYXR0ZXIuanMiXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2Ugc3RyaWN0XCI7XG5PYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgXCJfX2VzTW9kdWxlXCIsIHsgdmFsdWU6IHRydWUgfSk7XG5leHBvcnRzLk1lc3NhZ2VGb3JtYXR0ZXIgPSB2b2lkIDA7XG5jb25zdCB2ZXJzaW9uX2pzXzEgPSByZXF1aXJlKFwiLi4vdmVyc2lvbi5qc1wiKTtcbmNvbnN0IHV0aWxzX2pzXzEgPSByZXF1aXJlKFwiLi91dGlscy5qc1wiKTtcbmNsYXNzIE1lc3NhZ2VGb3JtYXR0ZXIge1xufVxuZXhwb3J0cy5NZXNzYWdlRm9ybWF0dGVyID0gTWVzc2FnZUZvcm1hdHRlcjtcbk1lc3NhZ2VGb3JtYXR0ZXIubWFrZVJlcXVlc3QgPSAobWV0aG9kLCBwYXJhbXMpID0+IHtcbiAgICBjb25zdCBpZCA9ICgwLCB1dGlsc19qc18xLmdlbmVyYXRlUmVxdWVzdElkKSgpO1xuICAgIHJldHVybiB7XG4gICAgICAgIGlkLFxuICAgICAgICBtZXRob2QsXG4gICAgICAgIHBhcmFtcyxcbiAgICAgICAgZW52OiB7XG4gICAgICAgICAgICBzZGtWZXJzaW9uOiAoMCwgdmVyc2lvbl9qc18xLmdldFNES1ZlcnNpb24pKCksXG4gICAgICAgIH0sXG4gICAgfTtcbn07XG5NZXNzYWdlRm9ybWF0dGVyLm1ha2VSZXNwb25zZSA9IChpZCwgZGF0YSwgdmVyc2lvbikgPT4gKHtcbiAgICBpZCxcbiAgICBzdWNjZXNzOiB0cnVlLFxuICAgIHZlcnNpb24sXG4gICAgZGF0YSxcbn0pO1xuTWVzc2FnZUZvcm1hdHRlci5tYWtlRXJyb3JSZXNwb25zZSA9IChpZCwgZXJyb3IsIHZlcnNpb24pID0+ICh7XG4gICAgaWQsXG4gICAgc3VjY2VzczogZmFsc2UsXG4gICAgZXJyb3IsXG4gICAgdmVyc2lvbixcbn0pO1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9bWVzc2FnZUZvcm1hdHRlci5qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@safe-global/safe-apps-sdk/dist/cjs/communication/messageFormatter.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@safe-global/safe-apps-sdk/dist/cjs/communication/methods.js":
/*!***********************************************************************************!*\
  !*** ./node_modules/@safe-global/safe-apps-sdk/dist/cjs/communication/methods.js ***!
  \***********************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.RestrictedMethods = exports.Methods = void 0;\nvar Methods;\n(function (Methods) {\n    Methods[\"sendTransactions\"] = \"sendTransactions\";\n    Methods[\"rpcCall\"] = \"rpcCall\";\n    Methods[\"getChainInfo\"] = \"getChainInfo\";\n    Methods[\"getSafeInfo\"] = \"getSafeInfo\";\n    Methods[\"getTxBySafeTxHash\"] = \"getTxBySafeTxHash\";\n    Methods[\"getSafeBalances\"] = \"getSafeBalances\";\n    Methods[\"signMessage\"] = \"signMessage\";\n    Methods[\"signTypedMessage\"] = \"signTypedMessage\";\n    Methods[\"getEnvironmentInfo\"] = \"getEnvironmentInfo\";\n    Methods[\"getOffChainSignature\"] = \"getOffChainSignature\";\n    Methods[\"requestAddressBook\"] = \"requestAddressBook\";\n    Methods[\"wallet_getPermissions\"] = \"wallet_getPermissions\";\n    Methods[\"wallet_requestPermissions\"] = \"wallet_requestPermissions\";\n})(Methods || (exports.Methods = Methods = {}));\nvar RestrictedMethods;\n(function (RestrictedMethods) {\n    RestrictedMethods[\"requestAddressBook\"] = \"requestAddressBook\";\n})(RestrictedMethods || (exports.RestrictedMethods = RestrictedMethods = {}));\n//# sourceMappingURL=methods.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@safe-global/safe-apps-sdk/dist/cjs/communication/methods.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@safe-global/safe-apps-sdk/dist/cjs/communication/utils.js":
/*!*********************************************************************************!*\
  !*** ./node_modules/@safe-global/safe-apps-sdk/dist/cjs/communication/utils.js ***!
  \*********************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.generateRequestId = void 0;\n// i.e. 0-255 -> '00'-'ff'\nconst dec2hex = (dec) => dec.toString(16).padStart(2, '0');\nconst generateId = (len) => {\n    const arr = new Uint8Array((len || 40) / 2);\n    window.crypto.getRandomValues(arr);\n    return Array.from(arr, dec2hex).join('');\n};\nconst generateRequestId = () => {\n    if (typeof window !== 'undefined') {\n        return generateId(10);\n    }\n    return new Date().getTime().toString(36);\n};\nexports.generateRequestId = generateRequestId;\n//# sourceMappingURL=utils.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHNhZmUtZ2xvYmFsL3NhZmUtYXBwcy1zZGsvZGlzdC9janMvY29tbXVuaWNhdGlvbi91dGlscy5qcyIsIm1hcHBpbmdzIjoiQUFBYTtBQUNiLDhDQUE2QyxFQUFFLGFBQWEsRUFBQztBQUM3RCx5QkFBeUI7QUFDekI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSx5QkFBeUI7QUFDekIiLCJzb3VyY2VzIjpbIkY6XFxBSSBjaGFpblxcY2hhaW5zaWdodC1wcm9kdWN0aW9uXFxub2RlX21vZHVsZXNcXEBzYWZlLWdsb2JhbFxcc2FmZS1hcHBzLXNka1xcZGlzdFxcY2pzXFxjb21tdW5pY2F0aW9uXFx1dGlscy5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBzdHJpY3RcIjtcbk9iamVjdC5kZWZpbmVQcm9wZXJ0eShleHBvcnRzLCBcIl9fZXNNb2R1bGVcIiwgeyB2YWx1ZTogdHJ1ZSB9KTtcbmV4cG9ydHMuZ2VuZXJhdGVSZXF1ZXN0SWQgPSB2b2lkIDA7XG4vLyBpLmUuIDAtMjU1IC0+ICcwMCctJ2ZmJ1xuY29uc3QgZGVjMmhleCA9IChkZWMpID0+IGRlYy50b1N0cmluZygxNikucGFkU3RhcnQoMiwgJzAnKTtcbmNvbnN0IGdlbmVyYXRlSWQgPSAobGVuKSA9PiB7XG4gICAgY29uc3QgYXJyID0gbmV3IFVpbnQ4QXJyYXkoKGxlbiB8fCA0MCkgLyAyKTtcbiAgICB3aW5kb3cuY3J5cHRvLmdldFJhbmRvbVZhbHVlcyhhcnIpO1xuICAgIHJldHVybiBBcnJheS5mcm9tKGFyciwgZGVjMmhleCkuam9pbignJyk7XG59O1xuY29uc3QgZ2VuZXJhdGVSZXF1ZXN0SWQgPSAoKSA9PiB7XG4gICAgaWYgKHR5cGVvZiB3aW5kb3cgIT09ICd1bmRlZmluZWQnKSB7XG4gICAgICAgIHJldHVybiBnZW5lcmF0ZUlkKDEwKTtcbiAgICB9XG4gICAgcmV0dXJuIG5ldyBEYXRlKCkuZ2V0VGltZSgpLnRvU3RyaW5nKDM2KTtcbn07XG5leHBvcnRzLmdlbmVyYXRlUmVxdWVzdElkID0gZ2VuZXJhdGVSZXF1ZXN0SWQ7XG4vLyMgc291cmNlTWFwcGluZ1VSTD11dGlscy5qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@safe-global/safe-apps-sdk/dist/cjs/communication/utils.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@safe-global/safe-apps-sdk/dist/cjs/decorators/requirePermissions.js":
/*!*******************************************************************************************!*\
  !*** ./node_modules/@safe-global/safe-apps-sdk/dist/cjs/decorators/requirePermissions.js ***!
  \*******************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nconst index_js_1 = __webpack_require__(/*! ../wallet/index.js */ \"(ssr)/./node_modules/@safe-global/safe-apps-sdk/dist/cjs/wallet/index.js\");\nconst permissions_js_1 = __webpack_require__(/*! ../types/permissions.js */ \"(ssr)/./node_modules/@safe-global/safe-apps-sdk/dist/cjs/types/permissions.js\");\nconst hasPermission = (required, permissions) => permissions.some((permission) => permission.parentCapability === required);\nconst requirePermission = () => (_, propertyKey, descriptor) => {\n    const originalMethod = descriptor.value;\n    descriptor.value = async function () {\n        // @ts-expect-error accessing private property from decorator. 'this' context is the class instance\n        const wallet = new index_js_1.Wallet(this.communicator);\n        let currentPermissions = await wallet.getPermissions();\n        if (!hasPermission(propertyKey, currentPermissions)) {\n            currentPermissions = await wallet.requestPermissions([{ [propertyKey]: {} }]);\n        }\n        if (!hasPermission(propertyKey, currentPermissions)) {\n            throw new permissions_js_1.PermissionsError('Permissions rejected', permissions_js_1.PERMISSIONS_REQUEST_REJECTED);\n        }\n        return originalMethod.apply(this);\n    };\n    return descriptor;\n};\nexports[\"default\"] = requirePermission;\n//# sourceMappingURL=requirePermissions.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHNhZmUtZ2xvYmFsL3NhZmUtYXBwcy1zZGsvZGlzdC9janMvZGVjb3JhdG9ycy9yZXF1aXJlUGVybWlzc2lvbnMuanMiLCJtYXBwaW5ncyI6IkFBQWE7QUFDYiw4Q0FBNkMsRUFBRSxhQUFhLEVBQUM7QUFDN0QsbUJBQW1CLG1CQUFPLENBQUMsb0dBQW9CO0FBQy9DLHlCQUF5QixtQkFBTyxDQUFDLDhHQUF5QjtBQUMxRDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esb0VBQW9FLG1CQUFtQjtBQUN2RjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esa0JBQWU7QUFDZiIsInNvdXJjZXMiOlsiRjpcXEFJIGNoYWluXFxjaGFpbnNpZ2h0LXByb2R1Y3Rpb25cXG5vZGVfbW9kdWxlc1xcQHNhZmUtZ2xvYmFsXFxzYWZlLWFwcHMtc2RrXFxkaXN0XFxjanNcXGRlY29yYXRvcnNcXHJlcXVpcmVQZXJtaXNzaW9ucy5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBzdHJpY3RcIjtcbk9iamVjdC5kZWZpbmVQcm9wZXJ0eShleHBvcnRzLCBcIl9fZXNNb2R1bGVcIiwgeyB2YWx1ZTogdHJ1ZSB9KTtcbmNvbnN0IGluZGV4X2pzXzEgPSByZXF1aXJlKFwiLi4vd2FsbGV0L2luZGV4LmpzXCIpO1xuY29uc3QgcGVybWlzc2lvbnNfanNfMSA9IHJlcXVpcmUoXCIuLi90eXBlcy9wZXJtaXNzaW9ucy5qc1wiKTtcbmNvbnN0IGhhc1Blcm1pc3Npb24gPSAocmVxdWlyZWQsIHBlcm1pc3Npb25zKSA9PiBwZXJtaXNzaW9ucy5zb21lKChwZXJtaXNzaW9uKSA9PiBwZXJtaXNzaW9uLnBhcmVudENhcGFiaWxpdHkgPT09IHJlcXVpcmVkKTtcbmNvbnN0IHJlcXVpcmVQZXJtaXNzaW9uID0gKCkgPT4gKF8sIHByb3BlcnR5S2V5LCBkZXNjcmlwdG9yKSA9PiB7XG4gICAgY29uc3Qgb3JpZ2luYWxNZXRob2QgPSBkZXNjcmlwdG9yLnZhbHVlO1xuICAgIGRlc2NyaXB0b3IudmFsdWUgPSBhc3luYyBmdW5jdGlvbiAoKSB7XG4gICAgICAgIC8vIEB0cy1leHBlY3QtZXJyb3IgYWNjZXNzaW5nIHByaXZhdGUgcHJvcGVydHkgZnJvbSBkZWNvcmF0b3IuICd0aGlzJyBjb250ZXh0IGlzIHRoZSBjbGFzcyBpbnN0YW5jZVxuICAgICAgICBjb25zdCB3YWxsZXQgPSBuZXcgaW5kZXhfanNfMS5XYWxsZXQodGhpcy5jb21tdW5pY2F0b3IpO1xuICAgICAgICBsZXQgY3VycmVudFBlcm1pc3Npb25zID0gYXdhaXQgd2FsbGV0LmdldFBlcm1pc3Npb25zKCk7XG4gICAgICAgIGlmICghaGFzUGVybWlzc2lvbihwcm9wZXJ0eUtleSwgY3VycmVudFBlcm1pc3Npb25zKSkge1xuICAgICAgICAgICAgY3VycmVudFBlcm1pc3Npb25zID0gYXdhaXQgd2FsbGV0LnJlcXVlc3RQZXJtaXNzaW9ucyhbeyBbcHJvcGVydHlLZXldOiB7fSB9XSk7XG4gICAgICAgIH1cbiAgICAgICAgaWYgKCFoYXNQZXJtaXNzaW9uKHByb3BlcnR5S2V5LCBjdXJyZW50UGVybWlzc2lvbnMpKSB7XG4gICAgICAgICAgICB0aHJvdyBuZXcgcGVybWlzc2lvbnNfanNfMS5QZXJtaXNzaW9uc0Vycm9yKCdQZXJtaXNzaW9ucyByZWplY3RlZCcsIHBlcm1pc3Npb25zX2pzXzEuUEVSTUlTU0lPTlNfUkVRVUVTVF9SRUpFQ1RFRCk7XG4gICAgICAgIH1cbiAgICAgICAgcmV0dXJuIG9yaWdpbmFsTWV0aG9kLmFwcGx5KHRoaXMpO1xuICAgIH07XG4gICAgcmV0dXJuIGRlc2NyaXB0b3I7XG59O1xuZXhwb3J0cy5kZWZhdWx0ID0gcmVxdWlyZVBlcm1pc3Npb247XG4vLyMgc291cmNlTWFwcGluZ1VSTD1yZXF1aXJlUGVybWlzc2lvbnMuanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@safe-global/safe-apps-sdk/dist/cjs/decorators/requirePermissions.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@safe-global/safe-apps-sdk/dist/cjs/eth/constants.js":
/*!***************************************************************************!*\
  !*** ./node_modules/@safe-global/safe-apps-sdk/dist/cjs/eth/constants.js ***!
  \***************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.RPC_CALLS = void 0;\nexports.RPC_CALLS = {\n    eth_call: 'eth_call',\n    eth_gasPrice: 'eth_gasPrice',\n    eth_getLogs: 'eth_getLogs',\n    eth_getBalance: 'eth_getBalance',\n    eth_getCode: 'eth_getCode',\n    eth_getBlockByHash: 'eth_getBlockByHash',\n    eth_getBlockByNumber: 'eth_getBlockByNumber',\n    eth_getStorageAt: 'eth_getStorageAt',\n    eth_getTransactionByHash: 'eth_getTransactionByHash',\n    eth_getTransactionReceipt: 'eth_getTransactionReceipt',\n    eth_getTransactionCount: 'eth_getTransactionCount',\n    eth_estimateGas: 'eth_estimateGas',\n    safe_setSettings: 'safe_setSettings',\n};\n//# sourceMappingURL=constants.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHNhZmUtZ2xvYmFsL3NhZmUtYXBwcy1zZGsvZGlzdC9janMvZXRoL2NvbnN0YW50cy5qcyIsIm1hcHBpbmdzIjoiQUFBYTtBQUNiLDhDQUE2QyxFQUFFLGFBQWEsRUFBQztBQUM3RCxpQkFBaUI7QUFDakIsaUJBQWlCO0FBQ2pCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsiRjpcXEFJIGNoYWluXFxjaGFpbnNpZ2h0LXByb2R1Y3Rpb25cXG5vZGVfbW9kdWxlc1xcQHNhZmUtZ2xvYmFsXFxzYWZlLWFwcHMtc2RrXFxkaXN0XFxjanNcXGV0aFxcY29uc3RhbnRzLmpzIl0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIHN0cmljdFwiO1xuT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsIFwiX19lc01vZHVsZVwiLCB7IHZhbHVlOiB0cnVlIH0pO1xuZXhwb3J0cy5SUENfQ0FMTFMgPSB2b2lkIDA7XG5leHBvcnRzLlJQQ19DQUxMUyA9IHtcbiAgICBldGhfY2FsbDogJ2V0aF9jYWxsJyxcbiAgICBldGhfZ2FzUHJpY2U6ICdldGhfZ2FzUHJpY2UnLFxuICAgIGV0aF9nZXRMb2dzOiAnZXRoX2dldExvZ3MnLFxuICAgIGV0aF9nZXRCYWxhbmNlOiAnZXRoX2dldEJhbGFuY2UnLFxuICAgIGV0aF9nZXRDb2RlOiAnZXRoX2dldENvZGUnLFxuICAgIGV0aF9nZXRCbG9ja0J5SGFzaDogJ2V0aF9nZXRCbG9ja0J5SGFzaCcsXG4gICAgZXRoX2dldEJsb2NrQnlOdW1iZXI6ICdldGhfZ2V0QmxvY2tCeU51bWJlcicsXG4gICAgZXRoX2dldFN0b3JhZ2VBdDogJ2V0aF9nZXRTdG9yYWdlQXQnLFxuICAgIGV0aF9nZXRUcmFuc2FjdGlvbkJ5SGFzaDogJ2V0aF9nZXRUcmFuc2FjdGlvbkJ5SGFzaCcsXG4gICAgZXRoX2dldFRyYW5zYWN0aW9uUmVjZWlwdDogJ2V0aF9nZXRUcmFuc2FjdGlvblJlY2VpcHQnLFxuICAgIGV0aF9nZXRUcmFuc2FjdGlvbkNvdW50OiAnZXRoX2dldFRyYW5zYWN0aW9uQ291bnQnLFxuICAgIGV0aF9lc3RpbWF0ZUdhczogJ2V0aF9lc3RpbWF0ZUdhcycsXG4gICAgc2FmZV9zZXRTZXR0aW5nczogJ3NhZmVfc2V0U2V0dGluZ3MnLFxufTtcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPWNvbnN0YW50cy5qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@safe-global/safe-apps-sdk/dist/cjs/eth/constants.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@safe-global/safe-apps-sdk/dist/cjs/eth/index.js":
/*!***********************************************************************!*\
  !*** ./node_modules/@safe-global/safe-apps-sdk/dist/cjs/eth/index.js ***!
  \***********************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.Eth = void 0;\nconst constants_js_1 = __webpack_require__(/*! ../eth/constants.js */ \"(ssr)/./node_modules/@safe-global/safe-apps-sdk/dist/cjs/eth/constants.js\");\nconst methods_js_1 = __webpack_require__(/*! ../communication/methods.js */ \"(ssr)/./node_modules/@safe-global/safe-apps-sdk/dist/cjs/communication/methods.js\");\nconst inputFormatters = {\n    defaultBlockParam: (arg = 'latest') => arg,\n    returnFullTxObjectParam: (arg = false) => arg,\n    blockNumberToHex: (arg) => Number.isInteger(arg) ? `0x${arg.toString(16)}` : arg,\n};\nclass Eth {\n    constructor(communicator) {\n        this.communicator = communicator;\n        this.call = this.buildRequest({\n            call: constants_js_1.RPC_CALLS.eth_call,\n            formatters: [null, inputFormatters.defaultBlockParam],\n        });\n        this.getBalance = this.buildRequest({\n            call: constants_js_1.RPC_CALLS.eth_getBalance,\n            formatters: [null, inputFormatters.defaultBlockParam],\n        });\n        this.getCode = this.buildRequest({\n            call: constants_js_1.RPC_CALLS.eth_getCode,\n            formatters: [null, inputFormatters.defaultBlockParam],\n        });\n        this.getStorageAt = this.buildRequest({\n            call: constants_js_1.RPC_CALLS.eth_getStorageAt,\n            formatters: [null, inputFormatters.blockNumberToHex, inputFormatters.defaultBlockParam],\n        });\n        this.getPastLogs = this.buildRequest({\n            call: constants_js_1.RPC_CALLS.eth_getLogs,\n        });\n        this.getBlockByHash = this.buildRequest({\n            call: constants_js_1.RPC_CALLS.eth_getBlockByHash,\n            formatters: [null, inputFormatters.returnFullTxObjectParam],\n        });\n        this.getBlockByNumber = this.buildRequest({\n            call: constants_js_1.RPC_CALLS.eth_getBlockByNumber,\n            formatters: [inputFormatters.blockNumberToHex, inputFormatters.returnFullTxObjectParam],\n        });\n        this.getTransactionByHash = this.buildRequest({\n            call: constants_js_1.RPC_CALLS.eth_getTransactionByHash,\n        });\n        this.getTransactionReceipt = this.buildRequest({\n            call: constants_js_1.RPC_CALLS.eth_getTransactionReceipt,\n        });\n        this.getTransactionCount = this.buildRequest({\n            call: constants_js_1.RPC_CALLS.eth_getTransactionCount,\n            formatters: [null, inputFormatters.defaultBlockParam],\n        });\n        this.getGasPrice = this.buildRequest({\n            call: constants_js_1.RPC_CALLS.eth_gasPrice,\n        });\n        this.getEstimateGas = (transaction) => this.buildRequest({\n            call: constants_js_1.RPC_CALLS.eth_estimateGas,\n        })([transaction]);\n        this.setSafeSettings = this.buildRequest({\n            call: constants_js_1.RPC_CALLS.safe_setSettings,\n        });\n    }\n    buildRequest(args) {\n        const { call, formatters } = args;\n        return async (params) => {\n            if (formatters && Array.isArray(params)) {\n                formatters.forEach((formatter, i) => {\n                    if (formatter) {\n                        params[i] = formatter(params[i]);\n                    }\n                });\n            }\n            const payload = {\n                call,\n                params: params || [],\n            };\n            const response = await this.communicator.send(methods_js_1.Methods.rpcCall, payload);\n            return response.data;\n        };\n    }\n}\nexports.Eth = Eth;\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHNhZmUtZ2xvYmFsL3NhZmUtYXBwcy1zZGsvZGlzdC9janMvZXRoL2luZGV4LmpzIiwibWFwcGluZ3MiOiJBQUFhO0FBQ2IsOENBQTZDLEVBQUUsYUFBYSxFQUFDO0FBQzdELFdBQVc7QUFDWCx1QkFBdUIsbUJBQU8sQ0FBQyxzR0FBcUI7QUFDcEQscUJBQXFCLG1CQUFPLENBQUMsc0hBQTZCO0FBQzFEO0FBQ0E7QUFDQTtBQUNBLDREQUE0RCxpQkFBaUI7QUFDN0U7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxTQUFTO0FBQ1Q7QUFDQTtBQUNBO0FBQ0EsU0FBUztBQUNUO0FBQ0E7QUFDQTtBQUNBLFNBQVM7QUFDVDtBQUNBO0FBQ0E7QUFDQSxTQUFTO0FBQ1Q7QUFDQTtBQUNBLFNBQVM7QUFDVDtBQUNBO0FBQ0E7QUFDQSxTQUFTO0FBQ1Q7QUFDQTtBQUNBO0FBQ0EsU0FBUztBQUNUO0FBQ0E7QUFDQSxTQUFTO0FBQ1Q7QUFDQTtBQUNBLFNBQVM7QUFDVDtBQUNBO0FBQ0E7QUFDQSxTQUFTO0FBQ1Q7QUFDQTtBQUNBLFNBQVM7QUFDVDtBQUNBO0FBQ0EsU0FBUztBQUNUO0FBQ0E7QUFDQSxTQUFTO0FBQ1Q7QUFDQTtBQUNBLGdCQUFnQixtQkFBbUI7QUFDbkM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsaUJBQWlCO0FBQ2pCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsV0FBVztBQUNYIiwic291cmNlcyI6WyJGOlxcQUkgY2hhaW5cXGNoYWluc2lnaHQtcHJvZHVjdGlvblxcbm9kZV9tb2R1bGVzXFxAc2FmZS1nbG9iYWxcXHNhZmUtYXBwcy1zZGtcXGRpc3RcXGNqc1xcZXRoXFxpbmRleC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBzdHJpY3RcIjtcbk9iamVjdC5kZWZpbmVQcm9wZXJ0eShleHBvcnRzLCBcIl9fZXNNb2R1bGVcIiwgeyB2YWx1ZTogdHJ1ZSB9KTtcbmV4cG9ydHMuRXRoID0gdm9pZCAwO1xuY29uc3QgY29uc3RhbnRzX2pzXzEgPSByZXF1aXJlKFwiLi4vZXRoL2NvbnN0YW50cy5qc1wiKTtcbmNvbnN0IG1ldGhvZHNfanNfMSA9IHJlcXVpcmUoXCIuLi9jb21tdW5pY2F0aW9uL21ldGhvZHMuanNcIik7XG5jb25zdCBpbnB1dEZvcm1hdHRlcnMgPSB7XG4gICAgZGVmYXVsdEJsb2NrUGFyYW06IChhcmcgPSAnbGF0ZXN0JykgPT4gYXJnLFxuICAgIHJldHVybkZ1bGxUeE9iamVjdFBhcmFtOiAoYXJnID0gZmFsc2UpID0+IGFyZyxcbiAgICBibG9ja051bWJlclRvSGV4OiAoYXJnKSA9PiBOdW1iZXIuaXNJbnRlZ2VyKGFyZykgPyBgMHgke2FyZy50b1N0cmluZygxNil9YCA6IGFyZyxcbn07XG5jbGFzcyBFdGgge1xuICAgIGNvbnN0cnVjdG9yKGNvbW11bmljYXRvcikge1xuICAgICAgICB0aGlzLmNvbW11bmljYXRvciA9IGNvbW11bmljYXRvcjtcbiAgICAgICAgdGhpcy5jYWxsID0gdGhpcy5idWlsZFJlcXVlc3Qoe1xuICAgICAgICAgICAgY2FsbDogY29uc3RhbnRzX2pzXzEuUlBDX0NBTExTLmV0aF9jYWxsLFxuICAgICAgICAgICAgZm9ybWF0dGVyczogW251bGwsIGlucHV0Rm9ybWF0dGVycy5kZWZhdWx0QmxvY2tQYXJhbV0sXG4gICAgICAgIH0pO1xuICAgICAgICB0aGlzLmdldEJhbGFuY2UgPSB0aGlzLmJ1aWxkUmVxdWVzdCh7XG4gICAgICAgICAgICBjYWxsOiBjb25zdGFudHNfanNfMS5SUENfQ0FMTFMuZXRoX2dldEJhbGFuY2UsXG4gICAgICAgICAgICBmb3JtYXR0ZXJzOiBbbnVsbCwgaW5wdXRGb3JtYXR0ZXJzLmRlZmF1bHRCbG9ja1BhcmFtXSxcbiAgICAgICAgfSk7XG4gICAgICAgIHRoaXMuZ2V0Q29kZSA9IHRoaXMuYnVpbGRSZXF1ZXN0KHtcbiAgICAgICAgICAgIGNhbGw6IGNvbnN0YW50c19qc18xLlJQQ19DQUxMUy5ldGhfZ2V0Q29kZSxcbiAgICAgICAgICAgIGZvcm1hdHRlcnM6IFtudWxsLCBpbnB1dEZvcm1hdHRlcnMuZGVmYXVsdEJsb2NrUGFyYW1dLFxuICAgICAgICB9KTtcbiAgICAgICAgdGhpcy5nZXRTdG9yYWdlQXQgPSB0aGlzLmJ1aWxkUmVxdWVzdCh7XG4gICAgICAgICAgICBjYWxsOiBjb25zdGFudHNfanNfMS5SUENfQ0FMTFMuZXRoX2dldFN0b3JhZ2VBdCxcbiAgICAgICAgICAgIGZvcm1hdHRlcnM6IFtudWxsLCBpbnB1dEZvcm1hdHRlcnMuYmxvY2tOdW1iZXJUb0hleCwgaW5wdXRGb3JtYXR0ZXJzLmRlZmF1bHRCbG9ja1BhcmFtXSxcbiAgICAgICAgfSk7XG4gICAgICAgIHRoaXMuZ2V0UGFzdExvZ3MgPSB0aGlzLmJ1aWxkUmVxdWVzdCh7XG4gICAgICAgICAgICBjYWxsOiBjb25zdGFudHNfanNfMS5SUENfQ0FMTFMuZXRoX2dldExvZ3MsXG4gICAgICAgIH0pO1xuICAgICAgICB0aGlzLmdldEJsb2NrQnlIYXNoID0gdGhpcy5idWlsZFJlcXVlc3Qoe1xuICAgICAgICAgICAgY2FsbDogY29uc3RhbnRzX2pzXzEuUlBDX0NBTExTLmV0aF9nZXRCbG9ja0J5SGFzaCxcbiAgICAgICAgICAgIGZvcm1hdHRlcnM6IFtudWxsLCBpbnB1dEZvcm1hdHRlcnMucmV0dXJuRnVsbFR4T2JqZWN0UGFyYW1dLFxuICAgICAgICB9KTtcbiAgICAgICAgdGhpcy5nZXRCbG9ja0J5TnVtYmVyID0gdGhpcy5idWlsZFJlcXVlc3Qoe1xuICAgICAgICAgICAgY2FsbDogY29uc3RhbnRzX2pzXzEuUlBDX0NBTExTLmV0aF9nZXRCbG9ja0J5TnVtYmVyLFxuICAgICAgICAgICAgZm9ybWF0dGVyczogW2lucHV0Rm9ybWF0dGVycy5ibG9ja051bWJlclRvSGV4LCBpbnB1dEZvcm1hdHRlcnMucmV0dXJuRnVsbFR4T2JqZWN0UGFyYW1dLFxuICAgICAgICB9KTtcbiAgICAgICAgdGhpcy5nZXRUcmFuc2FjdGlvbkJ5SGFzaCA9IHRoaXMuYnVpbGRSZXF1ZXN0KHtcbiAgICAgICAgICAgIGNhbGw6IGNvbnN0YW50c19qc18xLlJQQ19DQUxMUy5ldGhfZ2V0VHJhbnNhY3Rpb25CeUhhc2gsXG4gICAgICAgIH0pO1xuICAgICAgICB0aGlzLmdldFRyYW5zYWN0aW9uUmVjZWlwdCA9IHRoaXMuYnVpbGRSZXF1ZXN0KHtcbiAgICAgICAgICAgIGNhbGw6IGNvbnN0YW50c19qc18xLlJQQ19DQUxMUy5ldGhfZ2V0VHJhbnNhY3Rpb25SZWNlaXB0LFxuICAgICAgICB9KTtcbiAgICAgICAgdGhpcy5nZXRUcmFuc2FjdGlvbkNvdW50ID0gdGhpcy5idWlsZFJlcXVlc3Qoe1xuICAgICAgICAgICAgY2FsbDogY29uc3RhbnRzX2pzXzEuUlBDX0NBTExTLmV0aF9nZXRUcmFuc2FjdGlvbkNvdW50LFxuICAgICAgICAgICAgZm9ybWF0dGVyczogW251bGwsIGlucHV0Rm9ybWF0dGVycy5kZWZhdWx0QmxvY2tQYXJhbV0sXG4gICAgICAgIH0pO1xuICAgICAgICB0aGlzLmdldEdhc1ByaWNlID0gdGhpcy5idWlsZFJlcXVlc3Qoe1xuICAgICAgICAgICAgY2FsbDogY29uc3RhbnRzX2pzXzEuUlBDX0NBTExTLmV0aF9nYXNQcmljZSxcbiAgICAgICAgfSk7XG4gICAgICAgIHRoaXMuZ2V0RXN0aW1hdGVHYXMgPSAodHJhbnNhY3Rpb24pID0+IHRoaXMuYnVpbGRSZXF1ZXN0KHtcbiAgICAgICAgICAgIGNhbGw6IGNvbnN0YW50c19qc18xLlJQQ19DQUxMUy5ldGhfZXN0aW1hdGVHYXMsXG4gICAgICAgIH0pKFt0cmFuc2FjdGlvbl0pO1xuICAgICAgICB0aGlzLnNldFNhZmVTZXR0aW5ncyA9IHRoaXMuYnVpbGRSZXF1ZXN0KHtcbiAgICAgICAgICAgIGNhbGw6IGNvbnN0YW50c19qc18xLlJQQ19DQUxMUy5zYWZlX3NldFNldHRpbmdzLFxuICAgICAgICB9KTtcbiAgICB9XG4gICAgYnVpbGRSZXF1ZXN0KGFyZ3MpIHtcbiAgICAgICAgY29uc3QgeyBjYWxsLCBmb3JtYXR0ZXJzIH0gPSBhcmdzO1xuICAgICAgICByZXR1cm4gYXN5bmMgKHBhcmFtcykgPT4ge1xuICAgICAgICAgICAgaWYgKGZvcm1hdHRlcnMgJiYgQXJyYXkuaXNBcnJheShwYXJhbXMpKSB7XG4gICAgICAgICAgICAgICAgZm9ybWF0dGVycy5mb3JFYWNoKChmb3JtYXR0ZXIsIGkpID0+IHtcbiAgICAgICAgICAgICAgICAgICAgaWYgKGZvcm1hdHRlcikge1xuICAgICAgICAgICAgICAgICAgICAgICAgcGFyYW1zW2ldID0gZm9ybWF0dGVyKHBhcmFtc1tpXSk7XG4gICAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICB9KTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIGNvbnN0IHBheWxvYWQgPSB7XG4gICAgICAgICAgICAgICAgY2FsbCxcbiAgICAgICAgICAgICAgICBwYXJhbXM6IHBhcmFtcyB8fCBbXSxcbiAgICAgICAgICAgIH07XG4gICAgICAgICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IHRoaXMuY29tbXVuaWNhdG9yLnNlbmQobWV0aG9kc19qc18xLk1ldGhvZHMucnBjQ2FsbCwgcGF5bG9hZCk7XG4gICAgICAgICAgICByZXR1cm4gcmVzcG9uc2UuZGF0YTtcbiAgICAgICAgfTtcbiAgICB9XG59XG5leHBvcnRzLkV0aCA9IEV0aDtcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPWluZGV4LmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@safe-global/safe-apps-sdk/dist/cjs/eth/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@safe-global/safe-apps-sdk/dist/cjs/index.js":
/*!*******************************************************************!*\
  !*** ./node_modules/@safe-global/safe-apps-sdk/dist/cjs/index.js ***!
  \*******************************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

eval("\nvar __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    var desc = Object.getOwnPropertyDescriptor(m, k);\n    if (!desc || (\"get\" in desc ? !m.__esModule : desc.writable || desc.configurable)) {\n      desc = { enumerable: true, get: function() { return m[k]; } };\n    }\n    Object.defineProperty(o, k2, desc);\n}) : (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    o[k2] = m[k];\n}));\nvar __exportStar = (this && this.__exportStar) || function(m, exports) {\n    for (var p in m) if (p !== \"default\" && !Object.prototype.hasOwnProperty.call(exports, p)) __createBinding(exports, m, p);\n};\nvar __importDefault = (this && this.__importDefault) || function (mod) {\n    return (mod && mod.__esModule) ? mod : { \"default\": mod };\n};\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.getSDKVersion = void 0;\nconst sdk_js_1 = __importDefault(__webpack_require__(/*! ./sdk.js */ \"(ssr)/./node_modules/@safe-global/safe-apps-sdk/dist/cjs/sdk.js\"));\nexports[\"default\"] = sdk_js_1.default;\n__exportStar(__webpack_require__(/*! ./sdk.js */ \"(ssr)/./node_modules/@safe-global/safe-apps-sdk/dist/cjs/sdk.js\"), exports);\n__exportStar(__webpack_require__(/*! ./types/index.js */ \"(ssr)/./node_modules/@safe-global/safe-apps-sdk/dist/cjs/types/index.js\"), exports);\n__exportStar(__webpack_require__(/*! ./communication/methods.js */ \"(ssr)/./node_modules/@safe-global/safe-apps-sdk/dist/cjs/communication/methods.js\"), exports);\n__exportStar(__webpack_require__(/*! ./communication/messageFormatter.js */ \"(ssr)/./node_modules/@safe-global/safe-apps-sdk/dist/cjs/communication/messageFormatter.js\"), exports);\nvar version_js_1 = __webpack_require__(/*! ./version.js */ \"(ssr)/./node_modules/@safe-global/safe-apps-sdk/dist/cjs/version.js\");\nObject.defineProperty(exports, \"getSDKVersion\", ({ enumerable: true, get: function () { return version_js_1.getSDKVersion; } }));\n__exportStar(__webpack_require__(/*! ./eth/constants.js */ \"(ssr)/./node_modules/@safe-global/safe-apps-sdk/dist/cjs/eth/constants.js\"), exports);\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@safe-global/safe-apps-sdk/dist/cjs/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@safe-global/safe-apps-sdk/dist/cjs/safe/index.js":
/*!************************************************************************!*\
  !*** ./node_modules/@safe-global/safe-apps-sdk/dist/cjs/safe/index.js ***!
  \************************************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

eval("\nvar __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {\n    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;\n    if (typeof Reflect === \"object\" && typeof Reflect.decorate === \"function\") r = Reflect.decorate(decorators, target, key, desc);\n    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;\n    return c > 3 && r && Object.defineProperty(target, key, r), r;\n};\nvar __importDefault = (this && this.__importDefault) || function (mod) {\n    return (mod && mod.__esModule) ? mod : { \"default\": mod };\n};\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.Safe = void 0;\nconst viem_1 = __webpack_require__(/*! viem */ \"(ssr)/./node_modules/viem/_cjs/index.js\");\nconst signatures_js_1 = __webpack_require__(/*! ./signatures.js */ \"(ssr)/./node_modules/@safe-global/safe-apps-sdk/dist/cjs/safe/signatures.js\");\nconst methods_js_1 = __webpack_require__(/*! ../communication/methods.js */ \"(ssr)/./node_modules/@safe-global/safe-apps-sdk/dist/cjs/communication/methods.js\");\nconst constants_js_1 = __webpack_require__(/*! ../eth/constants.js */ \"(ssr)/./node_modules/@safe-global/safe-apps-sdk/dist/cjs/eth/constants.js\");\nconst index_js_1 = __webpack_require__(/*! ../types/index.js */ \"(ssr)/./node_modules/@safe-global/safe-apps-sdk/dist/cjs/types/index.js\");\nconst requirePermissions_js_1 = __importDefault(__webpack_require__(/*! ../decorators/requirePermissions.js */ \"(ssr)/./node_modules/@safe-global/safe-apps-sdk/dist/cjs/decorators/requirePermissions.js\"));\nclass Safe {\n    constructor(communicator) {\n        this.communicator = communicator;\n    }\n    async getChainInfo() {\n        const response = await this.communicator.send(methods_js_1.Methods.getChainInfo, undefined);\n        return response.data;\n    }\n    async getInfo() {\n        const response = await this.communicator.send(methods_js_1.Methods.getSafeInfo, undefined);\n        return response.data;\n    }\n    // There is a possibility that this method will change because we may add pagination to the endpoint\n    async experimental_getBalances({ currency = 'usd' } = {}) {\n        const response = await this.communicator.send(methods_js_1.Methods.getSafeBalances, {\n            currency,\n        });\n        return response.data;\n    }\n    async check1271Signature(messageHash, signature = '0x') {\n        const safeInfo = await this.getInfo();\n        const encodedIsValidSignatureCall = (0, viem_1.encodeFunctionData)({\n            abi: [\n                {\n                    constant: false,\n                    inputs: [\n                        {\n                            name: '_dataHash',\n                            type: 'bytes32',\n                        },\n                        {\n                            name: '_signature',\n                            type: 'bytes',\n                        },\n                    ],\n                    name: 'isValidSignature',\n                    outputs: [\n                        {\n                            name: '',\n                            type: 'bytes4',\n                        },\n                    ],\n                    payable: false,\n                    stateMutability: 'nonpayable',\n                    type: 'function',\n                },\n            ],\n            functionName: 'isValidSignature',\n            args: [messageHash, signature],\n        });\n        const payload = {\n            call: constants_js_1.RPC_CALLS.eth_call,\n            params: [\n                {\n                    to: safeInfo.safeAddress,\n                    data: encodedIsValidSignatureCall,\n                },\n                'latest',\n            ],\n        };\n        try {\n            const response = await this.communicator.send(methods_js_1.Methods.rpcCall, payload);\n            return response.data.slice(0, 10).toLowerCase() === signatures_js_1.MAGIC_VALUE;\n        }\n        catch (err) {\n            return false;\n        }\n    }\n    async check1271SignatureBytes(messageHash, signature = '0x') {\n        const safeInfo = await this.getInfo();\n        const encodedIsValidSignatureCall = (0, viem_1.encodeFunctionData)({\n            abi: [\n                {\n                    constant: false,\n                    inputs: [\n                        {\n                            name: '_data',\n                            type: 'bytes',\n                        },\n                        {\n                            name: '_signature',\n                            type: 'bytes',\n                        },\n                    ],\n                    name: 'isValidSignature',\n                    outputs: [\n                        {\n                            name: '',\n                            type: 'bytes4',\n                        },\n                    ],\n                    payable: false,\n                    stateMutability: 'nonpayable',\n                    type: 'function',\n                },\n            ],\n            functionName: 'isValidSignature',\n            args: [messageHash, signature],\n        });\n        const payload = {\n            call: constants_js_1.RPC_CALLS.eth_call,\n            params: [\n                {\n                    to: safeInfo.safeAddress,\n                    data: encodedIsValidSignatureCall,\n                },\n                'latest',\n            ],\n        };\n        try {\n            const response = await this.communicator.send(methods_js_1.Methods.rpcCall, payload);\n            return response.data.slice(0, 10).toLowerCase() === signatures_js_1.MAGIC_VALUE_BYTES;\n        }\n        catch (err) {\n            return false;\n        }\n    }\n    calculateMessageHash(message) {\n        return (0, viem_1.hashMessage)(message);\n    }\n    calculateTypedMessageHash(typedMessage) {\n        const chainId = typeof typedMessage.domain.chainId === 'object'\n            ? typedMessage.domain.chainId.toNumber()\n            : Number(typedMessage.domain.chainId);\n        let primaryType = typedMessage.primaryType;\n        if (!primaryType) {\n            const fields = Object.values(typedMessage.types);\n            // We try to infer primaryType (simplified ether's version)\n            const primaryTypes = Object.keys(typedMessage.types).filter((typeName) => fields.every((dataTypes) => dataTypes.every(({ type }) => type.replace('[', '').replace(']', '') !== typeName)));\n            if (primaryTypes.length === 0 || primaryTypes.length > 1)\n                throw new Error('Please specify primaryType');\n            primaryType = primaryTypes[0];\n        }\n        return (0, viem_1.hashTypedData)({\n            message: typedMessage.message,\n            domain: {\n                ...typedMessage.domain,\n                chainId,\n                verifyingContract: typedMessage.domain.verifyingContract,\n                salt: typedMessage.domain.salt,\n            },\n            types: typedMessage.types,\n            primaryType,\n        });\n    }\n    async getOffChainSignature(messageHash) {\n        const response = await this.communicator.send(methods_js_1.Methods.getOffChainSignature, messageHash);\n        return response.data;\n    }\n    async isMessageSigned(message, signature = '0x') {\n        let check;\n        if (typeof message === 'string') {\n            check = async () => {\n                const messageHash = this.calculateMessageHash(message);\n                const messageHashSigned = await this.isMessageHashSigned(messageHash, signature);\n                return messageHashSigned;\n            };\n        }\n        if ((0, index_js_1.isObjectEIP712TypedData)(message)) {\n            check = async () => {\n                const messageHash = this.calculateTypedMessageHash(message);\n                const messageHashSigned = await this.isMessageHashSigned(messageHash, signature);\n                return messageHashSigned;\n            };\n        }\n        if (check) {\n            const isValid = await check();\n            return isValid;\n        }\n        throw new Error('Invalid message type');\n    }\n    async isMessageHashSigned(messageHash, signature = '0x') {\n        const checks = [this.check1271Signature.bind(this), this.check1271SignatureBytes.bind(this)];\n        for (const check of checks) {\n            const isValid = await check(messageHash, signature);\n            if (isValid) {\n                return true;\n            }\n        }\n        return false;\n    }\n    async getEnvironmentInfo() {\n        const response = await this.communicator.send(methods_js_1.Methods.getEnvironmentInfo, undefined);\n        return response.data;\n    }\n    async requestAddressBook() {\n        const response = await this.communicator.send(methods_js_1.Methods.requestAddressBook, undefined);\n        return response.data;\n    }\n}\nexports.Safe = Safe;\n__decorate([\n    (0, requirePermissions_js_1.default)()\n], Safe.prototype, \"requestAddressBook\", null);\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@safe-global/safe-apps-sdk/dist/cjs/safe/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@safe-global/safe-apps-sdk/dist/cjs/safe/signatures.js":
/*!*****************************************************************************!*\
  !*** ./node_modules/@safe-global/safe-apps-sdk/dist/cjs/safe/signatures.js ***!
  \*****************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.MAGIC_VALUE_BYTES = exports.MAGIC_VALUE = void 0;\nconst MAGIC_VALUE = '0x1626ba7e';\nexports.MAGIC_VALUE = MAGIC_VALUE;\nconst MAGIC_VALUE_BYTES = '0x20c13b0b';\nexports.MAGIC_VALUE_BYTES = MAGIC_VALUE_BYTES;\n//# sourceMappingURL=signatures.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHNhZmUtZ2xvYmFsL3NhZmUtYXBwcy1zZGsvZGlzdC9janMvc2FmZS9zaWduYXR1cmVzLmpzIiwibWFwcGluZ3MiOiJBQUFhO0FBQ2IsOENBQTZDLEVBQUUsYUFBYSxFQUFDO0FBQzdELHlCQUF5QixHQUFHLG1CQUFtQjtBQUMvQztBQUNBLG1CQUFtQjtBQUNuQjtBQUNBLHlCQUF5QjtBQUN6QiIsInNvdXJjZXMiOlsiRjpcXEFJIGNoYWluXFxjaGFpbnNpZ2h0LXByb2R1Y3Rpb25cXG5vZGVfbW9kdWxlc1xcQHNhZmUtZ2xvYmFsXFxzYWZlLWFwcHMtc2RrXFxkaXN0XFxjanNcXHNhZmVcXHNpZ25hdHVyZXMuanMiXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2Ugc3RyaWN0XCI7XG5PYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgXCJfX2VzTW9kdWxlXCIsIHsgdmFsdWU6IHRydWUgfSk7XG5leHBvcnRzLk1BR0lDX1ZBTFVFX0JZVEVTID0gZXhwb3J0cy5NQUdJQ19WQUxVRSA9IHZvaWQgMDtcbmNvbnN0IE1BR0lDX1ZBTFVFID0gJzB4MTYyNmJhN2UnO1xuZXhwb3J0cy5NQUdJQ19WQUxVRSA9IE1BR0lDX1ZBTFVFO1xuY29uc3QgTUFHSUNfVkFMVUVfQllURVMgPSAnMHgyMGMxM2IwYic7XG5leHBvcnRzLk1BR0lDX1ZBTFVFX0JZVEVTID0gTUFHSUNfVkFMVUVfQllURVM7XG4vLyMgc291cmNlTWFwcGluZ1VSTD1zaWduYXR1cmVzLmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@safe-global/safe-apps-sdk/dist/cjs/safe/signatures.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@safe-global/safe-apps-sdk/dist/cjs/sdk.js":
/*!*****************************************************************!*\
  !*** ./node_modules/@safe-global/safe-apps-sdk/dist/cjs/sdk.js ***!
  \*****************************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

eval("\nvar __importDefault = (this && this.__importDefault) || function (mod) {\n    return (mod && mod.__esModule) ? mod : { \"default\": mod };\n};\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nconst index_js_1 = __importDefault(__webpack_require__(/*! ./communication/index.js */ \"(ssr)/./node_modules/@safe-global/safe-apps-sdk/dist/cjs/communication/index.js\"));\nconst index_js_2 = __webpack_require__(/*! ./txs/index.js */ \"(ssr)/./node_modules/@safe-global/safe-apps-sdk/dist/cjs/txs/index.js\");\nconst index_js_3 = __webpack_require__(/*! ./eth/index.js */ \"(ssr)/./node_modules/@safe-global/safe-apps-sdk/dist/cjs/eth/index.js\");\nconst index_js_4 = __webpack_require__(/*! ./safe/index.js */ \"(ssr)/./node_modules/@safe-global/safe-apps-sdk/dist/cjs/safe/index.js\");\nconst index_js_5 = __webpack_require__(/*! ./wallet/index.js */ \"(ssr)/./node_modules/@safe-global/safe-apps-sdk/dist/cjs/wallet/index.js\");\nclass SafeAppsSDK {\n    constructor(opts = {}) {\n        const { allowedDomains = null, debug = false } = opts;\n        this.communicator = new index_js_1.default(allowedDomains, debug);\n        this.eth = new index_js_3.Eth(this.communicator);\n        this.txs = new index_js_2.TXs(this.communicator);\n        this.safe = new index_js_4.Safe(this.communicator);\n        this.wallet = new index_js_5.Wallet(this.communicator);\n    }\n}\nexports[\"default\"] = SafeAppsSDK;\n//# sourceMappingURL=sdk.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@safe-global/safe-apps-sdk/dist/cjs/sdk.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@safe-global/safe-apps-sdk/dist/cjs/txs/index.js":
/*!***********************************************************************!*\
  !*** ./node_modules/@safe-global/safe-apps-sdk/dist/cjs/txs/index.js ***!
  \***********************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.TXs = void 0;\nconst methods_js_1 = __webpack_require__(/*! ../communication/methods.js */ \"(ssr)/./node_modules/@safe-global/safe-apps-sdk/dist/cjs/communication/methods.js\");\nconst index_js_1 = __webpack_require__(/*! ../types/index.js */ \"(ssr)/./node_modules/@safe-global/safe-apps-sdk/dist/cjs/types/index.js\");\nclass TXs {\n    constructor(communicator) {\n        this.communicator = communicator;\n    }\n    async getBySafeTxHash(safeTxHash) {\n        if (!safeTxHash) {\n            throw new Error('Invalid safeTxHash');\n        }\n        const response = await this.communicator.send(methods_js_1.Methods.getTxBySafeTxHash, { safeTxHash });\n        return response.data;\n    }\n    async signMessage(message) {\n        const messagePayload = {\n            message,\n        };\n        const response = await this.communicator.send(methods_js_1.Methods.signMessage, messagePayload);\n        return response.data;\n    }\n    async signTypedMessage(typedData) {\n        if (!(0, index_js_1.isObjectEIP712TypedData)(typedData)) {\n            throw new Error('Invalid typed data');\n        }\n        const response = await this.communicator.send(methods_js_1.Methods.signTypedMessage, { typedData });\n        return response.data;\n    }\n    async send({ txs, params }) {\n        if (!txs || !txs.length) {\n            throw new Error('No transactions were passed');\n        }\n        const messagePayload = {\n            txs,\n            params,\n        };\n        const response = await this.communicator.send(methods_js_1.Methods.sendTransactions, messagePayload);\n        return response.data;\n    }\n}\nexports.TXs = TXs;\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@safe-global/safe-apps-sdk/dist/cjs/txs/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@safe-global/safe-apps-sdk/dist/cjs/types/gateway.js":
/*!***************************************************************************!*\
  !*** ./node_modules/@safe-global/safe-apps-sdk/dist/cjs/types/gateway.js ***!
  \***************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.TransferDirection = exports.TransactionStatus = exports.TokenType = exports.Operation = void 0;\nvar safe_gateway_typescript_sdk_1 = __webpack_require__(/*! @safe-global/safe-gateway-typescript-sdk */ \"(ssr)/./node_modules/@safe-global/safe-gateway-typescript-sdk/dist/index.js\");\nObject.defineProperty(exports, \"Operation\", ({ enumerable: true, get: function () { return safe_gateway_typescript_sdk_1.Operation; } }));\nObject.defineProperty(exports, \"TokenType\", ({ enumerable: true, get: function () { return safe_gateway_typescript_sdk_1.TokenType; } }));\nObject.defineProperty(exports, \"TransactionStatus\", ({ enumerable: true, get: function () { return safe_gateway_typescript_sdk_1.TransactionStatus; } }));\nObject.defineProperty(exports, \"TransferDirection\", ({ enumerable: true, get: function () { return safe_gateway_typescript_sdk_1.TransferDirection; } }));\n//# sourceMappingURL=gateway.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHNhZmUtZ2xvYmFsL3NhZmUtYXBwcy1zZGsvZGlzdC9janMvdHlwZXMvZ2F0ZXdheS5qcyIsIm1hcHBpbmdzIjoiQUFBYTtBQUNiLDhDQUE2QyxFQUFFLGFBQWEsRUFBQztBQUM3RCx5QkFBeUIsR0FBRyx5QkFBeUIsR0FBRyxpQkFBaUIsR0FBRyxpQkFBaUI7QUFDN0Ysb0NBQW9DLG1CQUFPLENBQUMsNkhBQTBDO0FBQ3RGLDZDQUE0QyxFQUFFLHFDQUFxQyxtREFBbUQsRUFBQztBQUN2SSw2Q0FBNEMsRUFBRSxxQ0FBcUMsbURBQW1ELEVBQUM7QUFDdkkscURBQW9ELEVBQUUscUNBQXFDLDJEQUEyRCxFQUFDO0FBQ3ZKLHFEQUFvRCxFQUFFLHFDQUFxQywyREFBMkQsRUFBQztBQUN2SiIsInNvdXJjZXMiOlsiRjpcXEFJIGNoYWluXFxjaGFpbnNpZ2h0LXByb2R1Y3Rpb25cXG5vZGVfbW9kdWxlc1xcQHNhZmUtZ2xvYmFsXFxzYWZlLWFwcHMtc2RrXFxkaXN0XFxjanNcXHR5cGVzXFxnYXRld2F5LmpzIl0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIHN0cmljdFwiO1xuT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsIFwiX19lc01vZHVsZVwiLCB7IHZhbHVlOiB0cnVlIH0pO1xuZXhwb3J0cy5UcmFuc2ZlckRpcmVjdGlvbiA9IGV4cG9ydHMuVHJhbnNhY3Rpb25TdGF0dXMgPSBleHBvcnRzLlRva2VuVHlwZSA9IGV4cG9ydHMuT3BlcmF0aW9uID0gdm9pZCAwO1xudmFyIHNhZmVfZ2F0ZXdheV90eXBlc2NyaXB0X3Nka18xID0gcmVxdWlyZShcIkBzYWZlLWdsb2JhbC9zYWZlLWdhdGV3YXktdHlwZXNjcmlwdC1zZGtcIik7XG5PYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgXCJPcGVyYXRpb25cIiwgeyBlbnVtZXJhYmxlOiB0cnVlLCBnZXQ6IGZ1bmN0aW9uICgpIHsgcmV0dXJuIHNhZmVfZ2F0ZXdheV90eXBlc2NyaXB0X3Nka18xLk9wZXJhdGlvbjsgfSB9KTtcbk9iamVjdC5kZWZpbmVQcm9wZXJ0eShleHBvcnRzLCBcIlRva2VuVHlwZVwiLCB7IGVudW1lcmFibGU6IHRydWUsIGdldDogZnVuY3Rpb24gKCkgeyByZXR1cm4gc2FmZV9nYXRld2F5X3R5cGVzY3JpcHRfc2RrXzEuVG9rZW5UeXBlOyB9IH0pO1xuT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsIFwiVHJhbnNhY3Rpb25TdGF0dXNcIiwgeyBlbnVtZXJhYmxlOiB0cnVlLCBnZXQ6IGZ1bmN0aW9uICgpIHsgcmV0dXJuIHNhZmVfZ2F0ZXdheV90eXBlc2NyaXB0X3Nka18xLlRyYW5zYWN0aW9uU3RhdHVzOyB9IH0pO1xuT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsIFwiVHJhbnNmZXJEaXJlY3Rpb25cIiwgeyBlbnVtZXJhYmxlOiB0cnVlLCBnZXQ6IGZ1bmN0aW9uICgpIHsgcmV0dXJuIHNhZmVfZ2F0ZXdheV90eXBlc2NyaXB0X3Nka18xLlRyYW5zZmVyRGlyZWN0aW9uOyB9IH0pO1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9Z2F0ZXdheS5qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@safe-global/safe-apps-sdk/dist/cjs/types/gateway.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@safe-global/safe-apps-sdk/dist/cjs/types/index.js":
/*!*************************************************************************!*\
  !*** ./node_modules/@safe-global/safe-apps-sdk/dist/cjs/types/index.js ***!
  \*************************************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

eval("\nvar __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    var desc = Object.getOwnPropertyDescriptor(m, k);\n    if (!desc || (\"get\" in desc ? !m.__esModule : desc.writable || desc.configurable)) {\n      desc = { enumerable: true, get: function() { return m[k]; } };\n    }\n    Object.defineProperty(o, k2, desc);\n}) : (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    o[k2] = m[k];\n}));\nvar __exportStar = (this && this.__exportStar) || function(m, exports) {\n    for (var p in m) if (p !== \"default\" && !Object.prototype.hasOwnProperty.call(exports, p)) __createBinding(exports, m, p);\n};\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\n__exportStar(__webpack_require__(/*! ./sdk.js */ \"(ssr)/./node_modules/@safe-global/safe-apps-sdk/dist/cjs/types/sdk.js\"), exports);\n__exportStar(__webpack_require__(/*! ./rpc.js */ \"(ssr)/./node_modules/@safe-global/safe-apps-sdk/dist/cjs/types/rpc.js\"), exports);\n__exportStar(__webpack_require__(/*! ./gateway.js */ \"(ssr)/./node_modules/@safe-global/safe-apps-sdk/dist/cjs/types/gateway.js\"), exports);\n__exportStar(__webpack_require__(/*! ./messaging.js */ \"(ssr)/./node_modules/@safe-global/safe-apps-sdk/dist/cjs/types/messaging.js\"), exports);\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@safe-global/safe-apps-sdk/dist/cjs/types/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@safe-global/safe-apps-sdk/dist/cjs/types/messaging.js":
/*!*****************************************************************************!*\
  !*** ./node_modules/@safe-global/safe-apps-sdk/dist/cjs/types/messaging.js ***!
  \*****************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nconst methods_js_1 = __webpack_require__(/*! ../communication/methods.js */ \"(ssr)/./node_modules/@safe-global/safe-apps-sdk/dist/cjs/communication/methods.js\");\n//# sourceMappingURL=messaging.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHNhZmUtZ2xvYmFsL3NhZmUtYXBwcy1zZGsvZGlzdC9janMvdHlwZXMvbWVzc2FnaW5nLmpzIiwibWFwcGluZ3MiOiJBQUFhO0FBQ2IsOENBQTZDLEVBQUUsYUFBYSxFQUFDO0FBQzdELHFCQUFxQixtQkFBTyxDQUFDLHNIQUE2QjtBQUMxRCIsInNvdXJjZXMiOlsiRjpcXEFJIGNoYWluXFxjaGFpbnNpZ2h0LXByb2R1Y3Rpb25cXG5vZGVfbW9kdWxlc1xcQHNhZmUtZ2xvYmFsXFxzYWZlLWFwcHMtc2RrXFxkaXN0XFxjanNcXHR5cGVzXFxtZXNzYWdpbmcuanMiXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2Ugc3RyaWN0XCI7XG5PYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgXCJfX2VzTW9kdWxlXCIsIHsgdmFsdWU6IHRydWUgfSk7XG5jb25zdCBtZXRob2RzX2pzXzEgPSByZXF1aXJlKFwiLi4vY29tbXVuaWNhdGlvbi9tZXRob2RzLmpzXCIpO1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9bWVzc2FnaW5nLmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@safe-global/safe-apps-sdk/dist/cjs/types/messaging.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@safe-global/safe-apps-sdk/dist/cjs/types/permissions.js":
/*!*******************************************************************************!*\
  !*** ./node_modules/@safe-global/safe-apps-sdk/dist/cjs/types/permissions.js ***!
  \*******************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.PermissionsError = exports.PERMISSIONS_REQUEST_REJECTED = void 0;\nexports.PERMISSIONS_REQUEST_REJECTED = 4001;\nclass PermissionsError extends Error {\n    constructor(message, code, data) {\n        super(message);\n        this.code = code;\n        this.data = data;\n        // Should adjust prototype manually because how TS handles the type extension compilation\n        // https://github.com/Microsoft/TypeScript/wiki/Breaking-Changes#extending-built-ins-like-error-array-and-map-may-no-longer-work\n        Object.setPrototypeOf(this, PermissionsError.prototype);\n    }\n}\nexports.PermissionsError = PermissionsError;\n//# sourceMappingURL=permissions.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHNhZmUtZ2xvYmFsL3NhZmUtYXBwcy1zZGsvZGlzdC9janMvdHlwZXMvcGVybWlzc2lvbnMuanMiLCJtYXBwaW5ncyI6IkFBQWE7QUFDYiw4Q0FBNkMsRUFBRSxhQUFhLEVBQUM7QUFDN0Qsd0JBQXdCLEdBQUcsb0NBQW9DO0FBQy9ELG9DQUFvQztBQUNwQztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHdCQUF3QjtBQUN4QiIsInNvdXJjZXMiOlsiRjpcXEFJIGNoYWluXFxjaGFpbnNpZ2h0LXByb2R1Y3Rpb25cXG5vZGVfbW9kdWxlc1xcQHNhZmUtZ2xvYmFsXFxzYWZlLWFwcHMtc2RrXFxkaXN0XFxjanNcXHR5cGVzXFxwZXJtaXNzaW9ucy5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBzdHJpY3RcIjtcbk9iamVjdC5kZWZpbmVQcm9wZXJ0eShleHBvcnRzLCBcIl9fZXNNb2R1bGVcIiwgeyB2YWx1ZTogdHJ1ZSB9KTtcbmV4cG9ydHMuUGVybWlzc2lvbnNFcnJvciA9IGV4cG9ydHMuUEVSTUlTU0lPTlNfUkVRVUVTVF9SRUpFQ1RFRCA9IHZvaWQgMDtcbmV4cG9ydHMuUEVSTUlTU0lPTlNfUkVRVUVTVF9SRUpFQ1RFRCA9IDQwMDE7XG5jbGFzcyBQZXJtaXNzaW9uc0Vycm9yIGV4dGVuZHMgRXJyb3Ige1xuICAgIGNvbnN0cnVjdG9yKG1lc3NhZ2UsIGNvZGUsIGRhdGEpIHtcbiAgICAgICAgc3VwZXIobWVzc2FnZSk7XG4gICAgICAgIHRoaXMuY29kZSA9IGNvZGU7XG4gICAgICAgIHRoaXMuZGF0YSA9IGRhdGE7XG4gICAgICAgIC8vIFNob3VsZCBhZGp1c3QgcHJvdG90eXBlIG1hbnVhbGx5IGJlY2F1c2UgaG93IFRTIGhhbmRsZXMgdGhlIHR5cGUgZXh0ZW5zaW9uIGNvbXBpbGF0aW9uXG4gICAgICAgIC8vIGh0dHBzOi8vZ2l0aHViLmNvbS9NaWNyb3NvZnQvVHlwZVNjcmlwdC93aWtpL0JyZWFraW5nLUNoYW5nZXMjZXh0ZW5kaW5nLWJ1aWx0LWlucy1saWtlLWVycm9yLWFycmF5LWFuZC1tYXAtbWF5LW5vLWxvbmdlci13b3JrXG4gICAgICAgIE9iamVjdC5zZXRQcm90b3R5cGVPZih0aGlzLCBQZXJtaXNzaW9uc0Vycm9yLnByb3RvdHlwZSk7XG4gICAgfVxufVxuZXhwb3J0cy5QZXJtaXNzaW9uc0Vycm9yID0gUGVybWlzc2lvbnNFcnJvcjtcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPXBlcm1pc3Npb25zLmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@safe-global/safe-apps-sdk/dist/cjs/types/permissions.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@safe-global/safe-apps-sdk/dist/cjs/types/rpc.js":
/*!***********************************************************************!*\
  !*** ./node_modules/@safe-global/safe-apps-sdk/dist/cjs/types/rpc.js ***!
  \***********************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\n//# sourceMappingURL=rpc.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHNhZmUtZ2xvYmFsL3NhZmUtYXBwcy1zZGsvZGlzdC9janMvdHlwZXMvcnBjLmpzIiwibWFwcGluZ3MiOiJBQUFhO0FBQ2IsOENBQTZDLEVBQUUsYUFBYSxFQUFDO0FBQzdEIiwic291cmNlcyI6WyJGOlxcQUkgY2hhaW5cXGNoYWluc2lnaHQtcHJvZHVjdGlvblxcbm9kZV9tb2R1bGVzXFxAc2FmZS1nbG9iYWxcXHNhZmUtYXBwcy1zZGtcXGRpc3RcXGNqc1xcdHlwZXNcXHJwYy5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBzdHJpY3RcIjtcbk9iamVjdC5kZWZpbmVQcm9wZXJ0eShleHBvcnRzLCBcIl9fZXNNb2R1bGVcIiwgeyB2YWx1ZTogdHJ1ZSB9KTtcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPXJwYy5qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@safe-global/safe-apps-sdk/dist/cjs/types/rpc.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@safe-global/safe-apps-sdk/dist/cjs/types/sdk.js":
/*!***********************************************************************!*\
  !*** ./node_modules/@safe-global/safe-apps-sdk/dist/cjs/types/sdk.js ***!
  \***********************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.isObjectEIP712TypedData = void 0;\nconst isObjectEIP712TypedData = (obj) => {\n    return typeof obj === 'object' && obj != null && 'domain' in obj && 'types' in obj && 'message' in obj;\n};\nexports.isObjectEIP712TypedData = isObjectEIP712TypedData;\n//# sourceMappingURL=sdk.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHNhZmUtZ2xvYmFsL3NhZmUtYXBwcy1zZGsvZGlzdC9janMvdHlwZXMvc2RrLmpzIiwibWFwcGluZ3MiOiJBQUFhO0FBQ2IsOENBQTZDLEVBQUUsYUFBYSxFQUFDO0FBQzdELCtCQUErQjtBQUMvQjtBQUNBO0FBQ0E7QUFDQSwrQkFBK0I7QUFDL0IiLCJzb3VyY2VzIjpbIkY6XFxBSSBjaGFpblxcY2hhaW5zaWdodC1wcm9kdWN0aW9uXFxub2RlX21vZHVsZXNcXEBzYWZlLWdsb2JhbFxcc2FmZS1hcHBzLXNka1xcZGlzdFxcY2pzXFx0eXBlc1xcc2RrLmpzIl0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIHN0cmljdFwiO1xuT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsIFwiX19lc01vZHVsZVwiLCB7IHZhbHVlOiB0cnVlIH0pO1xuZXhwb3J0cy5pc09iamVjdEVJUDcxMlR5cGVkRGF0YSA9IHZvaWQgMDtcbmNvbnN0IGlzT2JqZWN0RUlQNzEyVHlwZWREYXRhID0gKG9iaikgPT4ge1xuICAgIHJldHVybiB0eXBlb2Ygb2JqID09PSAnb2JqZWN0JyAmJiBvYmogIT0gbnVsbCAmJiAnZG9tYWluJyBpbiBvYmogJiYgJ3R5cGVzJyBpbiBvYmogJiYgJ21lc3NhZ2UnIGluIG9iajtcbn07XG5leHBvcnRzLmlzT2JqZWN0RUlQNzEyVHlwZWREYXRhID0gaXNPYmplY3RFSVA3MTJUeXBlZERhdGE7XG4vLyMgc291cmNlTWFwcGluZ1VSTD1zZGsuanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@safe-global/safe-apps-sdk/dist/cjs/types/sdk.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@safe-global/safe-apps-sdk/dist/cjs/version.js":
/*!*********************************************************************!*\
  !*** ./node_modules/@safe-global/safe-apps-sdk/dist/cjs/version.js ***!
  \*********************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.getSDKVersion = void 0;\nconst getSDKVersion = () => '9.1.0';\nexports.getSDKVersion = getSDKVersion;\n//# sourceMappingURL=version.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHNhZmUtZ2xvYmFsL3NhZmUtYXBwcy1zZGsvZGlzdC9janMvdmVyc2lvbi5qcyIsIm1hcHBpbmdzIjoiQUFBYTtBQUNiLDhDQUE2QyxFQUFFLGFBQWEsRUFBQztBQUM3RCxxQkFBcUI7QUFDckI7QUFDQSxxQkFBcUI7QUFDckIiLCJzb3VyY2VzIjpbIkY6XFxBSSBjaGFpblxcY2hhaW5zaWdodC1wcm9kdWN0aW9uXFxub2RlX21vZHVsZXNcXEBzYWZlLWdsb2JhbFxcc2FmZS1hcHBzLXNka1xcZGlzdFxcY2pzXFx2ZXJzaW9uLmpzIl0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIHN0cmljdFwiO1xuT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsIFwiX19lc01vZHVsZVwiLCB7IHZhbHVlOiB0cnVlIH0pO1xuZXhwb3J0cy5nZXRTREtWZXJzaW9uID0gdm9pZCAwO1xuY29uc3QgZ2V0U0RLVmVyc2lvbiA9ICgpID0+ICc5LjEuMCc7XG5leHBvcnRzLmdldFNES1ZlcnNpb24gPSBnZXRTREtWZXJzaW9uO1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9dmVyc2lvbi5qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@safe-global/safe-apps-sdk/dist/cjs/version.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@safe-global/safe-apps-sdk/dist/cjs/wallet/index.js":
/*!**************************************************************************!*\
  !*** ./node_modules/@safe-global/safe-apps-sdk/dist/cjs/wallet/index.js ***!
  \**************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.Wallet = void 0;\nconst methods_js_1 = __webpack_require__(/*! ../communication/methods.js */ \"(ssr)/./node_modules/@safe-global/safe-apps-sdk/dist/cjs/communication/methods.js\");\nconst permissions_js_1 = __webpack_require__(/*! ../types/permissions.js */ \"(ssr)/./node_modules/@safe-global/safe-apps-sdk/dist/cjs/types/permissions.js\");\nclass Wallet {\n    constructor(communicator) {\n        this.communicator = communicator;\n    }\n    async getPermissions() {\n        const response = await this.communicator.send(methods_js_1.Methods.wallet_getPermissions, undefined);\n        return response.data;\n    }\n    async requestPermissions(permissions) {\n        if (!this.isPermissionRequestValid(permissions)) {\n            throw new permissions_js_1.PermissionsError('Permissions request is invalid', permissions_js_1.PERMISSIONS_REQUEST_REJECTED);\n        }\n        try {\n            const response = await this.communicator.send(methods_js_1.Methods.wallet_requestPermissions, permissions);\n            return response.data;\n        }\n        catch {\n            throw new permissions_js_1.PermissionsError('Permissions rejected', permissions_js_1.PERMISSIONS_REQUEST_REJECTED);\n        }\n    }\n    isPermissionRequestValid(permissions) {\n        return permissions.every((pr) => {\n            if (typeof pr === 'object') {\n                return Object.keys(pr).every((method) => {\n                    if (Object.values(methods_js_1.RestrictedMethods).includes(method)) {\n                        return true;\n                    }\n                    return false;\n                });\n            }\n            return false;\n        });\n    }\n}\nexports.Wallet = Wallet;\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@safe-global/safe-apps-sdk/dist/cjs/wallet/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@safe-global/safe-apps-sdk/dist/esm/communication/index.js":
/*!*********************************************************************************!*\
  !*** ./node_modules/@safe-global/safe-apps-sdk/dist/esm/communication/index.js ***!
  \*********************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Methods: () => (/* reexport safe */ _methods_js__WEBPACK_IMPORTED_MODULE_1__.Methods),\n/* harmony export */   RestrictedMethods: () => (/* reexport safe */ _methods_js__WEBPACK_IMPORTED_MODULE_1__.RestrictedMethods),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _messageFormatter_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./messageFormatter.js */ \"(ssr)/./node_modules/@safe-global/safe-apps-sdk/dist/esm/communication/messageFormatter.js\");\n/* harmony import */ var _methods_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./methods.js */ \"(ssr)/./node_modules/@safe-global/safe-apps-sdk/dist/esm/communication/methods.js\");\n\nclass PostMessageCommunicator {\n    constructor(allowedOrigins = null, debugMode = false) {\n        this.allowedOrigins = null;\n        this.callbacks = new Map();\n        this.debugMode = false;\n        this.isServer = typeof window === 'undefined';\n        this.isValidMessage = ({ origin, data, source }) => {\n            const emptyOrMalformed = !data;\n            const sentFromParentEl = !this.isServer && source === window.parent;\n            const majorVersionNumber = typeof data.version !== 'undefined' && parseInt(data.version.split('.')[0]);\n            const allowedSDKVersion = typeof majorVersionNumber === 'number' && majorVersionNumber >= 1;\n            let validOrigin = true;\n            if (Array.isArray(this.allowedOrigins)) {\n                validOrigin = this.allowedOrigins.find((regExp) => regExp.test(origin)) !== undefined;\n            }\n            return !emptyOrMalformed && sentFromParentEl && allowedSDKVersion && validOrigin;\n        };\n        this.logIncomingMessage = (msg) => {\n            console.info(`Safe Apps SDK v1: A message was received from origin ${msg.origin}. `, msg.data);\n        };\n        this.onParentMessage = (msg) => {\n            if (this.isValidMessage(msg)) {\n                this.debugMode && this.logIncomingMessage(msg);\n                this.handleIncomingMessage(msg.data);\n            }\n        };\n        this.handleIncomingMessage = (payload) => {\n            const { id } = payload;\n            const cb = this.callbacks.get(id);\n            if (cb) {\n                cb(payload);\n                this.callbacks.delete(id);\n            }\n        };\n        this.send = (method, params) => {\n            const request = _messageFormatter_js__WEBPACK_IMPORTED_MODULE_0__.MessageFormatter.makeRequest(method, params);\n            if (this.isServer) {\n                throw new Error(\"Window doesn't exist\");\n            }\n            window.parent.postMessage(request, '*');\n            return new Promise((resolve, reject) => {\n                this.callbacks.set(request.id, (response) => {\n                    if (!response.success) {\n                        reject(new Error(response.error));\n                        return;\n                    }\n                    resolve(response);\n                });\n            });\n        };\n        this.allowedOrigins = allowedOrigins;\n        this.debugMode = debugMode;\n        if (!this.isServer) {\n            window.addEventListener('message', this.onParentMessage);\n        }\n    }\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (PostMessageCommunicator);\n\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@safe-global/safe-apps-sdk/dist/esm/communication/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@safe-global/safe-apps-sdk/dist/esm/communication/messageFormatter.js":
/*!********************************************************************************************!*\
  !*** ./node_modules/@safe-global/safe-apps-sdk/dist/esm/communication/messageFormatter.js ***!
  \********************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   MessageFormatter: () => (/* binding */ MessageFormatter)\n/* harmony export */ });\n/* harmony import */ var _version_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../version.js */ \"(ssr)/./node_modules/@safe-global/safe-apps-sdk/dist/esm/version.js\");\n/* harmony import */ var _utils_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./utils.js */ \"(ssr)/./node_modules/@safe-global/safe-apps-sdk/dist/esm/communication/utils.js\");\n\n\nclass MessageFormatter {\n}\nMessageFormatter.makeRequest = (method, params) => {\n    const id = (0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.generateRequestId)();\n    return {\n        id,\n        method,\n        params,\n        env: {\n            sdkVersion: (0,_version_js__WEBPACK_IMPORTED_MODULE_0__.getSDKVersion)(),\n        },\n    };\n};\nMessageFormatter.makeResponse = (id, data, version) => ({\n    id,\n    success: true,\n    version,\n    data,\n});\nMessageFormatter.makeErrorResponse = (id, error, version) => ({\n    id,\n    success: false,\n    error,\n    version,\n});\n\n//# sourceMappingURL=messageFormatter.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHNhZmUtZ2xvYmFsL3NhZmUtYXBwcy1zZGsvZGlzdC9lc20vY29tbXVuaWNhdGlvbi9tZXNzYWdlRm9ybWF0dGVyLmpzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUE4QztBQUNDO0FBQy9DO0FBQ0E7QUFDQTtBQUNBLGVBQWUsNERBQWlCO0FBQ2hDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSx3QkFBd0IsMERBQWE7QUFDckMsU0FBUztBQUNUO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsQ0FBQztBQUNEO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxDQUFDO0FBQzJCO0FBQzVCIiwic291cmNlcyI6WyJGOlxcQUkgY2hhaW5cXGNoYWluc2lnaHQtcHJvZHVjdGlvblxcbm9kZV9tb2R1bGVzXFxAc2FmZS1nbG9iYWxcXHNhZmUtYXBwcy1zZGtcXGRpc3RcXGVzbVxcY29tbXVuaWNhdGlvblxcbWVzc2FnZUZvcm1hdHRlci5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBnZXRTREtWZXJzaW9uIH0gZnJvbSAnLi4vdmVyc2lvbi5qcyc7XG5pbXBvcnQgeyBnZW5lcmF0ZVJlcXVlc3RJZCB9IGZyb20gJy4vdXRpbHMuanMnO1xuY2xhc3MgTWVzc2FnZUZvcm1hdHRlciB7XG59XG5NZXNzYWdlRm9ybWF0dGVyLm1ha2VSZXF1ZXN0ID0gKG1ldGhvZCwgcGFyYW1zKSA9PiB7XG4gICAgY29uc3QgaWQgPSBnZW5lcmF0ZVJlcXVlc3RJZCgpO1xuICAgIHJldHVybiB7XG4gICAgICAgIGlkLFxuICAgICAgICBtZXRob2QsXG4gICAgICAgIHBhcmFtcyxcbiAgICAgICAgZW52OiB7XG4gICAgICAgICAgICBzZGtWZXJzaW9uOiBnZXRTREtWZXJzaW9uKCksXG4gICAgICAgIH0sXG4gICAgfTtcbn07XG5NZXNzYWdlRm9ybWF0dGVyLm1ha2VSZXNwb25zZSA9IChpZCwgZGF0YSwgdmVyc2lvbikgPT4gKHtcbiAgICBpZCxcbiAgICBzdWNjZXNzOiB0cnVlLFxuICAgIHZlcnNpb24sXG4gICAgZGF0YSxcbn0pO1xuTWVzc2FnZUZvcm1hdHRlci5tYWtlRXJyb3JSZXNwb25zZSA9IChpZCwgZXJyb3IsIHZlcnNpb24pID0+ICh7XG4gICAgaWQsXG4gICAgc3VjY2VzczogZmFsc2UsXG4gICAgZXJyb3IsXG4gICAgdmVyc2lvbixcbn0pO1xuZXhwb3J0IHsgTWVzc2FnZUZvcm1hdHRlciB9O1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9bWVzc2FnZUZvcm1hdHRlci5qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@safe-global/safe-apps-sdk/dist/esm/communication/messageFormatter.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@safe-global/safe-apps-sdk/dist/esm/communication/methods.js":
/*!***********************************************************************************!*\
  !*** ./node_modules/@safe-global/safe-apps-sdk/dist/esm/communication/methods.js ***!
  \***********************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Methods: () => (/* binding */ Methods),\n/* harmony export */   RestrictedMethods: () => (/* binding */ RestrictedMethods)\n/* harmony export */ });\nvar Methods;\n(function (Methods) {\n    Methods[\"sendTransactions\"] = \"sendTransactions\";\n    Methods[\"rpcCall\"] = \"rpcCall\";\n    Methods[\"getChainInfo\"] = \"getChainInfo\";\n    Methods[\"getSafeInfo\"] = \"getSafeInfo\";\n    Methods[\"getTxBySafeTxHash\"] = \"getTxBySafeTxHash\";\n    Methods[\"getSafeBalances\"] = \"getSafeBalances\";\n    Methods[\"signMessage\"] = \"signMessage\";\n    Methods[\"signTypedMessage\"] = \"signTypedMessage\";\n    Methods[\"getEnvironmentInfo\"] = \"getEnvironmentInfo\";\n    Methods[\"getOffChainSignature\"] = \"getOffChainSignature\";\n    Methods[\"requestAddressBook\"] = \"requestAddressBook\";\n    Methods[\"wallet_getPermissions\"] = \"wallet_getPermissions\";\n    Methods[\"wallet_requestPermissions\"] = \"wallet_requestPermissions\";\n})(Methods || (Methods = {}));\nvar RestrictedMethods;\n(function (RestrictedMethods) {\n    RestrictedMethods[\"requestAddressBook\"] = \"requestAddressBook\";\n})(RestrictedMethods || (RestrictedMethods = {}));\n//# sourceMappingURL=methods.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@safe-global/safe-apps-sdk/dist/esm/communication/methods.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@safe-global/safe-apps-sdk/dist/esm/communication/utils.js":
/*!*********************************************************************************!*\
  !*** ./node_modules/@safe-global/safe-apps-sdk/dist/esm/communication/utils.js ***!
  \*********************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   generateRequestId: () => (/* binding */ generateRequestId)\n/* harmony export */ });\n// i.e. 0-255 -> '00'-'ff'\nconst dec2hex = (dec) => dec.toString(16).padStart(2, '0');\nconst generateId = (len) => {\n    const arr = new Uint8Array((len || 40) / 2);\n    window.crypto.getRandomValues(arr);\n    return Array.from(arr, dec2hex).join('');\n};\nconst generateRequestId = () => {\n    if (typeof window !== 'undefined') {\n        return generateId(10);\n    }\n    return new Date().getTime().toString(36);\n};\n\n//# sourceMappingURL=utils.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHNhZmUtZ2xvYmFsL3NhZmUtYXBwcy1zZGsvZGlzdC9lc20vY29tbXVuaWNhdGlvbi91dGlscy5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDNkI7QUFDN0IiLCJzb3VyY2VzIjpbIkY6XFxBSSBjaGFpblxcY2hhaW5zaWdodC1wcm9kdWN0aW9uXFxub2RlX21vZHVsZXNcXEBzYWZlLWdsb2JhbFxcc2FmZS1hcHBzLXNka1xcZGlzdFxcZXNtXFxjb21tdW5pY2F0aW9uXFx1dGlscy5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBpLmUuIDAtMjU1IC0+ICcwMCctJ2ZmJ1xuY29uc3QgZGVjMmhleCA9IChkZWMpID0+IGRlYy50b1N0cmluZygxNikucGFkU3RhcnQoMiwgJzAnKTtcbmNvbnN0IGdlbmVyYXRlSWQgPSAobGVuKSA9PiB7XG4gICAgY29uc3QgYXJyID0gbmV3IFVpbnQ4QXJyYXkoKGxlbiB8fCA0MCkgLyAyKTtcbiAgICB3aW5kb3cuY3J5cHRvLmdldFJhbmRvbVZhbHVlcyhhcnIpO1xuICAgIHJldHVybiBBcnJheS5mcm9tKGFyciwgZGVjMmhleCkuam9pbignJyk7XG59O1xuY29uc3QgZ2VuZXJhdGVSZXF1ZXN0SWQgPSAoKSA9PiB7XG4gICAgaWYgKHR5cGVvZiB3aW5kb3cgIT09ICd1bmRlZmluZWQnKSB7XG4gICAgICAgIHJldHVybiBnZW5lcmF0ZUlkKDEwKTtcbiAgICB9XG4gICAgcmV0dXJuIG5ldyBEYXRlKCkuZ2V0VGltZSgpLnRvU3RyaW5nKDM2KTtcbn07XG5leHBvcnQgeyBnZW5lcmF0ZVJlcXVlc3RJZCB9O1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9dXRpbHMuanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@safe-global/safe-apps-sdk/dist/esm/communication/utils.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@safe-global/safe-apps-sdk/dist/esm/decorators/requirePermissions.js":
/*!*******************************************************************************************!*\
  !*** ./node_modules/@safe-global/safe-apps-sdk/dist/esm/decorators/requirePermissions.js ***!
  \*******************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _wallet_index_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../wallet/index.js */ \"(ssr)/./node_modules/@safe-global/safe-apps-sdk/dist/esm/wallet/index.js\");\n/* harmony import */ var _types_permissions_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../types/permissions.js */ \"(ssr)/./node_modules/@safe-global/safe-apps-sdk/dist/esm/types/permissions.js\");\n\n\nconst hasPermission = (required, permissions) => permissions.some((permission) => permission.parentCapability === required);\nconst requirePermission = () => (_, propertyKey, descriptor) => {\n    const originalMethod = descriptor.value;\n    descriptor.value = async function () {\n        // @ts-expect-error accessing private property from decorator. 'this' context is the class instance\n        const wallet = new _wallet_index_js__WEBPACK_IMPORTED_MODULE_0__.Wallet(this.communicator);\n        let currentPermissions = await wallet.getPermissions();\n        if (!hasPermission(propertyKey, currentPermissions)) {\n            currentPermissions = await wallet.requestPermissions([{ [propertyKey]: {} }]);\n        }\n        if (!hasPermission(propertyKey, currentPermissions)) {\n            throw new _types_permissions_js__WEBPACK_IMPORTED_MODULE_1__.PermissionsError('Permissions rejected', _types_permissions_js__WEBPACK_IMPORTED_MODULE_1__.PERMISSIONS_REQUEST_REJECTED);\n        }\n        return originalMethod.apply(this);\n    };\n    return descriptor;\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (requirePermission);\n//# sourceMappingURL=requirePermissions.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@safe-global/safe-apps-sdk/dist/esm/decorators/requirePermissions.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@safe-global/safe-apps-sdk/dist/esm/eth/constants.js":
/*!***************************************************************************!*\
  !*** ./node_modules/@safe-global/safe-apps-sdk/dist/esm/eth/constants.js ***!
  \***************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   RPC_CALLS: () => (/* binding */ RPC_CALLS)\n/* harmony export */ });\nconst RPC_CALLS = {\n    eth_call: 'eth_call',\n    eth_gasPrice: 'eth_gasPrice',\n    eth_getLogs: 'eth_getLogs',\n    eth_getBalance: 'eth_getBalance',\n    eth_getCode: 'eth_getCode',\n    eth_getBlockByHash: 'eth_getBlockByHash',\n    eth_getBlockByNumber: 'eth_getBlockByNumber',\n    eth_getStorageAt: 'eth_getStorageAt',\n    eth_getTransactionByHash: 'eth_getTransactionByHash',\n    eth_getTransactionReceipt: 'eth_getTransactionReceipt',\n    eth_getTransactionCount: 'eth_getTransactionCount',\n    eth_estimateGas: 'eth_estimateGas',\n    safe_setSettings: 'safe_setSettings',\n};\n//# sourceMappingURL=constants.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHNhZmUtZ2xvYmFsL3NhZmUtYXBwcy1zZGsvZGlzdC9lc20vZXRoL2NvbnN0YW50cy5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQU87QUFDUDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIkY6XFxBSSBjaGFpblxcY2hhaW5zaWdodC1wcm9kdWN0aW9uXFxub2RlX21vZHVsZXNcXEBzYWZlLWdsb2JhbFxcc2FmZS1hcHBzLXNka1xcZGlzdFxcZXNtXFxldGhcXGNvbnN0YW50cy5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgY29uc3QgUlBDX0NBTExTID0ge1xuICAgIGV0aF9jYWxsOiAnZXRoX2NhbGwnLFxuICAgIGV0aF9nYXNQcmljZTogJ2V0aF9nYXNQcmljZScsXG4gICAgZXRoX2dldExvZ3M6ICdldGhfZ2V0TG9ncycsXG4gICAgZXRoX2dldEJhbGFuY2U6ICdldGhfZ2V0QmFsYW5jZScsXG4gICAgZXRoX2dldENvZGU6ICdldGhfZ2V0Q29kZScsXG4gICAgZXRoX2dldEJsb2NrQnlIYXNoOiAnZXRoX2dldEJsb2NrQnlIYXNoJyxcbiAgICBldGhfZ2V0QmxvY2tCeU51bWJlcjogJ2V0aF9nZXRCbG9ja0J5TnVtYmVyJyxcbiAgICBldGhfZ2V0U3RvcmFnZUF0OiAnZXRoX2dldFN0b3JhZ2VBdCcsXG4gICAgZXRoX2dldFRyYW5zYWN0aW9uQnlIYXNoOiAnZXRoX2dldFRyYW5zYWN0aW9uQnlIYXNoJyxcbiAgICBldGhfZ2V0VHJhbnNhY3Rpb25SZWNlaXB0OiAnZXRoX2dldFRyYW5zYWN0aW9uUmVjZWlwdCcsXG4gICAgZXRoX2dldFRyYW5zYWN0aW9uQ291bnQ6ICdldGhfZ2V0VHJhbnNhY3Rpb25Db3VudCcsXG4gICAgZXRoX2VzdGltYXRlR2FzOiAnZXRoX2VzdGltYXRlR2FzJyxcbiAgICBzYWZlX3NldFNldHRpbmdzOiAnc2FmZV9zZXRTZXR0aW5ncycsXG59O1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9Y29uc3RhbnRzLmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@safe-global/safe-apps-sdk/dist/esm/eth/constants.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@safe-global/safe-apps-sdk/dist/esm/eth/index.js":
/*!***********************************************************************!*\
  !*** ./node_modules/@safe-global/safe-apps-sdk/dist/esm/eth/index.js ***!
  \***********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Eth: () => (/* binding */ Eth)\n/* harmony export */ });\n/* harmony import */ var _eth_constants_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../eth/constants.js */ \"(ssr)/./node_modules/@safe-global/safe-apps-sdk/dist/esm/eth/constants.js\");\n/* harmony import */ var _communication_methods_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../communication/methods.js */ \"(ssr)/./node_modules/@safe-global/safe-apps-sdk/dist/esm/communication/methods.js\");\n\n\nconst inputFormatters = {\n    defaultBlockParam: (arg = 'latest') => arg,\n    returnFullTxObjectParam: (arg = false) => arg,\n    blockNumberToHex: (arg) => Number.isInteger(arg) ? `0x${arg.toString(16)}` : arg,\n};\nclass Eth {\n    constructor(communicator) {\n        this.communicator = communicator;\n        this.call = this.buildRequest({\n            call: _eth_constants_js__WEBPACK_IMPORTED_MODULE_0__.RPC_CALLS.eth_call,\n            formatters: [null, inputFormatters.defaultBlockParam],\n        });\n        this.getBalance = this.buildRequest({\n            call: _eth_constants_js__WEBPACK_IMPORTED_MODULE_0__.RPC_CALLS.eth_getBalance,\n            formatters: [null, inputFormatters.defaultBlockParam],\n        });\n        this.getCode = this.buildRequest({\n            call: _eth_constants_js__WEBPACK_IMPORTED_MODULE_0__.RPC_CALLS.eth_getCode,\n            formatters: [null, inputFormatters.defaultBlockParam],\n        });\n        this.getStorageAt = this.buildRequest({\n            call: _eth_constants_js__WEBPACK_IMPORTED_MODULE_0__.RPC_CALLS.eth_getStorageAt,\n            formatters: [null, inputFormatters.blockNumberToHex, inputFormatters.defaultBlockParam],\n        });\n        this.getPastLogs = this.buildRequest({\n            call: _eth_constants_js__WEBPACK_IMPORTED_MODULE_0__.RPC_CALLS.eth_getLogs,\n        });\n        this.getBlockByHash = this.buildRequest({\n            call: _eth_constants_js__WEBPACK_IMPORTED_MODULE_0__.RPC_CALLS.eth_getBlockByHash,\n            formatters: [null, inputFormatters.returnFullTxObjectParam],\n        });\n        this.getBlockByNumber = this.buildRequest({\n            call: _eth_constants_js__WEBPACK_IMPORTED_MODULE_0__.RPC_CALLS.eth_getBlockByNumber,\n            formatters: [inputFormatters.blockNumberToHex, inputFormatters.returnFullTxObjectParam],\n        });\n        this.getTransactionByHash = this.buildRequest({\n            call: _eth_constants_js__WEBPACK_IMPORTED_MODULE_0__.RPC_CALLS.eth_getTransactionByHash,\n        });\n        this.getTransactionReceipt = this.buildRequest({\n            call: _eth_constants_js__WEBPACK_IMPORTED_MODULE_0__.RPC_CALLS.eth_getTransactionReceipt,\n        });\n        this.getTransactionCount = this.buildRequest({\n            call: _eth_constants_js__WEBPACK_IMPORTED_MODULE_0__.RPC_CALLS.eth_getTransactionCount,\n            formatters: [null, inputFormatters.defaultBlockParam],\n        });\n        this.getGasPrice = this.buildRequest({\n            call: _eth_constants_js__WEBPACK_IMPORTED_MODULE_0__.RPC_CALLS.eth_gasPrice,\n        });\n        this.getEstimateGas = (transaction) => this.buildRequest({\n            call: _eth_constants_js__WEBPACK_IMPORTED_MODULE_0__.RPC_CALLS.eth_estimateGas,\n        })([transaction]);\n        this.setSafeSettings = this.buildRequest({\n            call: _eth_constants_js__WEBPACK_IMPORTED_MODULE_0__.RPC_CALLS.safe_setSettings,\n        });\n    }\n    buildRequest(args) {\n        const { call, formatters } = args;\n        return async (params) => {\n            if (formatters && Array.isArray(params)) {\n                formatters.forEach((formatter, i) => {\n                    if (formatter) {\n                        params[i] = formatter(params[i]);\n                    }\n                });\n            }\n            const payload = {\n                call,\n                params: params || [],\n            };\n            const response = await this.communicator.send(_communication_methods_js__WEBPACK_IMPORTED_MODULE_1__.Methods.rpcCall, payload);\n            return response.data;\n        };\n    }\n}\n\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHNhZmUtZ2xvYmFsL3NhZmUtYXBwcy1zZGsvZGlzdC9lc20vZXRoL2luZGV4LmpzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFnRDtBQUNNO0FBQ3REO0FBQ0E7QUFDQTtBQUNBLDREQUE0RCxpQkFBaUI7QUFDN0U7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGtCQUFrQix3REFBUztBQUMzQjtBQUNBLFNBQVM7QUFDVDtBQUNBLGtCQUFrQix3REFBUztBQUMzQjtBQUNBLFNBQVM7QUFDVDtBQUNBLGtCQUFrQix3REFBUztBQUMzQjtBQUNBLFNBQVM7QUFDVDtBQUNBLGtCQUFrQix3REFBUztBQUMzQjtBQUNBLFNBQVM7QUFDVDtBQUNBLGtCQUFrQix3REFBUztBQUMzQixTQUFTO0FBQ1Q7QUFDQSxrQkFBa0Isd0RBQVM7QUFDM0I7QUFDQSxTQUFTO0FBQ1Q7QUFDQSxrQkFBa0Isd0RBQVM7QUFDM0I7QUFDQSxTQUFTO0FBQ1Q7QUFDQSxrQkFBa0Isd0RBQVM7QUFDM0IsU0FBUztBQUNUO0FBQ0Esa0JBQWtCLHdEQUFTO0FBQzNCLFNBQVM7QUFDVDtBQUNBLGtCQUFrQix3REFBUztBQUMzQjtBQUNBLFNBQVM7QUFDVDtBQUNBLGtCQUFrQix3REFBUztBQUMzQixTQUFTO0FBQ1Q7QUFDQSxrQkFBa0Isd0RBQVM7QUFDM0IsU0FBUztBQUNUO0FBQ0Esa0JBQWtCLHdEQUFTO0FBQzNCLFNBQVM7QUFDVDtBQUNBO0FBQ0EsZ0JBQWdCLG1CQUFtQjtBQUNuQztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxpQkFBaUI7QUFDakI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLDBEQUEwRCw4REFBTztBQUNqRTtBQUNBO0FBQ0E7QUFDQTtBQUNlO0FBQ2YiLCJzb3VyY2VzIjpbIkY6XFxBSSBjaGFpblxcY2hhaW5zaWdodC1wcm9kdWN0aW9uXFxub2RlX21vZHVsZXNcXEBzYWZlLWdsb2JhbFxcc2FmZS1hcHBzLXNka1xcZGlzdFxcZXNtXFxldGhcXGluZGV4LmpzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IFJQQ19DQUxMUyB9IGZyb20gJy4uL2V0aC9jb25zdGFudHMuanMnO1xuaW1wb3J0IHsgTWV0aG9kcyB9IGZyb20gJy4uL2NvbW11bmljYXRpb24vbWV0aG9kcy5qcyc7XG5jb25zdCBpbnB1dEZvcm1hdHRlcnMgPSB7XG4gICAgZGVmYXVsdEJsb2NrUGFyYW06IChhcmcgPSAnbGF0ZXN0JykgPT4gYXJnLFxuICAgIHJldHVybkZ1bGxUeE9iamVjdFBhcmFtOiAoYXJnID0gZmFsc2UpID0+IGFyZyxcbiAgICBibG9ja051bWJlclRvSGV4OiAoYXJnKSA9PiBOdW1iZXIuaXNJbnRlZ2VyKGFyZykgPyBgMHgke2FyZy50b1N0cmluZygxNil9YCA6IGFyZyxcbn07XG5jbGFzcyBFdGgge1xuICAgIGNvbnN0cnVjdG9yKGNvbW11bmljYXRvcikge1xuICAgICAgICB0aGlzLmNvbW11bmljYXRvciA9IGNvbW11bmljYXRvcjtcbiAgICAgICAgdGhpcy5jYWxsID0gdGhpcy5idWlsZFJlcXVlc3Qoe1xuICAgICAgICAgICAgY2FsbDogUlBDX0NBTExTLmV0aF9jYWxsLFxuICAgICAgICAgICAgZm9ybWF0dGVyczogW251bGwsIGlucHV0Rm9ybWF0dGVycy5kZWZhdWx0QmxvY2tQYXJhbV0sXG4gICAgICAgIH0pO1xuICAgICAgICB0aGlzLmdldEJhbGFuY2UgPSB0aGlzLmJ1aWxkUmVxdWVzdCh7XG4gICAgICAgICAgICBjYWxsOiBSUENfQ0FMTFMuZXRoX2dldEJhbGFuY2UsXG4gICAgICAgICAgICBmb3JtYXR0ZXJzOiBbbnVsbCwgaW5wdXRGb3JtYXR0ZXJzLmRlZmF1bHRCbG9ja1BhcmFtXSxcbiAgICAgICAgfSk7XG4gICAgICAgIHRoaXMuZ2V0Q29kZSA9IHRoaXMuYnVpbGRSZXF1ZXN0KHtcbiAgICAgICAgICAgIGNhbGw6IFJQQ19DQUxMUy5ldGhfZ2V0Q29kZSxcbiAgICAgICAgICAgIGZvcm1hdHRlcnM6IFtudWxsLCBpbnB1dEZvcm1hdHRlcnMuZGVmYXVsdEJsb2NrUGFyYW1dLFxuICAgICAgICB9KTtcbiAgICAgICAgdGhpcy5nZXRTdG9yYWdlQXQgPSB0aGlzLmJ1aWxkUmVxdWVzdCh7XG4gICAgICAgICAgICBjYWxsOiBSUENfQ0FMTFMuZXRoX2dldFN0b3JhZ2VBdCxcbiAgICAgICAgICAgIGZvcm1hdHRlcnM6IFtudWxsLCBpbnB1dEZvcm1hdHRlcnMuYmxvY2tOdW1iZXJUb0hleCwgaW5wdXRGb3JtYXR0ZXJzLmRlZmF1bHRCbG9ja1BhcmFtXSxcbiAgICAgICAgfSk7XG4gICAgICAgIHRoaXMuZ2V0UGFzdExvZ3MgPSB0aGlzLmJ1aWxkUmVxdWVzdCh7XG4gICAgICAgICAgICBjYWxsOiBSUENfQ0FMTFMuZXRoX2dldExvZ3MsXG4gICAgICAgIH0pO1xuICAgICAgICB0aGlzLmdldEJsb2NrQnlIYXNoID0gdGhpcy5idWlsZFJlcXVlc3Qoe1xuICAgICAgICAgICAgY2FsbDogUlBDX0NBTExTLmV0aF9nZXRCbG9ja0J5SGFzaCxcbiAgICAgICAgICAgIGZvcm1hdHRlcnM6IFtudWxsLCBpbnB1dEZvcm1hdHRlcnMucmV0dXJuRnVsbFR4T2JqZWN0UGFyYW1dLFxuICAgICAgICB9KTtcbiAgICAgICAgdGhpcy5nZXRCbG9ja0J5TnVtYmVyID0gdGhpcy5idWlsZFJlcXVlc3Qoe1xuICAgICAgICAgICAgY2FsbDogUlBDX0NBTExTLmV0aF9nZXRCbG9ja0J5TnVtYmVyLFxuICAgICAgICAgICAgZm9ybWF0dGVyczogW2lucHV0Rm9ybWF0dGVycy5ibG9ja051bWJlclRvSGV4LCBpbnB1dEZvcm1hdHRlcnMucmV0dXJuRnVsbFR4T2JqZWN0UGFyYW1dLFxuICAgICAgICB9KTtcbiAgICAgICAgdGhpcy5nZXRUcmFuc2FjdGlvbkJ5SGFzaCA9IHRoaXMuYnVpbGRSZXF1ZXN0KHtcbiAgICAgICAgICAgIGNhbGw6IFJQQ19DQUxMUy5ldGhfZ2V0VHJhbnNhY3Rpb25CeUhhc2gsXG4gICAgICAgIH0pO1xuICAgICAgICB0aGlzLmdldFRyYW5zYWN0aW9uUmVjZWlwdCA9IHRoaXMuYnVpbGRSZXF1ZXN0KHtcbiAgICAgICAgICAgIGNhbGw6IFJQQ19DQUxMUy5ldGhfZ2V0VHJhbnNhY3Rpb25SZWNlaXB0LFxuICAgICAgICB9KTtcbiAgICAgICAgdGhpcy5nZXRUcmFuc2FjdGlvbkNvdW50ID0gdGhpcy5idWlsZFJlcXVlc3Qoe1xuICAgICAgICAgICAgY2FsbDogUlBDX0NBTExTLmV0aF9nZXRUcmFuc2FjdGlvbkNvdW50LFxuICAgICAgICAgICAgZm9ybWF0dGVyczogW251bGwsIGlucHV0Rm9ybWF0dGVycy5kZWZhdWx0QmxvY2tQYXJhbV0sXG4gICAgICAgIH0pO1xuICAgICAgICB0aGlzLmdldEdhc1ByaWNlID0gdGhpcy5idWlsZFJlcXVlc3Qoe1xuICAgICAgICAgICAgY2FsbDogUlBDX0NBTExTLmV0aF9nYXNQcmljZSxcbiAgICAgICAgfSk7XG4gICAgICAgIHRoaXMuZ2V0RXN0aW1hdGVHYXMgPSAodHJhbnNhY3Rpb24pID0+IHRoaXMuYnVpbGRSZXF1ZXN0KHtcbiAgICAgICAgICAgIGNhbGw6IFJQQ19DQUxMUy5ldGhfZXN0aW1hdGVHYXMsXG4gICAgICAgIH0pKFt0cmFuc2FjdGlvbl0pO1xuICAgICAgICB0aGlzLnNldFNhZmVTZXR0aW5ncyA9IHRoaXMuYnVpbGRSZXF1ZXN0KHtcbiAgICAgICAgICAgIGNhbGw6IFJQQ19DQUxMUy5zYWZlX3NldFNldHRpbmdzLFxuICAgICAgICB9KTtcbiAgICB9XG4gICAgYnVpbGRSZXF1ZXN0KGFyZ3MpIHtcbiAgICAgICAgY29uc3QgeyBjYWxsLCBmb3JtYXR0ZXJzIH0gPSBhcmdzO1xuICAgICAgICByZXR1cm4gYXN5bmMgKHBhcmFtcykgPT4ge1xuICAgICAgICAgICAgaWYgKGZvcm1hdHRlcnMgJiYgQXJyYXkuaXNBcnJheShwYXJhbXMpKSB7XG4gICAgICAgICAgICAgICAgZm9ybWF0dGVycy5mb3JFYWNoKChmb3JtYXR0ZXIsIGkpID0+IHtcbiAgICAgICAgICAgICAgICAgICAgaWYgKGZvcm1hdHRlcikge1xuICAgICAgICAgICAgICAgICAgICAgICAgcGFyYW1zW2ldID0gZm9ybWF0dGVyKHBhcmFtc1tpXSk7XG4gICAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICB9KTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIGNvbnN0IHBheWxvYWQgPSB7XG4gICAgICAgICAgICAgICAgY2FsbCxcbiAgICAgICAgICAgICAgICBwYXJhbXM6IHBhcmFtcyB8fCBbXSxcbiAgICAgICAgICAgIH07XG4gICAgICAgICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IHRoaXMuY29tbXVuaWNhdG9yLnNlbmQoTWV0aG9kcy5ycGNDYWxsLCBwYXlsb2FkKTtcbiAgICAgICAgICAgIHJldHVybiByZXNwb25zZS5kYXRhO1xuICAgICAgICB9O1xuICAgIH1cbn1cbmV4cG9ydCB7IEV0aCB9O1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9aW5kZXguanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@safe-global/safe-apps-sdk/dist/esm/eth/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@safe-global/safe-apps-sdk/dist/esm/index.js":
/*!*******************************************************************!*\
  !*** ./node_modules/@safe-global/safe-apps-sdk/dist/esm/index.js ***!
  \*******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   MessageFormatter: () => (/* reexport safe */ _communication_messageFormatter_js__WEBPACK_IMPORTED_MODULE_3__.MessageFormatter),\n/* harmony export */   Methods: () => (/* reexport safe */ _communication_methods_js__WEBPACK_IMPORTED_MODULE_2__.Methods),\n/* harmony export */   Operation: () => (/* reexport safe */ _types_index_js__WEBPACK_IMPORTED_MODULE_1__.Operation),\n/* harmony export */   RPC_CALLS: () => (/* reexport safe */ _eth_constants_js__WEBPACK_IMPORTED_MODULE_5__.RPC_CALLS),\n/* harmony export */   RestrictedMethods: () => (/* reexport safe */ _communication_methods_js__WEBPACK_IMPORTED_MODULE_2__.RestrictedMethods),\n/* harmony export */   TokenType: () => (/* reexport safe */ _types_index_js__WEBPACK_IMPORTED_MODULE_1__.TokenType),\n/* harmony export */   TransactionStatus: () => (/* reexport safe */ _types_index_js__WEBPACK_IMPORTED_MODULE_1__.TransactionStatus),\n/* harmony export */   TransferDirection: () => (/* reexport safe */ _types_index_js__WEBPACK_IMPORTED_MODULE_1__.TransferDirection),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   getSDKVersion: () => (/* reexport safe */ _version_js__WEBPACK_IMPORTED_MODULE_4__.getSDKVersion),\n/* harmony export */   isObjectEIP712TypedData: () => (/* reexport safe */ _types_index_js__WEBPACK_IMPORTED_MODULE_1__.isObjectEIP712TypedData)\n/* harmony export */ });\n/* harmony import */ var _sdk_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./sdk.js */ \"(ssr)/./node_modules/@safe-global/safe-apps-sdk/dist/esm/sdk.js\");\n/* harmony import */ var _types_index_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./types/index.js */ \"(ssr)/./node_modules/@safe-global/safe-apps-sdk/dist/esm/types/index.js\");\n/* harmony import */ var _communication_methods_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./communication/methods.js */ \"(ssr)/./node_modules/@safe-global/safe-apps-sdk/dist/esm/communication/methods.js\");\n/* harmony import */ var _communication_messageFormatter_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./communication/messageFormatter.js */ \"(ssr)/./node_modules/@safe-global/safe-apps-sdk/dist/esm/communication/messageFormatter.js\");\n/* harmony import */ var _version_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./version.js */ \"(ssr)/./node_modules/@safe-global/safe-apps-sdk/dist/esm/version.js\");\n/* harmony import */ var _eth_constants_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./eth/constants.js */ \"(ssr)/./node_modules/@safe-global/safe-apps-sdk/dist/esm/eth/constants.js\");\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (_sdk_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"]);\n\n\n\n\n\n\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHNhZmUtZ2xvYmFsL3NhZmUtYXBwcy1zZGsvZGlzdC9lc20vaW5kZXguanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFBMkI7QUFDM0IsaUVBQWUsK0NBQUcsRUFBQztBQUNNO0FBQ1E7QUFDVTtBQUNTO0FBQ1A7QUFDVjtBQUNuQyIsInNvdXJjZXMiOlsiRjpcXEFJIGNoYWluXFxjaGFpbnNpZ2h0LXByb2R1Y3Rpb25cXG5vZGVfbW9kdWxlc1xcQHNhZmUtZ2xvYmFsXFxzYWZlLWFwcHMtc2RrXFxkaXN0XFxlc21cXGluZGV4LmpzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBTREsgZnJvbSAnLi9zZGsuanMnO1xuZXhwb3J0IGRlZmF1bHQgU0RLO1xuZXhwb3J0ICogZnJvbSAnLi9zZGsuanMnO1xuZXhwb3J0ICogZnJvbSAnLi90eXBlcy9pbmRleC5qcyc7XG5leHBvcnQgKiBmcm9tICcuL2NvbW11bmljYXRpb24vbWV0aG9kcy5qcyc7XG5leHBvcnQgKiBmcm9tICcuL2NvbW11bmljYXRpb24vbWVzc2FnZUZvcm1hdHRlci5qcyc7XG5leHBvcnQgeyBnZXRTREtWZXJzaW9uIH0gZnJvbSAnLi92ZXJzaW9uLmpzJztcbmV4cG9ydCAqIGZyb20gJy4vZXRoL2NvbnN0YW50cy5qcyc7XG4vLyMgc291cmNlTWFwcGluZ1VSTD1pbmRleC5qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@safe-global/safe-apps-sdk/dist/esm/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@safe-global/safe-apps-sdk/dist/esm/safe/index.js":
/*!************************************************************************!*\
  !*** ./node_modules/@safe-global/safe-apps-sdk/dist/esm/safe/index.js ***!
  \************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Safe: () => (/* binding */ Safe)\n/* harmony export */ });\n/* harmony import */ var viem__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! viem */ \"(ssr)/./node_modules/viem/_esm/utils/abi/encodeFunctionData.js\");\n/* harmony import */ var viem__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! viem */ \"(ssr)/./node_modules/viem/_esm/utils/signature/hashMessage.js\");\n/* harmony import */ var viem__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! viem */ \"(ssr)/./node_modules/viem/_esm/utils/signature/hashTypedData.js\");\n/* harmony import */ var _signatures_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./signatures.js */ \"(ssr)/./node_modules/@safe-global/safe-apps-sdk/dist/esm/safe/signatures.js\");\n/* harmony import */ var _communication_methods_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../communication/methods.js */ \"(ssr)/./node_modules/@safe-global/safe-apps-sdk/dist/esm/communication/methods.js\");\n/* harmony import */ var _eth_constants_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../eth/constants.js */ \"(ssr)/./node_modules/@safe-global/safe-apps-sdk/dist/esm/eth/constants.js\");\n/* harmony import */ var _types_index_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../types/index.js */ \"(ssr)/./node_modules/@safe-global/safe-apps-sdk/dist/esm/types/index.js\");\n/* harmony import */ var _decorators_requirePermissions_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../decorators/requirePermissions.js */ \"(ssr)/./node_modules/@safe-global/safe-apps-sdk/dist/esm/decorators/requirePermissions.js\");\nvar __decorate = (undefined && undefined.__decorate) || function (decorators, target, key, desc) {\n    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;\n    if (typeof Reflect === \"object\" && typeof Reflect.decorate === \"function\") r = Reflect.decorate(decorators, target, key, desc);\n    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;\n    return c > 3 && r && Object.defineProperty(target, key, r), r;\n};\n\n\n\n\n\n\nclass Safe {\n    constructor(communicator) {\n        this.communicator = communicator;\n    }\n    async getChainInfo() {\n        const response = await this.communicator.send(_communication_methods_js__WEBPACK_IMPORTED_MODULE_1__.Methods.getChainInfo, undefined);\n        return response.data;\n    }\n    async getInfo() {\n        const response = await this.communicator.send(_communication_methods_js__WEBPACK_IMPORTED_MODULE_1__.Methods.getSafeInfo, undefined);\n        return response.data;\n    }\n    // There is a possibility that this method will change because we may add pagination to the endpoint\n    async experimental_getBalances({ currency = 'usd' } = {}) {\n        const response = await this.communicator.send(_communication_methods_js__WEBPACK_IMPORTED_MODULE_1__.Methods.getSafeBalances, {\n            currency,\n        });\n        return response.data;\n    }\n    async check1271Signature(messageHash, signature = '0x') {\n        const safeInfo = await this.getInfo();\n        const encodedIsValidSignatureCall = (0,viem__WEBPACK_IMPORTED_MODULE_5__.encodeFunctionData)({\n            abi: [\n                {\n                    constant: false,\n                    inputs: [\n                        {\n                            name: '_dataHash',\n                            type: 'bytes32',\n                        },\n                        {\n                            name: '_signature',\n                            type: 'bytes',\n                        },\n                    ],\n                    name: 'isValidSignature',\n                    outputs: [\n                        {\n                            name: '',\n                            type: 'bytes4',\n                        },\n                    ],\n                    payable: false,\n                    stateMutability: 'nonpayable',\n                    type: 'function',\n                },\n            ],\n            functionName: 'isValidSignature',\n            args: [messageHash, signature],\n        });\n        const payload = {\n            call: _eth_constants_js__WEBPACK_IMPORTED_MODULE_2__.RPC_CALLS.eth_call,\n            params: [\n                {\n                    to: safeInfo.safeAddress,\n                    data: encodedIsValidSignatureCall,\n                },\n                'latest',\n            ],\n        };\n        try {\n            const response = await this.communicator.send(_communication_methods_js__WEBPACK_IMPORTED_MODULE_1__.Methods.rpcCall, payload);\n            return response.data.slice(0, 10).toLowerCase() === _signatures_js__WEBPACK_IMPORTED_MODULE_0__.MAGIC_VALUE;\n        }\n        catch (err) {\n            return false;\n        }\n    }\n    async check1271SignatureBytes(messageHash, signature = '0x') {\n        const safeInfo = await this.getInfo();\n        const encodedIsValidSignatureCall = (0,viem__WEBPACK_IMPORTED_MODULE_5__.encodeFunctionData)({\n            abi: [\n                {\n                    constant: false,\n                    inputs: [\n                        {\n                            name: '_data',\n                            type: 'bytes',\n                        },\n                        {\n                            name: '_signature',\n                            type: 'bytes',\n                        },\n                    ],\n                    name: 'isValidSignature',\n                    outputs: [\n                        {\n                            name: '',\n                            type: 'bytes4',\n                        },\n                    ],\n                    payable: false,\n                    stateMutability: 'nonpayable',\n                    type: 'function',\n                },\n            ],\n            functionName: 'isValidSignature',\n            args: [messageHash, signature],\n        });\n        const payload = {\n            call: _eth_constants_js__WEBPACK_IMPORTED_MODULE_2__.RPC_CALLS.eth_call,\n            params: [\n                {\n                    to: safeInfo.safeAddress,\n                    data: encodedIsValidSignatureCall,\n                },\n                'latest',\n            ],\n        };\n        try {\n            const response = await this.communicator.send(_communication_methods_js__WEBPACK_IMPORTED_MODULE_1__.Methods.rpcCall, payload);\n            return response.data.slice(0, 10).toLowerCase() === _signatures_js__WEBPACK_IMPORTED_MODULE_0__.MAGIC_VALUE_BYTES;\n        }\n        catch (err) {\n            return false;\n        }\n    }\n    calculateMessageHash(message) {\n        return (0,viem__WEBPACK_IMPORTED_MODULE_6__.hashMessage)(message);\n    }\n    calculateTypedMessageHash(typedMessage) {\n        const chainId = typeof typedMessage.domain.chainId === 'object'\n            ? typedMessage.domain.chainId.toNumber()\n            : Number(typedMessage.domain.chainId);\n        let primaryType = typedMessage.primaryType;\n        if (!primaryType) {\n            const fields = Object.values(typedMessage.types);\n            // We try to infer primaryType (simplified ether's version)\n            const primaryTypes = Object.keys(typedMessage.types).filter((typeName) => fields.every((dataTypes) => dataTypes.every(({ type }) => type.replace('[', '').replace(']', '') !== typeName)));\n            if (primaryTypes.length === 0 || primaryTypes.length > 1)\n                throw new Error('Please specify primaryType');\n            primaryType = primaryTypes[0];\n        }\n        return (0,viem__WEBPACK_IMPORTED_MODULE_7__.hashTypedData)({\n            message: typedMessage.message,\n            domain: {\n                ...typedMessage.domain,\n                chainId,\n                verifyingContract: typedMessage.domain.verifyingContract,\n                salt: typedMessage.domain.salt,\n            },\n            types: typedMessage.types,\n            primaryType,\n        });\n    }\n    async getOffChainSignature(messageHash) {\n        const response = await this.communicator.send(_communication_methods_js__WEBPACK_IMPORTED_MODULE_1__.Methods.getOffChainSignature, messageHash);\n        return response.data;\n    }\n    async isMessageSigned(message, signature = '0x') {\n        let check;\n        if (typeof message === 'string') {\n            check = async () => {\n                const messageHash = this.calculateMessageHash(message);\n                const messageHashSigned = await this.isMessageHashSigned(messageHash, signature);\n                return messageHashSigned;\n            };\n        }\n        if ((0,_types_index_js__WEBPACK_IMPORTED_MODULE_3__.isObjectEIP712TypedData)(message)) {\n            check = async () => {\n                const messageHash = this.calculateTypedMessageHash(message);\n                const messageHashSigned = await this.isMessageHashSigned(messageHash, signature);\n                return messageHashSigned;\n            };\n        }\n        if (check) {\n            const isValid = await check();\n            return isValid;\n        }\n        throw new Error('Invalid message type');\n    }\n    async isMessageHashSigned(messageHash, signature = '0x') {\n        const checks = [this.check1271Signature.bind(this), this.check1271SignatureBytes.bind(this)];\n        for (const check of checks) {\n            const isValid = await check(messageHash, signature);\n            if (isValid) {\n                return true;\n            }\n        }\n        return false;\n    }\n    async getEnvironmentInfo() {\n        const response = await this.communicator.send(_communication_methods_js__WEBPACK_IMPORTED_MODULE_1__.Methods.getEnvironmentInfo, undefined);\n        return response.data;\n    }\n    async requestAddressBook() {\n        const response = await this.communicator.send(_communication_methods_js__WEBPACK_IMPORTED_MODULE_1__.Methods.requestAddressBook, undefined);\n        return response.data;\n    }\n}\n__decorate([\n    (0,_decorators_requirePermissions_js__WEBPACK_IMPORTED_MODULE_4__[\"default\"])()\n], Safe.prototype, \"requestAddressBook\", null);\n\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@safe-global/safe-apps-sdk/dist/esm/safe/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@safe-global/safe-apps-sdk/dist/esm/safe/signatures.js":
/*!*****************************************************************************!*\
  !*** ./node_modules/@safe-global/safe-apps-sdk/dist/esm/safe/signatures.js ***!
  \*****************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   MAGIC_VALUE: () => (/* binding */ MAGIC_VALUE),\n/* harmony export */   MAGIC_VALUE_BYTES: () => (/* binding */ MAGIC_VALUE_BYTES)\n/* harmony export */ });\nconst MAGIC_VALUE = '0x1626ba7e';\nconst MAGIC_VALUE_BYTES = '0x20c13b0b';\n\n//# sourceMappingURL=signatures.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHNhZmUtZ2xvYmFsL3NhZmUtYXBwcy1zZGsvZGlzdC9lc20vc2FmZS9zaWduYXR1cmVzLmpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQUE7QUFDQTtBQUMwQztBQUMxQyIsInNvdXJjZXMiOlsiRjpcXEFJIGNoYWluXFxjaGFpbnNpZ2h0LXByb2R1Y3Rpb25cXG5vZGVfbW9kdWxlc1xcQHNhZmUtZ2xvYmFsXFxzYWZlLWFwcHMtc2RrXFxkaXN0XFxlc21cXHNhZmVcXHNpZ25hdHVyZXMuanMiXSwic291cmNlc0NvbnRlbnQiOlsiY29uc3QgTUFHSUNfVkFMVUUgPSAnMHgxNjI2YmE3ZSc7XG5jb25zdCBNQUdJQ19WQUxVRV9CWVRFUyA9ICcweDIwYzEzYjBiJztcbmV4cG9ydCB7IE1BR0lDX1ZBTFVFLCBNQUdJQ19WQUxVRV9CWVRFUyB9O1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9c2lnbmF0dXJlcy5qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@safe-global/safe-apps-sdk/dist/esm/safe/signatures.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@safe-global/safe-apps-sdk/dist/esm/sdk.js":
/*!*****************************************************************!*\
  !*** ./node_modules/@safe-global/safe-apps-sdk/dist/esm/sdk.js ***!
  \*****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _communication_index_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./communication/index.js */ \"(ssr)/./node_modules/@safe-global/safe-apps-sdk/dist/esm/communication/index.js\");\n/* harmony import */ var _txs_index_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./txs/index.js */ \"(ssr)/./node_modules/@safe-global/safe-apps-sdk/dist/esm/txs/index.js\");\n/* harmony import */ var _eth_index_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./eth/index.js */ \"(ssr)/./node_modules/@safe-global/safe-apps-sdk/dist/esm/eth/index.js\");\n/* harmony import */ var _safe_index_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./safe/index.js */ \"(ssr)/./node_modules/@safe-global/safe-apps-sdk/dist/esm/safe/index.js\");\n/* harmony import */ var _wallet_index_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./wallet/index.js */ \"(ssr)/./node_modules/@safe-global/safe-apps-sdk/dist/esm/wallet/index.js\");\n\n\n\n\n\nclass SafeAppsSDK {\n    constructor(opts = {}) {\n        const { allowedDomains = null, debug = false } = opts;\n        this.communicator = new _communication_index_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"](allowedDomains, debug);\n        this.eth = new _eth_index_js__WEBPACK_IMPORTED_MODULE_2__.Eth(this.communicator);\n        this.txs = new _txs_index_js__WEBPACK_IMPORTED_MODULE_1__.TXs(this.communicator);\n        this.safe = new _safe_index_js__WEBPACK_IMPORTED_MODULE_3__.Safe(this.communicator);\n        this.wallet = new _wallet_index_js__WEBPACK_IMPORTED_MODULE_4__.Wallet(this.communicator);\n    }\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (SafeAppsSDK);\n//# sourceMappingURL=sdk.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHNhZmUtZ2xvYmFsL3NhZmUtYXBwcy1zZGsvZGlzdC9lc20vc2RrLmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7OztBQUE2RDtBQUN4QjtBQUNBO0FBQ0U7QUFDSTtBQUMzQztBQUNBLHlCQUF5QjtBQUN6QixnQkFBZ0IsdUNBQXVDO0FBQ3ZELGdDQUFnQywrREFBcUI7QUFDckQsdUJBQXVCLDhDQUFHO0FBQzFCLHVCQUF1Qiw4Q0FBRztBQUMxQix3QkFBd0IsZ0RBQUk7QUFDNUIsMEJBQTBCLG9EQUFNO0FBQ2hDO0FBQ0E7QUFDQSxpRUFBZSxXQUFXLEVBQUM7QUFDM0IiLCJzb3VyY2VzIjpbIkY6XFxBSSBjaGFpblxcY2hhaW5zaWdodC1wcm9kdWN0aW9uXFxub2RlX21vZHVsZXNcXEBzYWZlLWdsb2JhbFxcc2FmZS1hcHBzLXNka1xcZGlzdFxcZXNtXFxzZGsuanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IEludGVyZmFjZUNvbW11bmljYXRvciBmcm9tICcuL2NvbW11bmljYXRpb24vaW5kZXguanMnO1xuaW1wb3J0IHsgVFhzIH0gZnJvbSAnLi90eHMvaW5kZXguanMnO1xuaW1wb3J0IHsgRXRoIH0gZnJvbSAnLi9ldGgvaW5kZXguanMnO1xuaW1wb3J0IHsgU2FmZSB9IGZyb20gJy4vc2FmZS9pbmRleC5qcyc7XG5pbXBvcnQgeyBXYWxsZXQgfSBmcm9tICcuL3dhbGxldC9pbmRleC5qcyc7XG5jbGFzcyBTYWZlQXBwc1NESyB7XG4gICAgY29uc3RydWN0b3Iob3B0cyA9IHt9KSB7XG4gICAgICAgIGNvbnN0IHsgYWxsb3dlZERvbWFpbnMgPSBudWxsLCBkZWJ1ZyA9IGZhbHNlIH0gPSBvcHRzO1xuICAgICAgICB0aGlzLmNvbW11bmljYXRvciA9IG5ldyBJbnRlcmZhY2VDb21tdW5pY2F0b3IoYWxsb3dlZERvbWFpbnMsIGRlYnVnKTtcbiAgICAgICAgdGhpcy5ldGggPSBuZXcgRXRoKHRoaXMuY29tbXVuaWNhdG9yKTtcbiAgICAgICAgdGhpcy50eHMgPSBuZXcgVFhzKHRoaXMuY29tbXVuaWNhdG9yKTtcbiAgICAgICAgdGhpcy5zYWZlID0gbmV3IFNhZmUodGhpcy5jb21tdW5pY2F0b3IpO1xuICAgICAgICB0aGlzLndhbGxldCA9IG5ldyBXYWxsZXQodGhpcy5jb21tdW5pY2F0b3IpO1xuICAgIH1cbn1cbmV4cG9ydCBkZWZhdWx0IFNhZmVBcHBzU0RLO1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9c2RrLmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@safe-global/safe-apps-sdk/dist/esm/sdk.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@safe-global/safe-apps-sdk/dist/esm/txs/index.js":
/*!***********************************************************************!*\
  !*** ./node_modules/@safe-global/safe-apps-sdk/dist/esm/txs/index.js ***!
  \***********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TXs: () => (/* binding */ TXs)\n/* harmony export */ });\n/* harmony import */ var _communication_methods_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../communication/methods.js */ \"(ssr)/./node_modules/@safe-global/safe-apps-sdk/dist/esm/communication/methods.js\");\n/* harmony import */ var _types_index_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../types/index.js */ \"(ssr)/./node_modules/@safe-global/safe-apps-sdk/dist/esm/types/index.js\");\n\n\nclass TXs {\n    constructor(communicator) {\n        this.communicator = communicator;\n    }\n    async getBySafeTxHash(safeTxHash) {\n        if (!safeTxHash) {\n            throw new Error('Invalid safeTxHash');\n        }\n        const response = await this.communicator.send(_communication_methods_js__WEBPACK_IMPORTED_MODULE_0__.Methods.getTxBySafeTxHash, { safeTxHash });\n        return response.data;\n    }\n    async signMessage(message) {\n        const messagePayload = {\n            message,\n        };\n        const response = await this.communicator.send(_communication_methods_js__WEBPACK_IMPORTED_MODULE_0__.Methods.signMessage, messagePayload);\n        return response.data;\n    }\n    async signTypedMessage(typedData) {\n        if (!(0,_types_index_js__WEBPACK_IMPORTED_MODULE_1__.isObjectEIP712TypedData)(typedData)) {\n            throw new Error('Invalid typed data');\n        }\n        const response = await this.communicator.send(_communication_methods_js__WEBPACK_IMPORTED_MODULE_0__.Methods.signTypedMessage, { typedData });\n        return response.data;\n    }\n    async send({ txs, params }) {\n        if (!txs || !txs.length) {\n            throw new Error('No transactions were passed');\n        }\n        const messagePayload = {\n            txs,\n            params,\n        };\n        const response = await this.communicator.send(_communication_methods_js__WEBPACK_IMPORTED_MODULE_0__.Methods.sendTransactions, messagePayload);\n        return response.data;\n    }\n}\n\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHNhZmUtZ2xvYmFsL3NhZmUtYXBwcy1zZGsvZGlzdC9lc20vdHhzL2luZGV4LmpzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFzRDtBQUNPO0FBQzdEO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxzREFBc0QsOERBQU8sc0JBQXNCLFlBQVk7QUFDL0Y7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esc0RBQXNELDhEQUFPO0FBQzdEO0FBQ0E7QUFDQTtBQUNBLGFBQWEsd0VBQXVCO0FBQ3BDO0FBQ0E7QUFDQSxzREFBc0QsOERBQU8scUJBQXFCLFdBQVc7QUFDN0Y7QUFDQTtBQUNBLGlCQUFpQixhQUFhO0FBQzlCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esc0RBQXNELDhEQUFPO0FBQzdEO0FBQ0E7QUFDQTtBQUNlO0FBQ2YiLCJzb3VyY2VzIjpbIkY6XFxBSSBjaGFpblxcY2hhaW5zaWdodC1wcm9kdWN0aW9uXFxub2RlX21vZHVsZXNcXEBzYWZlLWdsb2JhbFxcc2FmZS1hcHBzLXNka1xcZGlzdFxcZXNtXFx0eHNcXGluZGV4LmpzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IE1ldGhvZHMgfSBmcm9tICcuLi9jb21tdW5pY2F0aW9uL21ldGhvZHMuanMnO1xuaW1wb3J0IHsgaXNPYmplY3RFSVA3MTJUeXBlZERhdGEsIH0gZnJvbSAnLi4vdHlwZXMvaW5kZXguanMnO1xuY2xhc3MgVFhzIHtcbiAgICBjb25zdHJ1Y3Rvcihjb21tdW5pY2F0b3IpIHtcbiAgICAgICAgdGhpcy5jb21tdW5pY2F0b3IgPSBjb21tdW5pY2F0b3I7XG4gICAgfVxuICAgIGFzeW5jIGdldEJ5U2FmZVR4SGFzaChzYWZlVHhIYXNoKSB7XG4gICAgICAgIGlmICghc2FmZVR4SGFzaCkge1xuICAgICAgICAgICAgdGhyb3cgbmV3IEVycm9yKCdJbnZhbGlkIHNhZmVUeEhhc2gnKTtcbiAgICAgICAgfVxuICAgICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IHRoaXMuY29tbXVuaWNhdG9yLnNlbmQoTWV0aG9kcy5nZXRUeEJ5U2FmZVR4SGFzaCwgeyBzYWZlVHhIYXNoIH0pO1xuICAgICAgICByZXR1cm4gcmVzcG9uc2UuZGF0YTtcbiAgICB9XG4gICAgYXN5bmMgc2lnbk1lc3NhZ2UobWVzc2FnZSkge1xuICAgICAgICBjb25zdCBtZXNzYWdlUGF5bG9hZCA9IHtcbiAgICAgICAgICAgIG1lc3NhZ2UsXG4gICAgICAgIH07XG4gICAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgdGhpcy5jb21tdW5pY2F0b3Iuc2VuZChNZXRob2RzLnNpZ25NZXNzYWdlLCBtZXNzYWdlUGF5bG9hZCk7XG4gICAgICAgIHJldHVybiByZXNwb25zZS5kYXRhO1xuICAgIH1cbiAgICBhc3luYyBzaWduVHlwZWRNZXNzYWdlKHR5cGVkRGF0YSkge1xuICAgICAgICBpZiAoIWlzT2JqZWN0RUlQNzEyVHlwZWREYXRhKHR5cGVkRGF0YSkpIHtcbiAgICAgICAgICAgIHRocm93IG5ldyBFcnJvcignSW52YWxpZCB0eXBlZCBkYXRhJyk7XG4gICAgICAgIH1cbiAgICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCB0aGlzLmNvbW11bmljYXRvci5zZW5kKE1ldGhvZHMuc2lnblR5cGVkTWVzc2FnZSwgeyB0eXBlZERhdGEgfSk7XG4gICAgICAgIHJldHVybiByZXNwb25zZS5kYXRhO1xuICAgIH1cbiAgICBhc3luYyBzZW5kKHsgdHhzLCBwYXJhbXMgfSkge1xuICAgICAgICBpZiAoIXR4cyB8fCAhdHhzLmxlbmd0aCkge1xuICAgICAgICAgICAgdGhyb3cgbmV3IEVycm9yKCdObyB0cmFuc2FjdGlvbnMgd2VyZSBwYXNzZWQnKTtcbiAgICAgICAgfVxuICAgICAgICBjb25zdCBtZXNzYWdlUGF5bG9hZCA9IHtcbiAgICAgICAgICAgIHR4cyxcbiAgICAgICAgICAgIHBhcmFtcyxcbiAgICAgICAgfTtcbiAgICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCB0aGlzLmNvbW11bmljYXRvci5zZW5kKE1ldGhvZHMuc2VuZFRyYW5zYWN0aW9ucywgbWVzc2FnZVBheWxvYWQpO1xuICAgICAgICByZXR1cm4gcmVzcG9uc2UuZGF0YTtcbiAgICB9XG59XG5leHBvcnQgeyBUWHMgfTtcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPWluZGV4LmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@safe-global/safe-apps-sdk/dist/esm/txs/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@safe-global/safe-apps-sdk/dist/esm/types/gateway.js":
/*!***************************************************************************!*\
  !*** ./node_modules/@safe-global/safe-apps-sdk/dist/esm/types/gateway.js ***!
  \***************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Operation: () => (/* reexport safe */ _safe_global_safe_gateway_typescript_sdk__WEBPACK_IMPORTED_MODULE_0__.Operation),\n/* harmony export */   TokenType: () => (/* reexport safe */ _safe_global_safe_gateway_typescript_sdk__WEBPACK_IMPORTED_MODULE_0__.TokenType),\n/* harmony export */   TransactionStatus: () => (/* reexport safe */ _safe_global_safe_gateway_typescript_sdk__WEBPACK_IMPORTED_MODULE_0__.TransactionStatus),\n/* harmony export */   TransferDirection: () => (/* reexport safe */ _safe_global_safe_gateway_typescript_sdk__WEBPACK_IMPORTED_MODULE_0__.TransferDirection)\n/* harmony export */ });\n/* harmony import */ var _safe_global_safe_gateway_typescript_sdk__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @safe-global/safe-gateway-typescript-sdk */ \"(ssr)/./node_modules/@safe-global/safe-gateway-typescript-sdk/dist/index.js\");\n\n//# sourceMappingURL=gateway.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHNhZmUtZ2xvYmFsL3NhZmUtYXBwcy1zZGsvZGlzdC9lc20vdHlwZXMvZ2F0ZXdheS5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7OztBQUF1SDtBQUN2SCIsInNvdXJjZXMiOlsiRjpcXEFJIGNoYWluXFxjaGFpbnNpZ2h0LXByb2R1Y3Rpb25cXG5vZGVfbW9kdWxlc1xcQHNhZmUtZ2xvYmFsXFxzYWZlLWFwcHMtc2RrXFxkaXN0XFxlc21cXHR5cGVzXFxnYXRld2F5LmpzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCB7IE9wZXJhdGlvbiwgVG9rZW5UeXBlLCBUcmFuc2FjdGlvblN0YXR1cywgVHJhbnNmZXJEaXJlY3Rpb24sIH0gZnJvbSAnQHNhZmUtZ2xvYmFsL3NhZmUtZ2F0ZXdheS10eXBlc2NyaXB0LXNkayc7XG4vLyMgc291cmNlTWFwcGluZ1VSTD1nYXRld2F5LmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@safe-global/safe-apps-sdk/dist/esm/types/gateway.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@safe-global/safe-apps-sdk/dist/esm/types/index.js":
/*!*************************************************************************!*\
  !*** ./node_modules/@safe-global/safe-apps-sdk/dist/esm/types/index.js ***!
  \*************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Operation: () => (/* reexport safe */ _gateway_js__WEBPACK_IMPORTED_MODULE_2__.Operation),\n/* harmony export */   TokenType: () => (/* reexport safe */ _gateway_js__WEBPACK_IMPORTED_MODULE_2__.TokenType),\n/* harmony export */   TransactionStatus: () => (/* reexport safe */ _gateway_js__WEBPACK_IMPORTED_MODULE_2__.TransactionStatus),\n/* harmony export */   TransferDirection: () => (/* reexport safe */ _gateway_js__WEBPACK_IMPORTED_MODULE_2__.TransferDirection),\n/* harmony export */   isObjectEIP712TypedData: () => (/* reexport safe */ _sdk_js__WEBPACK_IMPORTED_MODULE_0__.isObjectEIP712TypedData)\n/* harmony export */ });\n/* harmony import */ var _sdk_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./sdk.js */ \"(ssr)/./node_modules/@safe-global/safe-apps-sdk/dist/esm/types/sdk.js\");\n/* harmony import */ var _rpc_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./rpc.js */ \"(ssr)/./node_modules/@safe-global/safe-apps-sdk/dist/esm/types/rpc.js\");\n/* harmony import */ var _gateway_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./gateway.js */ \"(ssr)/./node_modules/@safe-global/safe-apps-sdk/dist/esm/types/gateway.js\");\n/* harmony import */ var _messaging_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./messaging.js */ \"(ssr)/./node_modules/@safe-global/safe-apps-sdk/dist/esm/types/messaging.js\");\n\n\n\n\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHNhZmUtZ2xvYmFsL3NhZmUtYXBwcy1zZGsvZGlzdC9lc20vdHlwZXMvaW5kZXguanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7O0FBQXlCO0FBQ0E7QUFDSTtBQUNFO0FBQy9CIiwic291cmNlcyI6WyJGOlxcQUkgY2hhaW5cXGNoYWluc2lnaHQtcHJvZHVjdGlvblxcbm9kZV9tb2R1bGVzXFxAc2FmZS1nbG9iYWxcXHNhZmUtYXBwcy1zZGtcXGRpc3RcXGVzbVxcdHlwZXNcXGluZGV4LmpzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCAqIGZyb20gJy4vc2RrLmpzJztcbmV4cG9ydCAqIGZyb20gJy4vcnBjLmpzJztcbmV4cG9ydCAqIGZyb20gJy4vZ2F0ZXdheS5qcyc7XG5leHBvcnQgKiBmcm9tICcuL21lc3NhZ2luZy5qcyc7XG4vLyMgc291cmNlTWFwcGluZ1VSTD1pbmRleC5qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@safe-global/safe-apps-sdk/dist/esm/types/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@safe-global/safe-apps-sdk/dist/esm/types/messaging.js":
/*!*****************************************************************************!*\
  !*** ./node_modules/@safe-global/safe-apps-sdk/dist/esm/types/messaging.js ***!
  \*****************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var _communication_methods_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../communication/methods.js */ \"(ssr)/./node_modules/@safe-global/safe-apps-sdk/dist/esm/communication/methods.js\");\n\n//# sourceMappingURL=messaging.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHNhZmUtZ2xvYmFsL3NhZmUtYXBwcy1zZGsvZGlzdC9lc20vdHlwZXMvbWVzc2FnaW5nLmpzIiwibWFwcGluZ3MiOiI7O0FBQXNEO0FBQ3REIiwic291cmNlcyI6WyJGOlxcQUkgY2hhaW5cXGNoYWluc2lnaHQtcHJvZHVjdGlvblxcbm9kZV9tb2R1bGVzXFxAc2FmZS1nbG9iYWxcXHNhZmUtYXBwcy1zZGtcXGRpc3RcXGVzbVxcdHlwZXNcXG1lc3NhZ2luZy5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBNZXRob2RzIH0gZnJvbSAnLi4vY29tbXVuaWNhdGlvbi9tZXRob2RzLmpzJztcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPW1lc3NhZ2luZy5qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@safe-global/safe-apps-sdk/dist/esm/types/messaging.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@safe-global/safe-apps-sdk/dist/esm/types/permissions.js":
/*!*******************************************************************************!*\
  !*** ./node_modules/@safe-global/safe-apps-sdk/dist/esm/types/permissions.js ***!
  \*******************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   PERMISSIONS_REQUEST_REJECTED: () => (/* binding */ PERMISSIONS_REQUEST_REJECTED),\n/* harmony export */   PermissionsError: () => (/* binding */ PermissionsError)\n/* harmony export */ });\nconst PERMISSIONS_REQUEST_REJECTED = 4001;\nclass PermissionsError extends Error {\n    constructor(message, code, data) {\n        super(message);\n        this.code = code;\n        this.data = data;\n        // Should adjust prototype manually because how TS handles the type extension compilation\n        // https://github.com/Microsoft/TypeScript/wiki/Breaking-Changes#extending-built-ins-like-error-array-and-map-may-no-longer-work\n        Object.setPrototypeOf(this, PermissionsError.prototype);\n    }\n}\n//# sourceMappingURL=permissions.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHNhZmUtZ2xvYmFsL3NhZmUtYXBwcy1zZGsvZGlzdC9lc20vdHlwZXMvcGVybWlzc2lvbnMuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBTztBQUNBO0FBQ1A7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIkY6XFxBSSBjaGFpblxcY2hhaW5zaWdodC1wcm9kdWN0aW9uXFxub2RlX21vZHVsZXNcXEBzYWZlLWdsb2JhbFxcc2FmZS1hcHBzLXNka1xcZGlzdFxcZXNtXFx0eXBlc1xccGVybWlzc2lvbnMuanMiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGNvbnN0IFBFUk1JU1NJT05TX1JFUVVFU1RfUkVKRUNURUQgPSA0MDAxO1xuZXhwb3J0IGNsYXNzIFBlcm1pc3Npb25zRXJyb3IgZXh0ZW5kcyBFcnJvciB7XG4gICAgY29uc3RydWN0b3IobWVzc2FnZSwgY29kZSwgZGF0YSkge1xuICAgICAgICBzdXBlcihtZXNzYWdlKTtcbiAgICAgICAgdGhpcy5jb2RlID0gY29kZTtcbiAgICAgICAgdGhpcy5kYXRhID0gZGF0YTtcbiAgICAgICAgLy8gU2hvdWxkIGFkanVzdCBwcm90b3R5cGUgbWFudWFsbHkgYmVjYXVzZSBob3cgVFMgaGFuZGxlcyB0aGUgdHlwZSBleHRlbnNpb24gY29tcGlsYXRpb25cbiAgICAgICAgLy8gaHR0cHM6Ly9naXRodWIuY29tL01pY3Jvc29mdC9UeXBlU2NyaXB0L3dpa2kvQnJlYWtpbmctQ2hhbmdlcyNleHRlbmRpbmctYnVpbHQtaW5zLWxpa2UtZXJyb3ItYXJyYXktYW5kLW1hcC1tYXktbm8tbG9uZ2VyLXdvcmtcbiAgICAgICAgT2JqZWN0LnNldFByb3RvdHlwZU9mKHRoaXMsIFBlcm1pc3Npb25zRXJyb3IucHJvdG90eXBlKTtcbiAgICB9XG59XG4vLyMgc291cmNlTWFwcGluZ1VSTD1wZXJtaXNzaW9ucy5qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@safe-global/safe-apps-sdk/dist/esm/types/permissions.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@safe-global/safe-apps-sdk/dist/esm/types/rpc.js":
/*!***********************************************************************!*\
  !*** ./node_modules/@safe-global/safe-apps-sdk/dist/esm/types/rpc.js ***!
  \***********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n\n//# sourceMappingURL=rpc.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHNhZmUtZ2xvYmFsL3NhZmUtYXBwcy1zZGsvZGlzdC9lc20vdHlwZXMvcnBjLmpzIiwibWFwcGluZ3MiOiI7QUFBVTtBQUNWIiwic291cmNlcyI6WyJGOlxcQUkgY2hhaW5cXGNoYWluc2lnaHQtcHJvZHVjdGlvblxcbm9kZV9tb2R1bGVzXFxAc2FmZS1nbG9iYWxcXHNhZmUtYXBwcy1zZGtcXGRpc3RcXGVzbVxcdHlwZXNcXHJwYy5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQge307XG4vLyMgc291cmNlTWFwcGluZ1VSTD1ycGMuanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@safe-global/safe-apps-sdk/dist/esm/types/rpc.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@safe-global/safe-apps-sdk/dist/esm/types/sdk.js":
/*!***********************************************************************!*\
  !*** ./node_modules/@safe-global/safe-apps-sdk/dist/esm/types/sdk.js ***!
  \***********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   isObjectEIP712TypedData: () => (/* binding */ isObjectEIP712TypedData)\n/* harmony export */ });\nconst isObjectEIP712TypedData = (obj) => {\n    return typeof obj === 'object' && obj != null && 'domain' in obj && 'types' in obj && 'message' in obj;\n};\n//# sourceMappingURL=sdk.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHNhZmUtZ2xvYmFsL3NhZmUtYXBwcy1zZGsvZGlzdC9lc20vdHlwZXMvc2RrLmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBTztBQUNQO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsiRjpcXEFJIGNoYWluXFxjaGFpbnNpZ2h0LXByb2R1Y3Rpb25cXG5vZGVfbW9kdWxlc1xcQHNhZmUtZ2xvYmFsXFxzYWZlLWFwcHMtc2RrXFxkaXN0XFxlc21cXHR5cGVzXFxzZGsuanMiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGNvbnN0IGlzT2JqZWN0RUlQNzEyVHlwZWREYXRhID0gKG9iaikgPT4ge1xuICAgIHJldHVybiB0eXBlb2Ygb2JqID09PSAnb2JqZWN0JyAmJiBvYmogIT0gbnVsbCAmJiAnZG9tYWluJyBpbiBvYmogJiYgJ3R5cGVzJyBpbiBvYmogJiYgJ21lc3NhZ2UnIGluIG9iajtcbn07XG4vLyMgc291cmNlTWFwcGluZ1VSTD1zZGsuanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@safe-global/safe-apps-sdk/dist/esm/types/sdk.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@safe-global/safe-apps-sdk/dist/esm/version.js":
/*!*********************************************************************!*\
  !*** ./node_modules/@safe-global/safe-apps-sdk/dist/esm/version.js ***!
  \*********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getSDKVersion: () => (/* binding */ getSDKVersion)\n/* harmony export */ });\nconst getSDKVersion = () => '9.1.0';\n//# sourceMappingURL=version.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHNhZmUtZ2xvYmFsL3NhZmUtYXBwcy1zZGsvZGlzdC9lc20vdmVyc2lvbi5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQU87QUFDUCIsInNvdXJjZXMiOlsiRjpcXEFJIGNoYWluXFxjaGFpbnNpZ2h0LXByb2R1Y3Rpb25cXG5vZGVfbW9kdWxlc1xcQHNhZmUtZ2xvYmFsXFxzYWZlLWFwcHMtc2RrXFxkaXN0XFxlc21cXHZlcnNpb24uanMiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGNvbnN0IGdldFNES1ZlcnNpb24gPSAoKSA9PiAnOS4xLjAnO1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9dmVyc2lvbi5qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@safe-global/safe-apps-sdk/dist/esm/version.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@safe-global/safe-apps-sdk/dist/esm/wallet/index.js":
/*!**************************************************************************!*\
  !*** ./node_modules/@safe-global/safe-apps-sdk/dist/esm/wallet/index.js ***!
  \**************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Wallet: () => (/* binding */ Wallet)\n/* harmony export */ });\n/* harmony import */ var _communication_methods_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../communication/methods.js */ \"(ssr)/./node_modules/@safe-global/safe-apps-sdk/dist/esm/communication/methods.js\");\n/* harmony import */ var _types_permissions_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../types/permissions.js */ \"(ssr)/./node_modules/@safe-global/safe-apps-sdk/dist/esm/types/permissions.js\");\n\n\nclass Wallet {\n    constructor(communicator) {\n        this.communicator = communicator;\n    }\n    async getPermissions() {\n        const response = await this.communicator.send(_communication_methods_js__WEBPACK_IMPORTED_MODULE_0__.Methods.wallet_getPermissions, undefined);\n        return response.data;\n    }\n    async requestPermissions(permissions) {\n        if (!this.isPermissionRequestValid(permissions)) {\n            throw new _types_permissions_js__WEBPACK_IMPORTED_MODULE_1__.PermissionsError('Permissions request is invalid', _types_permissions_js__WEBPACK_IMPORTED_MODULE_1__.PERMISSIONS_REQUEST_REJECTED);\n        }\n        try {\n            const response = await this.communicator.send(_communication_methods_js__WEBPACK_IMPORTED_MODULE_0__.Methods.wallet_requestPermissions, permissions);\n            return response.data;\n        }\n        catch {\n            throw new _types_permissions_js__WEBPACK_IMPORTED_MODULE_1__.PermissionsError('Permissions rejected', _types_permissions_js__WEBPACK_IMPORTED_MODULE_1__.PERMISSIONS_REQUEST_REJECTED);\n        }\n    }\n    isPermissionRequestValid(permissions) {\n        return permissions.every((pr) => {\n            if (typeof pr === 'object') {\n                return Object.keys(pr).every((method) => {\n                    if (Object.values(_communication_methods_js__WEBPACK_IMPORTED_MODULE_0__.RestrictedMethods).includes(method)) {\n                        return true;\n                    }\n                    return false;\n                });\n            }\n            return false;\n        });\n    }\n}\n\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@safe-global/safe-apps-sdk/dist/esm/wallet/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@safe-global/safe-gateway-typescript-sdk/dist/config.js":
/*!******************************************************************************!*\
  !*** ./node_modules/@safe-global/safe-gateway-typescript-sdk/dist/config.js ***!
  \******************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.DEFAULT_BASE_URL = void 0;\nexports.DEFAULT_BASE_URL = 'https://safe-client.safe.global';\n//# sourceMappingURL=config.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHNhZmUtZ2xvYmFsL3NhZmUtZ2F0ZXdheS10eXBlc2NyaXB0LXNkay9kaXN0L2NvbmZpZy5qcyIsIm1hcHBpbmdzIjoiQUFBYTtBQUNiLDhDQUE2QyxFQUFFLGFBQWEsRUFBQztBQUM3RCx3QkFBd0I7QUFDeEIsd0JBQXdCO0FBQ3hCIiwic291cmNlcyI6WyJGOlxcQUkgY2hhaW5cXGNoYWluc2lnaHQtcHJvZHVjdGlvblxcbm9kZV9tb2R1bGVzXFxAc2FmZS1nbG9iYWxcXHNhZmUtZ2F0ZXdheS10eXBlc2NyaXB0LXNka1xcZGlzdFxcY29uZmlnLmpzIl0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIHN0cmljdFwiO1xuT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsIFwiX19lc01vZHVsZVwiLCB7IHZhbHVlOiB0cnVlIH0pO1xuZXhwb3J0cy5ERUZBVUxUX0JBU0VfVVJMID0gdm9pZCAwO1xuZXhwb3J0cy5ERUZBVUxUX0JBU0VfVVJMID0gJ2h0dHBzOi8vc2FmZS1jbGllbnQuc2FmZS5nbG9iYWwnO1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9Y29uZmlnLmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@safe-global/safe-gateway-typescript-sdk/dist/config.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@safe-global/safe-gateway-typescript-sdk/dist/endpoint.js":
/*!********************************************************************************!*\
  !*** ./node_modules/@safe-global/safe-gateway-typescript-sdk/dist/endpoint.js ***!
  \********************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.postEndpoint = postEndpoint;\nexports.putEndpoint = putEndpoint;\nexports.deleteEndpoint = deleteEndpoint;\nexports.getEndpoint = getEndpoint;\nconst utils_1 = __webpack_require__(/*! ./utils */ \"(ssr)/./node_modules/@safe-global/safe-gateway-typescript-sdk/dist/utils.js\");\nfunction makeUrl(baseUrl, path, pathParams, query) {\n    const pathname = (0, utils_1.insertParams)(path, pathParams);\n    const search = (0, utils_1.stringifyQuery)(query);\n    return `${baseUrl}${pathname}${search}`;\n}\nfunction postEndpoint(baseUrl, path, params) {\n    const url = makeUrl(baseUrl, path, params === null || params === void 0 ? void 0 : params.path, params === null || params === void 0 ? void 0 : params.query);\n    return (0, utils_1.fetchData)(url, 'POST', params === null || params === void 0 ? void 0 : params.body, params === null || params === void 0 ? void 0 : params.headers, params === null || params === void 0 ? void 0 : params.credentials);\n}\nfunction putEndpoint(baseUrl, path, params) {\n    const url = makeUrl(baseUrl, path, params === null || params === void 0 ? void 0 : params.path, params === null || params === void 0 ? void 0 : params.query);\n    return (0, utils_1.fetchData)(url, 'PUT', params === null || params === void 0 ? void 0 : params.body, params === null || params === void 0 ? void 0 : params.headers, params === null || params === void 0 ? void 0 : params.credentials);\n}\nfunction deleteEndpoint(baseUrl, path, params) {\n    const url = makeUrl(baseUrl, path, params === null || params === void 0 ? void 0 : params.path, params === null || params === void 0 ? void 0 : params.query);\n    return (0, utils_1.fetchData)(url, 'DELETE', params === null || params === void 0 ? void 0 : params.body, params === null || params === void 0 ? void 0 : params.headers, params === null || params === void 0 ? void 0 : params.credentials);\n}\nfunction getEndpoint(baseUrl, path, params, rawUrl) {\n    if (rawUrl) {\n        return (0, utils_1.getData)(rawUrl, undefined, params === null || params === void 0 ? void 0 : params.credentials);\n    }\n    const url = makeUrl(baseUrl, path, params === null || params === void 0 ? void 0 : params.path, params === null || params === void 0 ? void 0 : params.query);\n    return (0, utils_1.getData)(url, params === null || params === void 0 ? void 0 : params.headers, params === null || params === void 0 ? void 0 : params.credentials);\n}\n//# sourceMappingURL=endpoint.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@safe-global/safe-gateway-typescript-sdk/dist/endpoint.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@safe-global/safe-gateway-typescript-sdk/dist/index.js":
/*!*****************************************************************************!*\
  !*** ./node_modules/@safe-global/safe-gateway-typescript-sdk/dist/index.js ***!
  \*****************************************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

eval("\nvar __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    var desc = Object.getOwnPropertyDescriptor(m, k);\n    if (!desc || (\"get\" in desc ? !m.__esModule : desc.writable || desc.configurable)) {\n      desc = { enumerable: true, get: function() { return m[k]; } };\n    }\n    Object.defineProperty(o, k2, desc);\n}) : (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    o[k2] = m[k];\n}));\nvar __exportStar = (this && this.__exportStar) || function(m, exports) {\n    for (var p in m) if (p !== \"default\" && !Object.prototype.hasOwnProperty.call(exports, p)) __createBinding(exports, m, p);\n};\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.setBaseUrl = void 0;\nexports.relayTransaction = relayTransaction;\nexports.getRelayCount = getRelayCount;\nexports.getSafeInfo = getSafeInfo;\nexports.getIncomingTransfers = getIncomingTransfers;\nexports.getModuleTransactions = getModuleTransactions;\nexports.getMultisigTransactions = getMultisigTransactions;\nexports.getBalances = getBalances;\nexports.getFiatCurrencies = getFiatCurrencies;\nexports.getOwnedSafes = getOwnedSafes;\nexports.getAllOwnedSafes = getAllOwnedSafes;\nexports.getCollectibles = getCollectibles;\nexports.getCollectiblesPage = getCollectiblesPage;\nexports.getTransactionHistory = getTransactionHistory;\nexports.getTransactionQueue = getTransactionQueue;\nexports.getTransactionDetails = getTransactionDetails;\nexports.deleteTransaction = deleteTransaction;\nexports.postSafeGasEstimation = postSafeGasEstimation;\nexports.getNonces = getNonces;\nexports.proposeTransaction = proposeTransaction;\nexports.getConfirmationView = getConfirmationView;\nexports.getTxPreview = getTxPreview;\nexports.getChainsConfig = getChainsConfig;\nexports.getChainConfig = getChainConfig;\nexports.getSafeApps = getSafeApps;\nexports.getMasterCopies = getMasterCopies;\nexports.getDecodedData = getDecodedData;\nexports.getSafeMessages = getSafeMessages;\nexports.getSafeMessage = getSafeMessage;\nexports.proposeSafeMessage = proposeSafeMessage;\nexports.confirmSafeMessage = confirmSafeMessage;\nexports.getDelegates = getDelegates;\nexports.registerDevice = registerDevice;\nexports.unregisterSafe = unregisterSafe;\nexports.unregisterDevice = unregisterDevice;\nexports.registerEmail = registerEmail;\nexports.changeEmail = changeEmail;\nexports.resendEmailVerificationCode = resendEmailVerificationCode;\nexports.verifyEmail = verifyEmail;\nexports.getRegisteredEmail = getRegisteredEmail;\nexports.deleteRegisteredEmail = deleteRegisteredEmail;\nexports.registerRecoveryModule = registerRecoveryModule;\nexports.unsubscribeSingle = unsubscribeSingle;\nexports.unsubscribeAll = unsubscribeAll;\nexports.getSafeOverviews = getSafeOverviews;\nexports.getContract = getContract;\nexports.getAuthNonce = getAuthNonce;\nexports.verifyAuth = verifyAuth;\nexports.createAccount = createAccount;\nexports.getAccount = getAccount;\nexports.deleteAccount = deleteAccount;\nexports.getAccountDataTypes = getAccountDataTypes;\nexports.getAccountDataSettings = getAccountDataSettings;\nexports.putAccountDataSettings = putAccountDataSettings;\nexports.getIndexingStatus = getIndexingStatus;\nconst endpoint_1 = __webpack_require__(/*! ./endpoint */ \"(ssr)/./node_modules/@safe-global/safe-gateway-typescript-sdk/dist/endpoint.js\");\nconst config_1 = __webpack_require__(/*! ./config */ \"(ssr)/./node_modules/@safe-global/safe-gateway-typescript-sdk/dist/config.js\");\n__exportStar(__webpack_require__(/*! ./types/safe-info */ \"(ssr)/./node_modules/@safe-global/safe-gateway-typescript-sdk/dist/types/safe-info.js\"), exports);\n__exportStar(__webpack_require__(/*! ./types/safe-apps */ \"(ssr)/./node_modules/@safe-global/safe-gateway-typescript-sdk/dist/types/safe-apps.js\"), exports);\n__exportStar(__webpack_require__(/*! ./types/transactions */ \"(ssr)/./node_modules/@safe-global/safe-gateway-typescript-sdk/dist/types/transactions.js\"), exports);\n__exportStar(__webpack_require__(/*! ./types/chains */ \"(ssr)/./node_modules/@safe-global/safe-gateway-typescript-sdk/dist/types/chains.js\"), exports);\n__exportStar(__webpack_require__(/*! ./types/common */ \"(ssr)/./node_modules/@safe-global/safe-gateway-typescript-sdk/dist/types/common.js\"), exports);\n__exportStar(__webpack_require__(/*! ./types/master-copies */ \"(ssr)/./node_modules/@safe-global/safe-gateway-typescript-sdk/dist/types/master-copies.js\"), exports);\n__exportStar(__webpack_require__(/*! ./types/decoded-data */ \"(ssr)/./node_modules/@safe-global/safe-gateway-typescript-sdk/dist/types/decoded-data.js\"), exports);\n__exportStar(__webpack_require__(/*! ./types/safe-messages */ \"(ssr)/./node_modules/@safe-global/safe-gateway-typescript-sdk/dist/types/safe-messages.js\"), exports);\n__exportStar(__webpack_require__(/*! ./types/notifications */ \"(ssr)/./node_modules/@safe-global/safe-gateway-typescript-sdk/dist/types/notifications.js\"), exports);\n__exportStar(__webpack_require__(/*! ./types/relay */ \"(ssr)/./node_modules/@safe-global/safe-gateway-typescript-sdk/dist/types/relay.js\"), exports);\n// Can be set externally to a different CGW host\nlet baseUrl = config_1.DEFAULT_BASE_URL;\n/**\n * Set the base CGW URL\n */\nconst setBaseUrl = (url) => {\n    baseUrl = url;\n};\nexports.setBaseUrl = setBaseUrl;\n/* eslint-disable @typescript-eslint/explicit-module-boundary-types */\n/**\n * Relay a transaction from a Safe\n */\nfunction relayTransaction(chainId, body) {\n    return (0, endpoint_1.postEndpoint)(baseUrl, '/v1/chains/{chainId}/relay', { path: { chainId }, body });\n}\n/**\n * Get the relay limit and number of remaining relays remaining\n */\nfunction getRelayCount(chainId, address) {\n    return (0, endpoint_1.getEndpoint)(baseUrl, '/v1/chains/{chainId}/relay/{address}', { path: { chainId, address } });\n}\n/**\n * Get basic information about a Safe. E.g. owners, modules, version etc\n */\nfunction getSafeInfo(chainId, address) {\n    return (0, endpoint_1.getEndpoint)(baseUrl, '/v1/chains/{chainId}/safes/{address}', { path: { chainId, address } });\n}\n/**\n * Get filterable list of incoming transactions\n */\nfunction getIncomingTransfers(chainId, address, query, pageUrl) {\n    return (0, endpoint_1.getEndpoint)(baseUrl, '/v1/chains/{chainId}/safes/{address}/incoming-transfers/', {\n        path: { chainId, address },\n        query,\n    }, pageUrl);\n}\n/**\n * Get filterable list of module transactions\n */\nfunction getModuleTransactions(chainId, address, query, pageUrl) {\n    return (0, endpoint_1.getEndpoint)(baseUrl, '/v1/chains/{chainId}/safes/{address}/module-transactions/', {\n        path: { chainId, address },\n        query,\n    }, pageUrl);\n}\n/**\n * Get filterable list of multisig transactions\n */\nfunction getMultisigTransactions(chainId, address, query, pageUrl) {\n    return (0, endpoint_1.getEndpoint)(baseUrl, '/v1/chains/{chainId}/safes/{address}/multisig-transactions/', {\n        path: { chainId, address },\n        query,\n    }, pageUrl);\n}\n/**\n * Get the total balance and all assets stored in a Safe\n */\nfunction getBalances(chainId, address, currency = 'usd', query = {}) {\n    return (0, endpoint_1.getEndpoint)(baseUrl, '/v1/chains/{chainId}/safes/{address}/balances/{currency}', {\n        path: { chainId, address, currency },\n        query,\n    });\n}\n/**\n * Get a list of supported fiat currencies (e.g. USD, EUR etc)\n */\nfunction getFiatCurrencies() {\n    return (0, endpoint_1.getEndpoint)(baseUrl, '/v1/balances/supported-fiat-codes');\n}\n/**\n * Get the addresses of all Safes belonging to an owner\n */\nfunction getOwnedSafes(chainId, address) {\n    return (0, endpoint_1.getEndpoint)(baseUrl, '/v1/chains/{chainId}/owners/{address}/safes', { path: { chainId, address } });\n}\n/**\n * Get the addresses of all Safes belonging to an owner on all chains\n */\nfunction getAllOwnedSafes(address) {\n    return (0, endpoint_1.getEndpoint)(baseUrl, '/v1/owners/{address}/safes', { path: { address } });\n}\n/**\n * Get NFTs stored in a Safe\n */\nfunction getCollectibles(chainId, address, query = {}) {\n    return (0, endpoint_1.getEndpoint)(baseUrl, '/v1/chains/{chainId}/safes/{address}/collectibles', {\n        path: { chainId, address },\n        query,\n    });\n}\n/**\n * Get NFTs stored in a Safe\n */\nfunction getCollectiblesPage(chainId, address, query = {}, pageUrl) {\n    return (0, endpoint_1.getEndpoint)(baseUrl, '/v2/chains/{chainId}/safes/{address}/collectibles', { path: { chainId, address }, query }, pageUrl);\n}\n/**\n * Get a list of past Safe transactions\n */\nfunction getTransactionHistory(chainId, address, query = {}, pageUrl) {\n    return (0, endpoint_1.getEndpoint)(baseUrl, '/v1/chains/{chainId}/safes/{safe_address}/transactions/history', { path: { chainId, safe_address: address }, query }, pageUrl);\n}\n/**\n * Get the list of pending transactions\n */\nfunction getTransactionQueue(chainId, address, query = {}, pageUrl) {\n    return (0, endpoint_1.getEndpoint)(baseUrl, '/v1/chains/{chainId}/safes/{safe_address}/transactions/queued', { path: { chainId, safe_address: address }, query }, pageUrl);\n}\n/**\n * Get the details of an individual transaction by its id\n */\nfunction getTransactionDetails(chainId, transactionId) {\n    return (0, endpoint_1.getEndpoint)(baseUrl, '/v1/chains/{chainId}/transactions/{transactionId}', {\n        path: { chainId, transactionId },\n    });\n}\n/**\n * Delete a transaction by its safeTxHash\n */\nfunction deleteTransaction(chainId, safeTxHash, signature) {\n    return (0, endpoint_1.deleteEndpoint)(baseUrl, '/v1/chains/{chainId}/transactions/{safeTxHash}', {\n        path: { chainId, safeTxHash },\n        body: { signature },\n    });\n}\n/**\n * Request a gas estimate & recommmended tx nonce for a created transaction\n */\nfunction postSafeGasEstimation(chainId, address, body) {\n    return (0, endpoint_1.postEndpoint)(baseUrl, '/v2/chains/{chainId}/safes/{safe_address}/multisig-transactions/estimations', {\n        path: { chainId, safe_address: address },\n        body,\n    });\n}\nfunction getNonces(chainId, address) {\n    return (0, endpoint_1.getEndpoint)(baseUrl, '/v1/chains/{chainId}/safes/{safe_address}/nonces', {\n        path: { chainId, safe_address: address },\n    });\n}\n/**\n * Propose a new transaction for other owners to sign/execute\n */\nfunction proposeTransaction(chainId, address, body) {\n    return (0, endpoint_1.postEndpoint)(baseUrl, '/v1/chains/{chainId}/transactions/{safe_address}/propose', {\n        path: { chainId, safe_address: address },\n        body,\n    });\n}\n/**\n * Returns decoded data\n */\nfunction getConfirmationView(chainId, safeAddress, operation, data, to, value) {\n    return (0, endpoint_1.postEndpoint)(baseUrl, '/v1/chains/{chainId}/safes/{safe_address}/views/transaction-confirmation', {\n        path: { chainId, safe_address: safeAddress },\n        body: { operation, data, to, value },\n    });\n}\n/**\n * Get a tx preview\n */\nfunction getTxPreview(chainId, safeAddress, operation, data, to, value) {\n    return (0, endpoint_1.postEndpoint)(baseUrl, '/v1/chains/{chainId}/transactions/{safe_address}/preview', {\n        path: { chainId, safe_address: safeAddress },\n        body: { operation, data, to, value },\n    });\n}\n/**\n * Returns all defined chain configs\n */\nfunction getChainsConfig(query) {\n    return (0, endpoint_1.getEndpoint)(baseUrl, '/v1/chains', {\n        query,\n    });\n}\n/**\n * Returns a chain config\n */\nfunction getChainConfig(chainId) {\n    return (0, endpoint_1.getEndpoint)(baseUrl, '/v1/chains/{chainId}', {\n        path: { chainId: chainId },\n    });\n}\n/**\n * Returns Safe Apps List\n */\nfunction getSafeApps(chainId, query = {}) {\n    return (0, endpoint_1.getEndpoint)(baseUrl, '/v1/chains/{chainId}/safe-apps', {\n        path: { chainId: chainId },\n        query,\n    });\n}\n/**\n * Returns list of Master Copies\n */\nfunction getMasterCopies(chainId) {\n    return (0, endpoint_1.getEndpoint)(baseUrl, '/v1/chains/{chainId}/about/master-copies', {\n        path: { chainId: chainId },\n    });\n}\n/**\n * Returns decoded data\n */\nfunction getDecodedData(chainId, operation, encodedData, to) {\n    return (0, endpoint_1.postEndpoint)(baseUrl, '/v1/chains/{chainId}/data-decoder', {\n        path: { chainId: chainId },\n        body: { operation, data: encodedData, to },\n    });\n}\n/**\n * Returns list of `SafeMessage`s\n */\nfunction getSafeMessages(chainId, address, pageUrl) {\n    return (0, endpoint_1.getEndpoint)(baseUrl, '/v1/chains/{chainId}/safes/{safe_address}/messages', { path: { chainId, safe_address: address }, query: {} }, pageUrl);\n}\n/**\n * Returns a `SafeMessage`\n */\nfunction getSafeMessage(chainId, messageHash) {\n    return (0, endpoint_1.getEndpoint)(baseUrl, '/v1/chains/{chainId}/messages/{message_hash}', {\n        path: { chainId, message_hash: messageHash },\n    });\n}\n/**\n * Propose a new `SafeMessage` for other owners to sign\n */\nfunction proposeSafeMessage(chainId, address, body) {\n    return (0, endpoint_1.postEndpoint)(baseUrl, '/v1/chains/{chainId}/safes/{safe_address}/messages', {\n        path: { chainId, safe_address: address },\n        body,\n    });\n}\n/**\n * Add a confirmation to a `SafeMessage`\n */\nfunction confirmSafeMessage(chainId, messageHash, body) {\n    return (0, endpoint_1.postEndpoint)(baseUrl, '/v1/chains/{chainId}/messages/{message_hash}/signatures', {\n        path: { chainId, message_hash: messageHash },\n        body,\n    });\n}\n/**\n * Returns a list of delegates\n */\nfunction getDelegates(chainId, query = {}) {\n    return (0, endpoint_1.getEndpoint)(baseUrl, '/v2/chains/{chainId}/delegates', {\n        path: { chainId },\n        query,\n    });\n}\n/**\n * Registers a device/Safe for notifications\n */\nfunction registerDevice(body) {\n    return (0, endpoint_1.postEndpoint)(baseUrl, '/v1/register/notifications', {\n        body,\n    });\n}\n/**\n * Unregisters a Safe from notifications\n */\nfunction unregisterSafe(chainId, address, uuid) {\n    return (0, endpoint_1.deleteEndpoint)(baseUrl, '/v1/chains/{chainId}/notifications/devices/{uuid}/safes/{safe_address}', {\n        path: { chainId, safe_address: address, uuid },\n    });\n}\n/**\n * Unregisters a device from notifications\n */\nfunction unregisterDevice(chainId, uuid) {\n    return (0, endpoint_1.deleteEndpoint)(baseUrl, '/v1/chains/{chainId}/notifications/devices/{uuid}', {\n        path: { chainId, uuid },\n    });\n}\n/**\n * Registers a email address for a safe signer.\n *\n * The signer wallet has to sign a message of format: `email-register-{chainId}-{safeAddress}-{emailAddress}-{signer}-{timestamp}`\n * The signature is valid for 5 minutes.\n *\n * @param chainId\n * @param safeAddress\n * @param body Signer address and email address\n * @param headers Signature and Signature timestamp\n * @returns 200 if signature matches the data\n */\nfunction registerEmail(chainId, safeAddress, body, headers) {\n    return (0, endpoint_1.postEndpoint)(baseUrl, '/v1/chains/{chainId}/safes/{safe_address}/emails', {\n        path: { chainId, safe_address: safeAddress },\n        body,\n        headers,\n    });\n}\n/**\n * Changes an already registered email address for a safe signer. The new email address still needs to be verified.\n *\n * The signer wallet has to sign a message of format: `email-edit-{chainId}-{safeAddress}-{emailAddress}-{signer}-{timestamp}`\n * The signature is valid for 5 minutes.\n *\n * @param chainId\n * @param safeAddress\n * @param signerAddress\n * @param body New email address\n * @param headers Signature and Signature timestamp\n * @returns 202 if signature matches the data\n */\nfunction changeEmail(chainId, safeAddress, signerAddress, body, headers) {\n    return (0, endpoint_1.putEndpoint)(baseUrl, '/v1/chains/{chainId}/safes/{safe_address}/emails/{signer}', {\n        path: { chainId, safe_address: safeAddress, signer: signerAddress },\n        body,\n        headers,\n    });\n}\n/**\n * Resends an email verification code.\n */\nfunction resendEmailVerificationCode(chainId, safeAddress, signerAddress) {\n    return (0, endpoint_1.postEndpoint)(baseUrl, '/v1/chains/{chainId}/safes/{safe_address}/emails/{signer}/verify-resend', {\n        path: { chainId, safe_address: safeAddress, signer: signerAddress },\n        body: '',\n    });\n}\n/**\n * Verifies a pending email address registration.\n *\n * @param chainId\n * @param safeAddress\n * @param signerAddress address who signed the email registration\n * @param body Verification code\n */\nfunction verifyEmail(chainId, safeAddress, signerAddress, body) {\n    return (0, endpoint_1.putEndpoint)(baseUrl, '/v1/chains/{chainId}/safes/{safe_address}/emails/{signer}/verify', {\n        path: { chainId, safe_address: safeAddress, signer: signerAddress },\n        body,\n    });\n}\n/**\n * Gets the registered email address of the signer\n *\n * The signer wallet will have to sign a message of format: `email-retrieval-{chainId}-{safe}-{signer}-{timestamp}`\n * The signature is valid for 5 minutes.\n *\n * @param chainId\n * @param safeAddress\n * @param signerAddress address of the owner of the Safe\n *\n * @returns email address and verified flag\n */\nfunction getRegisteredEmail(chainId, safeAddress, signerAddress, headers) {\n    return (0, endpoint_1.getEndpoint)(baseUrl, '/v1/chains/{chainId}/safes/{safe_address}/emails/{signer}', {\n        path: { chainId, safe_address: safeAddress, signer: signerAddress },\n        headers,\n    });\n}\n/**\n * Delete a registered email address for the signer\n *\n * The signer wallet will have to sign a message of format: `email-delete-{chainId}-{safe}-{signer}-{timestamp}`\n * The signature is valid for 5 minutes.\n *\n * @param chainId\n * @param safeAddress\n * @param signerAddress\n * @param headers\n */\nfunction deleteRegisteredEmail(chainId, safeAddress, signerAddress, headers) {\n    return (0, endpoint_1.deleteEndpoint)(baseUrl, '/v1/chains/{chainId}/safes/{safe_address}/emails/{signer}', {\n        path: { chainId, safe_address: safeAddress, signer: signerAddress },\n        headers,\n    });\n}\n/**\n * Register a recovery module for receiving alerts\n * @param chainId\n * @param safeAddress\n * @param body - { moduleAddress: string }\n */\nfunction registerRecoveryModule(chainId, safeAddress, body) {\n    return (0, endpoint_1.postEndpoint)(baseUrl, '/v1/chains/{chainId}/safes/{safe_address}/recovery', {\n        path: { chainId, safe_address: safeAddress },\n        body,\n    });\n}\n/**\n * Delete email subscription for a single category\n * @param query\n */\nfunction unsubscribeSingle(query) {\n    return (0, endpoint_1.deleteEndpoint)(baseUrl, '/v1/subscriptions', { query });\n}\n/**\n * Delete email subscription for all categories\n * @param query\n */\nfunction unsubscribeAll(query) {\n    return (0, endpoint_1.deleteEndpoint)(baseUrl, '/v1/subscriptions/all', { query });\n}\n/**\n * Get Safe overviews per address\n */\nfunction getSafeOverviews(safes, query) {\n    return (0, endpoint_1.getEndpoint)(baseUrl, '/v1/safes', {\n        query: Object.assign(Object.assign({}, query), { safes: safes.join(',') }),\n    });\n}\nfunction getContract(chainId, contractAddress) {\n    return (0, endpoint_1.getEndpoint)(baseUrl, '/v1/chains/{chainId}/contracts/{contractAddress}', {\n        path: {\n            chainId: chainId,\n            contractAddress: contractAddress,\n        },\n    });\n}\nfunction getAuthNonce() {\n    return (0, endpoint_1.getEndpoint)(baseUrl, '/v1/auth/nonce', { credentials: 'include' });\n}\nfunction verifyAuth(body) {\n    return (0, endpoint_1.postEndpoint)(baseUrl, '/v1/auth/verify', {\n        body,\n        credentials: 'include',\n    });\n}\nfunction createAccount(body) {\n    return (0, endpoint_1.postEndpoint)(baseUrl, '/v1/accounts', {\n        body,\n        credentials: 'include',\n    });\n}\nfunction getAccount(address) {\n    return (0, endpoint_1.getEndpoint)(baseUrl, '/v1/accounts/{address}', {\n        path: { address },\n        credentials: 'include',\n    });\n}\nfunction deleteAccount(address) {\n    return (0, endpoint_1.deleteEndpoint)(baseUrl, '/v1/accounts/{address}', {\n        path: { address },\n        credentials: 'include',\n    });\n}\nfunction getAccountDataTypes() {\n    return (0, endpoint_1.getEndpoint)(baseUrl, '/v1/accounts/data-types');\n}\nfunction getAccountDataSettings(address) {\n    return (0, endpoint_1.getEndpoint)(baseUrl, '/v1/accounts/{address}/data-settings', {\n        path: { address },\n        credentials: 'include',\n    });\n}\nfunction putAccountDataSettings(address, body) {\n    return (0, endpoint_1.putEndpoint)(baseUrl, '/v1/accounts/{address}/data-settings', {\n        path: { address },\n        body,\n        credentials: 'include',\n    });\n}\nfunction getIndexingStatus(chainId) {\n    return (0, endpoint_1.getEndpoint)(baseUrl, '/v1/chains/{chainId}/about/indexing', {\n        path: { chainId },\n    });\n}\n/* eslint-enable @typescript-eslint/explicit-module-boundary-types */\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@safe-global/safe-gateway-typescript-sdk/dist/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@safe-global/safe-gateway-typescript-sdk/dist/types/chains.js":
/*!************************************************************************************!*\
  !*** ./node_modules/@safe-global/safe-gateway-typescript-sdk/dist/types/chains.js ***!
  \************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.FEATURES = exports.GAS_PRICE_TYPE = exports.RPC_AUTHENTICATION = void 0;\nvar RPC_AUTHENTICATION;\n(function (RPC_AUTHENTICATION) {\n    RPC_AUTHENTICATION[\"API_KEY_PATH\"] = \"API_KEY_PATH\";\n    RPC_AUTHENTICATION[\"NO_AUTHENTICATION\"] = \"NO_AUTHENTICATION\";\n    RPC_AUTHENTICATION[\"UNKNOWN\"] = \"UNKNOWN\";\n})(RPC_AUTHENTICATION || (exports.RPC_AUTHENTICATION = RPC_AUTHENTICATION = {}));\nvar GAS_PRICE_TYPE;\n(function (GAS_PRICE_TYPE) {\n    GAS_PRICE_TYPE[\"ORACLE\"] = \"ORACLE\";\n    GAS_PRICE_TYPE[\"FIXED\"] = \"FIXED\";\n    GAS_PRICE_TYPE[\"FIXED_1559\"] = \"FIXED1559\";\n    GAS_PRICE_TYPE[\"UNKNOWN\"] = \"UNKNOWN\";\n})(GAS_PRICE_TYPE || (exports.GAS_PRICE_TYPE = GAS_PRICE_TYPE = {}));\nvar FEATURES;\n(function (FEATURES) {\n    FEATURES[\"ERC721\"] = \"ERC721\";\n    FEATURES[\"SAFE_APPS\"] = \"SAFE_APPS\";\n    FEATURES[\"CONTRACT_INTERACTION\"] = \"CONTRACT_INTERACTION\";\n    FEATURES[\"DOMAIN_LOOKUP\"] = \"DOMAIN_LOOKUP\";\n    FEATURES[\"SPENDING_LIMIT\"] = \"SPENDING_LIMIT\";\n    FEATURES[\"EIP1559\"] = \"EIP1559\";\n    FEATURES[\"SAFE_TX_GAS_OPTIONAL\"] = \"SAFE_TX_GAS_OPTIONAL\";\n    FEATURES[\"TX_SIMULATION\"] = \"TX_SIMULATION\";\n    FEATURES[\"EIP1271\"] = \"EIP1271\";\n})(FEATURES || (exports.FEATURES = FEATURES = {}));\n//# sourceMappingURL=chains.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@safe-global/safe-gateway-typescript-sdk/dist/types/chains.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@safe-global/safe-gateway-typescript-sdk/dist/types/common.js":
/*!************************************************************************************!*\
  !*** ./node_modules/@safe-global/safe-gateway-typescript-sdk/dist/types/common.js ***!
  \************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.TokenType = void 0;\nvar TokenType;\n(function (TokenType) {\n    TokenType[\"ERC20\"] = \"ERC20\";\n    TokenType[\"ERC721\"] = \"ERC721\";\n    TokenType[\"NATIVE_TOKEN\"] = \"NATIVE_TOKEN\";\n    TokenType[\"UNKNOWN\"] = \"UNKNOWN\";\n})(TokenType || (exports.TokenType = TokenType = {}));\n//# sourceMappingURL=common.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHNhZmUtZ2xvYmFsL3NhZmUtZ2F0ZXdheS10eXBlc2NyaXB0LXNkay9kaXN0L3R5cGVzL2NvbW1vbi5qcyIsIm1hcHBpbmdzIjoiQUFBYTtBQUNiLDhDQUE2QyxFQUFFLGFBQWEsRUFBQztBQUM3RCxpQkFBaUI7QUFDakI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsQ0FBQyxnQkFBZ0IsaUJBQWlCLGlCQUFpQjtBQUNuRCIsInNvdXJjZXMiOlsiRjpcXEFJIGNoYWluXFxjaGFpbnNpZ2h0LXByb2R1Y3Rpb25cXG5vZGVfbW9kdWxlc1xcQHNhZmUtZ2xvYmFsXFxzYWZlLWdhdGV3YXktdHlwZXNjcmlwdC1zZGtcXGRpc3RcXHR5cGVzXFxjb21tb24uanMiXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2Ugc3RyaWN0XCI7XG5PYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgXCJfX2VzTW9kdWxlXCIsIHsgdmFsdWU6IHRydWUgfSk7XG5leHBvcnRzLlRva2VuVHlwZSA9IHZvaWQgMDtcbnZhciBUb2tlblR5cGU7XG4oZnVuY3Rpb24gKFRva2VuVHlwZSkge1xuICAgIFRva2VuVHlwZVtcIkVSQzIwXCJdID0gXCJFUkMyMFwiO1xuICAgIFRva2VuVHlwZVtcIkVSQzcyMVwiXSA9IFwiRVJDNzIxXCI7XG4gICAgVG9rZW5UeXBlW1wiTkFUSVZFX1RPS0VOXCJdID0gXCJOQVRJVkVfVE9LRU5cIjtcbiAgICBUb2tlblR5cGVbXCJVTktOT1dOXCJdID0gXCJVTktOT1dOXCI7XG59KShUb2tlblR5cGUgfHwgKGV4cG9ydHMuVG9rZW5UeXBlID0gVG9rZW5UeXBlID0ge30pKTtcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPWNvbW1vbi5qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@safe-global/safe-gateway-typescript-sdk/dist/types/common.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@safe-global/safe-gateway-typescript-sdk/dist/types/decoded-data.js":
/*!******************************************************************************************!*\
  !*** ./node_modules/@safe-global/safe-gateway-typescript-sdk/dist/types/decoded-data.js ***!
  \******************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.NativeStakingStatus = exports.ConfirmationViewTypes = void 0;\nvar ConfirmationViewTypes;\n(function (ConfirmationViewTypes) {\n    ConfirmationViewTypes[\"GENERIC\"] = \"GENERIC\";\n    ConfirmationViewTypes[\"COW_SWAP_ORDER\"] = \"COW_SWAP_ORDER\";\n    ConfirmationViewTypes[\"COW_SWAP_TWAP_ORDER\"] = \"COW_SWAP_TWAP_ORDER\";\n    ConfirmationViewTypes[\"KILN_NATIVE_STAKING_DEPOSIT\"] = \"KILN_NATIVE_STAKING_DEPOSIT\";\n    ConfirmationViewTypes[\"KILN_NATIVE_STAKING_VALIDATORS_EXIT\"] = \"KILN_NATIVE_STAKING_VALIDATORS_EXIT\";\n    ConfirmationViewTypes[\"KILN_NATIVE_STAKING_WITHDRAW\"] = \"KILN_NATIVE_STAKING_WITHDRAW\";\n})(ConfirmationViewTypes || (exports.ConfirmationViewTypes = ConfirmationViewTypes = {}));\nvar NativeStakingStatus;\n(function (NativeStakingStatus) {\n    NativeStakingStatus[\"NOT_STAKED\"] = \"NOT_STAKED\";\n    NativeStakingStatus[\"ACTIVATING\"] = \"ACTIVATING\";\n    NativeStakingStatus[\"DEPOSIT_IN_PROGRESS\"] = \"DEPOSIT_IN_PROGRESS\";\n    NativeStakingStatus[\"ACTIVE\"] = \"ACTIVE\";\n    NativeStakingStatus[\"EXIT_REQUESTED\"] = \"EXIT_REQUESTED\";\n    NativeStakingStatus[\"EXITING\"] = \"EXITING\";\n    NativeStakingStatus[\"EXITED\"] = \"EXITED\";\n    NativeStakingStatus[\"SLASHED\"] = \"SLASHED\";\n})(NativeStakingStatus || (exports.NativeStakingStatus = NativeStakingStatus = {}));\n//# sourceMappingURL=decoded-data.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@safe-global/safe-gateway-typescript-sdk/dist/types/decoded-data.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@safe-global/safe-gateway-typescript-sdk/dist/types/master-copies.js":
/*!*******************************************************************************************!*\
  !*** ./node_modules/@safe-global/safe-gateway-typescript-sdk/dist/types/master-copies.js ***!
  \*******************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\n//# sourceMappingURL=master-copies.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHNhZmUtZ2xvYmFsL3NhZmUtZ2F0ZXdheS10eXBlc2NyaXB0LXNkay9kaXN0L3R5cGVzL21hc3Rlci1jb3BpZXMuanMiLCJtYXBwaW5ncyI6IkFBQWE7QUFDYiw4Q0FBNkMsRUFBRSxhQUFhLEVBQUM7QUFDN0QiLCJzb3VyY2VzIjpbIkY6XFxBSSBjaGFpblxcY2hhaW5zaWdodC1wcm9kdWN0aW9uXFxub2RlX21vZHVsZXNcXEBzYWZlLWdsb2JhbFxcc2FmZS1nYXRld2F5LXR5cGVzY3JpcHQtc2RrXFxkaXN0XFx0eXBlc1xcbWFzdGVyLWNvcGllcy5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBzdHJpY3RcIjtcbk9iamVjdC5kZWZpbmVQcm9wZXJ0eShleHBvcnRzLCBcIl9fZXNNb2R1bGVcIiwgeyB2YWx1ZTogdHJ1ZSB9KTtcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPW1hc3Rlci1jb3BpZXMuanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@safe-global/safe-gateway-typescript-sdk/dist/types/master-copies.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@safe-global/safe-gateway-typescript-sdk/dist/types/notifications.js":
/*!*******************************************************************************************!*\
  !*** ./node_modules/@safe-global/safe-gateway-typescript-sdk/dist/types/notifications.js ***!
  \*******************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.DeviceType = void 0;\nvar DeviceType;\n(function (DeviceType) {\n    DeviceType[\"ANDROID\"] = \"ANDROID\";\n    DeviceType[\"IOS\"] = \"IOS\";\n    DeviceType[\"WEB\"] = \"WEB\";\n})(DeviceType || (exports.DeviceType = DeviceType = {}));\n//# sourceMappingURL=notifications.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHNhZmUtZ2xvYmFsL3NhZmUtZ2F0ZXdheS10eXBlc2NyaXB0LXNkay9kaXN0L3R5cGVzL25vdGlmaWNhdGlvbnMuanMiLCJtYXBwaW5ncyI6IkFBQWE7QUFDYiw4Q0FBNkMsRUFBRSxhQUFhLEVBQUM7QUFDN0Qsa0JBQWtCO0FBQ2xCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxDQUFDLGlCQUFpQixrQkFBa0Isa0JBQWtCO0FBQ3REIiwic291cmNlcyI6WyJGOlxcQUkgY2hhaW5cXGNoYWluc2lnaHQtcHJvZHVjdGlvblxcbm9kZV9tb2R1bGVzXFxAc2FmZS1nbG9iYWxcXHNhZmUtZ2F0ZXdheS10eXBlc2NyaXB0LXNka1xcZGlzdFxcdHlwZXNcXG5vdGlmaWNhdGlvbnMuanMiXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2Ugc3RyaWN0XCI7XG5PYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgXCJfX2VzTW9kdWxlXCIsIHsgdmFsdWU6IHRydWUgfSk7XG5leHBvcnRzLkRldmljZVR5cGUgPSB2b2lkIDA7XG52YXIgRGV2aWNlVHlwZTtcbihmdW5jdGlvbiAoRGV2aWNlVHlwZSkge1xuICAgIERldmljZVR5cGVbXCJBTkRST0lEXCJdID0gXCJBTkRST0lEXCI7XG4gICAgRGV2aWNlVHlwZVtcIklPU1wiXSA9IFwiSU9TXCI7XG4gICAgRGV2aWNlVHlwZVtcIldFQlwiXSA9IFwiV0VCXCI7XG59KShEZXZpY2VUeXBlIHx8IChleHBvcnRzLkRldmljZVR5cGUgPSBEZXZpY2VUeXBlID0ge30pKTtcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPW5vdGlmaWNhdGlvbnMuanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@safe-global/safe-gateway-typescript-sdk/dist/types/notifications.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@safe-global/safe-gateway-typescript-sdk/dist/types/relay.js":
/*!***********************************************************************************!*\
  !*** ./node_modules/@safe-global/safe-gateway-typescript-sdk/dist/types/relay.js ***!
  \***********************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\n//# sourceMappingURL=relay.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHNhZmUtZ2xvYmFsL3NhZmUtZ2F0ZXdheS10eXBlc2NyaXB0LXNkay9kaXN0L3R5cGVzL3JlbGF5LmpzIiwibWFwcGluZ3MiOiJBQUFhO0FBQ2IsOENBQTZDLEVBQUUsYUFBYSxFQUFDO0FBQzdEIiwic291cmNlcyI6WyJGOlxcQUkgY2hhaW5cXGNoYWluc2lnaHQtcHJvZHVjdGlvblxcbm9kZV9tb2R1bGVzXFxAc2FmZS1nbG9iYWxcXHNhZmUtZ2F0ZXdheS10eXBlc2NyaXB0LXNka1xcZGlzdFxcdHlwZXNcXHJlbGF5LmpzIl0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIHN0cmljdFwiO1xuT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsIFwiX19lc01vZHVsZVwiLCB7IHZhbHVlOiB0cnVlIH0pO1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9cmVsYXkuanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@safe-global/safe-gateway-typescript-sdk/dist/types/relay.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@safe-global/safe-gateway-typescript-sdk/dist/types/safe-apps.js":
/*!***************************************************************************************!*\
  !*** ./node_modules/@safe-global/safe-gateway-typescript-sdk/dist/types/safe-apps.js ***!
  \***************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.SafeAppSocialPlatforms = exports.SafeAppFeatures = exports.SafeAppAccessPolicyTypes = void 0;\nvar SafeAppAccessPolicyTypes;\n(function (SafeAppAccessPolicyTypes) {\n    SafeAppAccessPolicyTypes[\"NoRestrictions\"] = \"NO_RESTRICTIONS\";\n    SafeAppAccessPolicyTypes[\"DomainAllowlist\"] = \"DOMAIN_ALLOWLIST\";\n})(SafeAppAccessPolicyTypes || (exports.SafeAppAccessPolicyTypes = SafeAppAccessPolicyTypes = {}));\nvar SafeAppFeatures;\n(function (SafeAppFeatures) {\n    SafeAppFeatures[\"BATCHED_TRANSACTIONS\"] = \"BATCHED_TRANSACTIONS\";\n})(SafeAppFeatures || (exports.SafeAppFeatures = SafeAppFeatures = {}));\nvar SafeAppSocialPlatforms;\n(function (SafeAppSocialPlatforms) {\n    SafeAppSocialPlatforms[\"TWITTER\"] = \"TWITTER\";\n    SafeAppSocialPlatforms[\"GITHUB\"] = \"GITHUB\";\n    SafeAppSocialPlatforms[\"DISCORD\"] = \"DISCORD\";\n    SafeAppSocialPlatforms[\"TELEGRAM\"] = \"TELEGRAM\";\n})(SafeAppSocialPlatforms || (exports.SafeAppSocialPlatforms = SafeAppSocialPlatforms = {}));\n//# sourceMappingURL=safe-apps.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@safe-global/safe-gateway-typescript-sdk/dist/types/safe-apps.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@safe-global/safe-gateway-typescript-sdk/dist/types/safe-info.js":
/*!***************************************************************************************!*\
  !*** ./node_modules/@safe-global/safe-gateway-typescript-sdk/dist/types/safe-info.js ***!
  \***************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.ImplementationVersionState = void 0;\nvar ImplementationVersionState;\n(function (ImplementationVersionState) {\n    ImplementationVersionState[\"UP_TO_DATE\"] = \"UP_TO_DATE\";\n    ImplementationVersionState[\"OUTDATED\"] = \"OUTDATED\";\n    ImplementationVersionState[\"UNKNOWN\"] = \"UNKNOWN\";\n})(ImplementationVersionState || (exports.ImplementationVersionState = ImplementationVersionState = {}));\n//# sourceMappingURL=safe-info.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHNhZmUtZ2xvYmFsL3NhZmUtZ2F0ZXdheS10eXBlc2NyaXB0LXNkay9kaXN0L3R5cGVzL3NhZmUtaW5mby5qcyIsIm1hcHBpbmdzIjoiQUFBYTtBQUNiLDhDQUE2QyxFQUFFLGFBQWEsRUFBQztBQUM3RCxrQ0FBa0M7QUFDbEM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLENBQUMsaUNBQWlDLGtDQUFrQyxrQ0FBa0M7QUFDdEciLCJzb3VyY2VzIjpbIkY6XFxBSSBjaGFpblxcY2hhaW5zaWdodC1wcm9kdWN0aW9uXFxub2RlX21vZHVsZXNcXEBzYWZlLWdsb2JhbFxcc2FmZS1nYXRld2F5LXR5cGVzY3JpcHQtc2RrXFxkaXN0XFx0eXBlc1xcc2FmZS1pbmZvLmpzIl0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIHN0cmljdFwiO1xuT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsIFwiX19lc01vZHVsZVwiLCB7IHZhbHVlOiB0cnVlIH0pO1xuZXhwb3J0cy5JbXBsZW1lbnRhdGlvblZlcnNpb25TdGF0ZSA9IHZvaWQgMDtcbnZhciBJbXBsZW1lbnRhdGlvblZlcnNpb25TdGF0ZTtcbihmdW5jdGlvbiAoSW1wbGVtZW50YXRpb25WZXJzaW9uU3RhdGUpIHtcbiAgICBJbXBsZW1lbnRhdGlvblZlcnNpb25TdGF0ZVtcIlVQX1RPX0RBVEVcIl0gPSBcIlVQX1RPX0RBVEVcIjtcbiAgICBJbXBsZW1lbnRhdGlvblZlcnNpb25TdGF0ZVtcIk9VVERBVEVEXCJdID0gXCJPVVREQVRFRFwiO1xuICAgIEltcGxlbWVudGF0aW9uVmVyc2lvblN0YXRlW1wiVU5LTk9XTlwiXSA9IFwiVU5LTk9XTlwiO1xufSkoSW1wbGVtZW50YXRpb25WZXJzaW9uU3RhdGUgfHwgKGV4cG9ydHMuSW1wbGVtZW50YXRpb25WZXJzaW9uU3RhdGUgPSBJbXBsZW1lbnRhdGlvblZlcnNpb25TdGF0ZSA9IHt9KSk7XG4vLyMgc291cmNlTWFwcGluZ1VSTD1zYWZlLWluZm8uanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@safe-global/safe-gateway-typescript-sdk/dist/types/safe-info.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@safe-global/safe-gateway-typescript-sdk/dist/types/safe-messages.js":
/*!*******************************************************************************************!*\
  !*** ./node_modules/@safe-global/safe-gateway-typescript-sdk/dist/types/safe-messages.js ***!
  \*******************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.SafeMessageStatus = exports.SafeMessageListItemType = void 0;\nvar SafeMessageListItemType;\n(function (SafeMessageListItemType) {\n    SafeMessageListItemType[\"DATE_LABEL\"] = \"DATE_LABEL\";\n    SafeMessageListItemType[\"MESSAGE\"] = \"MESSAGE\";\n})(SafeMessageListItemType || (exports.SafeMessageListItemType = SafeMessageListItemType = {}));\nvar SafeMessageStatus;\n(function (SafeMessageStatus) {\n    SafeMessageStatus[\"NEEDS_CONFIRMATION\"] = \"NEEDS_CONFIRMATION\";\n    SafeMessageStatus[\"CONFIRMED\"] = \"CONFIRMED\";\n})(SafeMessageStatus || (exports.SafeMessageStatus = SafeMessageStatus = {}));\n//# sourceMappingURL=safe-messages.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHNhZmUtZ2xvYmFsL3NhZmUtZ2F0ZXdheS10eXBlc2NyaXB0LXNkay9kaXN0L3R5cGVzL3NhZmUtbWVzc2FnZXMuanMiLCJtYXBwaW5ncyI6IkFBQWE7QUFDYiw4Q0FBNkMsRUFBRSxhQUFhLEVBQUM7QUFDN0QseUJBQXlCLEdBQUcsK0JBQStCO0FBQzNEO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsQ0FBQyw4QkFBOEIsK0JBQStCLCtCQUErQjtBQUM3RjtBQUNBO0FBQ0E7QUFDQTtBQUNBLENBQUMsd0JBQXdCLHlCQUF5Qix5QkFBeUI7QUFDM0UiLCJzb3VyY2VzIjpbIkY6XFxBSSBjaGFpblxcY2hhaW5zaWdodC1wcm9kdWN0aW9uXFxub2RlX21vZHVsZXNcXEBzYWZlLWdsb2JhbFxcc2FmZS1nYXRld2F5LXR5cGVzY3JpcHQtc2RrXFxkaXN0XFx0eXBlc1xcc2FmZS1tZXNzYWdlcy5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBzdHJpY3RcIjtcbk9iamVjdC5kZWZpbmVQcm9wZXJ0eShleHBvcnRzLCBcIl9fZXNNb2R1bGVcIiwgeyB2YWx1ZTogdHJ1ZSB9KTtcbmV4cG9ydHMuU2FmZU1lc3NhZ2VTdGF0dXMgPSBleHBvcnRzLlNhZmVNZXNzYWdlTGlzdEl0ZW1UeXBlID0gdm9pZCAwO1xudmFyIFNhZmVNZXNzYWdlTGlzdEl0ZW1UeXBlO1xuKGZ1bmN0aW9uIChTYWZlTWVzc2FnZUxpc3RJdGVtVHlwZSkge1xuICAgIFNhZmVNZXNzYWdlTGlzdEl0ZW1UeXBlW1wiREFURV9MQUJFTFwiXSA9IFwiREFURV9MQUJFTFwiO1xuICAgIFNhZmVNZXNzYWdlTGlzdEl0ZW1UeXBlW1wiTUVTU0FHRVwiXSA9IFwiTUVTU0FHRVwiO1xufSkoU2FmZU1lc3NhZ2VMaXN0SXRlbVR5cGUgfHwgKGV4cG9ydHMuU2FmZU1lc3NhZ2VMaXN0SXRlbVR5cGUgPSBTYWZlTWVzc2FnZUxpc3RJdGVtVHlwZSA9IHt9KSk7XG52YXIgU2FmZU1lc3NhZ2VTdGF0dXM7XG4oZnVuY3Rpb24gKFNhZmVNZXNzYWdlU3RhdHVzKSB7XG4gICAgU2FmZU1lc3NhZ2VTdGF0dXNbXCJORUVEU19DT05GSVJNQVRJT05cIl0gPSBcIk5FRURTX0NPTkZJUk1BVElPTlwiO1xuICAgIFNhZmVNZXNzYWdlU3RhdHVzW1wiQ09ORklSTUVEXCJdID0gXCJDT05GSVJNRURcIjtcbn0pKFNhZmVNZXNzYWdlU3RhdHVzIHx8IChleHBvcnRzLlNhZmVNZXNzYWdlU3RhdHVzID0gU2FmZU1lc3NhZ2VTdGF0dXMgPSB7fSkpO1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9c2FmZS1tZXNzYWdlcy5qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@safe-global/safe-gateway-typescript-sdk/dist/types/safe-messages.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@safe-global/safe-gateway-typescript-sdk/dist/types/transactions.js":
/*!******************************************************************************************!*\
  !*** ./node_modules/@safe-global/safe-gateway-typescript-sdk/dist/types/transactions.js ***!
  \******************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.LabelValue = exports.StartTimeValue = exports.DurationType = exports.DetailedExecutionInfoType = exports.TransactionListItemType = exports.ConflictType = exports.TransactionInfoType = exports.SettingsInfoType = exports.TransactionTokenType = exports.TransferDirection = exports.TransactionStatus = exports.Operation = void 0;\nvar Operation;\n(function (Operation) {\n    Operation[Operation[\"CALL\"] = 0] = \"CALL\";\n    Operation[Operation[\"DELEGATE\"] = 1] = \"DELEGATE\";\n})(Operation || (exports.Operation = Operation = {}));\nvar TransactionStatus;\n(function (TransactionStatus) {\n    TransactionStatus[\"AWAITING_CONFIRMATIONS\"] = \"AWAITING_CONFIRMATIONS\";\n    TransactionStatus[\"AWAITING_EXECUTION\"] = \"AWAITING_EXECUTION\";\n    TransactionStatus[\"CANCELLED\"] = \"CANCELLED\";\n    TransactionStatus[\"FAILED\"] = \"FAILED\";\n    TransactionStatus[\"SUCCESS\"] = \"SUCCESS\";\n})(TransactionStatus || (exports.TransactionStatus = TransactionStatus = {}));\nvar TransferDirection;\n(function (TransferDirection) {\n    TransferDirection[\"INCOMING\"] = \"INCOMING\";\n    TransferDirection[\"OUTGOING\"] = \"OUTGOING\";\n    TransferDirection[\"UNKNOWN\"] = \"UNKNOWN\";\n})(TransferDirection || (exports.TransferDirection = TransferDirection = {}));\nvar TransactionTokenType;\n(function (TransactionTokenType) {\n    TransactionTokenType[\"ERC20\"] = \"ERC20\";\n    TransactionTokenType[\"ERC721\"] = \"ERC721\";\n    TransactionTokenType[\"NATIVE_COIN\"] = \"NATIVE_COIN\";\n})(TransactionTokenType || (exports.TransactionTokenType = TransactionTokenType = {}));\nvar SettingsInfoType;\n(function (SettingsInfoType) {\n    SettingsInfoType[\"SET_FALLBACK_HANDLER\"] = \"SET_FALLBACK_HANDLER\";\n    SettingsInfoType[\"ADD_OWNER\"] = \"ADD_OWNER\";\n    SettingsInfoType[\"REMOVE_OWNER\"] = \"REMOVE_OWNER\";\n    SettingsInfoType[\"SWAP_OWNER\"] = \"SWAP_OWNER\";\n    SettingsInfoType[\"CHANGE_THRESHOLD\"] = \"CHANGE_THRESHOLD\";\n    SettingsInfoType[\"CHANGE_IMPLEMENTATION\"] = \"CHANGE_IMPLEMENTATION\";\n    SettingsInfoType[\"ENABLE_MODULE\"] = \"ENABLE_MODULE\";\n    SettingsInfoType[\"DISABLE_MODULE\"] = \"DISABLE_MODULE\";\n    SettingsInfoType[\"SET_GUARD\"] = \"SET_GUARD\";\n    SettingsInfoType[\"DELETE_GUARD\"] = \"DELETE_GUARD\";\n})(SettingsInfoType || (exports.SettingsInfoType = SettingsInfoType = {}));\nvar TransactionInfoType;\n(function (TransactionInfoType) {\n    TransactionInfoType[\"TRANSFER\"] = \"Transfer\";\n    TransactionInfoType[\"SETTINGS_CHANGE\"] = \"SettingsChange\";\n    TransactionInfoType[\"CUSTOM\"] = \"Custom\";\n    TransactionInfoType[\"CREATION\"] = \"Creation\";\n    TransactionInfoType[\"SWAP_ORDER\"] = \"SwapOrder\";\n    TransactionInfoType[\"TWAP_ORDER\"] = \"TwapOrder\";\n    TransactionInfoType[\"SWAP_TRANSFER\"] = \"SwapTransfer\";\n    TransactionInfoType[\"NATIVE_STAKING_DEPOSIT\"] = \"NativeStakingDeposit\";\n    TransactionInfoType[\"NATIVE_STAKING_VALIDATORS_EXIT\"] = \"NativeStakingValidatorsExit\";\n    TransactionInfoType[\"NATIVE_STAKING_WITHDRAW\"] = \"NativeStakingWithdraw\";\n})(TransactionInfoType || (exports.TransactionInfoType = TransactionInfoType = {}));\nvar ConflictType;\n(function (ConflictType) {\n    ConflictType[\"NONE\"] = \"None\";\n    ConflictType[\"HAS_NEXT\"] = \"HasNext\";\n    ConflictType[\"END\"] = \"End\";\n})(ConflictType || (exports.ConflictType = ConflictType = {}));\nvar TransactionListItemType;\n(function (TransactionListItemType) {\n    TransactionListItemType[\"TRANSACTION\"] = \"TRANSACTION\";\n    TransactionListItemType[\"LABEL\"] = \"LABEL\";\n    TransactionListItemType[\"CONFLICT_HEADER\"] = \"CONFLICT_HEADER\";\n    TransactionListItemType[\"DATE_LABEL\"] = \"DATE_LABEL\";\n})(TransactionListItemType || (exports.TransactionListItemType = TransactionListItemType = {}));\nvar DetailedExecutionInfoType;\n(function (DetailedExecutionInfoType) {\n    DetailedExecutionInfoType[\"MULTISIG\"] = \"MULTISIG\";\n    DetailedExecutionInfoType[\"MODULE\"] = \"MODULE\";\n})(DetailedExecutionInfoType || (exports.DetailedExecutionInfoType = DetailedExecutionInfoType = {}));\nvar DurationType;\n(function (DurationType) {\n    DurationType[\"AUTO\"] = \"AUTO\";\n    DurationType[\"LIMIT_DURATION\"] = \"LIMIT_DURATION\";\n})(DurationType || (exports.DurationType = DurationType = {}));\nvar StartTimeValue;\n(function (StartTimeValue) {\n    StartTimeValue[\"AT_MINING_TIME\"] = \"AT_MINING_TIME\";\n    StartTimeValue[\"AT_EPOCH\"] = \"AT_EPOCH\";\n})(StartTimeValue || (exports.StartTimeValue = StartTimeValue = {}));\nvar LabelValue;\n(function (LabelValue) {\n    LabelValue[\"Queued\"] = \"Queued\";\n    LabelValue[\"Next\"] = \"Next\";\n})(LabelValue || (exports.LabelValue = LabelValue = {}));\n//# sourceMappingURL=transactions.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@safe-global/safe-gateway-typescript-sdk/dist/types/transactions.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@safe-global/safe-gateway-typescript-sdk/dist/utils.js":
/*!*****************************************************************************!*\
  !*** ./node_modules/@safe-global/safe-gateway-typescript-sdk/dist/utils.js ***!
  \*****************************************************************************/
/***/ (function(__unused_webpack_module, exports) {

eval("\nvar __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {\n    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }\n    return new (P || (P = Promise))(function (resolve, reject) {\n        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }\n        function rejected(value) { try { step(generator[\"throw\"](value)); } catch (e) { reject(e); } }\n        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }\n        step((generator = generator.apply(thisArg, _arguments || [])).next());\n    });\n};\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.insertParams = insertParams;\nexports.stringifyQuery = stringifyQuery;\nexports.fetchData = fetchData;\nexports.getData = getData;\nconst isErrorResponse = (data) => {\n    const isObject = typeof data === 'object' && data !== null;\n    return isObject && ('code' in data || 'statusCode' in data) && 'message' in data;\n};\nfunction replaceParam(str, key, value) {\n    return str.replace(new RegExp(`\\\\{${key}\\\\}`, 'g'), value);\n}\nfunction insertParams(template, params) {\n    return params\n        ? Object.keys(params).reduce((result, key) => {\n            return replaceParam(result, key, String(params[key]));\n        }, template)\n        : template;\n}\nfunction stringifyQuery(query) {\n    if (!query) {\n        return '';\n    }\n    const searchParams = new URLSearchParams();\n    Object.keys(query).forEach((key) => {\n        if (query[key] != null) {\n            searchParams.append(key, String(query[key]));\n        }\n    });\n    const searchString = searchParams.toString();\n    return searchString ? `?${searchString}` : '';\n}\nfunction parseResponse(resp) {\n    return __awaiter(this, void 0, void 0, function* () {\n        var _a;\n        let json;\n        try {\n            json = yield resp.json();\n        }\n        catch (_b) {\n            json = {};\n        }\n        if (!resp.ok) {\n            const errTxt = isErrorResponse(json)\n                ? `CGW error - ${(_a = json.code) !== null && _a !== void 0 ? _a : json.statusCode}: ${json.message}`\n                : `CGW error - status ${resp.statusText}`;\n            throw new Error(errTxt);\n        }\n        return json;\n    });\n}\nfunction fetchData(url, method, body, headers, credentials) {\n    return __awaiter(this, void 0, void 0, function* () {\n        const requestHeaders = Object.assign({ 'Content-Type': 'application/json' }, headers);\n        const options = {\n            method: method !== null && method !== void 0 ? method : 'POST',\n            headers: requestHeaders,\n        };\n        if (credentials) {\n            options['credentials'] = credentials;\n        }\n        if (body != null) {\n            options.body = typeof body === 'string' ? body : JSON.stringify(body);\n        }\n        const resp = yield fetch(url, options);\n        return parseResponse(resp);\n    });\n}\nfunction getData(url, headers, credentials) {\n    return __awaiter(this, void 0, void 0, function* () {\n        const options = {\n            method: 'GET',\n        };\n        if (headers) {\n            options['headers'] = Object.assign(Object.assign({}, headers), { 'Content-Type': 'application/json' });\n        }\n        if (credentials) {\n            options['credentials'] = credentials;\n        }\n        const resp = yield fetch(url, options);\n        return parseResponse(resp);\n    });\n}\n//# sourceMappingURL=utils.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@safe-global/safe-gateway-typescript-sdk/dist/utils.js\n");

/***/ })

};
;