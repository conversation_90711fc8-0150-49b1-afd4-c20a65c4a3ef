export declare const vechain: {
    blockExplorers: {
        readonly default: {
            readonly name: "Vechain Explorer";
            readonly url: "https://explore.vechain.org";
        };
        readonly vechainStats: {
            readonly name: "Vechain Stats";
            readonly url: "https://vechainstats.com";
        };
    };
    contracts?: import("../index.js").Prettify<{
        [key: string]: import("../../index.js").ChainContract | {
            [sourceId: number]: import("../../index.js").ChainContract | undefined;
        } | undefined;
    } & {
        ensRegistry?: import("../../index.js").ChainContract | undefined;
        ensUniversalResolver?: import("../../index.js").ChainContract | undefined;
        multicall3?: import("../../index.js").ChainContract | undefined;
        universalSignatureVerifier?: import("../../index.js").ChainContract | undefined;
    }> | undefined;
    id: 100009;
    name: "Vechain";
    nativeCurrency: {
        readonly name: "<PERSON><PERSON><PERSON><PERSON><PERSON>";
        readonly symbol: "VET";
        readonly decimals: 18;
    };
    rpcUrls: {
        readonly default: {
            readonly http: readonly ["https://mainnet.vechain.org"];
        };
    };
    sourceId?: number | undefined;
    testnet?: boolean | undefined;
    custom?: Record<string, unknown> | undefined;
    fees?: import("../../index.js").ChainFees<undefined> | undefined;
    formatters?: undefined;
    serializers?: import("../../index.js").ChainSerializers<undefined, import("../../index.js").TransactionSerializable> | undefined;
};
//# sourceMappingURL=vechain.d.ts.map