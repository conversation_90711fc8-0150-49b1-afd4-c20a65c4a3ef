"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/qr";
exports.ids = ["vendor-chunks/qr"];
exports.modules = {

/***/ "(ssr)/./node_modules/qr/index.js":
/*!**********************************!*\
  !*** ./node_modules/qr/index.js ***!
  \**********************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Bitmap: () => (/* binding */ Bitmap),\n/* harmony export */   ECMode: () => (/* binding */ ECMode),\n/* harmony export */   Encoding: () => (/* binding */ Encoding),\n/* harmony export */   _tests: () => (/* binding */ _tests),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   encodeQR: () => (/* binding */ encodeQR),\n/* harmony export */   utf8ToBytes: () => (/* binding */ utf8ToBytes),\n/* harmony export */   utils: () => (/* binding */ utils)\n/* harmony export */ });\n/*!\nCopyright (c) 2023 Paul Miller (paulmillr.com)\nThe library paulmillr-qr is dual-licensed under the Apache 2.0 OR MIT license.\nYou can select a license of your choice.\nLicensed under the Apache License, Version 2.0 (the \"License\");\nyou may not use this file except in compliance with the License.\nYou may obtain a copy of the License at\n\n    http://www.apache.org/licenses/LICENSE-2.0\n\nUnless required by applicable law or agreed to in writing, software\ndistributed under the License is distributed on an \"AS IS\" BASIS,\nWITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\nSee the License for the specific language governing permissions and\nlimitations under the License.\n*/\n/**\n * Methods for encoding (generating) QR code patterns.\n * Check out decode.ts for decoding (reading).\n * @module\n * @example\n```js\nimport encodeQR from 'qr';\nconst txt = 'Hello world';\nconst ascii = encodeQR(txt, 'ascii'); // Not all fonts are supported\nconst terminalFriendly = encodeQR(txt, 'term'); // 2x larger, all fonts are OK\nconst gifBytes = encodeQR(txt, 'gif'); // Uncompressed GIF\nconst svgElement = encodeQR(txt, 'svg'); // SVG vector image element\nconst array = encodeQR(txt, 'raw'); // 2d array for canvas or other libs\n// import decodeQR from 'qr/decode.js';\n```\n */\n// We do not use newline escape code directly in strings because it's not parser-friendly\nconst chCodes = { newline: 10, reset: 27 };\nfunction assertNumber(n) {\n    if (!Number.isSafeInteger(n))\n        throw new Error(`integer expected: ${n}`);\n}\nfunction validateVersion(ver) {\n    if (!Number.isSafeInteger(ver) || ver < 1 || ver > 40)\n        throw new Error(`Invalid version=${ver}. Expected number [1..40]`);\n}\nfunction bin(dec, pad) {\n    return dec.toString(2).padStart(pad, '0');\n}\nfunction mod(a, b) {\n    const result = a % b;\n    return result >= 0 ? result : b + result;\n}\nfunction fillArr(length, val) {\n    return new Array(length).fill(val);\n}\n/**\n * Interleaves byte blocks.\n * @param blocks [[1, 2, 3], [4, 5, 6]]\n * @returns [1, 4, 2, 5, 3, 6]\n */\nfunction interleaveBytes(...blocks) {\n    let len = 0;\n    for (const b of blocks)\n        len = Math.max(len, b.length);\n    const res = [];\n    for (let i = 0; i < len; i++) {\n        for (const b of blocks) {\n            if (i >= b.length)\n                continue; // outside of block, skip\n            res.push(b[i]);\n        }\n    }\n    return new Uint8Array(res);\n}\nfunction includesAt(lst, pattern, index) {\n    if (index < 0 || index + pattern.length > lst.length)\n        return false;\n    for (let i = 0; i < pattern.length; i++)\n        if (pattern[i] !== lst[index + i])\n            return false;\n    return true;\n}\n// Optimize for minimal score/penalty\nfunction best() {\n    let best;\n    let bestScore = Infinity;\n    return {\n        add(score, value) {\n            if (score >= bestScore)\n                return;\n            best = value;\n            bestScore = score;\n        },\n        get: () => best,\n        score: () => bestScore,\n    };\n}\n// Based on https://github.com/paulmillr/scure-base/blob/main/index.ts\nfunction alphabet(alphabet) {\n    return {\n        has: (char) => alphabet.includes(char),\n        decode: (input) => {\n            if (!Array.isArray(input) || (input.length && typeof input[0] !== 'string'))\n                throw new Error('alphabet.decode input should be array of strings');\n            return input.map((letter) => {\n                if (typeof letter !== 'string')\n                    throw new Error(`alphabet.decode: not string element=${letter}`);\n                const index = alphabet.indexOf(letter);\n                if (index === -1)\n                    throw new Error(`Unknown letter: \"${letter}\". Allowed: ${alphabet}`);\n                return index;\n            });\n        },\n        encode: (digits) => {\n            if (!Array.isArray(digits) || (digits.length && typeof digits[0] !== 'number'))\n                throw new Error('alphabet.encode input should be an array of numbers');\n            return digits.map((i) => {\n                assertNumber(i);\n                if (i < 0 || i >= alphabet.length)\n                    throw new Error(`Digit index outside alphabet: ${i} (alphabet: ${alphabet.length})`);\n                return alphabet[i];\n            });\n        },\n    };\n}\nclass Bitmap {\n    static size(size, limit) {\n        if (typeof size === 'number')\n            size = { height: size, width: size };\n        if (!Number.isSafeInteger(size.height) && size.height !== Infinity)\n            throw new Error(`Bitmap: invalid height=${size.height} (${typeof size.height})`);\n        if (!Number.isSafeInteger(size.width) && size.width !== Infinity)\n            throw new Error(`Bitmap: invalid width=${size.width} (${typeof size.width})`);\n        if (limit !== undefined) {\n            // Clamp length, so it won't overflow, also allows to use Infinity, so we draw until end\n            size = {\n                width: Math.min(size.width, limit.width),\n                height: Math.min(size.height, limit.height),\n            };\n        }\n        return size;\n    }\n    static fromString(s) {\n        // Remove linebreaks on start and end, so we draw in `` section\n        s = s.replace(/^\\n+/g, '').replace(/\\n+$/g, '');\n        const lines = s.split(String.fromCharCode(chCodes.newline));\n        const height = lines.length;\n        const data = new Array(height);\n        let width;\n        for (const line of lines) {\n            const row = line.split('').map((i) => {\n                if (i === 'X')\n                    return true;\n                if (i === ' ')\n                    return false;\n                if (i === '?')\n                    return undefined;\n                throw new Error(`Bitmap.fromString: unknown symbol=${i}`);\n            });\n            if (width && row.length !== width)\n                throw new Error(`Bitmap.fromString different row sizes: width=${width} cur=${row.length}`);\n            width = row.length;\n            data.push(row);\n        }\n        if (!width)\n            width = 0;\n        return new Bitmap({ height, width }, data);\n    }\n    data;\n    height;\n    width;\n    constructor(size, data) {\n        const { height, width } = Bitmap.size(size);\n        this.data = data || Array.from({ length: height }, () => fillArr(width, undefined));\n        this.height = height;\n        this.width = width;\n    }\n    point(p) {\n        return this.data[p.y][p.x];\n    }\n    isInside(p) {\n        return 0 <= p.x && p.x < this.width && 0 <= p.y && p.y < this.height;\n    }\n    size(offset) {\n        if (!offset)\n            return { height: this.height, width: this.width };\n        const { x, y } = this.xy(offset);\n        return { height: this.height - y, width: this.width - x };\n    }\n    xy(c) {\n        if (typeof c === 'number')\n            c = { x: c, y: c };\n        if (!Number.isSafeInteger(c.x))\n            throw new Error(`Bitmap: invalid x=${c.x}`);\n        if (!Number.isSafeInteger(c.y))\n            throw new Error(`Bitmap: invalid y=${c.y}`);\n        // Do modulo, so we can use negative positions\n        c.x = mod(c.x, this.width);\n        c.y = mod(c.y, this.height);\n        return c;\n    }\n    // Basically every operation can be represented as rect\n    rect(c, size, value) {\n        const { x, y } = this.xy(c);\n        const { height, width } = Bitmap.size(size, this.size({ x, y }));\n        for (let yPos = 0; yPos < height; yPos++) {\n            for (let xPos = 0; xPos < width; xPos++) {\n                // NOTE: we use give function relative coordinates inside box\n                this.data[y + yPos][x + xPos] =\n                    typeof value === 'function'\n                        ? value({ x: xPos, y: yPos }, this.data[y + yPos][x + xPos])\n                        : value;\n            }\n        }\n        return this;\n    }\n    // returns rectangular part of bitmap\n    rectRead(c, size, fn) {\n        return this.rect(c, size, (c, cur) => {\n            fn(c, cur);\n            return cur;\n        });\n    }\n    // Horizontal & vertical lines\n    hLine(c, len, value) {\n        return this.rect(c, { width: len, height: 1 }, value);\n    }\n    vLine(c, len, value) {\n        return this.rect(c, { width: 1, height: len }, value);\n    }\n    // add border\n    border(border = 2, value) {\n        const height = this.height + 2 * border;\n        const width = this.width + 2 * border;\n        const v = fillArr(border, value);\n        const h = Array.from({ length: border }, () => fillArr(width, value));\n        return new Bitmap({ height, width }, [...h, ...this.data.map((i) => [...v, ...i, ...v]), ...h]);\n    }\n    // Embed another bitmap on coordinates\n    embed(c, bm) {\n        return this.rect(c, bm.size(), ({ x, y }) => bm.data[y][x]);\n    }\n    // returns rectangular part of bitmap\n    rectSlice(c, size = this.size()) {\n        const rect = new Bitmap(Bitmap.size(size, this.size(this.xy(c))));\n        this.rect(c, size, ({ x, y }, cur) => (rect.data[y][x] = cur));\n        return rect;\n    }\n    // Change shape, replace rows with columns (data[y][x] -> data[x][y])\n    inverse() {\n        const { height, width } = this;\n        const res = new Bitmap({ height: width, width: height });\n        return res.rect({ x: 0, y: 0 }, Infinity, ({ x, y }) => this.data[x][y]);\n    }\n    // Each pixel size is multiplied by factor\n    scale(factor) {\n        if (!Number.isSafeInteger(factor) || factor > 1024)\n            throw new Error(`invalid scale factor: ${factor}`);\n        const { height, width } = this;\n        const res = new Bitmap({ height: factor * height, width: factor * width });\n        return res.rect({ x: 0, y: 0 }, Infinity, ({ x, y }) => this.data[Math.floor(y / factor)][Math.floor(x / factor)]);\n    }\n    clone() {\n        const res = new Bitmap(this.size());\n        return res.rect({ x: 0, y: 0 }, this.size(), ({ x, y }) => this.data[y][x]);\n    }\n    // Ensure that there is no undefined values left\n    assertDrawn() {\n        this.rectRead(0, Infinity, (_, cur) => {\n            if (typeof cur !== 'boolean')\n                throw new Error(`Invalid color type=${typeof cur}`);\n        });\n    }\n    // Simple string representation for debugging\n    toString() {\n        return this.data\n            .map((i) => i.map((j) => (j === undefined ? '?' : j ? 'X' : ' ')).join(''))\n            .join(String.fromCharCode(chCodes.newline));\n    }\n    toASCII() {\n        const { height, width, data } = this;\n        let out = '';\n        // Terminal character height is x2 of character width, so we process two rows of bitmap\n        // to produce one row of ASCII\n        for (let y = 0; y < height; y += 2) {\n            for (let x = 0; x < width; x++) {\n                const first = data[y][x];\n                const second = y + 1 >= height ? true : data[y + 1][x]; // if last row outside bitmap, make it black\n                if (!first && !second)\n                    out += '█'; // both rows white (empty)\n                else if (!first && second)\n                    out += '▀'; // top row white\n                else if (first && !second)\n                    out += '▄'; // down row white\n                else if (first && second)\n                    out += ' '; // both rows black\n            }\n            out += String.fromCharCode(chCodes.newline);\n        }\n        return out;\n    }\n    toTerm() {\n        const cc = String.fromCharCode(chCodes.reset);\n        const reset = cc + '[0m';\n        const whiteBG = cc + '[1;47m  ' + reset;\n        const darkBG = cc + `[40m  ` + reset;\n        return this.data\n            .map((i) => i.map((j) => (j ? darkBG : whiteBG)).join(''))\n            .join(String.fromCharCode(chCodes.newline));\n    }\n    toSVG(optimize = true) {\n        let out = `<svg viewBox=\"0 0 ${this.width} ${this.height}\" xmlns=\"http://www.w3.org/2000/svg\">`;\n        // Construct optimized SVG path data.\n        let pathData = '';\n        let prevPoint;\n        this.rectRead(0, Infinity, (point, val) => {\n            if (!val)\n                return;\n            const { x, y } = point;\n            if (!optimize) {\n                out += `<rect x=\"${x}\" y=\"${y}\" width=\"1\" height=\"1\" />`;\n                return;\n            }\n            // https://developer.mozilla.org/en-US/docs/Web/SVG/Reference/Attribute/d#path_commands\n            // Determine the shortest way to represent the initial cursor movement.\n            // M - Move cursor (without drawing) to absolute coordinate pair.\n            let m = `M${x} ${y}`;\n            // Only allow using the relative cursor move command if previous points\n            // were drawn.\n            if (prevPoint) {\n                // m - Move cursor (without drawing) to relative coordinate pair.\n                const relM = `m${x - prevPoint.x} ${y - prevPoint.y}`;\n                if (relM.length <= m.length)\n                    m = relM;\n            }\n            // Determine the shortest way to represent the cell's bottom line draw.\n            // H - Draw line from cursor position to absolute x coordinate.\n            // h - Draw line from cursor position to relative x coordinate.\n            const bH = x < 10 ? `H${x}` : 'h-1';\n            // v - Draw line from cursor position to relative y coordinate.\n            // Z - Close path (draws line from cursor position to M coordinate).\n            pathData += `${m}h1v1${bH}Z`;\n            prevPoint = point;\n        });\n        if (optimize)\n            out += `<path d=\"${pathData}\"/>`;\n        out += `</svg>`;\n        return out;\n    }\n    toGIF() {\n        // NOTE: Small, but inefficient implementation.\n        // Uses 1 byte per pixel.\n        const u16le = (i) => [i & 0xff, (i >>> 8) & 0xff];\n        const dims = [...u16le(this.width), ...u16le(this.height)];\n        const data = [];\n        this.rectRead(0, Infinity, (_, cur) => data.push(+(cur === true)));\n        const N = 126; // Block size\n        // prettier-ignore\n        const bytes = [\n            0x47, 0x49, 0x46, 0x38, 0x37, 0x61, ...dims, 0xf6, 0x00, 0x00, 0xff, 0xff, 0xff,\n            ...fillArr(3 * 127, 0x00), 0x2c, 0x00, 0x00, 0x00, 0x00, ...dims, 0x00, 0x07\n        ];\n        const fullChunks = Math.floor(data.length / N);\n        // Full blocks\n        for (let i = 0; i < fullChunks; i++)\n            bytes.push(N + 1, 0x80, ...data.slice(N * i, N * (i + 1)).map((i) => +i));\n        // Remaining bytes\n        bytes.push((data.length % N) + 1, 0x80, ...data.slice(fullChunks * N).map((i) => +i));\n        bytes.push(0x01, 0x81, 0x00, 0x3b);\n        return new Uint8Array(bytes);\n    }\n    toImage(isRGB = false) {\n        const { height, width } = this.size();\n        const data = new Uint8Array(height * width * (isRGB ? 3 : 4));\n        let i = 0;\n        for (let y = 0; y < height; y++) {\n            for (let x = 0; x < width; x++) {\n                const value = !!this.data[y][x] ? 0 : 255;\n                data[i++] = value;\n                data[i++] = value;\n                data[i++] = value;\n                if (!isRGB)\n                    data[i++] = 255; // alpha channel\n            }\n        }\n        return { height, width, data };\n    }\n}\n// End of utils\n// Runtime type-checking\n/** Error correction mode. low: 7%, medium: 15%, quartile: 25%, high: 30% */\nconst ECMode = ['low', 'medium', 'quartile', 'high'];\n/** QR Code encoding */\nconst Encoding = ['numeric', 'alphanumeric', 'byte', 'kanji', 'eci'];\n// Various constants & tables\n// prettier-ignore\nconst BYTES = [\n    // 1,  2,  3,   4,   5,   6,   7,   8,   9,  10,  11,  12,  13,  14,  15,  16,  17,  18,  19,   20,\n    26, 44, 70, 100, 134, 172, 196, 242, 292, 346, 404, 466, 532, 581, 655, 733, 815, 901, 991, 1085,\n    //  21,   22,   23,   24,   25,   26,   27,   28,   29,   30,   31,   32,   33,   34,   35,   36,   37,   38,   39,   40\n    1156, 1258, 1364, 1474, 1588, 1706, 1828, 1921, 2051, 2185, 2323, 2465, 2611, 2761, 2876, 3034, 3196, 3362, 3532, 3706,\n];\n// prettier-ignore\nconst WORDS_PER_BLOCK = {\n    // Version 1,  2,  3,  4,  5,  6,  7,  8,  9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40\n    low: [7, 10, 15, 20, 26, 18, 20, 24, 30, 18, 20, 24, 26, 30, 22, 24, 28, 30, 28, 28, 28, 28, 30, 30, 26, 28, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30],\n    medium: [10, 16, 26, 18, 24, 16, 18, 22, 22, 26, 30, 22, 22, 24, 24, 28, 28, 26, 26, 26, 26, 28, 28, 28, 28, 28, 28, 28, 28, 28, 28, 28, 28, 28, 28, 28, 28, 28, 28, 28],\n    quartile: [13, 22, 18, 26, 18, 24, 18, 22, 20, 24, 28, 26, 24, 20, 30, 24, 28, 28, 26, 30, 28, 30, 30, 30, 30, 28, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30],\n    high: [17, 28, 22, 16, 22, 28, 26, 26, 24, 28, 24, 28, 22, 24, 24, 30, 28, 28, 26, 28, 30, 24, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30],\n};\n// prettier-ignore\nconst ECC_BLOCKS = {\n    // Version   1, 2, 3, 4, 5, 6, 7, 8, 9,10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40\n    low: [1, 1, 1, 1, 1, 2, 2, 2, 2, 4, 4, 4, 4, 4, 6, 6, 6, 6, 7, 8, 8, 9, 9, 10, 12, 12, 12, 13, 14, 15, 16, 17, 18, 19, 19, 20, 21, 22, 24, 25],\n    medium: [1, 1, 1, 2, 2, 4, 4, 4, 5, 5, 5, 8, 9, 9, 10, 10, 11, 13, 14, 16, 17, 17, 18, 20, 21, 23, 25, 26, 28, 29, 31, 33, 35, 37, 38, 40, 43, 45, 47, 49],\n    quartile: [1, 1, 2, 2, 4, 4, 6, 6, 8, 8, 8, 10, 12, 16, 12, 17, 16, 18, 21, 20, 23, 23, 25, 27, 29, 34, 34, 35, 38, 40, 43, 45, 48, 51, 53, 56, 59, 62, 65, 68],\n    high: [1, 1, 2, 4, 4, 4, 5, 6, 8, 8, 11, 11, 16, 16, 18, 16, 19, 21, 25, 25, 25, 34, 30, 32, 35, 37, 40, 42, 45, 48, 51, 54, 57, 60, 63, 66, 70, 74, 77, 81],\n};\nconst info = {\n    size: {\n        encode: (ver) => 21 + 4 * (ver - 1), // ver1 = 21, ver40=177 blocks\n        decode: (size) => (size - 17) / 4,\n    },\n    sizeType: (ver) => Math.floor((ver + 7) / 17),\n    // Based on https://codereview.stackexchange.com/questions/74925/algorithm-to-generate-this-alignment-pattern-locations-table-for-qr-codes\n    alignmentPatterns(ver) {\n        if (ver === 1)\n            return [];\n        const first = 6;\n        const last = info.size.encode(ver) - first - 1;\n        const distance = last - first;\n        const count = Math.ceil(distance / 28);\n        let interval = Math.floor(distance / count);\n        if (interval % 2)\n            interval += 1;\n        else if ((distance % count) * 2 >= count)\n            interval += 2;\n        const res = [first];\n        for (let m = 1; m < count; m++)\n            res.push(last - (count - m) * interval);\n        res.push(last);\n        return res;\n    },\n    ECCode: {\n        low: 0b01,\n        medium: 0b00,\n        quartile: 0b11,\n        high: 0b10,\n    },\n    formatMask: 0b101010000010010,\n    formatBits(ecc, maskIdx) {\n        const data = (info.ECCode[ecc] << 3) | maskIdx;\n        let d = data;\n        for (let i = 0; i < 10; i++)\n            d = (d << 1) ^ ((d >> 9) * 0b10100110111);\n        return ((data << 10) | d) ^ info.formatMask;\n    },\n    versionBits(ver) {\n        let d = ver;\n        for (let i = 0; i < 12; i++)\n            d = (d << 1) ^ ((d >> 11) * 0b1111100100101);\n        return (ver << 12) | d;\n    },\n    alphabet: {\n        numeric: alphabet('0123456789'),\n        alphanumerc: alphabet('0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ $%*+-./:'),\n    }, // as Record<EncodingType, ReturnType<typeof alphabet>>,\n    lengthBits(ver, type) {\n        const table = {\n            numeric: [10, 12, 14],\n            alphanumeric: [9, 11, 13],\n            byte: [8, 16, 16],\n            kanji: [8, 10, 12],\n            eci: [0, 0, 0],\n        };\n        return table[type][info.sizeType(ver)];\n    },\n    modeBits: {\n        numeric: '0001',\n        alphanumeric: '0010',\n        byte: '0100',\n        kanji: '1000',\n        eci: '0111',\n    },\n    capacity(ver, ecc) {\n        const bytes = BYTES[ver - 1];\n        const words = WORDS_PER_BLOCK[ecc][ver - 1];\n        const numBlocks = ECC_BLOCKS[ecc][ver - 1];\n        const blockLen = Math.floor(bytes / numBlocks) - words;\n        const shortBlocks = numBlocks - (bytes % numBlocks);\n        return {\n            words,\n            numBlocks,\n            shortBlocks,\n            blockLen,\n            capacity: (bytes - words * numBlocks) * 8,\n            total: (words + blockLen) * numBlocks + numBlocks - shortBlocks,\n        };\n    },\n};\nconst PATTERNS = [\n    (x, y) => (x + y) % 2 == 0,\n    (_x, y) => y % 2 == 0,\n    (x, _y) => x % 3 == 0,\n    (x, y) => (x + y) % 3 == 0,\n    (x, y) => (Math.floor(y / 2) + Math.floor(x / 3)) % 2 == 0,\n    (x, y) => ((x * y) % 2) + ((x * y) % 3) == 0,\n    (x, y) => (((x * y) % 2) + ((x * y) % 3)) % 2 == 0,\n    (x, y) => (((x + y) % 2) + ((x * y) % 3)) % 2 == 0,\n];\n// Galois field && reed-solomon encoding\nconst GF = {\n    tables: ((p_poly) => {\n        const exp = fillArr(256, 0);\n        const log = fillArr(256, 0);\n        for (let i = 0, x = 1; i < 256; i++) {\n            exp[i] = x;\n            log[x] = i;\n            x <<= 1;\n            if (x & 0x100)\n                x ^= p_poly;\n        }\n        return { exp, log };\n    })(0x11d),\n    exp: (x) => GF.tables.exp[x],\n    log(x) {\n        if (x === 0)\n            throw new Error(`GF.log: invalid arg=${x}`);\n        return GF.tables.log[x] % 255;\n    },\n    mul(x, y) {\n        if (x === 0 || y === 0)\n            return 0;\n        return GF.tables.exp[(GF.tables.log[x] + GF.tables.log[y]) % 255];\n    },\n    add: (x, y) => x ^ y,\n    pow: (x, e) => GF.tables.exp[(GF.tables.log[x] * e) % 255],\n    inv(x) {\n        if (x === 0)\n            throw new Error(`GF.inverse: invalid arg=${x}`);\n        return GF.tables.exp[255 - GF.tables.log[x]];\n    },\n    polynomial(poly) {\n        if (poly.length == 0)\n            throw new Error('GF.polymomial: invalid length');\n        if (poly[0] !== 0)\n            return poly;\n        // Strip leading zeros\n        let i = 0;\n        for (; i < poly.length - 1 && poly[i] == 0; i++)\n            ;\n        return poly.slice(i);\n    },\n    monomial(degree, coefficient) {\n        if (degree < 0)\n            throw new Error(`GF.monomial: invalid degree=${degree}`);\n        if (coefficient == 0)\n            return [0];\n        let coefficients = fillArr(degree + 1, 0);\n        coefficients[0] = coefficient;\n        return GF.polynomial(coefficients);\n    },\n    degree: (a) => a.length - 1,\n    coefficient: (a, degree) => a[GF.degree(a) - degree],\n    mulPoly(a, b) {\n        if (a[0] === 0 || b[0] === 0)\n            return [0];\n        const res = fillArr(a.length + b.length - 1, 0);\n        for (let i = 0; i < a.length; i++) {\n            for (let j = 0; j < b.length; j++) {\n                res[i + j] = GF.add(res[i + j], GF.mul(a[i], b[j]));\n            }\n        }\n        return GF.polynomial(res);\n    },\n    mulPolyScalar(a, scalar) {\n        if (scalar == 0)\n            return [0];\n        if (scalar == 1)\n            return a;\n        const res = fillArr(a.length, 0);\n        for (let i = 0; i < a.length; i++)\n            res[i] = GF.mul(a[i], scalar);\n        return GF.polynomial(res);\n    },\n    mulPolyMonomial(a, degree, coefficient) {\n        if (degree < 0)\n            throw new Error('GF.mulPolyMonomial: invalid degree');\n        if (coefficient == 0)\n            return [0];\n        const res = fillArr(a.length + degree, 0);\n        for (let i = 0; i < a.length; i++)\n            res[i] = GF.mul(a[i], coefficient);\n        return GF.polynomial(res);\n    },\n    addPoly(a, b) {\n        if (a[0] === 0)\n            return b;\n        if (b[0] === 0)\n            return a;\n        let smaller = a;\n        let larger = b;\n        if (smaller.length > larger.length)\n            [smaller, larger] = [larger, smaller];\n        let sumDiff = fillArr(larger.length, 0);\n        let lengthDiff = larger.length - smaller.length;\n        let s = larger.slice(0, lengthDiff);\n        for (let i = 0; i < s.length; i++)\n            sumDiff[i] = s[i];\n        for (let i = lengthDiff; i < larger.length; i++)\n            sumDiff[i] = GF.add(smaller[i - lengthDiff], larger[i]);\n        return GF.polynomial(sumDiff);\n    },\n    remainderPoly(data, divisor) {\n        const out = Array.from(data);\n        for (let i = 0; i < data.length - divisor.length + 1; i++) {\n            const elm = out[i];\n            if (elm === 0)\n                continue;\n            for (let j = 1; j < divisor.length; j++) {\n                if (divisor[j] !== 0)\n                    out[i + j] = GF.add(out[i + j], GF.mul(divisor[j], elm));\n            }\n        }\n        return out.slice(data.length - divisor.length + 1, out.length);\n    },\n    divisorPoly(degree) {\n        let g = [1];\n        for (let i = 0; i < degree; i++)\n            g = GF.mulPoly(g, [1, GF.pow(2, i)]);\n        return g;\n    },\n    evalPoly(poly, a) {\n        if (a == 0)\n            return GF.coefficient(poly, 0); // Just return the x^0 coefficient\n        let res = poly[0];\n        for (let i = 1; i < poly.length; i++)\n            res = GF.add(GF.mul(a, res), poly[i]);\n        return res;\n    },\n    // TODO: cleanup\n    euclidian(a, b, R) {\n        // Force degree(a) >= degree(b)\n        if (GF.degree(a) < GF.degree(b))\n            [a, b] = [b, a];\n        let rLast = a;\n        let r = b;\n        let tLast = [0];\n        let t = [1];\n        // while degree of Ri ≥ t/2\n        while (2 * GF.degree(r) >= R) {\n            let rLastLast = rLast;\n            let tLastLast = tLast;\n            rLast = r;\n            tLast = t;\n            if (rLast[0] === 0)\n                throw new Error('rLast[0] === 0');\n            r = rLastLast;\n            let q = [0];\n            const dltInverse = GF.inv(rLast[0]);\n            while (GF.degree(r) >= GF.degree(rLast) && r[0] !== 0) {\n                const degreeDiff = GF.degree(r) - GF.degree(rLast);\n                const scale = GF.mul(r[0], dltInverse);\n                q = GF.addPoly(q, GF.monomial(degreeDiff, scale));\n                r = GF.addPoly(r, GF.mulPolyMonomial(rLast, degreeDiff, scale));\n            }\n            q = GF.mulPoly(q, tLast);\n            t = GF.addPoly(q, tLastLast);\n            if (GF.degree(r) >= GF.degree(rLast))\n                throw new Error(`Division failed r: ${r}, rLast: ${rLast}`);\n        }\n        const sigmaTildeAtZero = GF.coefficient(t, 0);\n        if (sigmaTildeAtZero == 0)\n            throw new Error('sigmaTilde(0) was zero');\n        const inverse = GF.inv(sigmaTildeAtZero);\n        return [GF.mulPolyScalar(t, inverse), GF.mulPolyScalar(r, inverse)];\n    },\n};\nfunction RS(eccWords) {\n    return {\n        encode(from) {\n            const d = GF.divisorPoly(eccWords);\n            const pol = Array.from(from);\n            pol.push(...d.slice(0, -1).fill(0));\n            return Uint8Array.from(GF.remainderPoly(pol, d));\n        },\n        decode(to) {\n            const res = to.slice();\n            const poly = GF.polynomial(Array.from(to));\n            // Find errors\n            let syndrome = fillArr(eccWords, 0);\n            let hasError = false;\n            for (let i = 0; i < eccWords; i++) {\n                const evl = GF.evalPoly(poly, GF.exp(i));\n                syndrome[syndrome.length - 1 - i] = evl;\n                if (evl !== 0)\n                    hasError = true;\n            }\n            if (!hasError)\n                return res;\n            syndrome = GF.polynomial(syndrome);\n            const monomial = GF.monomial(eccWords, 1);\n            const [errorLocator, errorEvaluator] = GF.euclidian(monomial, syndrome, eccWords);\n            // Error locations\n            const locations = fillArr(GF.degree(errorLocator), 0);\n            let e = 0;\n            for (let i = 1; i < 256 && e < locations.length; i++) {\n                if (GF.evalPoly(errorLocator, i) === 0)\n                    locations[e++] = GF.inv(i);\n            }\n            if (e !== locations.length)\n                throw new Error('RS.decode: invalid errors number');\n            for (let i = 0; i < locations.length; i++) {\n                const pos = res.length - 1 - GF.log(locations[i]);\n                if (pos < 0)\n                    throw new Error('RS.decode: invalid error location');\n                const xiInverse = GF.inv(locations[i]);\n                let denominator = 1;\n                for (let j = 0; j < locations.length; j++) {\n                    if (i === j)\n                        continue;\n                    denominator = GF.mul(denominator, GF.add(1, GF.mul(locations[j], xiInverse)));\n                }\n                res[pos] = GF.add(res[pos], GF.mul(GF.evalPoly(errorEvaluator, xiInverse), GF.inv(denominator)));\n            }\n            return res;\n        },\n    };\n}\n// Interleaves blocks\nfunction interleave(ver, ecc) {\n    const { words, shortBlocks, numBlocks, blockLen, total } = info.capacity(ver, ecc);\n    const rs = RS(words);\n    return {\n        encode(bytes) {\n            // Add error correction to bytes\n            const blocks = [];\n            const eccBlocks = [];\n            for (let i = 0; i < numBlocks; i++) {\n                const isShort = i < shortBlocks;\n                const len = blockLen + (isShort ? 0 : 1);\n                blocks.push(bytes.subarray(0, len));\n                eccBlocks.push(rs.encode(bytes.subarray(0, len)));\n                bytes = bytes.subarray(len);\n            }\n            const resBlocks = interleaveBytes(...blocks);\n            const resECC = interleaveBytes(...eccBlocks);\n            const res = new Uint8Array(resBlocks.length + resECC.length);\n            res.set(resBlocks);\n            res.set(resECC, resBlocks.length);\n            return res;\n        },\n        decode(data) {\n            if (data.length !== total)\n                throw new Error(`interleave.decode: len(data)=${data.length}, total=${total}`);\n            const blocks = [];\n            for (let i = 0; i < numBlocks; i++) {\n                const isShort = i < shortBlocks;\n                blocks.push(new Uint8Array(words + blockLen + (isShort ? 0 : 1)));\n            }\n            // Short blocks\n            let pos = 0;\n            for (let i = 0; i < blockLen; i++) {\n                for (let j = 0; j < numBlocks; j++)\n                    blocks[j][i] = data[pos++];\n            }\n            // Long blocks\n            for (let j = shortBlocks; j < numBlocks; j++)\n                blocks[j][blockLen] = data[pos++];\n            // ECC\n            for (let i = blockLen; i < blockLen + words; i++) {\n                for (let j = 0; j < numBlocks; j++) {\n                    const isShort = j < shortBlocks;\n                    blocks[j][i + (isShort ? 0 : 1)] = data[pos++];\n                }\n            }\n            // Decode\n            // Error-correct and copy data blocks together into a stream of bytes\n            const res = [];\n            for (const block of blocks)\n                res.push(...Array.from(rs.decode(block)).slice(0, -words));\n            return Uint8Array.from(res);\n        },\n    };\n}\n// Draw\n// Generic template per version+ecc+mask. Can be cached, to speedup calculations.\nfunction drawTemplate(ver, ecc, maskIdx, test = false) {\n    const size = info.size.encode(ver);\n    let b = new Bitmap(size + 2);\n    // Finder patterns\n    // We draw full pattern and later slice, since before addition of borders finder is truncated by one pixel on sides\n    const finder = new Bitmap(3).rect(0, 3, true).border(1, false).border(1, true).border(1, false);\n    b = b\n        .embed(0, finder) // top left\n        .embed({ x: -finder.width, y: 0 }, finder) // top right\n        .embed({ x: 0, y: -finder.height }, finder); // bottom left\n    b = b.rectSlice(1, size);\n    // Alignment patterns\n    const align = new Bitmap(1).rect(0, 1, true).border(1, false).border(1, true);\n    const alignPos = info.alignmentPatterns(ver);\n    for (const y of alignPos) {\n        for (const x of alignPos) {\n            if (b.data[y][x] !== undefined)\n                continue;\n            b.embed({ x: x - 2, y: y - 2 }, align); // center of pattern should be at position\n        }\n    }\n    // Timing patterns\n    b = b\n        .hLine({ x: 0, y: 6 }, Infinity, ({ x }, cur) => (cur === undefined ? x % 2 == 0 : cur))\n        .vLine({ x: 6, y: 0 }, Infinity, ({ y }, cur) => (cur === undefined ? y % 2 == 0 : cur));\n    // Format information\n    {\n        const bits = info.formatBits(ecc, maskIdx);\n        const getBit = (i) => !test && ((bits >> i) & 1) == 1;\n        // vertical\n        for (let i = 0; i < 6; i++)\n            b.data[i][8] = getBit(i); // right of top-left finder\n        // TODO: re-write as lines, like:\n        // b.vLine({ x: 8, y: 0 }, 6, ({ x, y }) => getBit(y));\n        for (let i = 6; i < 8; i++)\n            b.data[i + 1][8] = getBit(i); // after timing pattern\n        for (let i = 8; i < 15; i++)\n            b.data[size - 15 + i][8] = getBit(i); // right of bottom-left finder\n        // horizontal\n        for (let i = 0; i < 8; i++)\n            b.data[8][size - i - 1] = getBit(i); // under top-right finder\n        for (let i = 8; i < 9; i++)\n            b.data[8][15 - i - 1 + 1] = getBit(i); // VVV, after timing\n        for (let i = 9; i < 15; i++)\n            b.data[8][15 - i - 1] = getBit(i); // under top-left finder\n        b.data[size - 8][8] = !test; // bottom-left finder, right\n    }\n    // Version information\n    if (ver >= 7) {\n        const bits = info.versionBits(ver);\n        for (let i = 0; i < 18; i += 1) {\n            const bit = !test && ((bits >> i) & 1) == 1;\n            const x = Math.floor(i / 3);\n            const y = (i % 3) + size - 8 - 3;\n            // two copies\n            b.data[x][y] = bit;\n            b.data[y][x] = bit;\n        }\n    }\n    return b;\n}\n// zigzag: bottom->top && top->bottom\nfunction zigzag(tpl, maskIdx, fn) {\n    const size = tpl.height;\n    const pattern = PATTERNS[maskIdx];\n    // zig-zag pattern\n    let dir = -1;\n    let y = size - 1;\n    // two columns at time\n    for (let xOffset = size - 1; xOffset > 0; xOffset -= 2) {\n        if (xOffset == 6)\n            xOffset = 5; // skip vertical timing pattern\n        for (;; y += dir) {\n            for (let j = 0; j < 2; j += 1) {\n                const x = xOffset - j;\n                if (tpl.data[y][x] !== undefined)\n                    continue; // skip already written elements\n                fn(x, y, pattern(x, y));\n            }\n            if (y + dir < 0 || y + dir >= size)\n                break;\n        }\n        dir = -dir; // change direction\n    }\n}\n// NOTE: byte encoding is just representation, QR works with strings only. Most decoders will fail on raw byte array,\n// since they expect unicode or other text encoding inside bytes\nfunction detectType(str) {\n    let type = 'numeric';\n    for (let x of str) {\n        if (info.alphabet.numeric.has(x))\n            continue;\n        type = 'alphanumeric';\n        if (!info.alphabet.alphanumerc.has(x))\n            return 'byte';\n    }\n    return type;\n}\n/**\n * @example utf8ToBytes('abc') // new Uint8Array([97, 98, 99])\n */\nfunction utf8ToBytes(str) {\n    if (typeof str !== 'string')\n        throw new Error(`utf8ToBytes expected string, got ${typeof str}`);\n    return new Uint8Array(new TextEncoder().encode(str)); // https://bugzil.la/1681809\n}\nfunction encode(ver, ecc, data, type) {\n    let encoded = '';\n    let dataLen = data.length;\n    if (type === 'numeric') {\n        const t = info.alphabet.numeric.decode(data.split(''));\n        const n = t.length;\n        for (let i = 0; i < n - 2; i += 3)\n            encoded += bin(t[i] * 100 + t[i + 1] * 10 + t[i + 2], 10);\n        if (n % 3 === 1) {\n            encoded += bin(t[n - 1], 4);\n        }\n        else if (n % 3 === 2) {\n            encoded += bin(t[n - 2] * 10 + t[n - 1], 7);\n        }\n    }\n    else if (type === 'alphanumeric') {\n        const t = info.alphabet.alphanumerc.decode(data.split(''));\n        const n = t.length;\n        for (let i = 0; i < n - 1; i += 2)\n            encoded += bin(t[i] * 45 + t[i + 1], 11);\n        if (n % 2 == 1)\n            encoded += bin(t[n - 1], 6); // pad if odd number of chars\n    }\n    else if (type === 'byte') {\n        const utf8 = utf8ToBytes(data);\n        dataLen = utf8.length;\n        encoded = Array.from(utf8)\n            .map((i) => bin(i, 8))\n            .join('');\n    }\n    else {\n        throw new Error('encode: unsupported type');\n    }\n    const { capacity } = info.capacity(ver, ecc);\n    const len = bin(dataLen, info.lengthBits(ver, type));\n    let bits = info.modeBits[type] + len + encoded;\n    if (bits.length > capacity)\n        throw new Error('Capacity overflow');\n    // Terminator\n    bits += '0'.repeat(Math.min(4, Math.max(0, capacity - bits.length)));\n    // Pad bits string untill full byte\n    if (bits.length % 8)\n        bits += '0'.repeat(8 - (bits.length % 8));\n    // Add padding until capacity is full\n    const padding = '1110110000010001';\n    for (let idx = 0; bits.length !== capacity; idx++)\n        bits += padding[idx % padding.length];\n    // Convert a bitstring to array of bytes\n    const bytes = Uint8Array.from(bits.match(/(.{8})/g).map((i) => Number(`0b${i}`)));\n    return interleave(ver, ecc).encode(bytes);\n}\n// DRAW\nfunction drawQR(ver, ecc, data, maskIdx, test = false) {\n    const b = drawTemplate(ver, ecc, maskIdx, test);\n    let i = 0;\n    const need = 8 * data.length;\n    zigzag(b, maskIdx, (x, y, mask) => {\n        let value = false;\n        if (i < need) {\n            value = ((data[i >>> 3] >> ((7 - i) & 7)) & 1) !== 0;\n            i++;\n        }\n        b.data[y][x] = value !== mask; // !== as xor\n    });\n    if (i !== need)\n        throw new Error('QR: bytes left after draw');\n    return b;\n}\nfunction penalty(bm) {\n    const inverse = bm.inverse();\n    // Adjacent modules in row/column in same | No. of modules = (5 + i) color\n    const sameColor = (row) => {\n        let res = 0;\n        for (let i = 0, same = 1, last = undefined; i < row.length; i++) {\n            if (last === row[i]) {\n                same++;\n                if (i !== row.length - 1)\n                    continue; // handle last element\n            }\n            if (same >= 5)\n                res += 3 + (same - 5);\n            last = row[i];\n            same = 1;\n        }\n        return res;\n    };\n    let adjacent = 0;\n    bm.data.forEach((row) => (adjacent += sameColor(row)));\n    inverse.data.forEach((column) => (adjacent += sameColor(column)));\n    // Block of modules in same color (Block size = 2x2)\n    let box = 0;\n    let b = bm.data;\n    const lastW = bm.width - 1;\n    const lastH = bm.height - 1;\n    for (let x = 0; x < lastW; x++) {\n        for (let y = 0; y < lastH; y++) {\n            const x1 = x + 1;\n            const y1 = y + 1;\n            if (b[x][y] === b[x1][y] && b[x1][y] === b[x][y1] && b[x1][y] === b[x1][y1]) {\n                box += 3;\n            }\n        }\n    }\n    // 1:1:3:1:1 ratio (dark:light:dark:light:dark) pattern in row/column, preceded or followed by light area 4 modules wide\n    const finderPattern = (row) => {\n        const finderPattern = [true, false, true, true, true, false, true]; // dark:light:dark:light:dark\n        const lightPattern = [false, false, false, false]; // light area 4 modules wide\n        const p1 = [...finderPattern, ...lightPattern];\n        const p2 = [...lightPattern, ...finderPattern];\n        let res = 0;\n        for (let i = 0; i < row.length; i++) {\n            if (includesAt(row, p1, i))\n                res += 40;\n            if (includesAt(row, p2, i))\n                res += 40;\n        }\n        return res;\n    };\n    let finder = 0;\n    for (const row of bm.data)\n        finder += finderPattern(row);\n    for (const column of inverse.data)\n        finder += finderPattern(column);\n    // Proportion of dark modules in entire symbol\n    // Add 10 points to a deviation of 5% increment or decrement in the proportion\n    // ratio of dark module from the referential 50%\n    let darkPixels = 0;\n    bm.rectRead(0, Infinity, (_c, val) => (darkPixels += val ? 1 : 0));\n    const darkPercent = (darkPixels / (bm.height * bm.width)) * 100;\n    const dark = 10 * Math.floor(Math.abs(darkPercent - 50) / 5);\n    return adjacent + box + finder + dark;\n}\n// Selects best mask according to penalty, if no mask is provided\nfunction drawQRBest(ver, ecc, data, maskIdx) {\n    if (maskIdx === undefined) {\n        const bestMask = best();\n        for (let mask = 0; mask < PATTERNS.length; mask++)\n            bestMask.add(penalty(drawQR(ver, ecc, data, mask, true)), mask);\n        maskIdx = bestMask.get();\n    }\n    if (maskIdx === undefined)\n        throw new Error('Cannot find mask'); // Should never happen\n    return drawQR(ver, ecc, data, maskIdx);\n}\nfunction validateECC(ec) {\n    if (!ECMode.includes(ec))\n        throw new Error(`Invalid error correction mode=${ec}. Expected: ${ECMode}`);\n}\nfunction validateEncoding(enc) {\n    if (!Encoding.includes(enc))\n        throw new Error(`Encoding: invalid mode=${enc}. Expected: ${Encoding}`);\n    if (enc === 'kanji' || enc === 'eci')\n        throw new Error(`Encoding: ${enc} is not supported (yet?).`);\n}\nfunction validateMask(mask) {\n    if (![0, 1, 2, 3, 4, 5, 6, 7].includes(mask) || !PATTERNS[mask])\n        throw new Error(`Invalid mask=${mask}. Expected number [0..7]`);\n}\nfunction encodeQR(text, output = 'raw', opts = {}) {\n    const ecc = opts.ecc !== undefined ? opts.ecc : 'medium';\n    validateECC(ecc);\n    const encoding = opts.encoding !== undefined ? opts.encoding : detectType(text);\n    validateEncoding(encoding);\n    if (opts.mask !== undefined)\n        validateMask(opts.mask);\n    let ver = opts.version;\n    let data, err = new Error('Unknown error');\n    if (ver !== undefined) {\n        validateVersion(ver);\n        data = encode(ver, ecc, text, encoding);\n    }\n    else {\n        // If no version is provided, try to find smallest one which fits\n        // Currently just scans all version, can be significantly speedup if needed\n        for (let i = 1; i <= 40; i++) {\n            try {\n                data = encode(i, ecc, text, encoding);\n                ver = i;\n                break;\n            }\n            catch (e) {\n                err = e;\n            }\n        }\n    }\n    if (!ver || !data)\n        throw err;\n    let res = drawQRBest(ver, ecc, data, opts.mask);\n    res.assertDrawn();\n    const border = opts.border === undefined ? 2 : opts.border;\n    if (!Number.isSafeInteger(border))\n        throw new Error(`invalid border type=${typeof border}`);\n    res = res.border(border, false); // Add border\n    if (opts.scale !== undefined)\n        res = res.scale(opts.scale); // Scale image\n    if (output === 'raw')\n        return res.data;\n    else if (output === 'ascii')\n        return res.toASCII();\n    else if (output === 'svg')\n        return res.toSVG(opts.optimize);\n    else if (output === 'gif')\n        return res.toGIF();\n    else if (output === 'term')\n        return res.toTerm();\n    else\n        throw new Error(`Unknown output: ${output}`);\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (encodeQR);\nconst utils = {\n    best,\n    bin,\n    drawTemplate,\n    fillArr,\n    info,\n    interleave,\n    validateVersion,\n    zigzag,\n};\n// Unsafe API utils, exported only for tests\nconst _tests = {\n    Bitmap,\n    info,\n    detectType,\n    encode,\n    drawQR,\n    penalty,\n    PATTERNS,\n};\n// Type tests\n// const o1 = qr('test', 'ascii');\n// const o2 = qr('test', 'raw');\n// const o3 = qr('test', 'gif');\n// const o4 = qr('test', 'svg');\n// const o5 = qr('test', 'term');\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/qr/index.js\n");

/***/ })

};
;