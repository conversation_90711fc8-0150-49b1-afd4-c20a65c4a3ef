import { define<PERSON>hai<PERSON> } from '../../utils/chain/defineChain.js'

export const rootPorcini = /*#__PURE__*/ define<PERSON>hain({
  id: 7672,
  name: 'The Root Network - Porcini',
  nativeCurrency: {
    decimals: 18,
    name: '<PERSON><PERSON>',
    symbol: 'X<PERSON>',
  },
  rpcUrls: {
    default: {
      http: ['https://porcini.rootnet.app/archive'],
      webSocket: ['wss://porcini.rootnet.app/archive/ws'],
    },
  },
  blockExplorers: {
    default: {
      name: 'Roots<PERSON>',
      url: 'https://porcini.rootscan.io',
    },
  },
  contracts: {
    multicall3: {
      address: '0xc9C2E2429AeC354916c476B30d729deDdC94988d',
      blockCreated: 10555692,
    },
  },
  testnet: true,
})
