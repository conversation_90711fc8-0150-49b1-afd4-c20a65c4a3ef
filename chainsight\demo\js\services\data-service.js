/* ==================== DATA SERVICE ====================
   Centralized data management and state handling
   ============================================== */

class DataService {
    constructor() {
        this.cache = new Map();
        this.subscribers = new Map();
        this.storage = {
            local: window.localStorage,
            session: window.sessionStorage
        };
        this.apiUtils = new APIUtils();
        this.cacheTimeout = 5 * 60 * 1000; // 5 minutes default
    }

    /**
     * Subscribe to data changes
     * @param {string} key - Data key to watch
     * @param {Function} callback - Callback function
     * @returns {Function} - Unsubscribe function
     */
    subscribe(key, callback) {
        if (!this.subscribers.has(key)) {
            this.subscribers.set(key, new Set());
        }
        
        this.subscribers.get(key).add(callback);
        
        // Return unsubscribe function
        return () => {
            const keySubscribers = this.subscribers.get(key);
            if (keySubscribers) {
                keySubscribers.delete(callback);
                if (keySubscribers.size === 0) {
                    this.subscribers.delete(key);
                }
            }
        };
    }

    /**
     * Notify subscribers of data changes
     * @param {string} key - Data key
     * @param {*} data - New data
     */
    notify(key, data) {
        const keySubscribers = this.subscribers.get(key);
        if (keySubscribers) {
            keySubscribers.forEach(callback => {
                try {
                    callback(data, key);
                } catch (error) {
                    console.error('Error in data subscriber:', error);
                }
            });
        }
    }

    /**
     * Set data in cache
     * @param {string} key - Data key
     * @param {*} data - Data to store
     * @param {number} ttl - Time to live in milliseconds
     */
    set(key, data, ttl = this.cacheTimeout) {
        const cacheEntry = {
            data,
            timestamp: Date.now(),
            ttl
        };
        
        this.cache.set(key, cacheEntry);
        this.notify(key, data);
    }

    /**
     * Get data from cache
     * @param {string} key - Data key
     * @returns {*} - Cached data or null
     */
    get(key) {
        const cacheEntry = this.cache.get(key);
        
        if (!cacheEntry) {
            return null;
        }
        
        // Check if cache has expired
        if (Date.now() - cacheEntry.timestamp > cacheEntry.ttl) {
            this.cache.delete(key);
            return null;
        }
        
        return cacheEntry.data;
    }

    /**
     * Check if data exists in cache
     * @param {string} key - Data key
     * @returns {boolean}
     */
    has(key) {
        return this.get(key) !== null;
    }

    /**
     * Remove data from cache
     * @param {string} key - Data key
     */
    remove(key) {
        this.cache.delete(key);
        this.notify(key, null);
    }

    /**
     * Clear all cache
     */
    clear() {
        const keys = Array.from(this.cache.keys());
        this.cache.clear();
        
        // Notify all subscribers
        keys.forEach(key => this.notify(key, null));
    }

    /**
     * Fetch data with caching
     * @param {string} key - Cache key
     * @param {Function} fetchFn - Function to fetch data
     * @param {Object} options - Options
     * @returns {Promise} - Data promise
     */
    async fetch(key, fetchFn, options = {}) {
        const {
            useCache = true,
            ttl = this.cacheTimeout,
            forceRefresh = false
        } = options;

        // Return cached data if available and not forcing refresh
        if (useCache && !forceRefresh && this.has(key)) {
            return this.get(key);
        }

        try {
            const data = await fetchFn();
            
            if (useCache) {
                this.set(key, data, ttl);
            }
            
            return data;
        } catch (error) {
            // Return cached data if fetch fails and cache exists
            if (useCache && this.has(key)) {
                console.warn(`Fetch failed for ${key}, returning cached data:`, error);
                return this.get(key);
            }
            
            throw error;
        }
    }

    /**
     * Fetch data from API endpoint
     * @param {string} endpoint - API endpoint
     * @param {Object} options - Request options
     * @returns {Promise} - Response data
     */
    async fetchFromAPI(endpoint, options = {}) {
        const cacheKey = `api:${endpoint}:${JSON.stringify(options.params || {})}`;
        
        return this.fetch(cacheKey, async () => {
            const response = await this.apiUtils.request(endpoint, options);
            return response.json();
        }, options);
    }

    /**
     * Save data to persistent storage
     * @param {string} key - Storage key
     * @param {*} data - Data to store
     * @param {string} type - Storage type ('local' or 'session')
     */
    saveToStorage(key, data, type = 'local') {
        try {
            const storage = this.storage[type];
            if (storage) {
                storage.setItem(key, JSON.stringify(data));
            }
        } catch (error) {
            console.error('Failed to save to storage:', error);
        }
    }

    /**
     * Load data from persistent storage
     * @param {string} key - Storage key
     * @param {string} type - Storage type ('local' or 'session')
     * @returns {*} - Stored data or null
     */
    loadFromStorage(key, type = 'local') {
        try {
            const storage = this.storage[type];
            if (storage) {
                const item = storage.getItem(key);
                return item ? JSON.parse(item) : null;
            }
        } catch (error) {
            console.error('Failed to load from storage:', error);
        }
        return null;
    }

    /**
     * Remove data from persistent storage
     * @param {string} key - Storage key
     * @param {string} type - Storage type ('local' or 'session')
     */
    removeFromStorage(key, type = 'local') {
        try {
            const storage = this.storage[type];
            if (storage) {
                storage.removeItem(key);
            }
        } catch (error) {
            console.error('Failed to remove from storage:', error);
        }
    }

    /**
     * Batch operations
     * @param {Array} operations - Array of operations
     * @returns {Promise} - Results array
     */
    async batch(operations) {
        const results = [];
        
        for (const operation of operations) {
            try {
                const { type, key, data, options } = operation;
                
                switch (type) {
                    case 'set':
                        this.set(key, data, options?.ttl);
                        results.push({ success: true, key });
                        break;
                        
                    case 'get':
                        const value = this.get(key);
                        results.push({ success: true, key, data: value });
                        break;
                        
                    case 'fetch':
                        const fetchedData = await this.fetch(key, data, options);
                        results.push({ success: true, key, data: fetchedData });
                        break;
                        
                    case 'remove':
                        this.remove(key);
                        results.push({ success: true, key });
                        break;
                        
                    default:
                        results.push({ success: false, key, error: 'Unknown operation' });
                }
            } catch (error) {
                results.push({ success: false, key: operation.key, error: error.message });
            }
        }
        
        return results;
    }

    /**
     * Create a reactive data store
     * @param {string} key - Store key
     * @param {*} initialValue - Initial value
     * @returns {Object} - Store object with getter, setter, and subscribe
     */
    createStore(key, initialValue = null) {
        // Set initial value if not exists
        if (!this.has(key)) {
            this.set(key, initialValue);
        }

        return {
            get: () => this.get(key),
            set: (value) => this.set(key, value),
            subscribe: (callback) => this.subscribe(key, callback),
            update: (updater) => {
                const currentValue = this.get(key);
                const newValue = updater(currentValue);
                this.set(key, newValue);
                return newValue;
            }
        };
    }

    /**
     * Invalidate cache entries by pattern
     * @param {RegExp|string} pattern - Pattern to match keys
     */
    invalidate(pattern) {
        const regex = typeof pattern === 'string' ? new RegExp(pattern) : pattern;
        const keysToRemove = [];
        
        for (const key of this.cache.keys()) {
            if (regex.test(key)) {
                keysToRemove.push(key);
            }
        }
        
        keysToRemove.forEach(key => this.remove(key));
    }

    /**
     * Get cache statistics
     * @returns {Object} - Cache stats
     */
    getStats() {
        const now = Date.now();
        let expired = 0;
        let active = 0;
        
        for (const [key, entry] of this.cache.entries()) {
            if (now - entry.timestamp > entry.ttl) {
                expired++;
            } else {
                active++;
            }
        }
        
        return {
            total: this.cache.size,
            active,
            expired,
            subscribers: this.subscribers.size
        };
    }

    /**
     * Clean up expired cache entries
     */
    cleanup() {
        const now = Date.now();
        const expiredKeys = [];
        
        for (const [key, entry] of this.cache.entries()) {
            if (now - entry.timestamp > entry.ttl) {
                expiredKeys.push(key);
            }
        }
        
        expiredKeys.forEach(key => this.remove(key));
        
        return expiredKeys.length;
    }
}

// Create global instance
const dataService = new DataService();

// Auto cleanup every 5 minutes
setInterval(() => {
    dataService.cleanup();
}, 5 * 60 * 1000);

// Export for module systems
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { DataService, dataService };
}

// Global assignment for browser
if (typeof window !== 'undefined') {
    window.DataService = DataService;
    window.dataService = dataService;
}
