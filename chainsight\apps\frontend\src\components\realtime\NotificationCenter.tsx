'use client';

import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  Bell, 
  X, 
  CheckCircle, 
  AlertTriangle, 
  Info, 
  TrendingUp, 
  DollarSign,
  Shield,
  Zap,
  Clock
} from 'lucide-react';
import { useSystemUpdates } from '@/hooks/useWebSocket';

interface Notification {
  id: string;
  type: 'success' | 'warning' | 'info' | 'error' | 'market' | 'security' | 'system';
  title: string;
  message: string;
  timestamp: number;
  read: boolean;
  priority: 'low' | 'medium' | 'high' | 'critical';
  actionUrl?: string;
  actionText?: string;
}

interface NotificationCenterProps {
  maxNotifications?: number;
  autoHideDelay?: number;
  showTimestamp?: boolean;
  position?: 'top-right' | 'top-left' | 'bottom-right' | 'bottom-left';
}

const NotificationCenter: React.FC<NotificationCenterProps> = ({
  maxNotifications = 50,
  autoHideDelay = 5000,
  showTimestamp = true,
  position = 'top-right'
}) => {
  const { connected, notifications, unreadCount, markAsRead, clearNotifications } = useSystemUpdates();
  const [isOpen, setIsOpen] = useState(false);
  const [filter, setFilter] = useState<string>('all');

  const getNotificationIcon = (type: string) => {
    switch (type) {
      case 'success':
        return <CheckCircle className="w-5 h-5 text-green-400" />;
      case 'warning':
        return <AlertTriangle className="w-5 h-5 text-yellow-400" />;
      case 'error':
        return <X className="w-5 h-5 text-red-400" />;
      case 'market':
        return <TrendingUp className="w-5 h-5 text-blue-400" />;
      case 'security':
        return <Shield className="w-5 h-5 text-purple-400" />;
      case 'system':
        return <Zap className="w-5 h-5 text-orange-400" />;
      default:
        return <Info className="w-5 h-5 text-gray-400" />;
    }
  };

  const getNotificationColor = (type: string, priority: string) => {
    const baseColors = {
      success: 'border-green-400/30 bg-green-400/10',
      warning: 'border-yellow-400/30 bg-yellow-400/10',
      error: 'border-red-400/30 bg-red-400/10',
      market: 'border-blue-400/30 bg-blue-400/10',
      security: 'border-purple-400/30 bg-purple-400/10',
      system: 'border-orange-400/30 bg-orange-400/10',
      info: 'border-gray-400/30 bg-gray-400/10'
    };

    if (priority === 'critical') {
      return 'border-red-500 bg-red-500/20 shadow-red-500/20 shadow-lg';
    }

    return baseColors[type as keyof typeof baseColors] || baseColors.info;
  };

  const getPriorityBadge = (priority: string) => {
    const colors = {
      low: 'bg-gray-600 text-gray-300',
      medium: 'bg-blue-600 text-blue-100',
      high: 'bg-orange-600 text-orange-100',
      critical: 'bg-red-600 text-red-100'
    };

    return (
      <span className={`px-2 py-1 text-xs rounded-full ${colors[priority as keyof typeof colors]}`}>
        {priority.toUpperCase()}
      </span>
    );
  };

  const formatTime = (timestamp: number): string => {
    const now = Date.now();
    const diff = now - timestamp;
    
    if (diff < 60000) return 'Just now';
    if (diff < 3600000) return `${Math.floor(diff / 60000)}m ago`;
    if (diff < 86400000) return `${Math.floor(diff / 3600000)}h ago`;
    return new Date(timestamp).toLocaleDateString();
  };

  const filteredNotifications = notifications.filter(notification => {
    if (filter === 'all') return true;
    if (filter === 'unread') return !notification.read;
    return notification.type === filter;
  });

  const getPositionClasses = () => {
    switch (position) {
      case 'top-left':
        return 'top-4 left-4';
      case 'bottom-right':
        return 'bottom-4 right-4';
      case 'bottom-left':
        return 'bottom-4 left-4';
      default:
        return 'top-4 right-4';
    }
  };

  return (
    <div className={`fixed ${getPositionClasses()} z-50`}>
      {/* Notification Bell */}
      <motion.button
        whileHover={{ scale: 1.05 }}
        whileTap={{ scale: 0.95 }}
        onClick={() => setIsOpen(!isOpen)}
        className="relative bg-gradient-to-r from-gray-900 to-black border border-yellow-400/30 rounded-full p-3 shadow-luxury hover:border-yellow-400/50 transition-all duration-200"
      >
        <Bell className="w-6 h-6 text-yellow-400" />
        {unreadCount > 0 && (
          <motion.div
            initial={{ scale: 0 }}
            animate={{ scale: 1 }}
            className="absolute -top-2 -right-2 bg-red-500 text-white text-xs rounded-full w-6 h-6 flex items-center justify-center font-bold"
          >
            {unreadCount > 99 ? '99+' : unreadCount}
          </motion.div>
        )}
        {!connected && (
          <div className="absolute -bottom-1 -right-1 w-3 h-3 bg-red-500 rounded-full border-2 border-gray-900" />
        )}
      </motion.button>

      {/* Notification Panel */}
      <AnimatePresence>
        {isOpen && (
          <motion.div
            initial={{ opacity: 0, scale: 0.95, y: -10 }}
            animate={{ opacity: 1, scale: 1, y: 0 }}
            exit={{ opacity: 0, scale: 0.95, y: -10 }}
            className="absolute top-16 right-0 w-96 bg-gradient-to-br from-gray-900 to-black border border-yellow-400/20 rounded-xl shadow-luxury overflow-hidden"
          >
            {/* Header */}
            <div className="bg-gray-800/50 border-b border-gray-700 p-4">
              <div className="flex items-center justify-between mb-3">
                <h3 className="text-lg font-semibold text-white">Notifications</h3>
                <div className="flex items-center space-x-2">
                  <button
                    onClick={clearNotifications}
                    className="text-gray-400 hover:text-white text-sm px-2 py-1 rounded transition-colors"
                  >
                    Clear All
                  </button>
                  <button
                    onClick={() => setIsOpen(false)}
                    className="text-gray-400 hover:text-white p-1 rounded transition-colors"
                  >
                    <X className="w-4 h-4" />
                  </button>
                </div>
              </div>

              {/* Filters */}
              <div className="flex flex-wrap gap-2">
                {['all', 'unread', 'market', 'security', 'system'].map((filterType) => (
                  <button
                    key={filterType}
                    onClick={() => setFilter(filterType)}
                    className={`px-3 py-1 text-xs rounded-full transition-all duration-200 ${
                      filter === filterType
                        ? 'bg-yellow-400 text-black'
                        : 'bg-gray-700 text-gray-300 hover:bg-gray-600'
                    }`}
                  >
                    {filterType.charAt(0).toUpperCase() + filterType.slice(1)}
                  </button>
                ))}
              </div>
            </div>

            {/* Notifications List */}
            <div className="max-h-96 overflow-y-auto">
              {filteredNotifications.length === 0 ? (
                <div className="p-8 text-center text-gray-400">
                  <Bell className="w-12 h-12 mx-auto mb-3 opacity-50" />
                  <p>No notifications</p>
                </div>
              ) : (
                <div className="p-2 space-y-2">
                  {filteredNotifications.map((notification) => (
                    <motion.div
                      key={notification.id}
                      initial={{ opacity: 0, x: 20 }}
                      animate={{ opacity: 1, x: 0 }}
                      className={`p-3 rounded-lg border cursor-pointer transition-all duration-200 hover:bg-gray-800/30 ${
                        getNotificationColor(notification.type, notification.priority)
                      } ${!notification.read ? 'ring-1 ring-yellow-400/30' : ''}`}
                      onClick={() => markAsRead(notification.id)}
                    >
                      <div className="flex items-start space-x-3">
                        <div className="flex-shrink-0 mt-0.5">
                          {getNotificationIcon(notification.type)}
                        </div>
                        
                        <div className="flex-1 min-w-0">
                          <div className="flex items-center justify-between mb-1">
                            <h4 className="text-sm font-medium text-white truncate">
                              {notification.title}
                            </h4>
                            {notification.priority !== 'low' && getPriorityBadge(notification.priority)}
                          </div>
                          
                          <p className="text-sm text-gray-300 mb-2">
                            {notification.message}
                          </p>
                          
                          <div className="flex items-center justify-between">
                            {showTimestamp && (
                              <div className="flex items-center space-x-1 text-xs text-gray-500">
                                <Clock className="w-3 h-3" />
                                <span>{formatTime(notification.timestamp)}</span>
                              </div>
                            )}
                            
                            {notification.actionUrl && notification.actionText && (
                              <button className="text-xs text-yellow-400 hover:text-yellow-300 transition-colors">
                                {notification.actionText}
                              </button>
                            )}
                          </div>
                        </div>
                        
                        {!notification.read && (
                          <div className="w-2 h-2 bg-yellow-400 rounded-full flex-shrink-0 mt-2" />
                        )}
                      </div>
                    </motion.div>
                  ))}
                </div>
              )}
            </div>

            {/* Footer */}
            {notifications.length > 0 && (
              <div className="bg-gray-800/30 border-t border-gray-700 p-3 text-center">
                <span className="text-xs text-gray-400">
                  {notifications.length} total notifications
                </span>
              </div>
            )}
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};

export default NotificationCenter;
