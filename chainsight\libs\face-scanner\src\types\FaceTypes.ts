// Face Scanner Types and Interfaces

export interface FaceDetection {
  id: string;
  boundingBox: BoundingBox;
  confidence: number;
  landmarks?: FaceLandmarks;
  age?: AgeEstimation;
  emotion?: EmotionAnalysis;
  gender?: GenderPrediction;
  quality?: ImageQuality;
  timestamp: Date;
}

export interface BoundingBox {
  x: number;
  y: number;
  width: number;
  height: number;
}

export interface FaceLandmarks {
  leftEye: Point[];
  rightEye: Point[];
  nose: Point[];
  mouth: Point[];
  jawline: Point[];
  eyebrows: Point[];
}

export interface Point {
  x: number;
  y: number;
}

export interface AgeEstimation {
  estimatedAge: number;
  ageRange: {
    min: number;
    max: number;
  };
  confidence: number;
}

export interface EmotionAnalysis {
  dominantEmotion: Emotion;
  emotions: EmotionScore[];
  confidence: number;
}

export interface EmotionScore {
  emotion: Emotion;
  score: number;
}

export interface GenderPrediction {
  gender: 'male' | 'female';
  confidence: number;
}

export interface ImageQuality {
  sharpness: number;
  brightness: number;
  contrast: number;
  resolution: {
    width: number;
    height: number;
  };
  overallScore: number; // 0-1
}

export interface FaceComparison {
  similarity: number;
  isMatch: boolean;
  confidence: number;
  face1: FaceDetection;
  face2: FaceDetection;
  comparisonMethod: string;
  timestamp: Date;
}

export interface FaceDatabase {
  id: string;
  name: string;
  faces: StoredFace[];
  createdAt: Date;
  updatedAt: Date;
}

export interface StoredFace {
  id: string;
  personId?: string;
  personName?: string;
  encoding: number[];
  metadata?: Record<string, any>;
  imageUrl?: string;
  createdAt: Date;
}

export interface FaceSearchResult {
  face: StoredFace;
  similarity: number;
  confidence: number;
  rank: number;
}

export interface BatchProcessingResult {
  totalImages: number;
  processedImages: number;
  failedImages: number;
  detectedFaces: number;
  results: FaceDetection[];
  errors: ProcessingError[];
  processingTime: number;
}

export interface ProcessingError {
  imageId: string;
  error: string;
  timestamp: Date;
}

export interface FaceAnalysisOptions {
  detectAge?: boolean;
  detectEmotion?: boolean;
  detectGender?: boolean;
  extractLandmarks?: boolean;
  qualityCheck?: boolean;
  minFaceSize?: number;
  maxFaces?: number;
}

export interface ImageProcessingOptions {
  resize?: {
    width: number;
    height: number;
  };
  crop?: BoundingBox;
  enhance?: boolean;
  normalize?: boolean;
  format?: 'jpeg' | 'png' | 'webp';
  quality?: number;
}

export interface ModelInfo {
  name: string;
  version: string;
  type: 'detection' | 'recognition' | 'emotion' | 'age' | 'gender';
  accuracy: number;
  size: number; // in MB
  loadTime: number; // in ms
  isLoaded: boolean;
}

export interface FaceTrackingResult {
  trackingId: string;
  faces: Array<{
    frameNumber: number;
    timestamp: number;
    detection: FaceDetection;
  }>;
  duration: number;
  averageConfidence: number;
}

export interface LiveDetectionConfig {
  fps?: number;
  resolution?: {
    width: number;
    height: number;
  };
  enableTracking?: boolean;
  trackingThreshold?: number;
  bufferSize?: number;
}

// Enums
export enum Emotion {
  HAPPY = 'happy',
  SAD = 'sad',
  ANGRY = 'angry',
  SURPRISED = 'surprised',
  FEARFUL = 'fearful',
  DISGUSTED = 'disgusted',
  NEUTRAL = 'neutral'
}

export enum FaceQuality {
  EXCELLENT = 'excellent',
  GOOD = 'good',
  FAIR = 'fair',
  POOR = 'poor'
}

export enum ProcessingStatus {
  PENDING = 'pending',
  PROCESSING = 'processing',
  COMPLETED = 'completed',
  FAILED = 'failed'
}

export enum ComparisonMethod {
  EUCLIDEAN = 'euclidean',
  COSINE = 'cosine',
  MANHATTAN = 'manhattan',
  DEEP_LEARNING = 'deep_learning'
}

export enum DetectionModel {
  MTCNN = 'mtcnn',
  RETINAFACE = 'retinaface',
  BLAZEFACE = 'blazeface',
  SSD_MOBILENET = 'ssd_mobilenet'
}

export enum RecognitionModel {
  FACENET = 'facenet',
  ARCFACE = 'arcface',
  DLIB = 'dlib',
  OPENFACE = 'openface'
}
