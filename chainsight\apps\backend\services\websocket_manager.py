from fastapi import WebSocket
from typing import Dict, List, Set
import json
import asyncio
from datetime import datetime
import logging

logger = logging.getLogger(__name__)

class WebSocketManager:
    def __init__(self):
        # Store active connections
        self.active_connections: Dict[str, WebSocket] = {}
        
        # Store user subscriptions
        self.user_subscriptions: Dict[str, Set[str]] = {}
        
        # Store connection metadata
        self.connection_metadata: Dict[str, dict] = {}

    async def connect(self, websocket: WebSocket, client_id: str, user_id: str = None):
        """Connect a new WebSocket client"""
        try:
            await websocket.accept()
            self.active_connections[client_id] = websocket
            
            # Store metadata
            self.connection_metadata[client_id] = {
                "user_id": user_id,
                "connected_at": datetime.now(),
                "subscriptions": set()
            }
            
            logger.info(f"WebSocket client {client_id} connected (user: {user_id})")
            
            # Send welcome message
            await self.send_json({
                "type": "connection_established",
                "client_id": client_id,
                "timestamp": datetime.now().isoformat(),
                "message": "Connected to Chainsight WebSocket"
            }, client_id)
            
        except Exception as e:
            logger.error(f"Error connecting client {client_id}: {e}")
            raise

    def disconnect(self, client_id: str):
        """Disconnect a WebSocket client"""
        try:
            if client_id in self.active_connections:
                del self.active_connections[client_id]
            
            if client_id in self.connection_metadata:
                user_id = self.connection_metadata[client_id].get("user_id")
                del self.connection_metadata[client_id]
                
                # Clean up user subscriptions
                if user_id and user_id in self.user_subscriptions:
                    self.user_subscriptions[user_id].discard(client_id)
                    if not self.user_subscriptions[user_id]:
                        del self.user_subscriptions[user_id]
            
            logger.info(f"WebSocket client {client_id} disconnected")
            
        except Exception as e:
            logger.error(f"Error disconnecting client {client_id}: {e}")

    async def send_personal_message(self, message: str, client_id: str):
        """Send a text message to a specific client"""
        if client_id in self.active_connections:
            try:
                await self.active_connections[client_id].send_text(message)
            except Exception as e:
                logger.error(f"Error sending message to {client_id}: {e}")
                self.disconnect(client_id)

    async def send_json(self, data: dict, client_id: str):
        """Send JSON data to a specific client"""
        try:
            message = json.dumps(data, default=str)
            await self.send_personal_message(message, client_id)
        except Exception as e:
            logger.error(f"Error sending JSON to {client_id}: {e}")

    async def broadcast(self, message: str):
        """Broadcast a text message to all connected clients"""
        disconnected_clients = []
        
        for client_id in self.active_connections:
            try:
                await self.active_connections[client_id].send_text(message)
            except Exception as e:
                logger.error(f"Error broadcasting to {client_id}: {e}")
                disconnected_clients.append(client_id)
        
        # Clean up disconnected clients
        for client_id in disconnected_clients:
            self.disconnect(client_id)

    async def broadcast_json(self, data: dict):
        """Broadcast JSON data to all connected clients"""
        try:
            message = json.dumps(data, default=str)
            await self.broadcast(message)
        except Exception as e:
            logger.error(f"Error broadcasting JSON: {e}")

    async def send_to_user(self, data: dict, user_id: str):
        """Send data to all connections for a specific user"""
        user_clients = [
            client_id for client_id, metadata in self.connection_metadata.items()
            if metadata.get("user_id") == user_id
        ]
        
        for client_id in user_clients:
            await self.send_json(data, client_id)

    async def subscribe_client(self, client_id: str, subscription_type: str):
        """Subscribe a client to a specific type of updates"""
        if client_id in self.connection_metadata:
            self.connection_metadata[client_id]["subscriptions"].add(subscription_type)
            
            await self.send_json({
                "type": "subscription_confirmed",
                "subscription": subscription_type,
                "timestamp": datetime.now().isoformat()
            }, client_id)
            
            logger.info(f"Client {client_id} subscribed to {subscription_type}")

    async def unsubscribe_client(self, client_id: str, subscription_type: str):
        """Unsubscribe a client from a specific type of updates"""
        if client_id in self.connection_metadata:
            self.connection_metadata[client_id]["subscriptions"].discard(subscription_type)
            
            await self.send_json({
                "type": "unsubscription_confirmed", 
                "subscription": subscription_type,
                "timestamp": datetime.now().isoformat()
            }, client_id)
            
            logger.info(f"Client {client_id} unsubscribed from {subscription_type}")

    async def broadcast_to_subscribers(self, data: dict, subscription_type: str):
        """Broadcast data to all clients subscribed to a specific type"""
        subscriber_clients = [
            client_id for client_id, metadata in self.connection_metadata.items()
            if subscription_type in metadata.get("subscriptions", set())
        ]
        
        for client_id in subscriber_clients:
            await self.send_json(data, client_id)

    def get_connection_stats(self) -> dict:
        """Get statistics about current connections"""
        total_connections = len(self.active_connections)
        unique_users = len(set(
            metadata.get("user_id") for metadata in self.connection_metadata.values()
            if metadata.get("user_id")
        ))
        
        subscription_counts = {}
        for metadata in self.connection_metadata.values():
            for sub in metadata.get("subscriptions", set()):
                subscription_counts[sub] = subscription_counts.get(sub, 0) + 1
        
        return {
            "total_connections": total_connections,
            "unique_users": unique_users,
            "subscription_counts": subscription_counts,
            "active_clients": list(self.active_connections.keys())
        }

    async def ping_all_clients(self):
        """Send ping to all clients to check connection health"""
        ping_data = {
            "type": "ping",
            "timestamp": datetime.now().isoformat()
        }
        await self.broadcast_json(ping_data)

    async def send_market_update(self, market_data: dict):
        """Send market data updates to subscribed clients"""
        update_data = {
            "type": "market_update",
            "data": market_data,
            "timestamp": datetime.now().isoformat()
        }
        await self.broadcast_to_subscribers(update_data, "market_data")

    async def send_ai_notification(self, user_id: str, notification: dict):
        """Send AI-related notifications to a specific user"""
        notification_data = {
            "type": "ai_notification",
            "data": notification,
            "timestamp": datetime.now().isoformat()
        }
        await self.send_to_user(notification_data, user_id)

    async def send_system_alert(self, alert: dict, level: str = "info"):
        """Send system alerts to all connected clients"""
        alert_data = {
            "type": "system_alert",
            "level": level,
            "data": alert,
            "timestamp": datetime.now().isoformat()
        }
        await self.broadcast_json(alert_data)

# Global WebSocket manager instance
websocket_manager = WebSocketManager()

# Background task for periodic updates
async def periodic_updates():
    """Background task to send periodic updates"""
    while True:
        try:
            # Send market data updates every 5 seconds
            market_data = {
                "BTC": {"price": 43250, "change": 2.4},
                "ETH": {"price": 2680, "change": 1.8},
                "USDT": {"price": 1.0, "change": 0.1}
            }
            await websocket_manager.send_market_update(market_data)
            
            # Send system stats every 30 seconds
            if datetime.now().second % 30 == 0:
                stats = websocket_manager.get_connection_stats()
                await websocket_manager.send_system_alert({
                    "message": "System status update",
                    "stats": stats
                }, "info")
            
            await asyncio.sleep(5)
            
        except Exception as e:
            logger.error(f"Error in periodic updates: {e}")
            await asyncio.sleep(5)

# Start background task
def start_background_tasks():
    """Start background tasks for WebSocket manager"""
    asyncio.create_task(periodic_updates())
