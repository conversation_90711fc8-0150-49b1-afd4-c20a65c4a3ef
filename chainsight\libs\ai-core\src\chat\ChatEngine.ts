import { ChatMessage, ChatSession, AIResponse, ConversationContext, AIModelType } from '../types/AITypes';
import { v4 as uuidv4 } from 'uuid';

export class ChatEngine {
  private apiKey: string;
  private model: AIModelType;
  private temperature: number;
  private maxTokens: number;
  private sessions: Map<string, ChatSession> = new Map();

  constructor(
    apiKey: string,
    model: AIModelType = AIModelType.GPT_3_5_TURBO,
    temperature: number = 0.7,
    maxTokens: number = 500
  ) {
    this.apiKey = apiKey;
    this.model = model;
    this.temperature = temperature;
    this.maxTokens = maxTokens;
  }

  async createSession(userId: string, context?: string): Promise<ChatSession> {
    const session: ChatSession = {
      id: uuidv4(),
      userId,
      messages: [],
      createdAt: new Date(),
      updatedAt: new Date(),
      context
    };

    this.sessions.set(session.id, session);
    return session;
  }

  async sendMessage(
    sessionId: string,
    content: string,
    context?: ConversationContext
  ): Promise<AIResponse> {
    const startTime = Date.now();
    const session = this.sessions.get(sessionId);

    if (!session) {
      throw new Error(`Session ${sessionId} not found`);
    }

    // Add user message to session
    const userMessage: ChatMessage = {
      id: uuidv4(),
      role: 'user',
      content,
      timestamp: new Date()
    };

    session.messages.push(userMessage);

    try {
      // Generate AI response
      const aiContent = await this.generateResponse(session, context);
      
      // Add AI message to session
      const aiMessage: ChatMessage = {
        id: uuidv4(),
        role: 'assistant',
        content: aiContent,
        timestamp: new Date()
      };

      session.messages.push(aiMessage);
      session.updatedAt = new Date();

      const processingTime = Date.now() - startTime;

      return {
        content: aiContent,
        confidence: 0.85, // Mock confidence score
        processingTime,
        model: this.model,
        tokens: {
          prompt: this.estimateTokens(content),
          completion: this.estimateTokens(aiContent),
          total: this.estimateTokens(content) + this.estimateTokens(aiContent)
        }
      };

    } catch (error) {
      throw new Error(`Failed to generate AI response: ${error}`);
    }
  }

  private async generateResponse(
    session: ChatSession,
    context?: ConversationContext
  ): Promise<string> {
    // Build conversation history
    const messages = session.messages.slice(-10); // Keep last 10 messages for context
    
    // Create system prompt based on context
    const systemPrompt = this.buildSystemPrompt(session.context, context);
    
    // For demo purposes, we'll use intelligent fallback responses
    // In production, this would call OpenAI API
    return this.generateIntelligentResponse(messages, systemPrompt);
  }

  private buildSystemPrompt(sessionContext?: string, conversationContext?: ConversationContext): string {
    let prompt = "You are Chainsight AI, a luxury AI assistant specializing in blockchain, cryptocurrency, finance, design, HR, and legal analysis. ";
    
    if (sessionContext) {
      prompt += `Context: ${sessionContext}. `;
    }

    if (conversationContext?.domain) {
      prompt += `Focus on ${conversationContext.domain} domain. `;
    }

    prompt += "Provide helpful, accurate, and professional responses with a luxury tone.";
    
    return prompt;
  }

  private generateIntelligentResponse(messages: ChatMessage[], systemPrompt: string): string {
    const lastMessage = messages[messages.length - 1];
    if (!lastMessage) return "Hello! How can I assist you today?";

    const content = lastMessage.content.toLowerCase();

    // Crypto/Blockchain responses
    if (this.containsKeywords(content, ['crypto', 'bitcoin', 'ethereum', 'blockchain', 'token', 'defi'])) {
      return this.getCryptoResponse(content);
    }

    // Finance responses
    if (this.containsKeywords(content, ['stock', 'market', 'finance', 'trading', 'investment', 'portfolio'])) {
      return this.getFinanceResponse(content);
    }

    // Design responses
    if (this.containsKeywords(content, ['design', 'animation', 'lottie', 'svg', 'ui', 'ux'])) {
      return this.getDesignResponse(content);
    }

    // HR/Legal responses
    if (this.containsKeywords(content, ['resume', 'cv', 'contract', 'legal', 'hr', 'hiring'])) {
      return this.getHRLegalResponse(content);
    }

    // Face recognition responses
    if (this.containsKeywords(content, ['face', 'facial', 'recognition', 'emotion', 'age'])) {
      return this.getFaceRecognitionResponse(content);
    }

    // General responses
    if (this.containsKeywords(content, ['hello', 'hi', 'hey', 'greetings'])) {
      return "Hello! I'm your Chainsight AI assistant. I can help you with cryptocurrency analysis, market predictions, smart contract deployment, design creation, document analysis, and much more. What would you like to explore today?";
    }

    // Default intelligent response
    return this.getContextualResponse(messages);
  }

  private containsKeywords(text: string, keywords: string[]): boolean {
    return keywords.some(keyword => text.includes(keyword));
  }

  private getCryptoResponse(content: string): string {
    if (content.includes('price')) {
      return "I'm tracking real-time cryptocurrency prices. Bitcoin is currently at $43,250 (****%), Ethereum at $2,680 (****%). I can provide detailed price analysis, predictions, and help you deploy smart contracts. What specific crypto analysis do you need?";
    }
    if (content.includes('token') || content.includes('contract')) {
      return "I can help you create and deploy ERC20 tokens with custom features. Our smart contract generator supports standard tokens, deflationary tokens, and liquidity pool creation. Would you like to create a new token or analyze existing contracts?";
    }
    return "I specialize in cryptocurrency analysis and blockchain operations. I can help with market predictions, smart contract deployment, DeFi strategies, and real-time price monitoring. What blockchain task can I assist you with?";
  }

  private getFinanceResponse(content: string): string {
    if (content.includes('predict') || content.includes('forecast')) {
      return "I can provide AI-powered market predictions using advanced algorithms. My analysis includes price forecasting, trend analysis, volatility predictions, and risk assessments. Which assets would you like me to analyze?";
    }
    if (content.includes('portfolio')) {
      return "I can analyze your portfolio for diversification, risk assessment, and optimization opportunities. My AI models consider market correlations, volatility patterns, and growth potential. Share your holdings for a comprehensive analysis.";
    }
    return "I offer comprehensive financial analysis including market predictions, portfolio optimization, risk assessment, and trading insights. My AI models analyze real-time data to provide actionable investment recommendations. How can I help with your financial strategy?";
  }

  private getDesignResponse(content: string): string {
    if (content.includes('animation') || content.includes('lottie')) {
      return "I can create stunning animations including Lottie files, SVG animations, and CSS effects. Our design studio supports luxury themes with gold accents and smooth transitions. What type of animation would you like me to create?";
    }
    return "I can help you create professional design assets including animations, UI components, and visual elements. Our design system features luxury black themes with gold highlights. What design project can I assist you with?";
  }

  private getHRLegalResponse(content: string): string {
    if (content.includes('resume') || content.includes('cv')) {
      return "I can analyze resumes for key skills, experience evaluation, and improvement recommendations. My AI extracts relevant information and provides scoring based on industry standards. Upload a resume for detailed analysis.";
    }
    if (content.includes('contract')) {
      return "I can review legal contracts for key terms, potential risks, and compliance issues. My analysis covers employment agreements, service contracts, NDAs, and more. What type of contract would you like me to review?";
    }
    return "I provide comprehensive HR and legal analysis including resume screening, contract review, compliance checking, and document analysis. How can I assist with your HR or legal needs?";
  }

  private getFaceRecognitionResponse(content: string): string {
    return "I can analyze facial features for age estimation, emotion detection, and facial recognition. Our AI processes images in real-time with high accuracy. Upload an image to start facial analysis or comparison.";
  }

  private getContextualResponse(messages: ChatMessage[]): string {
    const recentMessages = messages.slice(-3);
    const conversationTopics = recentMessages.map(m => m.content.toLowerCase()).join(' ');

    if (this.containsKeywords(conversationTopics, ['help', 'assist', 'support'])) {
      return "I'm here to help! As your Chainsight AI assistant, I can assist with cryptocurrency analysis, financial predictions, smart contract deployment, design creation, document analysis, and facial recognition. What specific task would you like to work on?";
    }

    return "I understand you're looking for assistance. As your Chainsight AI, I specialize in blockchain operations, financial analysis, design creation, document review, and real-time market insights. Could you provide more details about what you'd like to accomplish?";
  }

  private estimateTokens(text: string): number {
    // Rough estimation: 1 token ≈ 4 characters
    return Math.ceil(text.length / 4);
  }

  getSession(sessionId: string): ChatSession | undefined {
    return this.sessions.get(sessionId);
  }

  deleteSession(sessionId: string): boolean {
    return this.sessions.delete(sessionId);
  }

  getUserSessions(userId: string): ChatSession[] {
    return Array.from(this.sessions.values()).filter(session => session.userId === userId);
  }

  clearOldSessions(maxAge: number = 24 * 60 * 60 * 1000): number {
    const cutoff = new Date(Date.now() - maxAge);
    let deletedCount = 0;

    for (const [sessionId, session] of this.sessions.entries()) {
      if (session.updatedAt < cutoff) {
        this.sessions.delete(sessionId);
        deletedCount++;
      }
    }

    return deletedCount;
  }
}
