/* ==================== MODAL COMPONENT ====================
   Reusable modal dialog component with glass effect
   ===================================================== */

class ModalComponent {
    constructor(options = {}) {
        this.options = {
            id: `modal-${Date.now()}`,
            title: '',
            content: '',
            size: 'medium', // small, medium, large, fullscreen
            closable: true,
            backdrop: true,
            keyboard: true,
            animation: true,
            autoFocus: true,
            className: '',
            onShow: null,
            onHide: null,
            onConfirm: null,
            onCancel: null,
            ...options
        };

        this.isVisible = false;
        this.element = null;
        this.backdrop = null;
        this.focusedElementBeforeModal = null;
        
        this.init();
    }

    /**
     * Initialize modal
     */
    init() {
        this.createElement();
        this.bindEvents();
    }

    /**
     * Create modal element
     */
    createElement() {
        // Create modal backdrop
        this.backdrop = DOMUtils.createElement('div', {
            className: `glass-modal ${this.options.className}`,
            id: this.options.id,
            'aria-hidden': 'true',
            role: 'dialog',
            'aria-modal': 'true'
        });

        // Create modal content container
        const contentContainer = DOMUtils.createElement('div', {
            className: `glass-modal-content modal-${this.options.size}`,
            role: 'document'
        });

        // Create modal header
        if (this.options.title || this.options.closable) {
            const header = this.createHeader();
            contentContainer.appendChild(header);
        }

        // Create modal body
        const body = this.createBody();
        contentContainer.appendChild(body);

        // Create modal footer if needed
        if (this.options.onConfirm || this.options.onCancel) {
            const footer = this.createFooter();
            contentContainer.appendChild(footer);
        }

        this.backdrop.appendChild(contentContainer);
        this.element = contentContainer;

        // Add to document
        document.body.appendChild(this.backdrop);
    }

    /**
     * Create modal header
     */
    createHeader() {
        const header = DOMUtils.createElement('div', {
            className: 'modal-header flex justify-between items-center mb-lg'
        });

        if (this.options.title) {
            const title = DOMUtils.createElement('h3', {
                className: 'modal-title text-2xl font-bold text-primary'
            }, this.options.title);
            header.appendChild(title);
        }

        if (this.options.closable) {
            const closeBtn = DOMUtils.createElement('button', {
                className: 'modal-close btn-icon glass-btn',
                'aria-label': 'Close modal',
                onclick: () => this.hide()
            }, '×');
            header.appendChild(closeBtn);
        }

        return header;
    }

    /**
     * Create modal body
     */
    createBody() {
        const body = DOMUtils.createElement('div', {
            className: 'modal-body'
        });

        if (typeof this.options.content === 'string') {
            body.innerHTML = this.options.content;
        } else if (this.options.content instanceof Element) {
            body.appendChild(this.options.content);
        }

        return body;
    }

    /**
     * Create modal footer
     */
    createFooter() {
        const footer = DOMUtils.createElement('div', {
            className: 'modal-footer flex justify-end gap-md mt-lg'
        });

        if (this.options.onCancel) {
            const cancelBtn = DOMUtils.createElement('button', {
                className: 'btn btn-ghost',
                onclick: () => this.cancel()
            }, 'Cancel');
            footer.appendChild(cancelBtn);
        }

        if (this.options.onConfirm) {
            const confirmBtn = DOMUtils.createElement('button', {
                className: 'btn btn-primary',
                onclick: () => this.confirm()
            }, 'Confirm');
            footer.appendChild(confirmBtn);
        }

        return footer;
    }

    /**
     * Bind event listeners
     */
    bindEvents() {
        // Backdrop click
        if (this.options.backdrop) {
            this.backdrop.addEventListener('click', (e) => {
                if (e.target === this.backdrop) {
                    this.hide();
                }
            });
        }

        // Keyboard events
        if (this.options.keyboard) {
            document.addEventListener('keydown', (e) => {
                if (this.isVisible && e.key === 'Escape') {
                    this.hide();
                }
            });
        }
    }

    /**
     * Show modal
     */
    show() {
        if (this.isVisible) return;

        // Store currently focused element
        this.focusedElementBeforeModal = document.activeElement;

        // Show modal
        this.backdrop.classList.add('active');
        this.isVisible = true;

        // Auto focus
        if (this.options.autoFocus) {
            const focusableElement = this.element.querySelector(
                'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])'
            );
            if (focusableElement) {
                focusableElement.focus();
            }
        }

        // Prevent body scroll
        document.body.style.overflow = 'hidden';

        // Callback
        if (this.options.onShow) {
            this.options.onShow(this);
        }

        // Dispatch event
        this.backdrop.dispatchEvent(new CustomEvent('modal:show', {
            detail: { modal: this }
        }));
    }

    /**
     * Hide modal
     */
    hide() {
        if (!this.isVisible) return;

        this.backdrop.classList.remove('active');
        this.isVisible = false;

        // Restore focus
        if (this.focusedElementBeforeModal) {
            this.focusedElementBeforeModal.focus();
        }

        // Restore body scroll
        document.body.style.overflow = '';

        // Callback
        if (this.options.onHide) {
            this.options.onHide(this);
        }

        // Dispatch event
        this.backdrop.dispatchEvent(new CustomEvent('modal:hide', {
            detail: { modal: this }
        }));
    }

    /**
     * Toggle modal visibility
     */
    toggle() {
        if (this.isVisible) {
            this.hide();
        } else {
            this.show();
        }
    }

    /**
     * Confirm action
     */
    confirm() {
        if (this.options.onConfirm) {
            const result = this.options.onConfirm(this);
            if (result !== false) {
                this.hide();
            }
        } else {
            this.hide();
        }
    }

    /**
     * Cancel action
     */
    cancel() {
        if (this.options.onCancel) {
            const result = this.options.onCancel(this);
            if (result !== false) {
                this.hide();
            }
        } else {
            this.hide();
        }
    }

    /**
     * Update modal content
     */
    setContent(content) {
        const body = this.element.querySelector('.modal-body');
        if (body) {
            if (typeof content === 'string') {
                body.innerHTML = content;
            } else if (content instanceof Element) {
                body.innerHTML = '';
                body.appendChild(content);
            }
        }
    }

    /**
     * Update modal title
     */
    setTitle(title) {
        const titleElement = this.element.querySelector('.modal-title');
        if (titleElement) {
            titleElement.textContent = title;
        }
    }

    /**
     * Destroy modal
     */
    destroy() {
        this.hide();
        
        if (this.backdrop && this.backdrop.parentNode) {
            this.backdrop.parentNode.removeChild(this.backdrop);
        }
        
        this.element = null;
        this.backdrop = null;
    }

    /**
     * Static method to create and show modal
     */
    static show(options) {
        const modal = new ModalComponent(options);
        modal.show();
        return modal;
    }

    /**
     * Static method to create confirmation modal
     */
    static confirm(options) {
        const defaultOptions = {
            title: 'Confirm Action',
            content: 'Are you sure you want to proceed?',
            size: 'small',
            onConfirm: () => true,
            onCancel: () => true
        };

        return ModalComponent.show({ ...defaultOptions, ...options });
    }

    /**
     * Static method to create alert modal
     */
    static alert(message, title = 'Alert') {
        return ModalComponent.show({
            title,
            content: message,
            size: 'small',
            onConfirm: () => true
        });
    }

    /**
     * Static method to create prompt modal
     */
    static prompt(message, defaultValue = '', title = 'Input Required') {
        return new Promise((resolve) => {
            const input = DOMUtils.createElement('input', {
                type: 'text',
                className: 'glass-input w-full mt-md',
                value: defaultValue,
                placeholder: 'Enter value...'
            });

            const content = DOMUtils.createElement('div', {}, [
                DOMUtils.createElement('p', { className: 'mb-md' }, message),
                input
            ]);

            const modal = ModalComponent.show({
                title,
                content,
                size: 'small',
                onConfirm: () => {
                    resolve(input.value);
                    return true;
                },
                onCancel: () => {
                    resolve(null);
                    return true;
                }
            });

            // Focus input after modal is shown
            setTimeout(() => input.focus(), 100);
        });
    }
}

// Add modal size styles
const modalStyles = `
.modal-small { max-width: 400px; }
.modal-medium { max-width: 600px; }
.modal-large { max-width: 800px; }
.modal-fullscreen { 
    max-width: 95vw; 
    max-height: 95vh; 
    width: 95vw; 
    height: 95vh; 
}
`;

// Inject styles
if (typeof document !== 'undefined') {
    const styleSheet = document.createElement('style');
    styleSheet.textContent = modalStyles;
    document.head.appendChild(styleSheet);
}

// Export for module systems
if (typeof module !== 'undefined' && module.exports) {
    module.exports = ModalComponent;
}

// Global assignment for browser
if (typeof window !== 'undefined') {
    window.ModalComponent = ModalComponent;
}
