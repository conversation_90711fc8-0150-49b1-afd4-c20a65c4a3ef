import React, { useEffect, useRef } from 'react';
import { motion } from 'framer-motion';
import { cn } from '../utils/cn';

export interface PriceData {
  timestamp: number;
  price: number;
  volume?: number;
}

export interface PriceChartProps {
  data: PriceData[];
  symbol: string;
  className?: string;
  height?: number;
  showVolume?: boolean;
  animate?: boolean;
  theme?: 'luxury' | 'default';
}

const PriceChart: React.FC<PriceChartProps> = ({
  data,
  symbol,
  className,
  height = 300,
  showVolume = false,
  animate = true,
  theme = 'luxury'
}) => {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (!canvasRef.current || !data.length) return;

    const canvas = canvasRef.current;
    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    // Set canvas size
    const rect = canvas.getBoundingClientRect();
    canvas.width = rect.width * window.devicePixelRatio;
    canvas.height = rect.height * window.devicePixelRatio;
    ctx.scale(window.devicePixelRatio, window.devicePixelRatio);

    // Clear canvas
    ctx.clearRect(0, 0, rect.width, rect.height);

    // Calculate price range
    const prices = data.map(d => d.price);
    const minPrice = Math.min(...prices);
    const maxPrice = Math.max(...prices);
    const priceRange = maxPrice - minPrice;

    // Chart dimensions
    const padding = 40;
    const chartWidth = rect.width - padding * 2;
    const chartHeight = rect.height - padding * 2;

    // Draw grid
    ctx.strokeStyle = theme === 'luxury' ? 'rgba(251, 191, 36, 0.1)' : 'rgba(156, 163, 175, 0.2)';
    ctx.lineWidth = 1;

    // Horizontal grid lines
    for (let i = 0; i <= 5; i++) {
      const y = padding + (chartHeight / 5) * i;
      ctx.beginPath();
      ctx.moveTo(padding, y);
      ctx.lineTo(rect.width - padding, y);
      ctx.stroke();
    }

    // Vertical grid lines
    for (let i = 0; i <= 10; i++) {
      const x = padding + (chartWidth / 10) * i;
      ctx.beginPath();
      ctx.moveTo(x, padding);
      ctx.lineTo(x, rect.height - padding);
      ctx.stroke();
    }

    // Draw price line
    ctx.strokeStyle = theme === 'luxury' ? '#fbbf24' : '#3b82f6';
    ctx.lineWidth = 2;
    ctx.beginPath();

    data.forEach((point, index) => {
      const x = padding + (chartWidth / (data.length - 1)) * index;
      const y = padding + chartHeight - ((point.price - minPrice) / priceRange) * chartHeight;

      if (index === 0) {
        ctx.moveTo(x, y);
      } else {
        ctx.lineTo(x, y);
      }
    });

    ctx.stroke();

    // Draw gradient fill
    const gradient = ctx.createLinearGradient(0, padding, 0, rect.height - padding);
    gradient.addColorStop(0, theme === 'luxury' ? 'rgba(251, 191, 36, 0.3)' : 'rgba(59, 130, 246, 0.3)');
    gradient.addColorStop(1, 'rgba(0, 0, 0, 0)');

    ctx.fillStyle = gradient;
    ctx.beginPath();
    ctx.moveTo(padding, rect.height - padding);

    data.forEach((point, index) => {
      const x = padding + (chartWidth / (data.length - 1)) * index;
      const y = padding + chartHeight - ((point.price - minPrice) / priceRange) * chartHeight;
      ctx.lineTo(x, y);
    });

    ctx.lineTo(rect.width - padding, rect.height - padding);
    ctx.closePath();
    ctx.fill();

    // Draw price labels
    ctx.fillStyle = theme === 'luxury' ? '#fbbf24' : '#9ca3af';
    ctx.font = '12px Inter, sans-serif';
    ctx.textAlign = 'right';

    for (let i = 0; i <= 5; i++) {
      const price = minPrice + (priceRange / 5) * (5 - i);
      const y = padding + (chartHeight / 5) * i + 4;
      ctx.fillText(`$${price.toFixed(2)}`, padding - 10, y);
    }

    // Draw current price indicator
    if (data.length > 0) {
      const lastPrice = data[data.length - 1].price;
      const lastY = padding + chartHeight - ((lastPrice - minPrice) / priceRange) * chartHeight;

      // Price line
      ctx.strokeStyle = theme === 'luxury' ? '#fbbf24' : '#3b82f6';
      ctx.setLineDash([5, 5]);
      ctx.beginPath();
      ctx.moveTo(padding, lastY);
      ctx.lineTo(rect.width - padding, lastY);
      ctx.stroke();
      ctx.setLineDash([]);

      // Price label
      ctx.fillStyle = theme === 'luxury' ? '#000' : '#fff';
      ctx.fillRect(rect.width - padding - 60, lastY - 10, 55, 20);
      ctx.fillStyle = theme === 'luxury' ? '#fbbf24' : '#3b82f6';
      ctx.textAlign = 'center';
      ctx.fillText(`$${lastPrice.toFixed(2)}`, rect.width - padding - 32, lastY + 4);
    }

  }, [data, theme]);

  const currentPrice = data.length > 0 ? data[data.length - 1].price : 0;
  const previousPrice = data.length > 1 ? data[data.length - 2].price : currentPrice;
  const priceChange = currentPrice - previousPrice;
  const priceChangePercent = previousPrice > 0 ? (priceChange / previousPrice) * 100 : 0;

  const ChartContainer = animate ? motion.div : 'div';
  
  const animationProps = animate ? {
    initial: { opacity: 0, scale: 0.95 },
    animate: { opacity: 1, scale: 1 },
    transition: { duration: 0.5 }
  } : {};

  return (
    <ChartContainer
      ref={containerRef}
      className={cn(
        'relative rounded-xl border p-4',
        theme === 'luxury' 
          ? 'bg-gradient-to-br from-gray-900 to-black border-yellow-400/20' 
          : 'bg-gray-900 border-gray-800',
        className
      )}
      {...animationProps}
    >
      {/* Header */}
      <div className="flex items-center justify-between mb-4">
        <div>
          <h3 className="text-lg font-semibold text-white">{symbol}</h3>
          <div className="flex items-center space-x-2">
            <span className="text-2xl font-bold text-white">
              ${currentPrice.toFixed(2)}
            </span>
            <span
              className={cn(
                'text-sm font-medium',
                priceChange >= 0 ? 'text-green-400' : 'text-red-400'
              )}
            >
              {priceChange >= 0 ? '+' : ''}
              {priceChangePercent.toFixed(2)}%
            </span>
          </div>
        </div>
        
        <div className="text-right">
          <div className="text-sm text-gray-400">24h Change</div>
          <div
            className={cn(
              'text-lg font-semibold',
              priceChange >= 0 ? 'text-green-400' : 'text-red-400'
            )}
          >
            {priceChange >= 0 ? '+' : ''}${Math.abs(priceChange).toFixed(2)}
          </div>
        </div>
      </div>

      {/* Chart */}
      <div className="relative" style={{ height }}>
        <canvas
          ref={canvasRef}
          className="w-full h-full"
          style={{ width: '100%', height: '100%' }}
        />
      </div>

      {/* Volume Chart (if enabled) */}
      {showVolume && (
        <div className="mt-4 h-16 bg-gray-800/50 rounded-lg p-2">
          <div className="text-xs text-gray-400 mb-1">Volume</div>
          <div className="flex items-end space-x-1 h-8">
            {data.slice(-20).map((point, index) => (
              <div
                key={index}
                className={cn(
                  'flex-1 rounded-sm',
                  theme === 'luxury' ? 'bg-yellow-400/30' : 'bg-blue-400/30'
                )}
                style={{
                  height: `${((point.volume || 0) / Math.max(...data.map(d => d.volume || 0))) * 100}%`
                }}
              />
            ))}
          </div>
        </div>
      )}
    </ChartContainer>
  );
};

export { PriceChart };
