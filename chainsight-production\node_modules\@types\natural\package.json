{"name": "@types/natural", "version": "5.1.5", "description": "TypeScript definitions for natural", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/natural", "license": "MIT", "contributors": [{"name": "<PERSON> <PERSON><PERSON> <PERSON>", "githubUsername": "dmoonfire", "url": "https://github.com/dmoonfire"}, {"name": "<PERSON>", "githubUsername": "forivall", "url": "https://github.com/forivall"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/natural"}, "scripts": {}, "dependencies": {"@types/node": "*"}, "typesPublisherContentHash": "09bd3fa5794f54f8fb3c04db4b64dc22373384e833bb418b27d27302bab43bf3", "typeScriptVersion": "4.5"}