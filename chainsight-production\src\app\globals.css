@import "tailwindcss";

/* Vibrant Demo Color Scheme */
:root {
  /* Primary Gradient: Purple to Blue */
  --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);

  /* Secondary Gradient: Pink to Red */
  --secondary-gradient: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);

  /* Accent Gradient: <PERSON><PERSON> to Blue */
  --accent-gradient: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);

  /* Success Gradient: Green to Cyan */
  --success-gradient: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);

  /* Warning Gradient: Pink to Yellow */
  --warning-gradient: linear-gradient(135deg, #fa709a 0%, #fee140 100%);

  /* Neon Colors */
  --neon-blue: #00d4ff;
  --neon-purple: #b537f2;
  --neon-pink: #ff006e;
  --neon-green: #39ff14;
  --neon-orange: #ff8c00;

  /* Cosmic Background */
  --cosmic-dark: #0c0c0c;
  --cosmic-mid: #1a1a2e;
  --cosmic-light: #16213e;

  /* Glass Effect */
  --glass-bg: rgba(255, 255, 255, 0.1);
  --glass-border: rgba(255, 255, 255, 0.2);
}

body {
  background: linear-gradient(135deg, var(--cosmic-dark) 0%, var(--cosmic-mid) 50%, var(--cosmic-light) 100%);
  color: #ffffff;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
  overflow-x: hidden;
  min-height: 100vh;
}

/* Glass morphism effect */
.glass {
  background: var(--glass-bg);
  backdrop-filter: blur(20px);
  border: 1px solid var(--glass-border);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
}

/* Neon glow effects */
.neon-glow-blue {
  text-shadow: 0 0 10px var(--neon-blue), 0 0 20px var(--neon-blue), 0 0 30px var(--neon-blue);
}

.neon-glow-purple {
  text-shadow: 0 0 10px var(--neon-purple), 0 0 20px var(--neon-purple), 0 0 30px var(--neon-purple);
}

.neon-glow-pink {
  text-shadow: 0 0 10px var(--neon-pink), 0 0 20px var(--neon-pink), 0 0 30px var(--neon-pink);
}

/* Rainbow pulse animation */
@keyframes rainbow-pulse {
  0% { color: var(--neon-blue); transform: scale(1); }
  25% { color: var(--neon-purple); transform: scale(1.05); }
  50% { color: var(--neon-pink); transform: scale(1.1); }
  75% { color: var(--neon-green); transform: scale(1.05); }
  100% { color: var(--neon-blue); transform: scale(1); }
}

.rainbow-pulse {
  animation: rainbow-pulse 3s ease-in-out infinite;
}

/* Floating animation */
@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-10px); }
}

.float {
  animation: float 3s ease-in-out infinite;
}

/* Gold gradient */
.gold-gradient {
  background: linear-gradient(135deg, #f59e0b, #d97706, #b45309);
}

/* Scrollbar styling */
::-webkit-scrollbar {
  width: 6px;
}

::-webkit-scrollbar-track {
  background: rgba(15, 23, 42, 0.5);
}

::-webkit-scrollbar-thumb {
  background: rgba(217, 119, 6, 0.5);
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: rgba(217, 119, 6, 0.7);
}
