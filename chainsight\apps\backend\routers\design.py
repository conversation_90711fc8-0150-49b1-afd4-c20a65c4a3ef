from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session
from pydantic import BaseModel
from typing import Dict, Any, List
import json
import uuid
from datetime import datetime

from db import get_db, DesignAsset, User
from routers.auth import get_current_user

router = APIRouter()

# Pydantic models
class AnimationRequest(BaseModel):
    animation_type: str  # 'lottie', 'svg', 'css'
    style: str  # 'minimal', 'luxury', 'modern', 'classic'
    color_scheme: str  # 'gold', 'blue', 'green', 'purple'
    duration: float = 2.0
    size: str = "medium"  # 'small', 'medium', 'large'

class AnimationResponse(BaseModel):
    id: int
    asset_name: str
    asset_type: str
    asset_data: Dict[str, Any]
    file_url: str
    preview_url: str

class DesignAssetResponse(BaseModel):
    id: int
    asset_name: str
    asset_type: str
    created_at: datetime

@router.post("/create-animation", response_model=AnimationResponse)
async def create_animation(
    request: AnimationRequest,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Create a new animation asset"""
    try:
        # Generate animation based on type
        if request.animation_type == "lottie":
            asset_data = generate_lottie_animation(request)
        elif request.animation_type == "svg":
            asset_data = generate_svg_animation(request)
        elif request.animation_type == "css":
            asset_data = generate_css_animation(request)
        else:
            raise HTTPException(status_code=400, detail="Invalid animation type")
        
        # Generate asset name
        asset_name = f"{request.style}_{request.animation_type}_{uuid.uuid4().hex[:8]}"
        
        # Save to database
        design_asset = DesignAsset(
            user_id=current_user.id,
            asset_name=asset_name,
            asset_type=request.animation_type,
            asset_data=asset_data,
            file_url=f"/api/design/assets/{asset_name}.{request.animation_type}"
        )
        db.add(design_asset)
        db.commit()
        db.refresh(design_asset)
        
        return AnimationResponse(
            id=design_asset.id,
            asset_name=asset_name,
            asset_type=request.animation_type,
            asset_data=asset_data,
            file_url=design_asset.file_url,
            preview_url=f"/api/design/preview/{design_asset.id}"
        )
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Animation creation failed: {str(e)}")

def generate_lottie_animation(request: AnimationRequest) -> Dict[str, Any]:
    """Generate Lottie animation JSON"""
    
    # Color mapping
    colors = {
        "gold": {"primary": "#FFD700", "secondary": "#FFA500"},
        "blue": {"primary": "#007BFF", "secondary": "#0056B3"},
        "green": {"primary": "#28A745", "secondary": "#1E7E34"},
        "purple": {"primary": "#6F42C1", "secondary": "#5A2D91"}
    }
    
    color_scheme = colors.get(request.color_scheme, colors["gold"])
    
    # Size mapping
    sizes = {"small": 100, "medium": 200, "large": 400}
    size = sizes.get(request.size, 200)
    
    # Generate Lottie JSON structure
    lottie_data = {
        "v": "5.7.4",
        "fr": 30,
        "ip": 0,
        "op": int(request.duration * 30),  # Convert to frames
        "w": size,
        "h": size,
        "nm": f"{request.style}_animation",
        "ddd": 0,
        "assets": [],
        "layers": [
            {
                "ddd": 0,
                "ind": 1,
                "ty": 4,
                "nm": "Shape Layer",
                "sr": 1,
                "ks": {
                    "o": {"a": 0, "k": 100},
                    "r": {
                        "a": 1,
                        "k": [
                            {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 0, "s": [0]},
                            {"t": int(request.duration * 30), "s": [360]}
                        ]
                    },
                    "p": {"a": 0, "k": [size/2, size/2, 0]},
                    "a": {"a": 0, "k": [0, 0, 0]},
                    "s": {"a": 0, "k": [100, 100, 100]}
                },
                "ao": 0,
                "shapes": [
                    {
                        "ty": "el",
                        "p": {"a": 0, "k": [0, 0]},
                        "s": {"a": 0, "k": [50, 50]}
                    },
                    {
                        "ty": "fl",
                        "c": {"a": 0, "k": [
                            int(color_scheme["primary"][1:3], 16)/255,
                            int(color_scheme["primary"][3:5], 16)/255,
                            int(color_scheme["primary"][5:7], 16)/255,
                            1
                        ]},
                        "o": {"a": 0, "k": 100}
                    }
                ],
                "ip": 0,
                "op": int(request.duration * 30),
                "st": 0,
                "bm": 0
            }
        ]
    }
    
    return lottie_data

def generate_svg_animation(request: AnimationRequest) -> Dict[str, Any]:
    """Generate SVG animation"""
    
    colors = {
        "gold": "#FFD700",
        "blue": "#007BFF", 
        "green": "#28A745",
        "purple": "#6F42C1"
    }
    
    color = colors.get(request.color_scheme, "#FFD700")
    sizes = {"small": 100, "medium": 200, "large": 400}
    size = sizes.get(request.size, 200)
    
    if request.style == "luxury":
        svg_content = f'''
        <svg width="{size}" height="{size}" viewBox="0 0 {size} {size}" xmlns="http://www.w3.org/2000/svg">
            <defs>
                <radialGradient id="luxuryGrad" cx="50%" cy="50%" r="50%">
                    <stop offset="0%" style="stop-color:{color};stop-opacity:1" />
                    <stop offset="100%" style="stop-color:#000;stop-opacity:0.8" />
                </radialGradient>
            </defs>
            <circle cx="{size//2}" cy="{size//2}" r="{size//4}" fill="url(#luxuryGrad)">
                <animateTransform attributeName="transform" type="rotate" 
                    values="0 {size//2} {size//2};360 {size//2} {size//2}" 
                    dur="{request.duration}s" repeatCount="indefinite"/>
            </circle>
            <circle cx="{size//2}" cy="{size//2}" r="{size//6}" fill="{color}" opacity="0.7">
                <animate attributeName="r" values="{size//6};{size//3};{size//6}" 
                    dur="{request.duration}s" repeatCount="indefinite"/>
            </circle>
        </svg>
        '''
    else:
        svg_content = f'''
        <svg width="{size}" height="{size}" viewBox="0 0 {size} {size}" xmlns="http://www.w3.org/2000/svg">
            <rect x="{size//4}" y="{size//4}" width="{size//2}" height="{size//2}" fill="{color}">
                <animateTransform attributeName="transform" type="rotate" 
                    values="0 {size//2} {size//2};360 {size//2} {size//2}" 
                    dur="{request.duration}s" repeatCount="indefinite"/>
            </rect>
        </svg>
        '''
    
    return {
        "svg_content": svg_content,
        "width": size,
        "height": size,
        "duration": request.duration
    }

def generate_css_animation(request: AnimationRequest) -> Dict[str, Any]:
    """Generate CSS animation"""
    
    colors = {
        "gold": "#FFD700",
        "blue": "#007BFF",
        "green": "#28A745", 
        "purple": "#6F42C1"
    }
    
    color = colors.get(request.color_scheme, "#FFD700")
    sizes = {"small": "50px", "medium": "100px", "large": "200px"}
    size = sizes.get(request.size, "100px")
    
    css_content = f'''
    @keyframes {request.style}Animation {{
        0% {{ transform: rotate(0deg) scale(1); }}
        50% {{ transform: rotate(180deg) scale(1.2); }}
        100% {{ transform: rotate(360deg) scale(1); }}
    }}
    
    .{request.style}-element {{
        width: {size};
        height: {size};
        background: linear-gradient(45deg, {color}, #000);
        border-radius: 50%;
        animation: {request.style}Animation {request.duration}s infinite ease-in-out;
        box-shadow: 0 0 20px {color};
    }}
    '''
    
    html_content = f'''
    <div class="{request.style}-element"></div>
    '''
    
    return {
        "css_content": css_content,
        "html_content": html_content,
        "duration": request.duration
    }

@router.get("/assets", response_model=List[DesignAssetResponse])
async def get_user_assets(
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Get all design assets for the current user"""
    assets = db.query(DesignAsset).filter(DesignAsset.user_id == current_user.id).all()
    
    return [
        DesignAssetResponse(
            id=asset.id,
            asset_name=asset.asset_name,
            asset_type=asset.asset_type,
            created_at=asset.created_at
        )
        for asset in assets
    ]

@router.get("/assets/{asset_id}")
async def get_asset(
    asset_id: int,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Get a specific design asset"""
    asset = db.query(DesignAsset).filter(
        DesignAsset.id == asset_id,
        DesignAsset.user_id == current_user.id
    ).first()
    
    if not asset:
        raise HTTPException(status_code=404, detail="Asset not found")
    
    return {
        "id": asset.id,
        "asset_name": asset.asset_name,
        "asset_type": asset.asset_type,
        "asset_data": asset.asset_data,
        "file_url": asset.file_url,
        "created_at": asset.created_at
    }

@router.delete("/assets/{asset_id}")
async def delete_asset(
    asset_id: int,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Delete a design asset"""
    asset = db.query(DesignAsset).filter(
        DesignAsset.id == asset_id,
        DesignAsset.user_id == current_user.id
    ).first()
    
    if not asset:
        raise HTTPException(status_code=404, detail="Asset not found")
    
    db.delete(asset)
    db.commit()
    
    return {"message": "Asset deleted successfully"}

@router.get("/preview/{asset_id}")
async def preview_asset(
    asset_id: int,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Get asset preview"""
    asset = db.query(DesignAsset).filter(
        DesignAsset.id == asset_id,
        DesignAsset.user_id == current_user.id
    ).first()
    
    if not asset:
        raise HTTPException(status_code=404, detail="Asset not found")
    
    if asset.asset_type == "svg":
        return {"content": asset.asset_data["svg_content"], "type": "svg"}
    elif asset.asset_type == "css":
        return {
            "css": asset.asset_data["css_content"],
            "html": asset.asset_data["html_content"],
            "type": "css"
        }
    elif asset.asset_type == "lottie":
        return {"data": asset.asset_data, "type": "lottie"}
    
    return {"message": "Preview not available for this asset type"}

@router.post("/export/{asset_id}")
async def export_asset(
    asset_id: int,
    format: str = "json",  # json, svg, css
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Export asset in specified format"""
    asset = db.query(DesignAsset).filter(
        DesignAsset.id == asset_id,
        DesignAsset.user_id == current_user.id
    ).first()
    
    if not asset:
        raise HTTPException(status_code=404, detail="Asset not found")
    
    if format == "json":
        return {"data": asset.asset_data, "filename": f"{asset.asset_name}.json"}
    elif format == "svg" and asset.asset_type == "svg":
        return {"data": asset.asset_data["svg_content"], "filename": f"{asset.asset_name}.svg"}
    elif format == "css" and asset.asset_type == "css":
        return {
            "css": asset.asset_data["css_content"],
            "html": asset.asset_data["html_content"],
            "filename": f"{asset.asset_name}.css"
        }
    else:
        raise HTTPException(status_code=400, detail="Invalid export format for this asset type")
