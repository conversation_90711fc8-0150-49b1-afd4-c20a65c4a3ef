version: '3.8'

services:
  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    container_name: chainsight-postgres
    environment:
      POSTGRES_DB: chainsight
      POSTGRES_USER: chainsight_user
      POSTGRES_PASSWORD: chainsight_password
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./apps/backend/init.sql:/docker-entrypoint-initdb.d/init.sql
    ports:
      - "5432:5432"
    networks:
      - chainsight-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U chainsight_user -d chainsight"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Redis Cache
  redis:
    image: redis:7-alpine
    container_name: chainsight-redis
    command: redis-server --appendonly yes --requirepass chainsight_redis_password
    volumes:
      - redis_data:/data
    ports:
      - "6379:6379"
    networks:
      - chainsight-network
    healthcheck:
      test: ["CMD", "redis-cli", "--raw", "incr", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Backend API
  backend:
    build:
      context: .
      dockerfile: Dockerfile.backend
    container_name: chainsight-backend
    environment:
      - DATABASE_URL=**************************************************************/chainsight
      - REDIS_URL=redis://:chainsight_redis_password@redis:6379/0
      - SECRET_KEY=your-super-secret-key-change-in-production
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - ENVIRONMENT=development
      - CORS_ORIGINS=http://localhost:3000,http://frontend:3000
    ports:
      - "8000:8000"
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    networks:
      - chainsight-network
    volumes:
      - ./apps/backend:/app/apps/backend
      - ./libs:/app/libs
      - ./plugins:/app/plugins
    restart: unless-stopped

  # Frontend Application
  frontend:
    build:
      context: .
      dockerfile: Dockerfile.frontend
    container_name: chainsight-frontend
    environment:
      - NEXT_PUBLIC_API_URL=http://localhost:8000
      - NEXT_PUBLIC_WS_URL=ws://localhost:8000
      - NODE_ENV=development
    ports:
      - "3000:3000"
    depends_on:
      - backend
    networks:
      - chainsight-network
    volumes:
      - ./apps/frontend:/app/apps/frontend
      - ./libs:/app/libs
      - ./plugins:/app/plugins
      - /app/node_modules
      - /app/apps/frontend/node_modules
    restart: unless-stopped

  # Nginx Reverse Proxy
  nginx:
    image: nginx:alpine
    container_name: chainsight-nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
      - ./ssl:/etc/nginx/ssl
    depends_on:
      - frontend
      - backend
    networks:
      - chainsight-network
    restart: unless-stopped

volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local

networks:
  chainsight-network:
    driver: bridge
