'use client';

import { motion } from 'framer-motion';
import { Palette, Image, Layers, Sparkles } from 'lucide-react';

export function DesignModule() {
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="space-y-6"
    >
      <div className="bg-white/10 backdrop-blur-xl rounded-2xl p-6 border border-white/20">
        <div className="flex items-center space-x-3 mb-6">
          <div className="w-12 h-12 bg-gradient-to-r from-pink-400 to-purple-500 rounded-xl flex items-center justify-center">
            <Palette className="w-6 h-6 text-white" />
          </div>
          <div>
            <h2 className="text-2xl font-bold text-white">Design Studio</h2>
            <p className="text-gray-400">AI-powered creative design tools</p>
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          <div className="bg-black/20 rounded-xl p-4">
            <div className="flex items-center space-x-3 mb-3">
              <Image className="w-5 h-5 text-pink-400" />
              <span className="text-white font-medium">Projects</span>
            </div>
            <p className="text-2xl font-bold text-pink-400">12</p>
            <p className="text-sm text-gray-400">Active designs</p>
          </div>

          <div className="bg-black/20 rounded-xl p-4">
            <div className="flex items-center space-x-3 mb-3">
              <Layers className="w-5 h-5 text-purple-400" />
              <span className="text-white font-medium">Templates</span>
            </div>
            <p className="text-2xl font-bold text-purple-400">48</p>
            <p className="text-sm text-gray-400">Available templates</p>
          </div>

          <div className="bg-black/20 rounded-xl p-4">
            <div className="flex items-center space-x-3 mb-3">
              <Sparkles className="w-5 h-5 text-yellow-400" />
              <span className="text-white font-medium">AI Generated</span>
            </div>
            <p className="text-2xl font-bold text-yellow-400">156</p>
            <p className="text-sm text-gray-400">This month</p>
          </div>
        </div>

        <div className="mt-6 p-4 bg-gradient-to-r from-pink-500/20 to-purple-500/20 rounded-xl border border-pink-500/30">
          <h3 className="text-white font-semibold mb-2">Design AI Suggestion</h3>
          <p className="text-gray-300 text-sm">
            Trending design patterns suggest incorporating more minimalist elements with 
            bold color accents. Consider using glass morphism effects for modern appeal.
          </p>
        </div>
      </div>
    </motion.div>
  );
}
