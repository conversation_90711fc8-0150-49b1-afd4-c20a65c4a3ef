{"name": "@chainsight/animation-designer", "version": "1.0.0", "description": "Animation design plugin for creating Lottie, SVG, and CSS animations", "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"build": "tsc", "dev": "tsc --watch", "test": "jest", "lint": "eslint src/**/*.ts", "clean": "rm -rf dist"}, "dependencies": {"lottie-web": "^5.12.2", "gsap": "^3.12.2", "three": "^0.155.0", "canvas": "^2.11.2", "svg.js": "^3.2.0", "animejs": "^3.2.1"}, "devDependencies": {"@types/node": "^20.5.0", "@types/three": "^0.155.0", "typescript": "^5.1.6", "jest": "^29.6.2", "@types/jest": "^29.5.4", "eslint": "^8.47.0", "@typescript-eslint/eslint-plugin": "^6.4.0", "@typescript-eslint/parser": "^6.4.0"}, "keywords": ["animation", "lottie", "svg", "css", "design", "chainsight"], "author": "Connectouch", "license": "MIT"}