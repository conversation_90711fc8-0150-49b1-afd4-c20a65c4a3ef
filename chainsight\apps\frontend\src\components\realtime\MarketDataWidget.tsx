'use client';

import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { TrendingUp, TrendingDown, Activity, DollarSign } from 'lucide-react';
import { useMarketDataStream } from '@/hooks/useWebSocket';
import { Card, CardHeader, CardTitle, CardContent } from '@chainsight/ui-lib';

interface MarketDataItem {
  symbol: string;
  price: number;
  change24h: number;
  changePercent24h: number;
  volume24h: number;
  marketCap: number;
  lastUpdate: number;
}

interface MarketDataWidgetProps {
  symbols?: string[];
  refreshInterval?: number;
  showVolume?: boolean;
  showMarketCap?: boolean;
  compact?: boolean;
}

const MarketDataWidget: React.FC<MarketDataWidgetProps> = ({
  symbols = ['BTC', 'ETH', 'USDT', 'BNB', 'ADA'],
  refreshInterval = 5000,
  showVolume = true,
  showMarketCap = true,
  compact = false
}) => {
  const { connected, marketData, emit } = useMarketDataStream();
  const [selectedSymbol, setSelectedSymbol] = useState(symbols[0]);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    if (connected) {
      // Subscribe to market data for specified symbols
      emit('subscribe_market_data', { symbols });
      
      // Set up refresh interval
      const interval = setInterval(() => {
        emit('refresh_market_data', { symbols });
      }, refreshInterval);

      setIsLoading(false);

      return () => clearInterval(interval);
    }
  }, [connected, symbols, refreshInterval, emit]);

  const formatPrice = (price: number): string => {
    if (price >= 1000) {
      return `$${price.toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 })}`;
    }
    return `$${price.toFixed(6)}`;
  };

  const formatVolume = (volume: number): string => {
    if (volume >= 1e9) {
      return `$${(volume / 1e9).toFixed(2)}B`;
    }
    if (volume >= 1e6) {
      return `$${(volume / 1e6).toFixed(2)}M`;
    }
    if (volume >= 1e3) {
      return `$${(volume / 1e3).toFixed(2)}K`;
    }
    return `$${volume.toFixed(2)}`;
  };

  const formatMarketCap = (marketCap: number): string => {
    if (marketCap >= 1e12) {
      return `$${(marketCap / 1e12).toFixed(2)}T`;
    }
    if (marketCap >= 1e9) {
      return `$${(marketCap / 1e9).toFixed(2)}B`;
    }
    if (marketCap >= 1e6) {
      return `$${(marketCap / 1e6).toFixed(2)}M`;
    }
    return `$${marketCap.toLocaleString()}`;
  };

  const getChangeColor = (change: number): string => {
    return change >= 0 ? 'text-green-400' : 'text-red-400';
  };

  const getChangeIcon = (change: number) => {
    return change >= 0 ? <TrendingUp className="w-4 h-4" /> : <TrendingDown className="w-4 h-4" />;
  };

  if (!connected) {
    return (
      <Card variant="luxury" className="w-full">
        <CardContent className="flex items-center justify-center py-8">
          <div className="flex items-center space-x-2 text-yellow-400">
            <Activity className="w-5 h-5 animate-pulse" />
            <span>Connecting to market data...</span>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (compact) {
    return (
      <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-4">
        {symbols.map((symbol) => {
          const data = marketData[symbol];
          if (!data) return null;

          return (
            <motion.div
              key={symbol}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              className="bg-gradient-to-br from-gray-900 to-black border border-yellow-400/20 rounded-lg p-4"
            >
              <div className="flex items-center justify-between mb-2">
                <span className="text-sm font-medium text-gray-300">{symbol}</span>
                <div className={`flex items-center space-x-1 ${getChangeColor(data.changePercent24h)}`}>
                  {getChangeIcon(data.changePercent24h)}
                  <span className="text-xs">{data.changePercent24h.toFixed(2)}%</span>
                </div>
              </div>
              <div className="text-lg font-bold text-white">
                {formatPrice(data.price)}
              </div>
            </motion.div>
          );
        })}
      </div>
    );
  }

  return (
    <Card variant="luxury" className="w-full">
      <CardHeader>
        <CardTitle className="flex items-center space-x-2">
          <DollarSign className="w-5 h-5 text-yellow-400" />
          <span>Live Market Data</span>
          {connected && (
            <div className="flex items-center space-x-1 text-green-400">
              <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse" />
              <span className="text-xs">Live</span>
            </div>
          )}
        </CardTitle>
      </CardHeader>

      <CardContent>
        {/* Symbol Selector */}
        <div className="flex flex-wrap gap-2 mb-6">
          {symbols.map((symbol) => (
            <button
              key={symbol}
              onClick={() => setSelectedSymbol(symbol)}
              className={`px-3 py-1 rounded-lg text-sm font-medium transition-all duration-200 ${
                selectedSymbol === symbol
                  ? 'bg-yellow-400 text-black'
                  : 'bg-gray-800 text-gray-300 hover:bg-gray-700'
              }`}
            >
              {symbol}
            </button>
          ))}
        </div>

        {/* Selected Symbol Details */}
        <AnimatePresence mode="wait">
          {marketData[selectedSymbol] && (
            <motion.div
              key={selectedSymbol}
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              exit={{ opacity: 0, x: -20 }}
              transition={{ duration: 0.3 }}
              className="space-y-4"
            >
              {/* Price and Change */}
              <div className="flex items-center justify-between">
                <div>
                  <div className="text-3xl font-bold text-white">
                    {formatPrice(marketData[selectedSymbol].price)}
                  </div>
                  <div className="text-sm text-gray-400">
                    Last updated: {new Date(marketData[selectedSymbol].lastUpdate).toLocaleTimeString()}
                  </div>
                </div>
                <div className={`text-right ${getChangeColor(marketData[selectedSymbol].changePercent24h)}`}>
                  <div className="flex items-center space-x-2 text-lg font-semibold">
                    {getChangeIcon(marketData[selectedSymbol].changePercent24h)}
                    <span>{marketData[selectedSymbol].changePercent24h.toFixed(2)}%</span>
                  </div>
                  <div className="text-sm">
                    {marketData[selectedSymbol].change24h >= 0 ? '+' : ''}
                    {formatPrice(Math.abs(marketData[selectedSymbol].change24h))}
                  </div>
                </div>
              </div>

              {/* Additional Metrics */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {showVolume && (
                  <div className="bg-gray-800/50 rounded-lg p-4">
                    <div className="text-sm text-gray-400 mb-1">24h Volume</div>
                    <div className="text-xl font-semibold text-white">
                      {formatVolume(marketData[selectedSymbol].volume24h)}
                    </div>
                  </div>
                )}

                {showMarketCap && (
                  <div className="bg-gray-800/50 rounded-lg p-4">
                    <div className="text-sm text-gray-400 mb-1">Market Cap</div>
                    <div className="text-xl font-semibold text-white">
                      {formatMarketCap(marketData[selectedSymbol].marketCap)}
                    </div>
                  </div>
                )}
              </div>
            </motion.div>
          )}
        </AnimatePresence>

        {/* All Symbols Overview */}
        <div className="mt-6 space-y-2">
          <h4 className="text-sm font-medium text-gray-400 mb-3">All Symbols</h4>
          {symbols.map((symbol) => {
            const data = marketData[symbol];
            if (!data) return null;

            return (
              <motion.div
                key={symbol}
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                className={`flex items-center justify-between p-3 rounded-lg cursor-pointer transition-all duration-200 ${
                  selectedSymbol === symbol
                    ? 'bg-yellow-400/10 border border-yellow-400/30'
                    : 'bg-gray-800/30 hover:bg-gray-800/50'
                }`}
                onClick={() => setSelectedSymbol(symbol)}
              >
                <div className="flex items-center space-x-3">
                  <span className="font-medium text-white">{symbol}</span>
                  <span className="text-gray-400">{formatPrice(data.price)}</span>
                </div>
                <div className={`flex items-center space-x-2 ${getChangeColor(data.changePercent24h)}`}>
                  {getChangeIcon(data.changePercent24h)}
                  <span className="text-sm font-medium">
                    {data.changePercent24h.toFixed(2)}%
                  </span>
                </div>
              </motion.div>
            );
          })}
        </div>
      </CardContent>
    </Card>
  );
};

export default MarketDataWidget;
