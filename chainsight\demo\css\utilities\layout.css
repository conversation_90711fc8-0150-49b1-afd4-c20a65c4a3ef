/* ==================== LAYOUT UTILITIES ====================
   Flexbox, Grid, and positioning utility classes
   ====================================================== */

/* Display */
.block { display: block; }
.inline-block { display: inline-block; }
.inline { display: inline; }
.flex { display: flex; }
.inline-flex { display: inline-flex; }
.grid { display: grid; }
.inline-grid { display: inline-grid; }
.hidden { display: none; }

/* Flexbox Direction */
.flex-row { flex-direction: row; }
.flex-row-reverse { flex-direction: row-reverse; }
.flex-col { flex-direction: column; }
.flex-col-reverse { flex-direction: column-reverse; }

/* Flexbox Wrap */
.flex-wrap { flex-wrap: wrap; }
.flex-wrap-reverse { flex-wrap: wrap-reverse; }
.flex-nowrap { flex-wrap: nowrap; }

/* Flexbox Justify Content */
.justify-start { justify-content: flex-start; }
.justify-end { justify-content: flex-end; }
.justify-center { justify-content: center; }
.justify-between { justify-content: space-between; }
.justify-around { justify-content: space-around; }
.justify-evenly { justify-content: space-evenly; }

/* Flexbox Align Items */
.items-start { align-items: flex-start; }
.items-end { align-items: flex-end; }
.items-center { align-items: center; }
.items-baseline { align-items: baseline; }
.items-stretch { align-items: stretch; }

/* Flexbox Align Content */
.content-start { align-content: flex-start; }
.content-end { align-content: flex-end; }
.content-center { align-content: center; }
.content-between { align-content: space-between; }
.content-around { align-content: space-around; }
.content-evenly { align-content: space-evenly; }

/* Flexbox Align Self */
.self-auto { align-self: auto; }
.self-start { align-self: flex-start; }
.self-end { align-self: flex-end; }
.self-center { align-self: center; }
.self-stretch { align-self: stretch; }
.self-baseline { align-self: baseline; }

/* Flexbox Flex */
.flex-1 { flex: 1 1 0%; }
.flex-auto { flex: 1 1 auto; }
.flex-initial { flex: 0 1 auto; }
.flex-none { flex: none; }

/* Flexbox Grow */
.flex-grow-0 { flex-grow: 0; }
.flex-grow { flex-grow: 1; }

/* Flexbox Shrink */
.flex-shrink-0 { flex-shrink: 0; }
.flex-shrink { flex-shrink: 1; }

/* Grid Template Columns */
.grid-cols-1 { grid-template-columns: repeat(1, minmax(0, 1fr)); }
.grid-cols-2 { grid-template-columns: repeat(2, minmax(0, 1fr)); }
.grid-cols-3 { grid-template-columns: repeat(3, minmax(0, 1fr)); }
.grid-cols-4 { grid-template-columns: repeat(4, minmax(0, 1fr)); }
.grid-cols-5 { grid-template-columns: repeat(5, minmax(0, 1fr)); }
.grid-cols-6 { grid-template-columns: repeat(6, minmax(0, 1fr)); }
.grid-cols-12 { grid-template-columns: repeat(12, minmax(0, 1fr)); }
.grid-cols-none { grid-template-columns: none; }

/* Grid Template Rows */
.grid-rows-1 { grid-template-rows: repeat(1, minmax(0, 1fr)); }
.grid-rows-2 { grid-template-rows: repeat(2, minmax(0, 1fr)); }
.grid-rows-3 { grid-template-rows: repeat(3, minmax(0, 1fr)); }
.grid-rows-4 { grid-template-rows: repeat(4, minmax(0, 1fr)); }
.grid-rows-5 { grid-template-rows: repeat(5, minmax(0, 1fr)); }
.grid-rows-6 { grid-template-rows: repeat(6, minmax(0, 1fr)); }
.grid-rows-none { grid-template-rows: none; }

/* Grid Column Span */
.col-auto { grid-column: auto; }
.col-span-1 { grid-column: span 1 / span 1; }
.col-span-2 { grid-column: span 2 / span 2; }
.col-span-3 { grid-column: span 3 / span 3; }
.col-span-4 { grid-column: span 4 / span 4; }
.col-span-5 { grid-column: span 5 / span 5; }
.col-span-6 { grid-column: span 6 / span 6; }
.col-span-full { grid-column: 1 / -1; }

/* Grid Row Span */
.row-auto { grid-row: auto; }
.row-span-1 { grid-row: span 1 / span 1; }
.row-span-2 { grid-row: span 2 / span 2; }
.row-span-3 { grid-row: span 3 / span 3; }
.row-span-4 { grid-row: span 4 / span 4; }
.row-span-5 { grid-row: span 5 / span 5; }
.row-span-6 { grid-row: span 6 / span 6; }
.row-span-full { grid-row: 1 / -1; }

/* Position */
.static { position: static; }
.fixed { position: fixed; }
.absolute { position: absolute; }
.relative { position: relative; }
.sticky { position: sticky; }

/* Top, Right, Bottom, Left */
.inset-0 { top: 0; right: 0; bottom: 0; left: 0; }
.inset-auto { top: auto; right: auto; bottom: auto; left: auto; }
.inset-x-0 { left: 0; right: 0; }
.inset-y-0 { top: 0; bottom: 0; }

.top-0 { top: 0; }
.top-auto { top: auto; }
.right-0 { right: 0; }
.right-auto { right: auto; }
.bottom-0 { bottom: 0; }
.bottom-auto { bottom: auto; }
.left-0 { left: 0; }
.left-auto { left: auto; }

/* Z-Index */
.z-0 { z-index: 0; }
.z-10 { z-index: 10; }
.z-20 { z-index: 20; }
.z-30 { z-index: 30; }
.z-40 { z-index: 40; }
.z-50 { z-index: 50; }
.z-auto { z-index: auto; }
.z-dropdown { z-index: var(--z-dropdown); }
.z-modal { z-index: var(--z-modal); }
.z-tooltip { z-index: var(--z-tooltip); }

/* Float */
.float-right { float: right; }
.float-left { float: left; }
.float-none { float: none; }

/* Clear */
.clear-left { clear: left; }
.clear-right { clear: right; }
.clear-both { clear: both; }
.clear-none { clear: none; }

/* Overflow */
.overflow-auto { overflow: auto; }
.overflow-hidden { overflow: hidden; }
.overflow-visible { overflow: visible; }
.overflow-scroll { overflow: scroll; }
.overflow-x-auto { overflow-x: auto; }
.overflow-x-hidden { overflow-x: hidden; }
.overflow-x-visible { overflow-x: visible; }
.overflow-x-scroll { overflow-x: scroll; }
.overflow-y-auto { overflow-y: auto; }
.overflow-y-hidden { overflow-y: hidden; }
.overflow-y-visible { overflow-y: visible; }
.overflow-y-scroll { overflow-y: scroll; }

/* Object Fit */
.object-contain { object-fit: contain; }
.object-cover { object-fit: cover; }
.object-fill { object-fit: fill; }
.object-none { object-fit: none; }
.object-scale-down { object-fit: scale-down; }

/* Object Position */
.object-bottom { object-position: bottom; }
.object-center { object-position: center; }
.object-left { object-position: left; }
.object-left-bottom { object-position: left bottom; }
.object-left-top { object-position: left top; }
.object-right { object-position: right; }
.object-right-bottom { object-position: right bottom; }
.object-right-top { object-position: right top; }
.object-top { object-position: top; }

/* Visibility */
.visible { visibility: visible; }
.invisible { visibility: hidden; }

/* Container */
.container {
    width: 100%;
    margin-left: auto;
    margin-right: auto;
    padding-left: var(--space-md);
    padding-right: var(--space-md);
}

@media (min-width: 640px) {
    .container { max-width: 640px; }
}

@media (min-width: 768px) {
    .container { max-width: 768px; }
}

@media (min-width: 1024px) {
    .container { max-width: 1024px; }
}

@media (min-width: 1280px) {
    .container { max-width: 1280px; }
}

@media (min-width: 1536px) {
    .container { max-width: 1536px; }
}

/* Responsive Utilities */
@media (max-width: 639px) {
    .sm\:hidden { display: none; }
    .sm\:block { display: block; }
    .sm\:flex { display: flex; }
    .sm\:grid { display: grid; }
}

@media (min-width: 640px) {
    .sm\:hidden { display: none; }
    .sm\:block { display: block; }
    .sm\:flex { display: flex; }
    .sm\:grid { display: grid; }
}

@media (min-width: 768px) {
    .md\:hidden { display: none; }
    .md\:block { display: block; }
    .md\:flex { display: flex; }
    .md\:grid { display: grid; }
}

@media (min-width: 1024px) {
    .lg\:hidden { display: none; }
    .lg\:block { display: block; }
    .lg\:flex { display: flex; }
    .lg\:grid { display: grid; }
}
