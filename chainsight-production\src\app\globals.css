@import "tailwindcss";

/* Luxury Black Theme with Gold Highlights */
:root {
  --primary-50: #fefce8;
  --primary-100: #fef9c3;
  --primary-200: #fef08a;
  --primary-300: #fde047;
  --primary-400: #facc15;
  --primary-500: #eab308;
  --primary-600: #ca8a04;
  --primary-700: #a16207;
  --primary-800: #854d0e;
  --primary-900: #713f12;

  --dark-50: #f8fafc;
  --dark-100: #f1f5f9;
  --dark-200: #e2e8f0;
  --dark-300: #cbd5e1;
  --dark-400: #94a3b8;
  --dark-500: #64748b;
  --dark-600: #475569;
  --dark-700: #334155;
  --dark-800: #1e293b;
  --dark-900: #0f172a;
  --dark-950: #020617;

  --accent-gold: #ffd700;
  --accent-gold-light: #ffed4e;
  --accent-gold-dark: #b8860b;
}

body {
  background: #0f172a;
  color: #f1f5f9;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
  overflow-x: hidden;
}

/* Glass morphism effect */
.glass {
  background: rgba(15, 23, 42, 0.8);
  backdrop-filter: blur(16px);
  border: 1px solid rgba(234, 179, 8, 0.2);
}

/* Text glow effect */
.text-glow {
  text-shadow: 0 0 20px rgba(234, 179, 8, 0.5);
}

/* Gold gradient */
.gold-gradient {
  background: linear-gradient(135deg, #f59e0b, #d97706, #b45309);
}

/* Scrollbar styling */
::-webkit-scrollbar {
  width: 6px;
}

::-webkit-scrollbar-track {
  background: rgba(15, 23, 42, 0.5);
}

::-webkit-scrollbar-thumb {
  background: rgba(217, 119, 6, 0.5);
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: rgba(217, 119, 6, 0.7);
}
