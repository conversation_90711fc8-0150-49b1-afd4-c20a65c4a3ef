/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/to-buffer";
exports.ids = ["vendor-chunks/to-buffer"];
exports.modules = {

/***/ "(ssr)/./node_modules/to-buffer/index.js":
/*!*****************************************!*\
  !*** ./node_modules/to-buffer/index.js ***!
  \*****************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

"use strict";
eval("\n\nvar Buffer = (__webpack_require__(/*! safe-buffer */ \"(ssr)/./node_modules/to-buffer/node_modules/safe-buffer/index.js\").Buffer);\nvar isArray = __webpack_require__(/*! isarray */ \"(ssr)/./node_modules/isarray/index.js\");\nvar typedArrayBuffer = __webpack_require__(/*! typed-array-buffer */ \"(ssr)/./node_modules/typed-array-buffer/index.js\");\n\nvar isView = ArrayBuffer.isView || function isView(obj) {\n\ttry {\n\t\ttypedArrayBuffer(obj);\n\t\treturn true;\n\t} catch (e) {\n\t\treturn false;\n\t}\n};\n\nvar useUint8Array = typeof Uint8Array !== 'undefined';\nvar useArrayBuffer = typeof ArrayBuffer !== 'undefined'\n\t&& typeof Uint8Array !== 'undefined';\nvar useFromArrayBuffer = useArrayBuffer && (Buffer.prototype instanceof Uint8Array || Buffer.TYPED_ARRAY_SUPPORT);\n\nmodule.exports = function toBuffer(data, encoding) {\n\t/*\n\t * No need to do anything for exact instance\n\t * This is only valid when safe-buffer.Buffer === buffer.Buffer, i.e. when Buffer.from/Buffer.alloc existed\n\t */\n\tif (data instanceof Buffer) {\n\t\treturn data;\n\t}\n\n\tif (typeof data === 'string') {\n\t\treturn Buffer.from(data, encoding);\n\t}\n\n\t/*\n\t * Wrap any TypedArray instances and DataViews\n\t * Makes sense only on engines with full TypedArray support -- let Buffer detect that\n\t */\n\tif (useArrayBuffer && isView(data)) {\n\t\t// Bug in Node.js <6.3.1, which treats this as out-of-bounds\n\t\tif (data.byteLength === 0) {\n\t\t\treturn Buffer.alloc(0);\n\t\t}\n\n\t\t// When Buffer is based on Uint8Array, we can just construct it from ArrayBuffer\n\t\tif (useFromArrayBuffer) {\n\t\t\tvar res = Buffer.from(data.buffer, data.byteOffset, data.byteLength);\n\t\t\t/*\n\t\t\t * Recheck result size, as offset/length doesn't work on Node.js <5.10\n\t\t\t * We just go to Uint8Array case if this fails\n\t\t\t */\n\t\t\tif (res.byteLength === data.byteLength) {\n\t\t\t\treturn res;\n\t\t\t}\n\t\t}\n\n\t\t// Convert to Uint8Array bytes and then to Buffer\n\t\tvar uint8 = data instanceof Uint8Array ? data : new Uint8Array(data.buffer, data.byteOffset, data.byteLength);\n\t\tvar result = Buffer.from(uint8);\n\n\t\t/*\n\t\t * Let's recheck that conversion succeeded\n\t\t * We have .length but not .byteLength when useFromArrayBuffer is false\n\t\t */\n\t\tif (result.length === data.byteLength) {\n\t\t\treturn result;\n\t\t}\n\t}\n\n\t/*\n\t * Uint8Array in engines where Buffer.from might not work with ArrayBuffer, just copy over\n\t * Doesn't make sense with other TypedArray instances\n\t */\n\tif (useUint8Array && data instanceof Uint8Array) {\n\t\treturn Buffer.from(data);\n\t}\n\n\tvar isArr = isArray(data);\n\tif (isArr) {\n\t\tfor (var i = 0; i < data.length; i += 1) {\n\t\t\tvar x = data[i];\n\t\t\tif (\n\t\t\t\ttypeof x !== 'number'\n\t\t\t\t|| x < 0\n\t\t\t\t|| x > 255\n\t\t\t\t|| ~~x !== x // NaN and integer check\n\t\t\t) {\n\t\t\t\tthrow new RangeError('Array items must be numbers in the range 0-255.');\n\t\t\t}\n\t\t}\n\t}\n\n\t/*\n\t * Old Buffer polyfill on an engine that doesn't have TypedArray support\n\t * Also, this is from a different Buffer polyfill implementation then we have, as instanceof check failed\n\t * Convert to our current Buffer implementation\n\t */\n\tif (\n\t\tisArr || (\n\t\t\tBuffer.isBuffer(data)\n\t\t\t\t&& data.constructor\n\t\t\t\t&& typeof data.constructor.isBuffer === 'function'\n\t\t\t\t&& data.constructor.isBuffer(data)\n\t\t)\n\t) {\n\t\treturn Buffer.from(data);\n\t}\n\n\tthrow new TypeError('The \"data\" argument must be a string, an Array, a Buffer, a Uint8Array, or a DataView.');\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/to-buffer/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/to-buffer/node_modules/safe-buffer/index.js":
/*!******************************************************************!*\
  !*** ./node_modules/to-buffer/node_modules/safe-buffer/index.js ***!
  \******************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval("/*! safe-buffer. MIT License. Feross Aboukhadijeh <https://feross.org/opensource> */\n/* eslint-disable node/no-deprecated-api */\nvar buffer = __webpack_require__(/*! buffer */ \"buffer\")\nvar Buffer = buffer.Buffer\n\n// alternative to using Object.keys for old browsers\nfunction copyProps (src, dst) {\n  for (var key in src) {\n    dst[key] = src[key]\n  }\n}\nif (Buffer.from && Buffer.alloc && Buffer.allocUnsafe && Buffer.allocUnsafeSlow) {\n  module.exports = buffer\n} else {\n  // Copy properties from require('buffer')\n  copyProps(buffer, exports)\n  exports.Buffer = SafeBuffer\n}\n\nfunction SafeBuffer (arg, encodingOrOffset, length) {\n  return Buffer(arg, encodingOrOffset, length)\n}\n\nSafeBuffer.prototype = Object.create(Buffer.prototype)\n\n// Copy static methods from Buffer\ncopyProps(Buffer, SafeBuffer)\n\nSafeBuffer.from = function (arg, encodingOrOffset, length) {\n  if (typeof arg === 'number') {\n    throw new TypeError('Argument must not be a number')\n  }\n  return Buffer(arg, encodingOrOffset, length)\n}\n\nSafeBuffer.alloc = function (size, fill, encoding) {\n  if (typeof size !== 'number') {\n    throw new TypeError('Argument must be a number')\n  }\n  var buf = Buffer(size)\n  if (fill !== undefined) {\n    if (typeof encoding === 'string') {\n      buf.fill(fill, encoding)\n    } else {\n      buf.fill(fill)\n    }\n  } else {\n    buf.fill(0)\n  }\n  return buf\n}\n\nSafeBuffer.allocUnsafe = function (size) {\n  if (typeof size !== 'number') {\n    throw new TypeError('Argument must be a number')\n  }\n  return Buffer(size)\n}\n\nSafeBuffer.allocUnsafeSlow = function (size) {\n  if (typeof size !== 'number') {\n    throw new TypeError('Argument must be a number')\n  }\n  return buffer.SlowBuffer(size)\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/to-buffer/node_modules/safe-buffer/index.js\n");

/***/ })

};
;