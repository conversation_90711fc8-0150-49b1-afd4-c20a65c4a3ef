/* ==================== APPLICATION CONFIGURATION ====================
   Centralized configuration management for Chainsight
   ============================================================== */

class AppConfig {
    constructor() {
        this.config = {
            // Environment settings
            environment: 'development', // development, staging, production
            debug: true,
            version: '1.0.0',
            
            // API Configuration
            api: {
                baseURL: '',
                timeout: 30000,
                retryAttempts: 3,
                retryDelay: 1000,
                endpoints: {
                    // Crypto & Blockchain
                    crypto: {
                        prices: '/api/crypto/prices',
                        market: '/api/crypto/market',
                        wallet: '/api/crypto/wallet',
                        trading: '/api/crypto/trading'
                    },
                    
                    // Finance
                    finance: {
                        stocks: '/api/finance/stocks',
                        portfolio: '/api/finance/portfolio',
                        analysis: '/api/finance/analysis',
                        recommendations: '/api/finance/recommendations'
                    },
                    
                    // AI Services
                    ai: {
                        chat: '/api/ai/chat',
                        analysis: '/api/ai/analysis',
                        generation: '/api/ai/generation'
                    },
                    
                    // Support
                    support: {
                        tickets: '/api/support/tickets',
                        analytics: '/api/support/analytics',
                        chat: '/api/support/chat'
                    },
                    
                    // Legal & HR
                    legal: {
                        documents: '/api/legal/documents',
                        compliance: '/api/legal/compliance',
                        hr: '/api/legal/hr'
                    },
                    
                    // Design
                    design: {
                        generate: '/api/design/generate',
                        templates: '/api/design/templates',
                        assets: '/api/design/assets'
                    },
                    
                    // Facial Recognition
                    facial: {
                        detect: '/api/facial/detect',
                        analyze: '/api/facial/analyze',
                        verify: '/api/facial/verify'
                    },
                    
                    // Web3
                    web3: {
                        wallet: '/api/web3/wallet',
                        defi: '/api/web3/defi',
                        contracts: '/api/web3/contracts'
                    }
                }
            },
            
            // External API Keys (should be loaded from environment)
            apiKeys: {
                openai: '********************************************************************************************************************************************************************',
                coinGecko: '',
                alphaVantage: '',
                polygon: '',
                etherscan: ''
            },
            
            // Feature Flags
            features: {
                realTimeData: true,
                aiAssistant: true,
                darkMode: true,
                notifications: true,
                analytics: true,
                caching: true,
                offlineMode: false,
                betaFeatures: false,
                
                // Module-specific features
                modules: {
                    crypto: {
                        liveTrading: false,
                        portfolioSync: true,
                        priceAlerts: true
                    },
                    finance: {
                        realTimeQuotes: true,
                        portfolioAnalysis: true,
                        riskAssessment: true
                    },
                    support: {
                        liveChat: true,
                        aiResponses: true,
                        ticketAutomation: true
                    },
                    legal: {
                        documentAnalysis: true,
                        complianceChecks: true,
                        hrAnalytics: true
                    },
                    design: {
                        aiGeneration: true,
                        templateLibrary: true,
                        collaborativeEditing: false
                    },
                    facial: {
                        realTimeDetection: true,
                        emotionAnalysis: true,
                        ageEstimation: true
                    },
                    web3: {
                        walletConnect: true,
                        defiIntegration: true,
                        nftSupport: false
                    }
                }
            },
            
            // UI Configuration
            ui: {
                theme: 'dark', // dark, light, auto
                language: 'en',
                animations: true,
                reducedMotion: false,
                
                // Layout settings
                layout: {
                    sidebar: {
                        collapsed: false,
                        width: 280
                    },
                    header: {
                        height: 70,
                        sticky: true
                    },
                    footer: {
                        visible: true,
                        height: 60
                    }
                },
                
                // Component defaults
                components: {
                    modal: {
                        backdrop: true,
                        keyboard: true,
                        animation: true
                    },
                    notification: {
                        position: 'top-right',
                        duration: 5000,
                        showProgress: true
                    },
                    tooltip: {
                        delay: 500,
                        animation: true
                    }
                }
            },
            
            // Performance settings
            performance: {
                cacheTimeout: 5 * 60 * 1000, // 5 minutes
                debounceDelay: 300,
                throttleDelay: 100,
                lazyLoadThreshold: 0.1,
                maxConcurrentRequests: 6,
                
                // Chart settings
                charts: {
                    maxDataPoints: 1000,
                    updateInterval: 1000,
                    animationDuration: 300
                }
            },
            
            // Security settings
            security: {
                csrfProtection: true,
                xssProtection: true,
                contentSecurityPolicy: true,
                
                // Session settings
                session: {
                    timeout: 30 * 60 * 1000, // 30 minutes
                    renewThreshold: 5 * 60 * 1000, // 5 minutes
                    storage: 'sessionStorage'
                }
            },
            
            // Analytics configuration
            analytics: {
                enabled: true,
                trackPageViews: true,
                trackUserInteractions: true,
                trackErrors: true,
                
                // Privacy settings
                anonymizeIp: true,
                respectDoNotTrack: true,
                cookieConsent: true
            },
            
            // Logging configuration
            logging: {
                level: 'info', // debug, info, warn, error
                console: true,
                remote: false,
                maxLogSize: 1000,
                
                // Module-specific logging
                modules: {
                    api: 'debug',
                    ui: 'info',
                    data: 'warn',
                    security: 'error'
                }
            }
        };
        
        this.subscribers = new Set();
        this.loadFromStorage();
    }

    /**
     * Get configuration value
     * @param {string} path - Dot-separated path to config value
     * @param {*} defaultValue - Default value if not found
     * @returns {*} - Configuration value
     */
    get(path, defaultValue = undefined) {
        const keys = path.split('.');
        let value = this.config;
        
        for (const key of keys) {
            if (value && typeof value === 'object' && key in value) {
                value = value[key];
            } else {
                return defaultValue;
            }
        }
        
        return value;
    }

    /**
     * Set configuration value
     * @param {string} path - Dot-separated path to config value
     * @param {*} value - Value to set
     */
    set(path, value) {
        const keys = path.split('.');
        const lastKey = keys.pop();
        let target = this.config;
        
        // Navigate to parent object
        for (const key of keys) {
            if (!target[key] || typeof target[key] !== 'object') {
                target[key] = {};
            }
            target = target[key];
        }
        
        // Set value
        target[lastKey] = value;
        
        // Notify subscribers
        this.notify(path, value);
        
        // Save to storage
        this.saveToStorage();
    }

    /**
     * Check if feature is enabled
     * @param {string} feature - Feature name (dot-separated path)
     * @returns {boolean}
     */
    isFeatureEnabled(feature) {
        return this.get(`features.${feature}`, false);
    }

    /**
     * Enable/disable feature
     * @param {string} feature - Feature name
     * @param {boolean} enabled - Enable state
     */
    setFeature(feature, enabled) {
        this.set(`features.${feature}`, enabled);
    }

    /**
     * Get API endpoint
     * @param {string} module - Module name
     * @param {string} endpoint - Endpoint name
     * @returns {string} - Full endpoint URL
     */
    getApiEndpoint(module, endpoint) {
        const baseURL = this.get('api.baseURL', '');
        const endpointPath = this.get(`api.endpoints.${module}.${endpoint}`, '');
        
        if (!endpointPath) {
            throw new Error(`API endpoint not found: ${module}.${endpoint}`);
        }
        
        return `${baseURL}${endpointPath}`;
    }

    /**
     * Subscribe to configuration changes
     * @param {Function} callback - Callback function
     * @returns {Function} - Unsubscribe function
     */
    subscribe(callback) {
        this.subscribers.add(callback);
        
        return () => {
            this.subscribers.delete(callback);
        };
    }

    /**
     * Notify subscribers of changes
     * @param {string} path - Changed path
     * @param {*} value - New value
     */
    notify(path, value) {
        this.subscribers.forEach(callback => {
            try {
                callback(path, value, this.config);
            } catch (error) {
                console.error('Error in config subscriber:', error);
            }
        });
    }

    /**
     * Load configuration from storage
     */
    loadFromStorage() {
        try {
            const stored = localStorage.getItem('chainsight-config');
            if (stored) {
                const parsedConfig = JSON.parse(stored);
                this.config = { ...this.config, ...parsedConfig };
            }
        } catch (error) {
            console.warn('Failed to load config from storage:', error);
        }
    }

    /**
     * Save configuration to storage
     */
    saveToStorage() {
        try {
            // Only save user-configurable settings
            const userConfig = {
                ui: this.config.ui,
                features: this.config.features,
                performance: this.config.performance
            };
            
            localStorage.setItem('chainsight-config', JSON.stringify(userConfig));
        } catch (error) {
            console.warn('Failed to save config to storage:', error);
        }
    }

    /**
     * Reset configuration to defaults
     */
    reset() {
        localStorage.removeItem('chainsight-config');
        location.reload(); // Reload to get fresh config
    }

    /**
     * Validate configuration
     * @returns {Array} - Array of validation errors
     */
    validate() {
        const errors = [];
        
        // Check required API keys for enabled features
        if (this.isFeatureEnabled('aiAssistant') && !this.get('apiKeys.openai')) {
            errors.push('OpenAI API key is required for AI assistant feature');
        }
        
        // Check environment-specific settings
        if (this.get('environment') === 'production' && this.get('debug')) {
            errors.push('Debug mode should be disabled in production');
        }
        
        return errors;
    }
}

// Create global instance
const appConfig = new AppConfig();

// Export for module systems
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { AppConfig, appConfig };
}

// Global assignment for browser
if (typeof window !== 'undefined') {
    window.AppConfig = AppConfig;
    window.appConfig = appConfig;
}
