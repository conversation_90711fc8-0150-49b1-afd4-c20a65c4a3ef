// Animation Designer Plugin - Main exports
export * from './generators/LottieGenerator';
export * from './generators/SVGAnimationGenerator';
export * from './generators/CSSAnimationGenerator';
export * from './generators/ThreeJSGenerator';
export * from './templates/AnimationTemplates';
export * from './utils/AnimationUtils';
export * from './types/AnimationTypes';

// Plugin configuration
export interface AnimationDesignerConfig {
  outputFormat?: 'lottie' | 'svg' | 'css' | 'threejs';
  quality?: 'low' | 'medium' | 'high';
  fps?: number;
  duration?: number;
  autoplay?: boolean;
  loop?: boolean;
  luxuryTheme?: boolean;
}

// Default configuration
export const defaultAnimationConfig: AnimationDesignerConfig = {
  outputFormat: 'lottie',
  quality: 'high',
  fps: 60,
  duration: 3000,
  autoplay: true,
  loop: true,
  luxuryTheme: true
};

// Animation Designer Plugin Class
export class AnimationDesigner {
  private config: AnimationDesignerConfig;

  constructor(config: Partial<AnimationDesignerConfig> = {}) {
    this.config = { ...defaultAnimationConfig, ...config };
  }

  getConfig(): AnimationDesignerConfig {
    return this.config;
  }

  updateConfig(newConfig: Partial<AnimationDesignerConfig>): void {
    this.config = { ...this.config, ...newConfig };
  }

  // Quick animation generators
  async createLoadingAnimation(type: 'spinner' | 'dots' | 'bars' | 'pulse' = 'spinner'): Promise<any> {
    const templates = await import('./templates/LoadingTemplates');
    return templates.generateLoadingAnimation(type, this.config);
  }

  async createLogoAnimation(logoPath: string, effect: 'fade' | 'slide' | 'bounce' | 'glow' = 'glow'): Promise<any> {
    const templates = await import('./templates/LogoTemplates');
    return templates.generateLogoAnimation(logoPath, effect, this.config);
  }

  async createButtonAnimation(style: 'hover' | 'click' | 'focus' | 'luxury' = 'luxury'): Promise<any> {
    const templates = await import('./templates/ButtonTemplates');
    return templates.generateButtonAnimation(style, this.config);
  }

  async createChartAnimation(chartType: 'line' | 'bar' | 'pie' | 'area' = 'line'): Promise<any> {
    const templates = await import('./templates/ChartTemplates');
    return templates.generateChartAnimation(chartType, this.config);
  }

  // Utility methods
  getSupportedFormats(): string[] {
    return ['lottie', 'svg', 'css', 'threejs', 'gif', 'mp4'];
  }

  getAnimationTypes(): string[] {
    return [
      'loading', 'logo', 'button', 'chart', 'transition',
      'particle', 'morphing', 'text', 'icon', 'background'
    ];
  }

  getLuxuryEffects(): string[] {
    return [
      'gold-glow', 'shimmer', 'gradient-flow', 'particle-burst',
      'luxury-fade', 'premium-slide', 'elegant-bounce', 'royal-pulse'
    ];
  }

  // Template management
  async getTemplate(name: string): Promise<any> {
    const templates = await import('./templates/AnimationTemplates');
    return templates.getTemplate(name);
  }

  async listTemplates(): Promise<string[]> {
    const templates = await import('./templates/AnimationTemplates');
    return templates.listTemplates();
  }

  // Export functionality
  async exportAnimation(animation: any, format: string): Promise<string> {
    const exporter = await import('./exporters/AnimationExporter');
    return exporter.exportAnimation(animation, format, this.config);
  }

  // Preview functionality
  async generatePreview(animation: any): Promise<string> {
    const previewer = await import('./preview/AnimationPreviewer');
    return previewer.generatePreview(animation, this.config);
  }
}

// Export singleton instance
export const animationDesigner = new AnimationDesigner();

// Plugin metadata
export const PLUGIN_INFO = {
  name: 'Animation Designer',
  version: '1.0.0',
  description: 'Create stunning animations with luxury themes',
  author: 'Connectouch',
  capabilities: [
    'Lottie animation generation',
    'SVG animation creation',
    'CSS animation design',
    'Three.js 3D animations',
    'Luxury theme integration',
    'Template library',
    'Multi-format export'
  ]
};
