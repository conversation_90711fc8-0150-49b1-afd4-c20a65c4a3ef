'use client';

import { motion } from 'framer-motion';
import { Zap, Globe, Code, Layers } from 'lucide-react';

export function Web3Module() {
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="space-y-6"
    >
      <div className="bg-white/10 backdrop-blur-xl rounded-2xl p-6 border border-white/20">
        <div className="flex items-center space-x-3 mb-6">
          <div className="w-12 h-12 bg-gradient-to-r from-purple-400 to-pink-500 rounded-xl flex items-center justify-center">
            <Globe className="w-6 h-6 text-white" />
          </div>
          <div>
            <h2 className="text-2xl font-bold text-white">Web3 Development</h2>
            <p className="text-gray-400">Blockchain and DeFi development tools</p>
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          <div className="bg-black/20 rounded-xl p-4">
            <div className="flex items-center space-x-3 mb-3">
              <Code className="w-5 h-5 text-purple-400" />
              <span className="text-white font-medium">Smart Contracts</span>
            </div>
            <p className="text-2xl font-bold text-purple-400">8</p>
            <p className="text-sm text-gray-400">Deployed contracts</p>
          </div>

          <div className="bg-black/20 rounded-xl p-4">
            <div className="flex items-center space-x-3 mb-3">
              <Zap className="w-5 h-5 text-yellow-400" />
              <span className="text-white font-medium">Gas Fees</span>
            </div>
            <p className="text-2xl font-bold text-yellow-400">Optimal</p>
            <p className="text-sm text-gray-400">Current network</p>
          </div>

          <div className="bg-black/20 rounded-xl p-4">
            <div className="flex items-center space-x-3 mb-3">
              <Layers className="w-5 h-5 text-blue-400" />
              <span className="text-white font-medium">DApps</span>
            </div>
            <p className="text-2xl font-bold text-blue-400">5</p>
            <p className="text-sm text-gray-400">Active applications</p>
          </div>
        </div>

        <div className="mt-6 p-4 bg-gradient-to-r from-purple-500/20 to-pink-500/20 rounded-xl border border-purple-500/30">
          <h3 className="text-white font-semibold mb-2">Web3 AI Analysis</h3>
          <p className="text-gray-300 text-sm">
            Ethereum network congestion is low. Optimal time for contract deployment. 
            Consider implementing Layer 2 solutions for reduced transaction costs.
          </p>
        </div>
      </div>
    </motion.div>
  );
}
