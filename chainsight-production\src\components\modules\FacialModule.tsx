'use client';

import { motion } from 'framer-motion';
import { Camera, Eye, Shield, Users } from 'lucide-react';

export function FacialModule() {
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="space-y-6"
    >
      <div className="bg-white/10 backdrop-blur-xl rounded-2xl p-6 border border-white/20">
        <div className="flex items-center space-x-3 mb-6">
          <div className="w-12 h-12 bg-gradient-to-r from-cyan-400 to-blue-500 rounded-xl flex items-center justify-center">
            <Camera className="w-6 h-6 text-white" />
          </div>
          <div>
            <h2 className="text-2xl font-bold text-white">Facial Recognition</h2>
            <p className="text-gray-400">Advanced biometric analysis and security</p>
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          <div className="bg-black/20 rounded-xl p-4">
            <div className="flex items-center space-x-3 mb-3">
              <Eye className="w-5 h-5 text-cyan-400" />
              <span className="text-white font-medium">Accuracy</span>
            </div>
            <p className="text-2xl font-bold text-cyan-400">99.7%</p>
            <p className="text-sm text-gray-400">Recognition rate</p>
          </div>

          <div className="bg-black/20 rounded-xl p-4">
            <div className="flex items-center space-x-3 mb-3">
              <Users className="w-5 h-5 text-blue-400" />
              <span className="text-white font-medium">Profiles</span>
            </div>
            <p className="text-2xl font-bold text-blue-400">2,847</p>
            <p className="text-sm text-gray-400">Registered faces</p>
          </div>

          <div className="bg-black/20 rounded-xl p-4">
            <div className="flex items-center space-x-3 mb-3">
              <Shield className="w-5 h-5 text-green-400" />
              <span className="text-white font-medium">Security</span>
            </div>
            <p className="text-2xl font-bold text-green-400">Active</p>
            <p className="text-sm text-gray-400">Real-time monitoring</p>
          </div>
        </div>

        <div className="mt-6 p-4 bg-gradient-to-r from-cyan-500/20 to-blue-500/20 rounded-xl border border-cyan-500/30">
          <h3 className="text-white font-semibold mb-2">Biometric Analysis</h3>
          <p className="text-gray-300 text-sm">
            Facial recognition system is operating at optimal performance. All security 
            protocols are active. No unauthorized access attempts detected in the last 24 hours.
          </p>
        </div>
      </div>
    </motion.div>
  );
}
