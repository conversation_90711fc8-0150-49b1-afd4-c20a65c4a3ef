// Finance Analyzer Library - Main exports
export * from './analysis/TechnicalAnalyzer';
export * from './analysis/FundamentalAnalyzer';
export * from './prediction/PricePredictor';
export * from './prediction/TrendAnalyzer';
export * from './portfolio/PortfolioOptimizer';
export * from './portfolio/RiskAnalyzer';
export * from './market/MarketDataProvider';
export * from './indicators/TechnicalIndicators';
export * from './types/FinanceTypes';
export * from './utils/FinanceUtils';

// Core finance configuration
export interface FinanceConfig {
  alphaVantageApiKey?: string;
  yahooFinanceEnabled?: boolean;
  defaultTimeframe?: string;
  riskFreeRate?: number;
  benchmarkSymbol?: string;
  enableCaching?: boolean;
  cacheTimeout?: number;
}

// Default configuration
export const defaultFinanceConfig: FinanceConfig = {
  yahooFinanceEnabled: true,
  defaultTimeframe: '1y',
  riskFreeRate: 0.02, // 2% risk-free rate
  benchmarkSymbol: 'SPY',
  enableCaching: true,
  cacheTimeout: 300000 // 5 minutes
};

// Initialize Finance Analyzer
export class FinanceAnalyzer {
  private config: FinanceConfig;
  private cache: Map<string, { data: any; timestamp: number }> = new Map();

  constructor(config: Partial<FinanceConfig> = {}) {
    this.config = { ...defaultFinanceConfig, ...config };
  }

  getConfig(): FinanceConfig {
    return this.config;
  }

  updateConfig(newConfig: Partial<FinanceConfig>): void {
    this.config = { ...this.config, ...newConfig };
  }

  // Cache management
  private getCachedData(key: string): any | null {
    if (!this.config.enableCaching) return null;
    
    const cached = this.cache.get(key);
    if (!cached) return null;
    
    const isExpired = Date.now() - cached.timestamp > (this.config.cacheTimeout || 300000);
    if (isExpired) {
      this.cache.delete(key);
      return null;
    }
    
    return cached.data;
  }

  private setCachedData(key: string, data: any): void {
    if (!this.config.enableCaching) return;
    
    this.cache.set(key, {
      data,
      timestamp: Date.now()
    });
  }

  clearCache(): void {
    this.cache.clear();
  }

  // Utility methods
  getSupportedTimeframes(): string[] {
    return ['1d', '5d', '1mo', '3mo', '6mo', '1y', '2y', '5y', '10y', 'ytd', 'max'];
  }

  getSupportedIndicators(): string[] {
    return [
      'SMA', 'EMA', 'RSI', 'MACD', 'BollingerBands', 
      'Stochastic', 'Williams%R', 'CCI', 'ADX', 'ATR'
    ];
  }

  getMarketSectors(): string[] {
    return [
      'Technology', 'Healthcare', 'Financial Services', 'Consumer Cyclical',
      'Communication Services', 'Industrials', 'Consumer Defensive',
      'Energy', 'Utilities', 'Real Estate', 'Basic Materials'
    ];
  }
}

// Export singleton instance
export const financeAnalyzer = new FinanceAnalyzer();
