/* ==================== BUTTON COMPONENTS ====================
   Button styles and variants
   ======================================================= */

/* Base Button */
.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: var(--space-xs);
    padding: var(--space-sm) var(--space-lg);
    border: 1px solid transparent;
    border-radius: var(--radius-lg);
    font-family: var(--font-primary);
    font-size: var(--text-base);
    font-weight: var(--font-medium);
    line-height: 1;
    text-decoration: none;
    cursor: pointer;
    user-select: none;
    transition: all var(--transition-fast);
    position: relative;
    overflow: hidden;
    white-space: nowrap;
}

.btn:focus {
    outline: 2px solid var(--neon-blue);
    outline-offset: 2px;
}

.btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    pointer-events: none;
}

/* Button Sizes */
.btn-xs {
    padding: var(--space-xs) var(--space-sm);
    font-size: var(--text-xs);
    border-radius: var(--radius-md);
}

.btn-sm {
    padding: var(--space-xs) var(--space-md);
    font-size: var(--text-sm);
    border-radius: var(--radius-md);
}

.btn-lg {
    padding: var(--space-md) var(--space-xl);
    font-size: var(--text-lg);
    border-radius: var(--radius-xl);
}

.btn-xl {
    padding: var(--space-lg) var(--space-2xl);
    font-size: var(--text-xl);
    border-radius: var(--radius-2xl);
}

/* Primary Button */
.btn-primary {
    background: var(--primary-gradient);
    color: var(--text-primary);
    border-color: transparent;
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.6);
}

.btn-primary:active {
    transform: translateY(0);
}

/* Secondary Button */
.btn-secondary {
    background: var(--secondary-gradient);
    color: var(--text-primary);
    border-color: transparent;
    box-shadow: 0 4px 15px rgba(240, 147, 251, 0.4);
}

.btn-secondary:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(240, 147, 251, 0.6);
}

/* Accent Button */
.btn-accent {
    background: var(--accent-gradient);
    color: var(--text-primary);
    border-color: transparent;
    box-shadow: 0 4px 15px rgba(79, 172, 254, 0.4);
}

.btn-accent:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(79, 172, 254, 0.6);
}

/* Success Button */
.btn-success {
    background: var(--success-gradient);
    color: var(--luxury-black);
    border-color: transparent;
    box-shadow: 0 4px 15px rgba(67, 233, 123, 0.4);
}

.btn-success:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(67, 233, 123, 0.6);
}

/* Warning Button */
.btn-warning {
    background: var(--warning-gradient);
    color: var(--luxury-black);
    border-color: transparent;
    box-shadow: 0 4px 15px rgba(250, 112, 154, 0.4);
}

.btn-warning:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(250, 112, 154, 0.6);
}

/* Danger Button */
.btn-danger {
    background: linear-gradient(135deg, #ff416c 0%, #ff4b2b 100%);
    color: var(--text-primary);
    border-color: transparent;
    box-shadow: 0 4px 15px rgba(255, 65, 108, 0.4);
}

.btn-danger:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(255, 65, 108, 0.6);
}

/* Ghost Button */
.btn-ghost {
    background: transparent;
    color: var(--text-primary);
    border-color: var(--glass-border);
}

.btn-ghost:hover {
    background: var(--glass-bg);
    border-color: var(--neon-blue);
    box-shadow: 0 0 20px rgba(0, 212, 255, 0.3);
}

/* Outline Button */
.btn-outline {
    background: transparent;
    color: var(--neon-blue);
    border-color: var(--neon-blue);
}

.btn-outline:hover {
    background: var(--neon-blue);
    color: var(--luxury-black);
    box-shadow: 0 0 20px rgba(0, 212, 255, 0.5);
}

/* Glass Button */
.btn-glass {
    background: var(--glass-bg);
    backdrop-filter: var(--glass-backdrop);
    -webkit-backdrop-filter: var(--glass-backdrop);
    color: var(--text-primary);
    border-color: var(--glass-border);
    box-shadow: var(--glass-shadow);
}

.btn-glass:hover {
    background: rgba(255, 255, 255, 0.15);
    border-color: rgba(255, 255, 255, 0.3);
    transform: translateY(-2px);
}

/* Neon Button */
.btn-neon {
    background: transparent;
    color: var(--neon-blue);
    border-color: var(--neon-blue);
    text-shadow: 0 0 10px var(--neon-blue);
    box-shadow: 
        inset 0 0 10px rgba(0, 212, 255, 0.2),
        0 0 20px rgba(0, 212, 255, 0.3);
}

.btn-neon:hover {
    background: var(--neon-blue);
    color: var(--luxury-black);
    text-shadow: none;
    box-shadow: 
        inset 0 0 20px rgba(0, 212, 255, 0.5),
        0 0 40px rgba(0, 212, 255, 0.8);
}

/* Loading Button */
.btn-loading {
    position: relative;
    color: transparent;
    pointer-events: none;
}

.btn-loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 16px;
    height: 16px;
    margin: -8px 0 0 -8px;
    border: 2px solid transparent;
    border-top-color: currentColor;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

/* Button Groups */
.btn-group {
    display: inline-flex;
    border-radius: var(--radius-lg);
    overflow: hidden;
    box-shadow: var(--shadow-md);
}

.btn-group .btn {
    border-radius: 0;
    border-right-width: 0;
}

.btn-group .btn:first-child {
    border-top-left-radius: var(--radius-lg);
    border-bottom-left-radius: var(--radius-lg);
}

.btn-group .btn:last-child {
    border-top-right-radius: var(--radius-lg);
    border-bottom-right-radius: var(--radius-lg);
    border-right-width: 1px;
}

/* Icon Buttons */
.btn-icon {
    padding: var(--space-sm);
    aspect-ratio: 1;
    border-radius: 50%;
}

.btn-icon-sm {
    padding: var(--space-xs);
    aspect-ratio: 1;
    border-radius: 50%;
}

.btn-icon-lg {
    padding: var(--space-md);
    aspect-ratio: 1;
    border-radius: 50%;
}

/* Floating Action Button */
.btn-fab {
    position: fixed;
    bottom: var(--space-xl);
    right: var(--space-xl);
    width: 56px;
    height: 56px;
    border-radius: 50%;
    background: var(--primary-gradient);
    color: var(--text-primary);
    border: none;
    box-shadow: 
        0 6px 20px rgba(102, 126, 234, 0.4),
        0 2px 4px rgba(0, 0, 0, 0.1);
    z-index: var(--z-fixed);
    transition: all var(--transition-normal);
}

.btn-fab:hover {
    transform: scale(1.1);
    box-shadow: 
        0 8px 30px rgba(102, 126, 234, 0.6),
        0 4px 8px rgba(0, 0, 0, 0.2);
}

/* Button Animations */
.btn-pulse {
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% {
        box-shadow: 0 0 0 0 rgba(0, 212, 255, 0.7);
    }
    70% {
        box-shadow: 0 0 0 10px rgba(0, 212, 255, 0);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(0, 212, 255, 0);
    }
}

.btn-bounce:hover {
    animation: bounce 0.6s;
}

@keyframes bounce {
    0%, 20%, 60%, 100% {
        transform: translateY(0);
    }
    40% {
        transform: translateY(-10px);
    }
    80% {
        transform: translateY(-5px);
    }
}

/* Responsive Buttons */
@media (max-width: 768px) {
    .btn {
        padding: var(--space-xs) var(--space-md);
        font-size: var(--text-sm);
    }
    
    .btn-lg {
        padding: var(--space-sm) var(--space-lg);
        font-size: var(--text-base);
    }
    
    .btn-fab {
        bottom: var(--space-lg);
        right: var(--space-lg);
        width: 48px;
        height: 48px;
    }
}
