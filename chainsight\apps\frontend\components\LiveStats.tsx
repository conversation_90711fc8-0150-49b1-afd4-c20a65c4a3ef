'use client';

import { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { TrendingUp, TrendingDown, DollarSign, Activity, Users, Zap } from 'lucide-react';

interface StatData {
  label: string;
  value: string;
  change: string;
  trend: 'up' | 'down';
  icon: any;
}

export function LiveStats() {
  const [stats, setStats] = useState<StatData[]>([
    {
      label: 'BTC Price',
      value: '$43,250',
      change: '+2.4%',
      trend: 'up',
      icon: DollarSign,
    },
    {
      label: 'ETH Price',
      value: '$2,680',
      change: '+1.8%',
      trend: 'up',
      icon: TrendingUp,
    },
    {
      label: 'Active Users',
      value: '12,847',
      change: '+15.2%',
      trend: 'up',
      icon: Users,
    },
    {
      label: 'Transactions',
      value: '1.2M',
      change: '+8.7%',
      trend: 'up',
      icon: Activity,
    },
    {
      label: 'Gas Price',
      value: '25 gwei',
      change: '-12.3%',
      trend: 'down',
      icon: Zap,
    },
    {
      label: 'Market Cap',
      value: '$1.8T',
      change: '+3.1%',
      trend: 'up',
      icon: TrendingUp,
    },
  ]);

  // Simulate real-time updates
  useEffect(() => {
    const interval = setInterval(() => {
      setStats(prevStats => 
        prevStats.map(stat => ({
          ...stat,
          value: updateValue(stat.value),
          change: updateChange(),
          trend: Math.random() > 0.3 ? 'up' : 'down',
        }))
      );
    }, 5000);

    return () => clearInterval(interval);
  }, []);

  const updateValue = (currentValue: string) => {
    const numericPart = currentValue.match(/[\d,]+/)?.[0] || '0';
    const prefix = currentValue.match(/^[^\d]*/)?.[0] || '';
    const suffix = currentValue.match(/[^\d]*$/)?.[0] || '';
    
    const num = parseInt(numericPart.replace(/,/g, ''));
    const variation = Math.floor(Math.random() * 200) - 100; // ±100
    const newNum = Math.max(0, num + variation);
    
    return `${prefix}${newNum.toLocaleString()}${suffix}`;
  };

  const updateChange = () => {
    const change = (Math.random() * 20 - 10).toFixed(1); // -10% to +10%
    return `${change >= 0 ? '+' : ''}${change}%`;
  };

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
      {stats.map((stat, index) => (
        <motion.div
          key={stat.label}
          initial={{ opacity: 0, scale: 0.9 }}
          whileInView={{ opacity: 1, scale: 1 }}
          transition={{ delay: index * 0.1, duration: 0.5 }}
          className="glass rounded-xl p-6 hover:border-primary-500/50 transition-all duration-300"
        >
          <div className="flex items-center justify-between mb-4">
            <div className={`w-10 h-10 rounded-lg bg-gradient-to-r ${
              stat.trend === 'up' 
                ? 'from-green-500 to-emerald-500' 
                : 'from-red-500 to-rose-500'
            } flex items-center justify-center`}>
              <stat.icon className="w-5 h-5 text-white" />
            </div>
            
            <div className={`flex items-center space-x-1 text-sm font-medium ${
              stat.trend === 'up' ? 'text-green-400' : 'text-red-400'
            }`}>
              {stat.trend === 'up' ? (
                <TrendingUp className="w-4 h-4" />
              ) : (
                <TrendingDown className="w-4 h-4" />
              )}
              <span>{stat.change}</span>
            </div>
          </div>
          
          <div>
            <h3 className="text-2xl font-bold text-white mb-1">
              {stat.value}
            </h3>
            <p className="text-dark-300 text-sm">
              {stat.label}
            </p>
          </div>
          
          {/* Live indicator */}
          <div className="flex items-center space-x-2 mt-4">
            <motion.div
              animate={{ scale: [1, 1.2, 1] }}
              transition={{ duration: 2, repeat: Infinity }}
              className="w-2 h-2 bg-primary-500 rounded-full"
            />
            <span className="text-xs text-dark-400">Live</span>
          </div>
        </motion.div>
      ))}
    </div>
  );
}
