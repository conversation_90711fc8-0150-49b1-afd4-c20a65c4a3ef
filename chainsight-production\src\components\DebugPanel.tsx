'use client';

import { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  Bug, 
  X, 
  Download, 
  Trash2, 
  AlertTriangle, 
  Info, 
  CheckCircle,
  Clock,
  Monitor,
  Wifi,
  Database
} from 'lucide-react';
import { debugger, debugUtils } from '@/lib/debug';

interface DebugPanelProps {
  isOpen: boolean;
  onClose: () => void;
}

export function DebugPanel({ isOpen, onClose }: DebugPanelProps) {
  const [errorReport, setErrorReport] = useState<any>(null);
  const [activeTab, setActiveTab] = useState<'errors' | 'system' | 'performance'>('errors');

  useEffect(() => {
    if (isOpen) {
      const updateReport = () => {
        setErrorReport(debugger.getErrorReport());
      };

      updateReport();
      const interval = setInterval(updateReport, 2000); // Update every 2 seconds

      return () => clearInterval(interval);
    }
  }, [isOpen]);

  const handleExportLog = () => {
    debugger.exportErrorLog();
  };

  const handleClearErrors = () => {
    debugger.clearErrors();
    setErrorReport(debugger.getErrorReport());
  };

  const handleTestError = () => {
    // Trigger test errors for demonstration
    console.error('Test Error: This is a sample error for testing');
    console.warn('Test Warning: This is a sample warning for testing');

    // Trigger a JavaScript error
    setTimeout(() => {
      try {
        throw new Error('Test Runtime Error: Simulated error for testing error boundary');
      } catch (error) {
        debugger.logError('Test Error Handler', error);
      }
    }, 100);
  };

  if (!isOpen) return null;

  return (
    <AnimatePresence>
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
        className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4"
        onClick={onClose}
      >
        <motion.div
          initial={{ scale: 0.9, opacity: 0 }}
          animate={{ scale: 1, opacity: 1 }}
          exit={{ scale: 0.9, opacity: 0 }}
          className="bg-cosmic-dark/95 backdrop-blur-xl border border-white/10 rounded-2xl w-full max-w-4xl max-h-[80vh] overflow-hidden"
          onClick={(e) => e.stopPropagation()}
        >
          {/* Header */}
          <div className="flex items-center justify-between p-6 border-b border-white/10">
            <div className="flex items-center space-x-3">
              <div className="p-2 bg-blue-500/20 rounded-lg">
                <Bug className="w-6 h-6 text-blue-400" />
              </div>
              <div>
                <h2 className="text-xl font-bold text-white">Debug Panel</h2>
                <p className="text-gray-400 text-sm">Real-time platform diagnostics</p>
              </div>
            </div>
            <button
              onClick={onClose}
              className="p-2 hover:bg-white/10 rounded-lg transition-colors"
            >
              <X className="w-5 h-5 text-gray-400" />
            </button>
          </div>

          {/* Tabs */}
          <div className="flex border-b border-white/10">
            {[
              { id: 'errors', label: 'Errors', icon: AlertTriangle },
              { id: 'system', label: 'System', icon: Monitor },
              { id: 'performance', label: 'Performance', icon: Clock },
            ].map(({ id, label, icon: Icon }) => (
              <button
                key={id}
                onClick={() => setActiveTab(id as any)}
                className={`flex items-center space-x-2 px-6 py-3 transition-colors ${
                  activeTab === id
                    ? 'bg-blue-500/20 text-blue-400 border-b-2 border-blue-400'
                    : 'text-gray-400 hover:text-white hover:bg-white/5'
                }`}
              >
                <Icon className="w-4 h-4" />
                <span>{label}</span>
                {id === 'errors' && errorReport?.totalErrors > 0 && (
                  <span className="bg-red-500 text-white text-xs px-2 py-1 rounded-full">
                    {errorReport.totalErrors}
                  </span>
                )}
              </button>
            ))}
          </div>

          {/* Content */}
          <div className="p-6 overflow-y-auto max-h-[60vh]">
            {activeTab === 'errors' && (
              <div className="space-y-4">
                {/* Error Summary */}
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div className="bg-red-500/10 border border-red-500/20 rounded-lg p-4">
                    <div className="flex items-center space-x-2">
                      <AlertTriangle className="w-5 h-5 text-red-400" />
                      <span className="text-red-400 font-semibold">Total Errors</span>
                    </div>
                    <p className="text-2xl font-bold text-white mt-2">
                      {errorReport?.totalErrors || 0}
                    </p>
                  </div>
                  
                  <div className="bg-yellow-500/10 border border-yellow-500/20 rounded-lg p-4">
                    <div className="flex items-center space-x-2">
                      <Info className="w-5 h-5 text-yellow-400" />
                      <span className="text-yellow-400 font-semibold">Error Types</span>
                    </div>
                    <p className="text-2xl font-bold text-white mt-2">
                      {Object.keys(errorReport?.errorsByType || {}).length}
                    </p>
                  </div>
                  
                  <div className="bg-green-500/10 border border-green-500/20 rounded-lg p-4">
                    <div className="flex items-center space-x-2">
                      <CheckCircle className="w-5 h-5 text-green-400" />
                      <span className="text-green-400 font-semibold">Status</span>
                    </div>
                    <p className="text-lg font-semibold text-white mt-2">
                      {errorReport?.totalErrors === 0 ? 'Healthy' : 'Issues Detected'}
                    </p>
                  </div>
                </div>

                {/* Action Buttons */}
                <div className="flex flex-wrap gap-3">
                  <button
                    onClick={handleExportLog}
                    className="flex items-center space-x-2 px-4 py-2 bg-blue-500/20 hover:bg-blue-500/30 text-blue-400 rounded-lg transition-colors"
                  >
                    <Download className="w-4 h-4" />
                    <span>Export Log</span>
                  </button>
                  <button
                    onClick={handleClearErrors}
                    className="flex items-center space-x-2 px-4 py-2 bg-red-500/20 hover:bg-red-500/30 text-red-400 rounded-lg transition-colors"
                  >
                    <Trash2 className="w-4 h-4" />
                    <span>Clear Errors</span>
                  </button>
                  <button
                    onClick={handleTestError}
                    className="flex items-center space-x-2 px-4 py-2 bg-yellow-500/20 hover:bg-yellow-500/30 text-yellow-400 rounded-lg transition-colors"
                  >
                    <Bug className="w-4 h-4" />
                    <span>Test Errors</span>
                  </button>
                </div>

                {/* Recent Errors */}
                <div>
                  <h3 className="text-lg font-semibold text-white mb-3">Recent Errors</h3>
                  <div className="space-y-2">
                    {errorReport?.recentErrors?.length > 0 ? (
                      errorReport.recentErrors.map((error: any, index: number) => (
                        <div
                          key={index}
                          className="bg-red-500/10 border border-red-500/20 rounded-lg p-3"
                        >
                          <div className="flex items-start justify-between">
                            <div className="flex-1">
                              <p className="text-red-400 font-semibold">{error.context}</p>
                              <p className="text-gray-300 text-sm mt-1">
                                {typeof error.error === 'string' 
                                  ? error.error 
                                  : error.error?.message || 'Unknown error'
                                }
                              </p>
                            </div>
                            <span className="text-xs text-gray-500">
                              {new Date(error.timestamp).toLocaleTimeString()}
                            </span>
                          </div>
                        </div>
                      ))
                    ) : (
                      <div className="text-center py-8 text-gray-400">
                        <CheckCircle className="w-12 h-12 mx-auto mb-3 text-green-400" />
                        <p>No errors detected! Platform is running smoothly.</p>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            )}

            {activeTab === 'system' && (
              <div className="space-y-4">
                <h3 className="text-lg font-semibold text-white mb-3">System Information</h3>
                {errorReport?.systemInfo && (
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="bg-white/5 rounded-lg p-4">
                      <h4 className="text-white font-semibold mb-2">Browser</h4>
                      <p className="text-gray-300 text-sm">{errorReport.systemInfo.userAgent}</p>
                    </div>
                    <div className="bg-white/5 rounded-lg p-4">
                      <h4 className="text-white font-semibold mb-2">Platform</h4>
                      <p className="text-gray-300">{errorReport.systemInfo.platform}</p>
                    </div>
                    <div className="bg-white/5 rounded-lg p-4">
                      <h4 className="text-white font-semibold mb-2">Screen Resolution</h4>
                      <p className="text-gray-300">
                        {errorReport.systemInfo.screen?.width} × {errorReport.systemInfo.screen?.height}
                      </p>
                    </div>
                    <div className="bg-white/5 rounded-lg p-4">
                      <h4 className="text-white font-semibold mb-2">Viewport</h4>
                      <p className="text-gray-300">
                        {errorReport.systemInfo.viewport?.width} × {errorReport.systemInfo.viewport?.height}
                      </p>
                    </div>
                  </div>
                )}
              </div>
            )}

            {activeTab === 'performance' && (
              <div className="space-y-4">
                <h3 className="text-lg font-semibold text-white mb-3">Performance Metrics</h3>
                <div className="bg-white/5 rounded-lg p-4">
                  <p className="text-gray-300">Performance monitoring is active. Check browser DevTools for detailed metrics.</p>
                </div>
              </div>
            )}
          </div>
        </motion.div>
      </motion.div>
    </AnimatePresence>
  );
}
