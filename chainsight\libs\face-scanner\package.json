{"name": "@chainsight/face-scanner", "version": "1.0.0", "description": "Face recognition and analysis library for Chainsight platform", "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"build": "tsc", "dev": "tsc --watch", "test": "jest", "lint": "eslint src/**/*.ts", "clean": "rm -rf dist"}, "dependencies": {"@tensorflow/tfjs": "^4.10.0", "@tensorflow/tfjs-node": "^4.10.0", "face-api.js": "^0.22.2", "canvas": "^2.11.2", "sharp": "^0.32.5", "jimp": "^0.22.10"}, "devDependencies": {"@types/node": "^20.5.0", "typescript": "^5.1.6", "jest": "^29.6.2", "@types/jest": "^29.5.4", "eslint": "^8.47.0", "@typescript-eslint/eslint-plugin": "^6.4.0", "@typescript-eslint/parser": "^6.4.0"}, "keywords": ["face-recognition", "computer-vision", "ai", "tensorflow", "chainsight"], "author": "Connectouch", "license": "MIT"}