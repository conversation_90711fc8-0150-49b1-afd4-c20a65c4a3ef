'use client';

import { motion } from 'framer-motion';
import { TrendingUp, DollarSign, BarChart3, Pie<PERSON>hart } from 'lucide-react';

export function FinanceModule() {
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="space-y-6"
    >
      <div className="bg-white/10 backdrop-blur-xl rounded-2xl p-6 border border-white/20">
        <div className="flex items-center space-x-3 mb-6">
          <div className="w-12 h-12 bg-gradient-to-r from-green-400 to-blue-500 rounded-xl flex items-center justify-center">
            <TrendingUp className="w-6 h-6 text-white" />
          </div>
          <div>
            <h2 className="text-2xl font-bold text-white">AI Finance</h2>
            <p className="text-gray-400">Advanced financial analysis and insights</p>
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          <div className="bg-black/20 rounded-xl p-4">
            <div className="flex items-center space-x-3 mb-3">
              <DollarSign className="w-5 h-5 text-green-400" />
              <span className="text-white font-medium">Portfolio Value</span>
            </div>
            <p className="text-2xl font-bold text-green-400">$125,430</p>
            <p className="text-sm text-gray-400">+12.5% this month</p>
          </div>

          <div className="bg-black/20 rounded-xl p-4">
            <div className="flex items-center space-x-3 mb-3">
              <BarChart3 className="w-5 h-5 text-blue-400" />
              <span className="text-white font-medium">Market Analysis</span>
            </div>
            <p className="text-2xl font-bold text-blue-400">Bullish</p>
            <p className="text-sm text-gray-400">Strong upward trend</p>
          </div>

          <div className="bg-black/20 rounded-xl p-4">
            <div className="flex items-center space-x-3 mb-3">
              <PieChart className="w-5 h-5 text-purple-400" />
              <span className="text-white font-medium">Risk Score</span>
            </div>
            <p className="text-2xl font-bold text-purple-400">7.2/10</p>
            <p className="text-sm text-gray-400">Moderate risk</p>
          </div>
        </div>

        <div className="mt-6 p-4 bg-gradient-to-r from-green-500/20 to-blue-500/20 rounded-xl border border-green-500/30">
          <h3 className="text-white font-semibold mb-2">AI Recommendation</h3>
          <p className="text-gray-300 text-sm">
            Based on current market conditions and your portfolio performance, consider diversifying 
            into emerging tech stocks and reducing exposure to volatile assets.
          </p>
        </div>
      </div>
    </motion.div>
  );
}
