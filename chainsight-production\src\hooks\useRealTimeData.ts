'use client';

import { useState, useEffect, useRef } from 'react';
import { debugUtils } from '@/lib/debug';

interface ConnectionStatus {
  status: 'connected' | 'disconnected' | 'connecting';
  lastUpdate: Date;
  latency: number;
}

interface MarketData {
  crypto: any[];
  stocks: any[];
  forex: any[];
  lastUpdate: Date;
}

export function useRealTimeData() {
  const [connectionStatus, setConnectionStatus] = useState<ConnectionStatus>({
    status: 'connecting',
    lastUpdate: new Date(),
    latency: 0
  });
  
  const [marketData, setMarketData] = useState<MarketData>({
    crypto: [],
    stocks: [],
    forex: [],
    lastUpdate: new Date()
  });

  const wsRef = useRef<WebSocket | null>(null);
  const reconnectTimeoutRef = useRef<NodeJS.Timeout>();

  const connectWebSocket = () => {
    try {
      debugUtils.wsConnect('Demo WebSocket (Simulated)');

      // In production, this would connect to your real WebSocket server
      // For now, we'll simulate real-time updates
      setConnectionStatus(prev => ({ ...prev, status: 'connecting' }));
      
      // Simulate connection
      setTimeout(() => {
        setConnectionStatus({
          status: 'connected',
          lastUpdate: new Date(),
          latency: Math.floor(Math.random() * 50) + 10
        });
      }, 1000);

      // Simulate real-time market data updates
      const interval = setInterval(() => {
        const now = new Date();
        
        // Update connection status
        setConnectionStatus(prev => ({
          ...prev,
          lastUpdate: now,
          latency: Math.floor(Math.random() * 50) + 10
        }));

        // Update market data with realistic fluctuations
        setMarketData(prev => ({
          crypto: prev.crypto.map(item => ({
            ...item,
            price: item.price * (1 + (Math.random() - 0.5) * 0.02), // ±1% change
            change: (Math.random() - 0.5) * 10 // ±5% change
          })),
          stocks: prev.stocks.map(item => ({
            ...item,
            price: item.price * (1 + (Math.random() - 0.5) * 0.01), // ±0.5% change
            change: (Math.random() - 0.5) * 5 // ±2.5% change
          })),
          forex: prev.forex,
          lastUpdate: now
        }));
      }, 5000); // Update every 5 seconds

      return () => {
        clearInterval(interval);
      };

    } catch (error) {
      console.error('WebSocket connection failed:', error);
      setConnectionStatus(prev => ({ ...prev, status: 'disconnected' }));
      
      // Retry connection after 5 seconds
      reconnectTimeoutRef.current = setTimeout(connectWebSocket, 5000);
    }
  };

  useEffect(() => {
    // Initialize with some sample data
    setMarketData({
      crypto: [
        { id: 'bitcoin', symbol: 'BTC', price: 43250, change: 2.5 },
        { id: 'ethereum', symbol: 'ETH', price: 2650, change: -1.2 },
        { id: 'binancecoin', symbol: 'BNB', price: 315, change: 0.8 }
      ],
      stocks: [
        { symbol: 'AAPL', price: 185.50, change: 1.2 },
        { symbol: 'GOOGL', price: 142.30, change: -0.5 },
        { symbol: 'MSFT', price: 378.90, change: 0.8 }
      ],
      forex: [
        { pair: 'EUR/USD', rate: 1.0875, change: 0.15 },
        { pair: 'GBP/USD', rate: 1.2650, change: -0.25 },
        { pair: 'USD/JPY', rate: 149.85, change: 0.35 }
      ],
      lastUpdate: new Date()
    });

    // Start WebSocket connection
    const cleanup = connectWebSocket();

    return () => {
      if (cleanup) cleanup();
      if (reconnectTimeoutRef.current) {
        clearTimeout(reconnectTimeoutRef.current);
      }
      if (wsRef.current) {
        wsRef.current.close();
      }
    };
  }, []);

  return {
    connectionStatus,
    marketData
  };
}
