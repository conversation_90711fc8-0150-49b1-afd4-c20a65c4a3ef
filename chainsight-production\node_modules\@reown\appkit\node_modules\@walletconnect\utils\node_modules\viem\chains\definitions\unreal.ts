import { define<PERSON>hai<PERSON> } from '../../utils/chain/defineChain.js'

export const unreal = /*#__PURE__*/ defineChain({
  id: 18233,
  name: 'Unreal',
  nativeCurrency: {
    name: 'reETH',
    decimals: 18,
    symbol: 'reETH',
  },
  rpcUrls: {
    default: { http: ['https://rpc.unreal-orbit.gelato.digital'] },
  },
  blockExplorers: {
    default: {
      name: 'Unreal Explorer',
      url: 'https://unreal.blockscout.com',
      apiUrl: 'https://unreal.blockscout.com/api/v2',
    },
  },
  testnet: true,
  contracts: {
    multicall3: {
      address: '******************************************',
      blockCreated: 1745,
    },
  },
})
