"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/thread-stream";
exports.ids = ["vendor-chunks/thread-stream"];
exports.modules = {

/***/ "(ssr)/./node_modules/thread-stream/index.js":
/*!*********************************************!*\
  !*** ./node_modules/thread-stream/index.js ***!
  \*********************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nconst { EventEmitter } = __webpack_require__(/*! events */ \"events\")\nconst { Worker } = __webpack_require__(/*! worker_threads */ \"worker_threads\")\nconst { join } = __webpack_require__(/*! path */ \"path\")\nconst { pathToFileURL } = __webpack_require__(/*! url */ \"url\")\nconst { wait } = __webpack_require__(/*! ./lib/wait */ \"(ssr)/./node_modules/thread-stream/lib/wait.js\")\nconst {\n  WRITE_INDEX,\n  READ_INDEX\n} = __webpack_require__(/*! ./lib/indexes */ \"(ssr)/./node_modules/thread-stream/lib/indexes.js\")\nconst buffer = __webpack_require__(/*! buffer */ \"buffer\")\nconst assert = __webpack_require__(/*! assert */ \"assert\")\n\nconst kImpl = Symbol('kImpl')\n\n// V8 limit for string size\nconst MAX_STRING = buffer.constants.MAX_STRING_LENGTH\n\nclass FakeWeakRef {\n  constructor (value) {\n    this._value = value\n  }\n\n  deref () {\n    return this._value\n  }\n}\n\nconst FinalizationRegistry = global.FinalizationRegistry || class FakeFinalizationRegistry {\n  register () {}\n  unregister () {}\n}\n\nconst WeakRef = global.WeakRef || FakeWeakRef\n\nconst registry = new FinalizationRegistry((worker) => {\n  if (worker.exited) {\n    return\n  }\n  worker.terminate()\n})\n\nfunction createWorker (stream, opts) {\n  const { filename, workerData } = opts\n\n  const bundlerOverrides = '__bundlerPathsOverrides' in globalThis ? globalThis.__bundlerPathsOverrides : {}\n  const toExecute = bundlerOverrides['thread-stream-worker'] || join(__dirname, 'lib', 'worker.js')\n\n  const worker = new Worker(toExecute, {\n    ...opts.workerOpts,\n    workerData: {\n      filename: filename.indexOf('file://') === 0\n        ? filename\n        : pathToFileURL(filename).href,\n      dataBuf: stream[kImpl].dataBuf,\n      stateBuf: stream[kImpl].stateBuf,\n      workerData\n    }\n  })\n\n  // We keep a strong reference for now,\n  // we need to start writing first\n  worker.stream = new FakeWeakRef(stream)\n\n  worker.on('message', onWorkerMessage)\n  worker.on('exit', onWorkerExit)\n  registry.register(stream, worker)\n\n  return worker\n}\n\nfunction drain (stream) {\n  assert(!stream[kImpl].sync)\n  if (stream[kImpl].needDrain) {\n    stream[kImpl].needDrain = false\n    stream.emit('drain')\n  }\n}\n\nfunction nextFlush (stream) {\n  const writeIndex = Atomics.load(stream[kImpl].state, WRITE_INDEX)\n  let leftover = stream[kImpl].data.length - writeIndex\n\n  if (leftover > 0) {\n    if (stream[kImpl].buf.length === 0) {\n      stream[kImpl].flushing = false\n\n      if (stream[kImpl].ending) {\n        end(stream)\n      } else if (stream[kImpl].needDrain) {\n        process.nextTick(drain, stream)\n      }\n\n      return\n    }\n\n    let toWrite = stream[kImpl].buf.slice(0, leftover)\n    let toWriteBytes = Buffer.byteLength(toWrite)\n    if (toWriteBytes <= leftover) {\n      stream[kImpl].buf = stream[kImpl].buf.slice(leftover)\n      // process._rawDebug('writing ' + toWrite.length)\n      write(stream, toWrite, nextFlush.bind(null, stream))\n    } else {\n      // multi-byte utf-8\n      stream.flush(() => {\n        // err is already handled in flush()\n        if (stream.destroyed) {\n          return\n        }\n\n        Atomics.store(stream[kImpl].state, READ_INDEX, 0)\n        Atomics.store(stream[kImpl].state, WRITE_INDEX, 0)\n\n        // Find a toWrite length that fits the buffer\n        // it must exists as the buffer is at least 4 bytes length\n        // and the max utf-8 length for a char is 4 bytes.\n        while (toWriteBytes > stream[kImpl].data.length) {\n          leftover = leftover / 2\n          toWrite = stream[kImpl].buf.slice(0, leftover)\n          toWriteBytes = Buffer.byteLength(toWrite)\n        }\n        stream[kImpl].buf = stream[kImpl].buf.slice(leftover)\n        write(stream, toWrite, nextFlush.bind(null, stream))\n      })\n    }\n  } else if (leftover === 0) {\n    if (writeIndex === 0 && stream[kImpl].buf.length === 0) {\n      // we had a flushSync in the meanwhile\n      return\n    }\n    stream.flush(() => {\n      Atomics.store(stream[kImpl].state, READ_INDEX, 0)\n      Atomics.store(stream[kImpl].state, WRITE_INDEX, 0)\n      nextFlush(stream)\n    })\n  } else {\n    // This should never happen\n    throw new Error('overwritten')\n  }\n}\n\nfunction onWorkerMessage (msg) {\n  const stream = this.stream.deref()\n  if (stream === undefined) {\n    this.exited = true\n    // Terminate the worker.\n    this.terminate()\n    return\n  }\n\n  switch (msg.code) {\n    case 'READY':\n      // Replace the FakeWeakRef with a\n      // proper one.\n      this.stream = new WeakRef(stream)\n\n      stream.flush(() => {\n        stream[kImpl].ready = true\n        stream.emit('ready')\n      })\n      break\n    case 'ERROR':\n      destroy(stream, msg.err)\n      break\n    default:\n      throw new Error('this should not happen: ' + msg.code)\n  }\n}\n\nfunction onWorkerExit (code) {\n  const stream = this.stream.deref()\n  if (stream === undefined) {\n    // Nothing to do, the worker already exit\n    return\n  }\n  registry.unregister(stream)\n  stream.worker.exited = true\n  stream.worker.off('exit', onWorkerExit)\n  destroy(stream, code !== 0 ? new Error('The worker thread exited') : null)\n}\n\nclass ThreadStream extends EventEmitter {\n  constructor (opts = {}) {\n    super()\n\n    if (opts.bufferSize < 4) {\n      throw new Error('bufferSize must at least fit a 4-byte utf-8 char')\n    }\n\n    this[kImpl] = {}\n    this[kImpl].stateBuf = new SharedArrayBuffer(128)\n    this[kImpl].state = new Int32Array(this[kImpl].stateBuf)\n    this[kImpl].dataBuf = new SharedArrayBuffer(opts.bufferSize || 4 * 1024 * 1024)\n    this[kImpl].data = Buffer.from(this[kImpl].dataBuf)\n    this[kImpl].sync = opts.sync || false\n    this[kImpl].ending = false\n    this[kImpl].ended = false\n    this[kImpl].needDrain = false\n    this[kImpl].destroyed = false\n    this[kImpl].flushing = false\n    this[kImpl].ready = false\n    this[kImpl].finished = false\n    this[kImpl].errored = null\n    this[kImpl].closed = false\n    this[kImpl].buf = ''\n\n    // TODO (fix): Make private?\n    this.worker = createWorker(this, opts) // TODO (fix): make private\n  }\n\n  write (data) {\n    if (this[kImpl].destroyed) {\n      throw new Error('the worker has exited')\n    }\n\n    if (this[kImpl].ending) {\n      throw new Error('the worker is ending')\n    }\n\n    if (this[kImpl].flushing && this[kImpl].buf.length + data.length >= MAX_STRING) {\n      try {\n        writeSync(this)\n        this[kImpl].flushing = true\n      } catch (err) {\n        destroy(this, err)\n        return false\n      }\n    }\n\n    this[kImpl].buf += data\n\n    if (this[kImpl].sync) {\n      try {\n        writeSync(this)\n        return true\n      } catch (err) {\n        destroy(this, err)\n        return false\n      }\n    }\n\n    if (!this[kImpl].flushing) {\n      this[kImpl].flushing = true\n      setImmediate(nextFlush, this)\n    }\n\n    this[kImpl].needDrain = this[kImpl].data.length - this[kImpl].buf.length - Atomics.load(this[kImpl].state, WRITE_INDEX) <= 0\n    return !this[kImpl].needDrain\n  }\n\n  end () {\n    if (this[kImpl].destroyed) {\n      return\n    }\n\n    this[kImpl].ending = true\n    end(this)\n  }\n\n  flush (cb) {\n    if (this[kImpl].destroyed) {\n      if (typeof cb === 'function') {\n        process.nextTick(cb, new Error('the worker has exited'))\n      }\n      return\n    }\n\n    // TODO write all .buf\n    const writeIndex = Atomics.load(this[kImpl].state, WRITE_INDEX)\n    // process._rawDebug(`(flush) readIndex (${Atomics.load(this.state, READ_INDEX)}) writeIndex (${Atomics.load(this.state, WRITE_INDEX)})`)\n    wait(this[kImpl].state, READ_INDEX, writeIndex, Infinity, (err, res) => {\n      if (err) {\n        destroy(this, err)\n        process.nextTick(cb, err)\n        return\n      }\n      if (res === 'not-equal') {\n        // TODO handle deadlock\n        this.flush(cb)\n        return\n      }\n      process.nextTick(cb)\n    })\n  }\n\n  flushSync () {\n    if (this[kImpl].destroyed) {\n      return\n    }\n\n    writeSync(this)\n    flushSync(this)\n  }\n\n  unref () {\n    this.worker.unref()\n  }\n\n  ref () {\n    this.worker.ref()\n  }\n\n  get ready () {\n    return this[kImpl].ready\n  }\n\n  get destroyed () {\n    return this[kImpl].destroyed\n  }\n\n  get closed () {\n    return this[kImpl].closed\n  }\n\n  get writable () {\n    return !this[kImpl].destroyed && !this[kImpl].ending\n  }\n\n  get writableEnded () {\n    return this[kImpl].ending\n  }\n\n  get writableFinished () {\n    return this[kImpl].finished\n  }\n\n  get writableNeedDrain () {\n    return this[kImpl].needDrain\n  }\n\n  get writableObjectMode () {\n    return false\n  }\n\n  get writableErrored () {\n    return this[kImpl].errored\n  }\n}\n\nfunction destroy (stream, err) {\n  if (stream[kImpl].destroyed) {\n    return\n  }\n  stream[kImpl].destroyed = true\n\n  if (err) {\n    stream[kImpl].errored = err\n    stream.emit('error', err)\n  }\n\n  if (!stream.worker.exited) {\n    stream.worker.terminate()\n      .catch(() => {})\n      .then(() => {\n        stream[kImpl].closed = true\n        stream.emit('close')\n      })\n  } else {\n    setImmediate(() => {\n      stream[kImpl].closed = true\n      stream.emit('close')\n    })\n  }\n}\n\nfunction write (stream, data, cb) {\n  // data is smaller than the shared buffer length\n  const current = Atomics.load(stream[kImpl].state, WRITE_INDEX)\n  const length = Buffer.byteLength(data)\n  stream[kImpl].data.write(data, current)\n  Atomics.store(stream[kImpl].state, WRITE_INDEX, current + length)\n  Atomics.notify(stream[kImpl].state, WRITE_INDEX)\n  cb()\n  return true\n}\n\nfunction end (stream) {\n  if (stream[kImpl].ended || !stream[kImpl].ending || stream[kImpl].flushing) {\n    return\n  }\n  stream[kImpl].ended = true\n\n  try {\n    stream.flushSync()\n\n    let readIndex = Atomics.load(stream[kImpl].state, READ_INDEX)\n\n    // process._rawDebug('writing index')\n    Atomics.store(stream[kImpl].state, WRITE_INDEX, -1)\n    // process._rawDebug(`(end) readIndex (${Atomics.load(stream.state, READ_INDEX)}) writeIndex (${Atomics.load(stream.state, WRITE_INDEX)})`)\n    Atomics.notify(stream[kImpl].state, WRITE_INDEX)\n\n    // Wait for the process to complete\n    let spins = 0\n    while (readIndex !== -1) {\n      // process._rawDebug(`read = ${read}`)\n      Atomics.wait(stream[kImpl].state, READ_INDEX, readIndex, 1000)\n      readIndex = Atomics.load(stream[kImpl].state, READ_INDEX)\n\n      if (readIndex === -2) {\n        throw new Error('end() failed')\n      }\n\n      if (++spins === 10) {\n        throw new Error('end() took too long (10s)')\n      }\n    }\n\n    process.nextTick(() => {\n      stream[kImpl].finished = true\n      stream.emit('finish')\n    })\n  } catch (err) {\n    destroy(stream, err)\n  }\n  // process._rawDebug('end finished...')\n}\n\nfunction writeSync (stream) {\n  const cb = () => {\n    if (stream[kImpl].ending) {\n      end(stream)\n    } else if (stream[kImpl].needDrain) {\n      process.nextTick(drain, stream)\n    }\n  }\n  stream[kImpl].flushing = false\n\n  while (stream[kImpl].buf.length !== 0) {\n    const writeIndex = Atomics.load(stream[kImpl].state, WRITE_INDEX)\n    let leftover = stream[kImpl].data.length - writeIndex\n    if (leftover === 0) {\n      flushSync(stream)\n      Atomics.store(stream[kImpl].state, READ_INDEX, 0)\n      Atomics.store(stream[kImpl].state, WRITE_INDEX, 0)\n      continue\n    } else if (leftover < 0) {\n      // stream should never happen\n      throw new Error('overwritten')\n    }\n\n    let toWrite = stream[kImpl].buf.slice(0, leftover)\n    let toWriteBytes = Buffer.byteLength(toWrite)\n    if (toWriteBytes <= leftover) {\n      stream[kImpl].buf = stream[kImpl].buf.slice(leftover)\n      // process._rawDebug('writing ' + toWrite.length)\n      write(stream, toWrite, cb)\n    } else {\n      // multi-byte utf-8\n      flushSync(stream)\n      Atomics.store(stream[kImpl].state, READ_INDEX, 0)\n      Atomics.store(stream[kImpl].state, WRITE_INDEX, 0)\n\n      // Find a toWrite length that fits the buffer\n      // it must exists as the buffer is at least 4 bytes length\n      // and the max utf-8 length for a char is 4 bytes.\n      while (toWriteBytes > stream[kImpl].buf.length) {\n        leftover = leftover / 2\n        toWrite = stream[kImpl].buf.slice(0, leftover)\n        toWriteBytes = Buffer.byteLength(toWrite)\n      }\n      stream[kImpl].buf = stream[kImpl].buf.slice(leftover)\n      write(stream, toWrite, cb)\n    }\n  }\n}\n\nfunction flushSync (stream) {\n  if (stream[kImpl].flushing) {\n    throw new Error('unable to flush while flushing')\n  }\n\n  // process._rawDebug('flushSync started')\n\n  const writeIndex = Atomics.load(stream[kImpl].state, WRITE_INDEX)\n\n  let spins = 0\n\n  // TODO handle deadlock\n  while (true) {\n    const readIndex = Atomics.load(stream[kImpl].state, READ_INDEX)\n\n    if (readIndex === -2) {\n      throw new Error('_flushSync failed')\n    }\n\n    // process._rawDebug(`(flushSync) readIndex (${readIndex}) writeIndex (${writeIndex})`)\n    if (readIndex !== writeIndex) {\n      // TODO stream timeouts for some reason.\n      Atomics.wait(stream[kImpl].state, READ_INDEX, readIndex, 1000)\n    } else {\n      break\n    }\n\n    if (++spins === 10) {\n      throw new Error('_flushSync took too long (10s)')\n    }\n  }\n  // process._rawDebug('flushSync finished')\n}\n\nmodule.exports = ThreadStream\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/thread-stream/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/thread-stream/lib/indexes.js":
/*!***************************************************!*\
  !*** ./node_modules/thread-stream/lib/indexes.js ***!
  \***************************************************/
/***/ ((module) => {

eval("\n\nconst WRITE_INDEX = 4\nconst READ_INDEX = 8\n\nmodule.exports = {\n  WRITE_INDEX,\n  READ_INDEX\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvdGhyZWFkLXN0cmVhbS9saWIvaW5kZXhlcy5qcyIsIm1hcHBpbmdzIjoiQUFBWTs7QUFFWjtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBIiwic291cmNlcyI6WyJGOlxcQUkgY2hhaW5cXGNoYWluc2lnaHQtcHJvZHVjdGlvblxcbm9kZV9tb2R1bGVzXFx0aHJlYWQtc3RyZWFtXFxsaWJcXGluZGV4ZXMuanMiXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBzdHJpY3QnXG5cbmNvbnN0IFdSSVRFX0lOREVYID0gNFxuY29uc3QgUkVBRF9JTkRFWCA9IDhcblxubW9kdWxlLmV4cG9ydHMgPSB7XG4gIFdSSVRFX0lOREVYLFxuICBSRUFEX0lOREVYXG59XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/thread-stream/lib/indexes.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/thread-stream/lib/wait.js":
/*!************************************************!*\
  !*** ./node_modules/thread-stream/lib/wait.js ***!
  \************************************************/
/***/ ((module) => {

eval("\n\nconst MAX_TIMEOUT = 1000\n\nfunction wait (state, index, expected, timeout, done) {\n  const max = Date.now() + timeout\n  let current = Atomics.load(state, index)\n  if (current === expected) {\n    done(null, 'ok')\n    return\n  }\n  let prior = current\n  const check = (backoff) => {\n    if (Date.now() > max) {\n      done(null, 'timed-out')\n    } else {\n      setTimeout(() => {\n        prior = current\n        current = Atomics.load(state, index)\n        if (current === prior) {\n          check(backoff >= MAX_TIMEOUT ? MAX_TIMEOUT : backoff * 2)\n        } else {\n          if (current === expected) done(null, 'ok')\n          else done(null, 'not-equal')\n        }\n      }, backoff)\n    }\n  }\n  check(1)\n}\n\n// let waitDiffCount = 0\nfunction waitDiff (state, index, expected, timeout, done) {\n  // const id = waitDiffCount++\n  // process._rawDebug(`>>> waitDiff ${id}`)\n  const max = Date.now() + timeout\n  let current = Atomics.load(state, index)\n  if (current !== expected) {\n    done(null, 'ok')\n    return\n  }\n  const check = (backoff) => {\n    // process._rawDebug(`${id} ${index} current ${current} expected ${expected}`)\n    // process._rawDebug('' + backoff)\n    if (Date.now() > max) {\n      done(null, 'timed-out')\n    } else {\n      setTimeout(() => {\n        current = Atomics.load(state, index)\n        if (current !== expected) {\n          done(null, 'ok')\n        } else {\n          check(backoff >= MAX_TIMEOUT ? MAX_TIMEOUT : backoff * 2)\n        }\n      }, backoff)\n    }\n  }\n  check(1)\n}\n\nmodule.exports = { wait, waitDiff }\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/thread-stream/lib/wait.js\n");

/***/ })

};
;